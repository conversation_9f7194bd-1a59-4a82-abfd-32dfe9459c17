package com.tapdata.tm.license.service;

import cn.hutool.core.io.FileUtil;
import com.tapdata.tm.Settings.entity.Settings;
import com.tapdata.tm.Settings.service.SettingsService;
import com.tapdata.tm.Settings.service.SettingsServiceImpl;
import com.tapdata.tm.commons.schema.DataSourceDefinitionDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceDefinitionService;
import com.tapdata.tm.license.dto.*;
import com.tapdata.tm.license.repository.LicenseRepository;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.utils.OEMReplaceUtil;
import com.tapdata.tm.worker.dto.WorkerDto;
import com.tapdata.tm.worker.entity.Worker;
import com.tapdata.tm.worker.service.WorkerService;
import io.tapdata.entity.error.CoreException;
import lombok.SneakyThrows;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class LicenseServiceTest {
	private LicenseRepository licenseRepository;
	private LicenseService licenseService;
	private WorkerService workerService;
	private TaskService taskService;
	private SettingsService settingsService;
	private DataSourceDefinitionService dataSourceDefinitionService;

	private List<Settings> settingsList = new ArrayList<>();
	@BeforeEach
	void buildLicenseService(){
		Settings port = new Settings();
		port.setKey("smtp.server.host");
		port.setValue("465");
		Settings host = new Settings();
		host.setKey("smtp.server.host");
		host.setValue("test");
		Settings form = new Settings();
		form.setKey("email.send.address");
		form.setValue("test");
		Settings user = new Settings();
		user.setKey("smtp.server.user");
		user.setValue("test");
		Settings pwd = new Settings();
		pwd.setKey("smtp.server.password");
		pwd.setValue("test");
		Settings address = new Settings();
		address.setKey("email.receivers");
		address.setValue("test");
		settingsList.add(port);
		settingsList.add(host);
		settingsList.add(form);
		settingsList.add(user);
		settingsList.add(pwd);
		settingsList.add(address);
		licenseRepository = mock(LicenseRepository.class);
		licenseService = spy(new LicenseService(licenseRepository));
		workerService = mock(WorkerService.class);
		ReflectionTestUtils.setField(licenseService,"workerService",workerService);
		taskService = mock(TaskService.class);
		ReflectionTestUtils.setField(licenseService,"taskService",taskService);
		settingsService = mock(SettingsServiceImpl.class);
		ReflectionTestUtils.setField(licenseService,"settingsService",settingsService);
		dataSourceDefinitionService = mock(DataSourceDefinitionService.class);
		ReflectionTestUtils.setField(licenseService,"dataSourceDefinitionService",dataSourceDefinitionService);
	}
	@Nested
	class TestCheckEngineValid{
		@Test
		@DisplayName("processId is null")
		void test1(){
			String processId = null;
			assertThrows(IllegalArgumentException.class,()->licenseService.checkEngineValid(processId,mock(UserDetail.class)));
		}
		@Test
		@DisplayName("processId is blank")
		void test2(){
			String processId = "   ";
			assertThrows(IllegalArgumentException.class,()->licenseService.checkEngineValid(processId,mock(UserDetail.class)));
		}
		@Test
		@DisplayName("engine limit exceed")
		void test3(){
			ReflectionTestUtils.setField(licenseService,"engineLimit",1);
			doReturn(2).when(licenseService).checkBoundEngineCount();
			doReturn("111").when(licenseService).checkRunningTaskNumberByProcessId();
			when(workerService.unbindByProcessId("111")).thenReturn(true);
			CheckEngineValidResultDto actual = licenseService.checkEngineValid("111", mock(UserDetail.class));
			assertEquals(false,actual.getResult());
		}
		@Test
		@DisplayName("unbind failed")
		void test4(){
			ReflectionTestUtils.setField(licenseService,"engineLimit",1);
			doReturn(2).when(licenseService).checkBoundEngineCount();
			doReturn("111").when(licenseService).checkRunningTaskNumberByProcessId();
			when(workerService.unbindByProcessId("111")).thenReturn(false);
			assertThrows(CoreException.class,()->licenseService.checkEngineValid("111", mock(UserDetail.class)));
		}
		@Test
		@DisplayName("engine has been bound")
		void test5(){
			ReflectionTestUtils.setField(licenseService,"engineLimit",1);
			doReturn(1).when(licenseService).checkBoundEngineCount();
			WorkerDto workerDto = new WorkerDto();
			workerDto.setLicenseBind(true);
			when(workerService.queryWorkerByProcessId("111")).thenReturn(workerDto);
			CheckEngineValidResultDto actual = licenseService.checkEngineValid("111", mock(UserDetail.class));
			assertEquals(true,actual.getResult());
		}
		@Test
		@DisplayName("engine unbound and equal limit")
		void test6(){
			ReflectionTestUtils.setField(licenseService,"engineLimit",1);
			doReturn(1).when(licenseService).checkBoundEngineCount();
			WorkerDto workerDto = new WorkerDto();
			workerDto.setLicenseBind(false);
			when(workerService.queryWorkerByProcessId("111")).thenReturn(workerDto);
			CheckEngineValidResultDto actual = licenseService.checkEngineValid("111", mock(UserDetail.class));
			assertEquals(false,actual.getResult());
		}
		@Test
		@DisplayName("engine to bind")
		void test7(){
			ReflectionTestUtils.setField(licenseService,"engineLimit",1);
			doReturn(0).when(licenseService).checkBoundEngineCount();
			WorkerDto workerDto = new WorkerDto();
			workerDto.setLicenseBind(false);
			when(workerService.queryWorkerByProcessId("111")).thenReturn(workerDto);
			when(workerService.bindByProcessId(workerDto,"111",mock(UserDetail.class))).thenReturn(true);
			CheckEngineValidResultDto actual = licenseService.checkEngineValid("111", mock(UserDetail.class));
			assertEquals(false,actual.getResult());
		}
		@Test
		@DisplayName("worker is null and engine to bind")
		void test8(){
			ReflectionTestUtils.setField(licenseService,"engineLimit",1);
			doReturn(0).when(licenseService).checkBoundEngineCount();
			WorkerDto workerDto = null;
			when(workerService.queryWorkerByProcessId("111")).thenReturn(workerDto);
			when(workerService.bindByProcessId(workerDto,"111",mock(UserDetail.class))).thenReturn(true);
			CheckEngineValidResultDto actual = licenseService.checkEngineValid("111", mock(UserDetail.class));
			assertEquals(false,actual.getResult());
		}
	}
	@Nested
	class TestCheckBoundEngineCount{
		@Test
		@DisplayName("check bound engine count")
		void test1(){
			when(workerService.queryAllBindWorker()).thenReturn(new ArrayList<>());
			int actual = licenseService.checkBoundEngineCount();
			assertEquals(0,actual);
		}
	}
	@Nested
	class TestCheckRunningTaskNumberByProcessId{
		@Test
		@DisplayName("check running task number by processId")
		void test1(){
			List<Worker> workerList =  new ArrayList<>();
			Worker worker1 = new Worker();
			worker1.setProcessId("1111");
			Worker worker2 = new Worker();
			worker2.setProcessId("2222");
			workerList.add(worker1);
			workerList.add(worker2);
			when(workerService.queryAllBindWorker()).thenReturn(workerList);
			when(taskService.findRunningTasksByAgentId(worker1.getProcessId())).thenReturn(1);
			when(taskService.findRunningTasksByAgentId(worker2.getProcessId())).thenReturn(2);
			String actual = licenseService.checkRunningTaskNumberByProcessId();
			assertEquals(worker1.getProcessId(),actual);
		}
	}

	@Nested
	class testSendLicenseAlarm {
		@BeforeEach
		void beforeEach(){
			doCallRealMethod().when(settingsService).getMailAccount(null);
		}
		@Test
		@DisplayName("Send emails based on system settings")
		void test1() {
			try (MockedStatic<MessageFormat> mockedStatic = Mockito.mockStatic(MessageFormat.class)) {
				Long expires = System.currentTimeMillis() + *********;
				Settings settings = new Settings();
				settings.setValue("10");
				when(settingsService.getByKey("license_alarm")).thenReturn(settings);
				when(settingsService.findAll()).thenReturn(settingsList);
				mockedStatic.when(() -> MessageFormat.format(anyString(), anyLong())).thenAnswer(invocationOnMock -> {
					long result = invocationOnMock.getArgument(1);
					Assertions.assertEquals(10, result);
					return null;
				});
				licenseService.sendLicenseAlarm(expires);
				AtomicBoolean result = (AtomicBoolean) ReflectionTestUtils.getField(licenseService, "isSendFlag");
				Assertions.assertFalse(result.get());
			}
		}


		@Test
		@DisplayName("Email notification occurs in the last week")
		void test2() {
			try (MockedStatic<MessageFormat> mockedStatic = Mockito.mockStatic(MessageFormat.class)) {
				Long expires = System.currentTimeMillis() + 604801000;
				when(settingsService.findAll()).thenReturn(settingsList);
				mockedStatic.when(() -> MessageFormat.format(anyString(), anyLong())).thenAnswer(invocationOnMock -> {
					long result = invocationOnMock.getArgument(1);
					Assertions.assertEquals(7, result);
					return null;
				});
				licenseService.sendLicenseAlarm(expires);
				AtomicBoolean result = (AtomicBoolean) ReflectionTestUtils.getField(licenseService, "isSendFlag");
				Assertions.assertFalse(result.get());
			}
		}

		@Test
		@DisplayName("Email notification occurs in the last week")
		void test3() {
			try (MockedStatic<MessageFormat> mockedStatic = Mockito.mockStatic(MessageFormat.class)) {
				Long expires = System.currentTimeMillis() + 86401000;
				when(settingsService.findAll()).thenReturn(settingsList);
				mockedStatic.when(() -> MessageFormat.format(anyString(), anyLong())).thenAnswer(invocationOnMock -> {
					long result = invocationOnMock.getArgument(1);
					Assertions.assertEquals(1, result);
					return null;
				});
				licenseService.sendLicenseAlarm(expires);
				AtomicBoolean result = (AtomicBoolean) ReflectionTestUtils.getField(licenseService, "isSendFlag");
				Assertions.assertFalse(result.get());
			}
		}

		@Test
		@DisplayName("Send emails after expiration")
		void test4() {
			Long expires = System.currentTimeMillis() - 1000;
			when(settingsService.findAll()).thenReturn(settingsList);
			licenseService.sendLicenseAlarm(expires);
			AtomicBoolean result = (AtomicBoolean) ReflectionTestUtils.getField(licenseService, "isSendFlag");
			Assertions.assertFalse(result.get());
		}

		@Test
		@DisplayName("No need to send email")
		void test5() {
			Long expires = System.currentTimeMillis() + 8640000000L;
			when(settingsService.findAll()).thenReturn(settingsList);
			licenseService.sendLicenseAlarm(expires);
			AtomicBoolean result = (AtomicBoolean) ReflectionTestUtils.getField(licenseService, "isSendFlag");
			Assertions.assertTrue(result.get());
		}
	}
	@Nested
	class UpdateLicenseByHostnameTest{
		@Test
		void testUpdateLicenseByHostname(){
			LicenseDto fileLicenseDto = mock(LicenseDto.class);
			LicenseDto dbLicenseDto = new LicenseDto();
			String license = "license-test";
			Long expiresOn = 1710129200369L;
			String sid = "123456";
			when(fileLicenseDto.getLicense()).thenReturn(license);
			LicenseDto.ValidityPeriod period = mock(LicenseDto.ValidityPeriod.class);
			when(fileLicenseDto.getValidity_period()).thenReturn(period);
			when(period.getExpires_on()).thenReturn(expiresOn);
			when(fileLicenseDto.getSid()).thenReturn(sid);
			when(fileLicenseDto.getValidity_period()).thenReturn(period);
			licenseService.updateLicenseByHostname(fileLicenseDto,dbLicenseDto);
			assertEquals(license,dbLicenseDto.getLicense());
			assertEquals(new Date(expiresOn),dbLicenseDto.getExpirationDate());
			assertEquals(sid,dbLicenseDto.getSid());
			assertEquals(period,dbLicenseDto.getValidity_period());
		}
	}
	@Nested
	class UpdateLicenseTest{
		@Test
		void testUpdateLicense(){
			LicenseDto dbLicense = new LicenseDto();
			LicenseUpdateDto updateDto = mock(LicenseUpdateDto.class);
			String license = "license-test";
			Long expiresOn = 1710129200369L;
			String sid = "123456";
			LicenseUpdateReqDto reqDto = mock(LicenseUpdateReqDto.class);
			when(updateDto.getReqDto()).thenReturn(reqDto);
			when(reqDto.getLicense()).thenReturn(license);
			LicenseUpdateDto.ValidityPeriod period = mock(LicenseUpdateDto.ValidityPeriod.class);
			when(updateDto.getValidity_period()).thenReturn(period);
			when(period.getExpires_on()).thenReturn(expiresOn);
			when(updateDto.getSid()).thenReturn(sid);
			when(updateDto.getValidity_period()).thenReturn(period);
			licenseService.updateLicense(dbLicense,updateDto);
			assertEquals(license,dbLicense.getLicense());
			assertEquals(new Date(expiresOn),dbLicense.getExpirationDate());
			assertEquals(sid,dbLicense.getSid());
			assertEquals(expiresOn,dbLicense.getValidity_period().getExpires_on());
		}
	}
	@Nested
	@DisplayName("test for syncLicenseFileAndMongodb method")
	class SyncLicenseFileAndMongodbTest{
		@Test
		@SneakyThrows
		@DisplayName("when fileLicenseDto is null")
		void testSyncLicenseFileAndMongodbWithNullFile(){
			try (MockedStatic<FileUtil> fileUtilMockedStatic = Mockito
				.mockStatic(FileUtil.class)) {
				LicenseDto fileLicenseDto = mock(LicenseDto.class);
				LicenseDto dbLicenseDto = mock(LicenseDto.class);
				String license = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
				Path licenseFilePath = Paths.get("license.txt");
				ReflectLicenseFilePath(licenseFilePath);
				fileUtilMockedStatic.when(()->FileUtil.writeString(license,licenseFilePath.toFile(), StandardCharsets.UTF_8)).thenReturn(null);
				when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
				when(licenseService.findOne(any())).thenReturn(dbLicenseDto);
				when(fileLicenseDto.getLicense()).thenReturn(null);
				when(dbLicenseDto.getLicense()).thenReturn(license);
				licenseService.syncLicenseFileAndMongodb();
				fileUtilMockedStatic.verify(()->FileUtil.writeString(license,licenseFilePath.toFile(), StandardCharsets.UTF_8),new Times(1));
			}
		}
		@Test
		@SneakyThrows
		@DisplayName("when dbLicenseDto is null")
		void testSyncLicenseFileAndMongodbWithUpdateDB(){
			LicenseDto fileLicenseDto = mock(LicenseDto.class);
			LicenseDto dbLicenseDto = mock(LicenseDto.class);
			String license = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
			when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
			when(fileLicenseDto.getLicense()).thenReturn(license);
			when(dbLicenseDto.getLicense()).thenReturn(null);
			when(fileLicenseDto.getLicense_sid()).thenReturn(null);
			LicenseDto.ValidityPeriod period = mock(LicenseDto.ValidityPeriod.class);
			when(fileLicenseDto.getValidity_period()).thenReturn(period);
			licenseService.syncLicenseFileAndMongodb();
			verify(licenseService, new Times(1)).updateLicenseByHostname(any(),any());
		}
		@Test
		@SneakyThrows
		@DisplayName("when fileLicenseDto equals dbLicenseDto")
		void testSyncLicenseFileAndMongodb(){
			LicenseDto fileLicenseDto = mock(LicenseDto.class);
			LicenseDto dbLicenseDto = fileLicenseDto;
			String license = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
			when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
			when(licenseService.findOne(any())).thenReturn(dbLicenseDto);
			when(fileLicenseDto.getLicense()).thenReturn(license);
			when(dbLicenseDto.getLicense()).thenReturn(license);
			licenseService.syncLicenseFileAndMongodb();
			verify(licenseService, new Times(0)).updateLicenseByHostname(any(),any());
		}
		@Test
		@SneakyThrows
		@DisplayName("when fileLicenseDto is new")
		void testSyncLicenseFileAndMongodbWithNewFile(){
			LicenseDto fileLicenseDto = mock(LicenseDto.class);
			LicenseDto dbLicenseDto = mock(LicenseDto.class);
			String fileLicense = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
			String dbLicense = "+50xtNFS7lr70o5sB9oSZVogiTVcUTOMcT9VR7Jqk9V0I0q9hUJ1G0r+nbm6uwoSejxIC1Qjgt/FiOI2R/S72re60Zzy0xTTmDcw5JyVgIYbgUzYU2Lmivkc6k1sCgwwmPHNRDTXW1JWUFhRVNsQf6spQ+1Kgxb6Nr3c4M/QxF+QSsx1Fl+2QNu/y3hdjMK0.cN4hRnYnh65jpExJQishpyykFAmjPlpvPoy1/AalkOTD2baJ6NlYraxTIjqdYeU96HLZQIFZQHj1lKIKZ5v+29QDuckDeQYiLIl5r2HhR+CZH75BTa2XNqeuBrUNC+lXUA7Tf2z5LxCd5hh0/cqQu4CCfx2gfoqLTTqKTZqwsemWYUjU6lbspQUpiE/kychOTcXDlY0m3sZMyGgz1HDKXuotW5UtoUHgMS7bKpC9mTkaSAObbAaJbnowG8vGAuzVVeR5VEkd7MlB0jtn+FHcwtZPWxsWAEOsmGI0icfUaHYskkUpARqLR8Vs4rWfuGCvByvPCyTR1WVZ0qMSLnOfwQ==";
			when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
			when(licenseService.findOne(any())).thenReturn(dbLicenseDto);
			when(fileLicenseDto.getLicense()).thenReturn(fileLicense);
			when(dbLicenseDto.getLicense()).thenReturn(dbLicense);
			Path licenseFilePath = Paths.get("license.txt");
			File file = new File(licenseFilePath.toString());
			file.createNewFile();
			ReflectLicenseFilePath(licenseFilePath);
			when(dbLicenseDto.getLastUpdAt()).thenReturn(new Date(**********));
			when(fileLicenseDto.getLicense_sid()).thenReturn(null);
			LicenseDto.ValidityPeriod period = mock(LicenseDto.ValidityPeriod.class);
			when(fileLicenseDto.getValidity_period()).thenReturn(period);
			licenseService.syncLicenseFileAndMongodb();
			verify(licenseService, new Times(1)).updateLicenseByHostname(any(),any());
			file.delete();
		}
		@Test
		@SneakyThrows
		@DisplayName("when dbLicenseDto is new")
		void testSyncLicenseFileAndMongodbWithNewDB() {
			try (MockedStatic<FileUtil> fileUtilMockedStatic = Mockito
				.mockStatic(FileUtil.class)) {
				LicenseDto fileLicenseDto = mock(LicenseDto.class);
				LicenseDto dbLicenseDto = mock(LicenseDto.class);
				Path licenseFilePath = Paths.get("license.txt");
				String fileLicense = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
				String dbLicense = "+50xtNFS7lr70o5sB9oSZVogiTVcUTOMcT9VR7Jqk9V0I0q9hUJ1G0r+nbm6uwoSejxIC1Qjgt/FiOI2R/S72re60Zzy0xTTmDcw5JyVgIYbgUzYU2Lmivkc6k1sCgwwmPHNRDTXW1JWUFhRVNsQf6spQ+1Kgxb6Nr3c4M/QxF+QSsx1Fl+2QNu/y3hdjMK0.cN4hRnYnh65jpExJQishpyykFAmjPlpvPoy1/AalkOTD2baJ6NlYraxTIjqdYeU96HLZQIFZQHj1lKIKZ5v+29QDuckDeQYiLIl5r2HhR+CZH75BTa2XNqeuBrUNC+lXUA7Tf2z5LxCd5hh0/cqQu4CCfx2gfoqLTTqKTZqwsemWYUjU6lbspQUpiE/kychOTcXDlY0m3sZMyGgz1HDKXuotW5UtoUHgMS7bKpC9mTkaSAObbAaJbnowG8vGAuzVVeR5VEkd7MlB0jtn+FHcwtZPWxsWAEOsmGI0icfUaHYskkUpARqLR8Vs4rWfuGCvByvPCyTR1WVZ0qMSLnOfwQ==";
				fileUtilMockedStatic.when(() -> FileUtil.writeString(dbLicense, licenseFilePath.toFile(), StandardCharsets.UTF_8)).thenReturn(null);
				when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
				when(licenseService.findOne(any())).thenReturn(dbLicenseDto);
				when(fileLicenseDto.getLicense()).thenReturn(fileLicense);
				when(dbLicenseDto.getLicense()).thenReturn(dbLicense);
				File file = new File(licenseFilePath.toString());
				file.createNewFile();
				ReflectLicenseFilePath(licenseFilePath);
				when(dbLicenseDto.getLastUpdAt()).thenReturn(new Date());
				licenseService.syncLicenseFileAndMongodb();
				fileUtilMockedStatic.verify(() -> FileUtil.writeString(dbLicense, licenseFilePath.toFile(), StandardCharsets.UTF_8), new Times(1));
				file.delete();
			}
		}
		@Test
		@SneakyThrows
		@DisplayName("when getLastUpdAt is null")
		void testSyncLicenseFileAndMongodbWithoutLastUpdAt() {
			LicenseDto fileLicenseDto = mock(LicenseDto.class);
			LicenseDto dbLicenseDto = mock(LicenseDto.class);
			Path licenseFilePath = Paths.get("license.txt");
			String fileLicense = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
			String dbLicense = "+50xtNFS7lr70o5sB9oSZVogiTVcUTOMcT9VR7Jqk9V0I0q9hUJ1G0r+nbm6uwoSejxIC1Qjgt/FiOI2R/S72re60Zzy0xTTmDcw5JyVgIYbgUzYU2Lmivkc6k1sCgwwmPHNRDTXW1JWUFhRVNsQf6spQ+1Kgxb6Nr3c4M/QxF+QSsx1Fl+2QNu/y3hdjMK0.cN4hRnYnh65jpExJQishpyykFAmjPlpvPoy1/AalkOTD2baJ6NlYraxTIjqdYeU96HLZQIFZQHj1lKIKZ5v+29QDuckDeQYiLIl5r2HhR+CZH75BTa2XNqeuBrUNC+lXUA7Tf2z5LxCd5hh0/cqQu4CCfx2gfoqLTTqKTZqwsemWYUjU6lbspQUpiE/kychOTcXDlY0m3sZMyGgz1HDKXuotW5UtoUHgMS7bKpC9mTkaSAObbAaJbnowG8vGAuzVVeR5VEkd7MlB0jtn+FHcwtZPWxsWAEOsmGI0icfUaHYskkUpARqLR8Vs4rWfuGCvByvPCyTR1WVZ0qMSLnOfwQ==";
			when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
			when(licenseService.findOne(any())).thenReturn(dbLicenseDto);
			when(fileLicenseDto.getLicense()).thenReturn(fileLicense);
			when(dbLicenseDto.getLicense()).thenReturn(dbLicense);
			File file = new File(licenseFilePath.toString());
			file.createNewFile();
			ReflectLicenseFilePath(licenseFilePath);
			when(dbLicenseDto.getLastUpdAt()).thenReturn(null);
			when(fileLicenseDto.getLicense_sid()).thenReturn(null);
			LicenseDto.ValidityPeriod period = mock(LicenseDto.ValidityPeriod.class);
			when(fileLicenseDto.getValidity_period()).thenReturn(period);
			licenseService.syncLicenseFileAndMongodb();
			verify(licenseService, new Times(1)).updateLicenseByHostname(any(), any());
			file.delete();
		}
		@Test
		@SneakyThrows
		@DisplayName("when fileLastModified is null")
		void testSyncLicenseFileAndMongodbWithoutFileLastModified() {
			try (MockedStatic<FileUtil> fileUtilMockedStatic = Mockito
				.mockStatic(FileUtil.class)) {
				LicenseDto fileLicenseDto = mock(LicenseDto.class);
				LicenseDto dbLicenseDto = mock(LicenseDto.class);
				Path licenseFilePath = Paths.get("license.txt");
				String fileLicense = "FVPmkJyXXhaAxfMN5BgzT3w360A4rHjnuXCW5QpkOG4Bms7IDotv9IZT3qIHZoo5qX/QrIZAWxtmYt1CBwTt7cCCp1RayHLyXcJtDlRq0s64R8+XuTQM0A92lIIGhp4O5i6OwYZapj8FZ3VGcBIr4OEGrvszPYehCCshHioyD5uQOxr3R1C9nzKaYH1CHz6q.sy8/KbfgtHTQED7qFj9NPb/fO/YekOJwC17HqyEnpxIi+riFS67ASrJ0DItgpYePdCHeL9wKA0e2Wk3wlm1hM/qifT52AxebVc0vdL0S4DERd9k9FD5necUb12T3zvi7S8I3MLwXEiL7jN281jMfvyue4IWTg9ukQ7K8/pY93QYzWASuDLJ6uVZ+gRXtx9uNwfRHYrK2gmKHsVNPGctjKucCJAyouSDOzlDg3oNnKT2M8wA1pSTXJZwXWJDmzrSPWRkvu8lPsEyuyZPd5HMXuhDOGh9OaKr0Jy2d4ShL0sRx/yk9HObbc3TTVWX5c2bMFmUFcrQwX3FgEyXZqHo49g==";
				String dbLicense = "+50xtNFS7lr70o5sB9oSZVogiTVcUTOMcT9VR7Jqk9V0I0q9hUJ1G0r+nbm6uwoSejxIC1Qjgt/FiOI2R/S72re60Zzy0xTTmDcw5JyVgIYbgUzYU2Lmivkc6k1sCgwwmPHNRDTXW1JWUFhRVNsQf6spQ+1Kgxb6Nr3c4M/QxF+QSsx1Fl+2QNu/y3hdjMK0.cN4hRnYnh65jpExJQishpyykFAmjPlpvPoy1/AalkOTD2baJ6NlYraxTIjqdYeU96HLZQIFZQHj1lKIKZ5v+29QDuckDeQYiLIl5r2HhR+CZH75BTa2XNqeuBrUNC+lXUA7Tf2z5LxCd5hh0/cqQu4CCfx2gfoqLTTqKTZqwsemWYUjU6lbspQUpiE/kychOTcXDlY0m3sZMyGgz1HDKXuotW5UtoUHgMS7bKpC9mTkaSAObbAaJbnowG8vGAuzVVeR5VEkd7MlB0jtn+FHcwtZPWxsWAEOsmGI0icfUaHYskkUpARqLR8Vs4rWfuGCvByvPCyTR1WVZ0qMSLnOfwQ==";
				fileUtilMockedStatic.when(() -> FileUtil.writeString(dbLicense, licenseFilePath.toFile(), StandardCharsets.UTF_8)).thenReturn(null);
				when(licenseService.readLicenseFromFile()).thenReturn(fileLicenseDto);
				when(licenseService.findOne(any())).thenReturn(dbLicenseDto);
				when(fileLicenseDto.getLicense()).thenReturn(fileLicense);
				when(dbLicenseDto.getLicense()).thenReturn(dbLicense);
				ReflectLicenseFilePath(licenseFilePath);
				when(dbLicenseDto.getLastUpdAt()).thenReturn(new Date(**********));
				LicenseDto.ValidityPeriod period = mock(LicenseDto.ValidityPeriod.class);
				when(fileLicenseDto.getValidity_period()).thenReturn(period);
				licenseService.syncLicenseFileAndMongodb();
				fileUtilMockedStatic.verify(() -> FileUtil.writeString(dbLicense, licenseFilePath.toFile(), StandardCharsets.UTF_8), new Times(1));
			}
		}

		private void ReflectLicenseFilePath(Path licenseFilePath) throws NoSuchFieldException, IllegalAccessException {
			Field path = LicenseService.class.getDeclaredField("licenseFilePath");
			path.setAccessible(true);
			Field modifiers = Field.class.getDeclaredField("modifiers");
			modifiers.setAccessible(true);
			modifiers.setInt(path, path.getModifiers() & ~Modifier.FINAL);
			path.set(licenseService, licenseFilePath);
		}
	}
	@Nested
	class checkLicenseTypeTest{
		@Test
		@DisplayName("test checkLicenseType method when sid is empty")
		void test1(){
			LicenseDto licenseDto = mock(LicenseDto.class);
			when(licenseDto.getLicense_sid()).thenReturn("");
			doCallRealMethod().when(licenseService).checkLicenseType(licenseDto);
			boolean actual = licenseService.checkLicenseType(licenseDto);
			assertTrue(actual);
		}
		@Test
		@DisplayName("test checkLicenseType method when application does not match license type")
		void test2(){
			try (MockedStatic<OEMReplaceUtil> mb = Mockito
				.mockStatic(OEMReplaceUtil.class)) {
				mb.when(OEMReplaceUtil::oemType).thenReturn(null);
				LicenseDto licenseDto = mock(LicenseDto.class);
				when(licenseDto.getLicense_sid()).thenReturn("111");
				when(licenseDto.getLicenseType()).thenReturn("PIPELINE");
				doCallRealMethod().when(licenseService).checkLicenseType(licenseDto);
				boolean actual = licenseService.checkLicenseType(licenseDto);
				assertFalse(actual);
			}
		}
		@Test
		@DisplayName("test checkLicenseType method when application match license type")
		void test3(){
			try (MockedStatic<OEMReplaceUtil> mb = Mockito
				.mockStatic(OEMReplaceUtil.class)) {
				mb.when(OEMReplaceUtil::oemType).thenReturn("datapp");
				LicenseDto licenseDto = mock(LicenseDto.class);
				when(licenseDto.getLicense_sid()).thenReturn("111");
				when(licenseDto.getLicenseType()).thenReturn("PIPELINE");
				doCallRealMethod().when(licenseService).checkLicenseType(licenseDto);
				boolean actual = licenseService.checkLicenseType(licenseDto);
				assertTrue(actual);
			}
		}
	}
	@Nested
	class checkTaskPipelineLimitTest{
		private TaskDto taskDto;
		private UserDetail user;
		private TaskPipelineLimitService taskPipelineService;
		@BeforeEach
		void beforeEach(){
			taskDto = mock(TaskDto.class);
			user = mock(UserDetail.class);
			doCallRealMethod().when(licenseService).checkTaskPipelineLimit(taskDto,user);
			taskPipelineService = mock(TaskPipelineLimitService.class);
			ReflectionTestUtils.setField(licenseService,"datasourcePipelineLimit",1);
			ReflectionTestUtils.setField(licenseService,"taskPipelineService",taskPipelineService);
		}
		@Test
		void testCheckTaskPipelineLimit(){
			ObjectId id = mock(ObjectId.class);
			when(taskDto.getId()).thenReturn(id);
			when(taskDto.getUserId()).thenReturn("66d055c410cfdf4d6a99a9ef");
			ReflectionTestUtils.setField(licenseService,"licenseType","PIPELINE");
			licenseService.checkTaskPipelineLimit(taskDto,user);
			verify(taskPipelineService,new Times(1)).recordTaskPipelineGrant(taskDto,1,user);
		}
		@Test
		void testCheckTaskPipelineLimitForOP(){
			ObjectId id = mock(ObjectId.class);
			when(taskDto.getId()).thenReturn(id);
			when(taskDto.getUserId()).thenReturn("66d055c410cfdf4d6a99a9ef");
			ReflectionTestUtils.setField(licenseService,"licenseType","OP");
			boolean actual = licenseService.checkTaskPipelineLimit(taskDto, user);
			assertTrue(actual);
			verify(taskPipelineService,new Times(0)).recordTaskPipelineGrant(taskDto,1,user);
		}
	}
	@Nested
	class checkLicenseTest{
		@Test
		@SneakyThrows
		void testForLicenseTypeNotMatch(){
			LicenseDto licenseDto = mock(LicenseDto.class);
			when(licenseDto.getLicenseType()).thenReturn("PIPELINE");
			when(licenseDto.getDatasourcePipelineLimit()).thenReturn(1);
			LicenseDto.ValidityPeriod validityPeriod = mock(LicenseDto.ValidityPeriod.class);
			when(licenseDto.getValidity_period()).thenReturn(validityPeriod);
			when(validityPeriod.getExpires_on()).thenReturn(new Date().getTime()+10000L);
			when(licenseService.getLicenseDto()).thenReturn(licenseDto);
			when(licenseService.checkLicenseType(licenseDto)).thenReturn(false);
			doCallRealMethod().when(licenseService).checkLicense();
			Map<String, Object> actual = licenseService.checkLicense();
			assertEquals("invalid_license_type",actual.get("status"));
		}
	}

	@Nested
	class getFeatureTest {
		@Test
		void testGetFeatureWhenNonLicense() throws Throwable {
			when(licenseService.getLicenseDto()).thenReturn(null);
			LicenseDto feature = licenseService.getFeatures();
			Assertions.assertNotNull(feature);
			Assertions.assertNotNull(feature.getFeatures());
			Assertions.assertNotNull(feature.getLicenseType());
			Assertions.assertEquals(LicenseType.LITE.name(), feature.getLicenseType());
		}

		@Test
		void testGetFeatureForLite() throws Throwable {
			LicenseDto licenseDto = new LicenseDto();
			licenseDto.setLicenseType(LicenseType.OP.name());
			licenseDto.setSid("test");

			when(licenseService.getLicenseDto()).thenReturn(licenseDto);
			LicenseDto feature = licenseService.getFeatures();
			Assertions.assertNotNull(feature);
			Assertions.assertNull(feature.getFeatures());
			Assertions.assertNotNull(feature.getLicenseType());
			Assertions.assertEquals(LicenseType.OP.name(), feature.getLicenseType());
			Assertions.assertEquals("test", feature.getSid());
		}
	}

	@Test
	void testLoadLiteDefaultFeatures() {

		LicenseService licenseService = new LicenseService(licenseRepository);
		List<Feature> result = licenseService.loadLiteDefaultFeatures(null);
		Assertions.assertNull(result);

		result = licenseService.loadLiteDefaultFeatures(LicenseType.OP);
		Assertions.assertNull(result);

		result = licenseService.loadLiteDefaultFeatures(LicenseType.PIPELINE);
		Assertions.assertNull(result);

		result = licenseService.loadLiteDefaultFeatures(LicenseType.LITE);
		Assertions.assertNotNull(result);
		int liteFeatureSize = result.size();

		result = licenseService.loadLiteDefaultFeatures(LicenseType.SERVICE);
		Assertions.assertNotNull(result);
		int serviceFeatureSize = result.size();

		Assertions.assertTrue(liteFeatureSize > serviceFeatureSize);
	}

	@Test
	void testDeclareFirstDeployTime() {

		Long time = licenseService.declareFirstDeployTime();
		Assertions.assertNotNull(time);

		ObjectId workerId = new ObjectId("6323b88bdd985f735b091e49");
		WorkerDto workerDto = new WorkerDto();
		workerDto.setId(workerId);

		when(workerService.findOne(any(Query.class))).thenReturn(workerDto);
		time = licenseService.declareFirstDeployTime();
		Assertions.assertNotNull(time);
		Assertions.assertEquals(workerId.getTimestamp() * 1000L, time);

		ObjectId dataSourceDefinitionId = new ObjectId();
		DataSourceDefinitionDto dataSourceDefinition = new DataSourceDefinitionDto();
		dataSourceDefinition.setId(dataSourceDefinitionId);
		when(workerService.findOne(any(Query.class))).thenReturn(null);
		when(dataSourceDefinitionService.findOne(any(Query.class))).thenReturn(dataSourceDefinition);
		time = licenseService.declareFirstDeployTime();
		Assertions.assertNotNull(time);
		Assertions.assertEquals(dataSourceDefinitionId.getTimestamp() * 1000L, time);

		when(workerService.findOne(any(Query.class))).thenReturn(workerDto);
		when(dataSourceDefinitionService.findOne(any(Query.class))).thenReturn(dataSourceDefinition);
		time = licenseService.declareFirstDeployTime();
		Assertions.assertNotNull(time);
		Assertions.assertEquals(workerId.getTimestamp() * 1000L, time);

	}
}
