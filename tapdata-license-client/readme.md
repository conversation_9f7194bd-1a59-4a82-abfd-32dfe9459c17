# License 客户端代码

tapdata-license-client 工程用于给 TM 添加授权控制，使用注入方式，将授权代码注入 TM 可执行 jar 中

## 应用调试
1. IDEA 中 `com.tapdata.tm.TMApplication` 应用能正常跑的情况下
2. 拷贝上述配置，新增为 `com.tapdata.tm.TMApplicationLicense`
3. 运行新配置即可进行授权代码调试

## 授权注入
1. 将 `tapdata` 和 `tapdata-license` 两个工程放在同个目录下
2. 打包 `tapdata` 工程（会生成 TM 应用包：`tapdata/manager/tm/target/tm-0.0.1-SNAPSHOT-exec.jar`）
3. 编译 `tapdata-license-client` 工程
   - 执行 `%MODULE_DIR%/build/build.sh` 即可
   - 执行后会将 `%MODULE_DIR%/build/BOOT-INF` 下的文件进行重置
4. 注入授权代码，执行 `%MODULE_DIR%/build/inject2JarInPath.sh` 或 `%MODULE_DIR%/build/inject2Jar.sh <tm-jar-path>`

## 代码变更流程
1. 从 `main` 分支检出备份分支，名为上一个发布版本
   - 如：当前迭代分支为 `release-v3.5.8` 则检出 `release-v3.5.7`
2. 从 `main` 分支检出功能/修复分支（`feat-<issuesNumber>-main-<useName>` 或 `fix-<issuesNumber>-main-<useName>`）
3. 添加新代码
4. 需要先将 tapdata 项目 install 到本地：`mvn clean install -T 1C -U`
5. 编译工程，执行：`builid/build.sh`
6. 提交新代码（包括 `%MODULE_DIR%/build/BOOT-INF` 目录下的变更）
7. 测试新分支代码
8. PR 合并到 main 分支，并删除功能分支
