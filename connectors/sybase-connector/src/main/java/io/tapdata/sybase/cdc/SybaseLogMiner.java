package io.tapdata.sybase.cdc;

import com.sybase.jdbcx.SybConnection;
import io.tapdata.common.cdc.NormalLogMiner;
import io.tapdata.common.cdc.NormalRedo;
import io.tapdata.common.cdc.NormalTransaction;
import io.tapdata.constant.TapLog;
import io.tapdata.entity.error.CoreException;
import io.tapdata.entity.event.TapBaseEvent;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.entity.utils.cache.KVReadOnlyMap;
import io.tapdata.exception.TapCodeException;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.consumer.StreamReadConsumer;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import io.tapdata.sybase.SybaseBeforeCdc;
import io.tapdata.sybase.error.SybaseErrorCode;
import io.tapdata.sybase.extend.SybaseConfig;
import io.tapdata.sybase.extend.SybaseContext;
import io.tapdata.sybase.filter.FilterEntity;
import io.tapdata.sybase.filter.ReadFilter;
import io.tapdata.sybase.filter.accept.Accepter;
import io.tapdata.sybase.util.ConnectorUtil;
import io.tapdata.sybase.util.Utils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.Date;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static io.tapdata.common.cdc.NormalRedo.OperationEnum.*;
import static io.tapdata.sybase.cdc.SybaseSqlConstant.*;
import static io.tapdata.sybase.extend.SybaseContext.SELECT_FLOAT_LENGTH;
import static java.sql.Statement.CLOSE_ALL_RESULTS;

public class SybaseLogMiner extends NormalLogMiner {

    private final SybaseContext sybaseContext;
    private final SybaseConfig sybaseConfig;
    private Connection connection;
    private Statement statement;
    private SybaseOffset sybaseOffset;
    private final Map<String, List<String>> columnNamesMap;
    private Long lastEventTimestamp = 0L;
    protected Accepter cdcAccept;

    private Boolean debug = false;

    private int scanRecords = 0;
    private final String encode;
    private final String decode;
    private boolean needEncode = false;
    private Map<String, Map<String, String>> typeAndNameMap;
    private Map<String, Set<String>> dateTypeSetMap;

    private Map<String, String> idToTable;

    Map<String, TapTable> tapTableMap;

    private Map<String, Map<String, Object>> dbccColumnsMap = new HashMap<>();

    static Map<Integer, Integer> decimalLength = new HashMap<Integer, Integer>() {
        {
            put(1, 4);
            put(2, 4);
            put(3, 6);
            put(4, 6);
            put(5, 8);
            put(6, 8);
            put(7, 8);
            put(8, 10);
            put(9, 10);
            put(10, 12);
            put(11, 12);
            put(12, 12);
            put(13, 14);
            put(14, 14);
            put(15, 16);
            put(16, 16);
            put(17, 18);
            put(18, 18);
            put(19, 18);
            put(20, 20);
            put(21, 20);
            put(22, 22);
            put(23, 22);
            put(24, 22);
            put(25, 24);
            put(26, 24);
            put(27, 26);
            put(28, 26);
            put(29, 28);
            put(30, 28);
            put(31, 28);
            put(32, 30);
            put(33, 30);
            put(34, 32);
            put(35, 32);
            put(36, 32);
            put(37, 34);
            put(38, 34);
        }
    };


    public SybaseLogMiner(SybaseContext sybaseContext, String connectorId, Log tapLogger) {
        this.sybaseContext = sybaseContext;
        this.sybaseConfig = (SybaseConfig) sybaseContext.getConfig();
        this.connectorId = connectorId;
        this.tapLogger = tapLogger;
        this.columnNamesMap = new HashMap<>();
        encode = sybaseConfig.getAutoEncode() ? Optional.ofNullable(sybaseConfig.getEncode()).orElse("cp850") : null;
        decode = sybaseConfig.getAutoEncode() ? Optional.ofNullable(sybaseConfig.getDecode()).orElse("big5") : null;
        typeAndNameMap = new HashMap<>();
        dateTypeSetMap = new HashMap<>();
    }

    public SybaseLogMiner cdcAccept(StreamReadConsumer cdcConsumer,
                                    Map<String, TapTable> tapTableMap,
                                    Map<String, Set<String>> blockFields,
                                    List<ConnectionConfigWithTables> connectionConfigWithTables,
                                    Predicate<Void> isAlive) {
        cdcAccept = Accepter.create(cdcConsumer,
                new FilterEntity()
                        .readType(ReadFilter.TYPE.fromType(sybaseConfig.getLogCdcQuery()))
                        .sybaseConfig(sybaseConfig)
                        .log(tapLogger)
                        .isAlive(isAlive)
                        .connectionConfigWithTables(connectionConfigWithTables),
                blockFields,
                tapTableMap
        );
        this.tapTableMap = tapTableMap;
        return this;
    }

    @Override
    public void init(List<String> tableList, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.init(tableList, tableMap, offsetState, recordSize, consumer);
        this.sybaseOffset = (SybaseOffset) offsetState;
        generateColumnNamesMap();
    }

    @Override
    public void multiInit(List<ConnectionConfigWithTables> connectionConfigWithTables, KVReadOnlyMap<TapTable> tableMap, Object offsetState, int recordSize, StreamReadConsumer consumer) throws Throwable {
        super.multiInit(connectionConfigWithTables, tableMap, offsetState, recordSize, consumer);
        this.sybaseOffset = (SybaseOffset) offsetState;
        multiGenerateColumnNamesMap();
    }

    private void generateColumnNamesMap() {
        tableList.forEach(table -> {
            List<String> columnNames = sortColumnName(tableMap.get(table).getNameFieldMap());
            columnNamesMap.put(table, columnNames);
        });
    }

    private void multiGenerateColumnNamesMap() {
        schemaTableMap.forEach((schema, tables) -> tables.forEach(table -> {
            List<String> columnNames = sortColumnName(tableMap.get(schema + "." + table).getNameFieldMap());
            columnNamesMap.put(schema + "." + table, columnNames);
        }));
    }

    private List<String> sortColumnName(Map<String, TapField> nameFieldMap) {
        return nameFieldMap.entrySet().stream().sorted(Comparator.comparing(v ->
                EmptyKit.isNull(v.getValue().getPos()) ? 99999 : v.getValue().getPos())).map(Map.Entry::getKey).collect(Collectors.toList());
    }

    public Object doEncodeAndDecode(String dataType, Object object) {
        if (null == dataType || null == sybaseConfig.getAutoEncode() || !sybaseConfig.getAutoEncode()) return object;
        dataType = dataType.toUpperCase();
        if (dataType.contains("char") || dataType.contains("sysname")) {
            if (object instanceof String) {
                try {
                    return Utils.convertString((String) object, sybaseConfig.getEncode(), sybaseConfig.getDecode());
                } catch (Exception e) {
                    throw new CoreException("Read column value failed, column name: {}, type: {}, data: {}, error: {}",
                            dataType,
                            object,
                            e.getMessage());
                }
            }
        }
        return object;
    }

    private boolean updateLogsHold(SybaseOffset sybaseOffset) throws Exception {
        if (debug) {
            return true;
        }

        rebuildConnection();
        try {
            statement.execute("dump tran " + sybaseConfig.getDatabase() + " with truncate_only");
            tapLogger.info("dump tran success");
        } catch (Exception e) {
            tapLogger.warn("dump tran failed, error: {}", e.getMessage());
        }

        try {
            statement.execute("dbcc settrunc('ltm', 'pageid', " + (sybaseOffset.getStartRid()) + ")");
            tapLogger.info("update logs hold success, startRid: {}", sybaseOffset.getStartRid());
        } catch (Exception e) {
            tapLogger.warn("update logs hold failed, error: {}", e.getMessage());
            return false;
        }

        try {
            statement.close();
        } catch (Exception e) {
        }
        try {
            connection.close();
        } catch (Exception e) {
        }
        return true;
    }

    private SybaseOffset getRescanOffset(SybaseOffset sybaseOffset, Map<String, Map<String, Long>> uncommitTrans, Boolean pageBroken, Map<String, Boolean> transFilter, Map<String, Boolean> repeatFilter, int transTimestampOffset)  throws SQLException {
        SybaseOffset sybaseOffset1 = new SybaseOffset();
        if (uncommitTrans.isEmpty()) {
            if (pageBroken) {
                tapLogger.info("uncommit trans is emtpy, and page broken, will return current directly: {}, {}", sybaseOffset.getStartRid(), sybaseOffset.getRowId());
                return sybaseOffset;
            } else {
                return sybaseOffset;
            }
        }

        tapLogger.info("uncommit trans is not empty, len is: " + uncommitTrans.size());
        tapLogger.info("uncommit trans is: {}", uncommitTrans);

        long minStartRid = sybaseOffset.getStartRid();
        long minRowId = sybaseOffset.getRowId();
        List<String> removed = new ArrayList<>();
        for (Map.Entry<String, Map<String, Long>> entry : uncommitTrans.entrySet()) {
            long timestamp = entry.getValue().get("enTimestamp");
            // 30 分钟过期
            if (timestamp < System.currentTimeMillis() - 1800000) {
                tapLogger.info("transId: {} is expired, will remove it from uncommitTrans", entry.getKey());
                removed.add(entry.getKey());
                continue;
            }
            if (entry.getValue().get("startRid") < minStartRid || entry.getValue().get("startRid") > minStartRid + 5000 || (entry.getValue().get("startRid") == minStartRid && entry.getValue().get("rowId") < minRowId)) {
                minStartRid = entry.getValue().get("startRid");
                minRowId = entry.getValue().get("rowId");
            }
        }

        for (String key : removed) {
            uncommitTrans.remove(key);
        }

        if (minStartRid != sybaseOffset.getStartRid() || minRowId != sybaseOffset.getRowId()) {
            tapLogger.info("uncommit trans find, will rescan from {} {} but not {} {}", minStartRid, minRowId, sybaseOffset.getStartRid(), sybaseOffset.getRowId());
            List<Map<String, Object>> rescanEvents = rescanArchiveLog(minStartRid, minRowId);
            for (Map<String, Object> rescanEvent : rescanEvents) {
                NormalRedo normalRedo1 = new NormalRedo();
                long h = 0;
                long l = 0;
                try {
                    h = Long.parseLong(rescanEvent.get("highTs").toString());
                } catch (Exception e2) {
                    tapLogger.warn("rescan Event highTs: {}", rescanEvent);
                    continue;
                }
                l = Long.parseLong(rescanEvent.get("lowTs").toString());
                Long ts = Long.parseLong(rescanEvent.get("ts").toString()) + transTimestampOffset;
                normalRedo1.setTransactionId(String.valueOf(rescanEvent.get("transId")));
                normalRedo1.setCdcSequenceStr(minStartRid + "-" + minRowId + "-" + h + "-" + l);
                normalRedo1.setTimestamp(ts);
                normalRedo1.setOperation(String.valueOf(rescanEvent.get("op")));
                normalRedo1.setTableName(String.valueOf(rescanEvent.get("table")));
                normalRedo1.setNameSpace(String.valueOf(sybaseContext.getConfig().getSchema()));
                normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("after"));
                normalRedo1.setUndoRecord((Map<String, Object>) rescanEvent.get("before"));

                if (DELETE.toString().equals(normalRedo1.getOperation())) {
                    normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("before"));
                }

                if (COMMIT.toString().equals(normalRedo1.getOperation())) {
                    uncommitTrans.remove(normalRedo1.getTransactionId());
                }

                if (filter(transFilter, repeatFilter, h, l, normalRedo1, uncommitTrans)) {
                    continue;
                }
                if (debug) {
                    tapLogger.info("enqueue redo log content transId: {}, op: {}, before: {}, after: {}, cdc str: {}", normalRedo1.getTransactionId(), normalRedo1.getOperation(), normalRedo1.getUndoRecord(), normalRedo1.getRedoRecord(), normalRedo1.getCdcSequenceStr());
                }
                enqueueRedoLogContent2(normalRedo1, transFilter, repeatFilter);
            }
        } else {
            if (pageBroken) {
                tapLogger.info("uncommit not empty, but minOffset same with offset, pageBroken find, will increase rowId");
                sybaseOffset.setRowId(minRowId + 1);
                minRowId += 1;
            }
        }
        sybaseOffset1.setStartRid(minStartRid);
        sybaseOffset1.setRowId(minRowId);
        return sybaseOffset1;
    }

    private SybaseOffset getRescanOffsetSimple(SybaseOffset sybaseOffset, Map<String, Map<String, Long>> uncommitTrans)  throws SQLException {
        if (uncommitTrans.isEmpty()) {
            return sybaseOffset;
        }

        tapLogger.info("uncommit trans is not empty, len is: " + uncommitTrans.size());
        tapLogger.info("uncommit trans is: {}", uncommitTrans);

        long minStartRid = sybaseOffset.getStartRid();
        long minRowId = sybaseOffset.getRowId();
        List<String> removed = new ArrayList<>();
        for (Map.Entry<String, Map<String, Long>> entry : uncommitTrans.entrySet()) {
            long timestamp = entry.getValue().get("enTimestamp");
            // 30 分钟过期
            if (timestamp < System.currentTimeMillis() - 1800000) {
                tapLogger.info("transId: {} is expired, will remove it from uncommitTrans", entry.getKey());
                removed.add(entry.getKey());
                continue;
            }
            if (entry.getValue().get("startRid") < minStartRid || entry.getValue().get("startRid") > minStartRid + 5000 || (entry.getValue().get("startRid") == minStartRid && entry.getValue().get("rowId") < minRowId)) {
                minStartRid = entry.getValue().get("startRid");
                minRowId = entry.getValue().get("rowId");
            }
        }

        for (String key : removed) {
            uncommitTrans.remove(key);
        }

        SybaseOffset sybaseOffset1 = new SybaseOffset();
        sybaseOffset1.setStartRid(minStartRid);
        sybaseOffset1.setRowId(minRowId);
        return sybaseOffset1;
    }

    public static String compareAndSubtract(String hexStr) {
        // 将十六进制字符串转为十进制
        int num = Integer.parseInt(hexStr, 16);
        // 十六进制的 20 等于十进制的 32
        int threshold = 0x20;

        // 判断是否大于 20
        if (num >= threshold) {
            // 减去 20 并转为十六进制字符串
            String result = Integer.toHexString(num - threshold);
            // 确保结果是两位小写字符串，补0如果需要
            return result.length() < 2 ? "0" + result : result;
        } else {
            // 原样返回
            return hexStr;
        }
    }

    public static List<Integer> getNewVarColumnLengths(Integer varStart, String hexString, Integer varNumber, boolean isLittleEndian) {
        List<String> lenStrList = new ArrayList<>();
        for (int i = hexString.length()-4; i >= 0; i -= 4) {
            String lenStr = hexString.substring(i, i + 4);
            if (isLittleEndian) {
                lenStr = lenStr.substring(2, 4) + lenStr.substring(0, 2);
            }
            lenStr = compareAndSubtract(lenStr.substring(0,2)) + lenStr.substring(2,4);
            lenStrList.add(lenStr);
            if (lenStrList.size() > varNumber) {
                break;
            }
        }

        // 将 lenStrList 里面的字符串转为 10 进制, 放到 List<Integer> 里
        List<Integer> lenList = new ArrayList<>();
        int sumL = 0;
        int lastLength = 0;
        int doneLength = 0;
        for (int i=0; i < lenStrList.size()-1; i += 1) {
            String len1 = lenStrList.get(i);
            String len2 = lenStrList.get(i+1);
            int len1_10 = Integer.parseInt(len1, 16);
            int len2_10 = Integer.parseInt(len2, 16);
            int l = (len2_10-len1_10)*2;
            sumL += l;
            doneLength = varStart + sumL + 4 * i;

            if (l < 0 || doneLength > hexString.length()) {
                lenList.add(lastLength);
                break;
            } else {
                if (doneLength == hexString.length() || lenList.size() == varNumber) {
                    lenList.add(l);
                    break;
                } else {
                    lenList.add(l);
                }
            }
            lastLength = hexString.length() - doneLength - 4 * 2;
        }

        // 如果 lenList 里的数量小于 Integer varNumber, 后面补 0
        while (lenList.size() < varNumber) {
            lenList.add(0);
        }
        return lenList;

    }

    public static List<Integer> getVarColumnLengths(Integer varStart, String varString, Integer varNumber) {
        String ss = varString;
        ss = ss.replace(" ", "");
        ss = ss.replace("\n", "");
        int i = 0;
        int maxK = ss.length() / 256;
        for (int j = ss.length()-2; j >= i; j -= 2) {
            String b = ss.substring(j, j + 2);
            // b 是一个 16 进制字符串, 转为 10 进制
            int b10 = Integer.parseInt(b, 16);
            //System.out.println("j: " + j + ", b:" + b + ", b10: " + b10);
            for (int k=0; k <= maxK; k += 1) {
                int nextKJ = j - 2 * (k+1);
                if (nextKJ < 0) {
                    break;
                }
                String nextBKJ = ss.substring(nextKJ, nextKJ + 2);
                int nextB10KJ = Integer.parseInt(nextBKJ, 16);
                //System.out.printf("j: %d, b: %s, b10: %d, k: %d, nextKJ: %d, x1: %d, x2: %d%n", j, b, b10, k, nextKJ, (2 * b10 + 256 * k), nextKJ);
                if ((2 * b10 + 256 * 2 * k) == nextKJ) {
                    String padStr = ss.substring(nextKJ + 2, nextKJ + 2 * k + 2);
                    // padStr 是 161617 这种, 将其两两分组, 按照出现的个数, 转为十进制, 存储在 Map<Integer, Integer> 里面, key 是十进制的数字, value 是出现的次数
                    Map<Integer, Integer> padMap = new HashMap<>();
                    for (int m = 0; m < padStr.length(); m += 2) {
                        String pad = padStr.substring(m, m + 2);
                        int pad10 = Integer.parseInt(pad, 16);
                        padMap.put(pad10, padMap.getOrDefault(pad10, 0) + 1);
                    }
                    String lenStr = ss.substring(nextKJ + 2 * k + 2);
                    // lenStr 两两一组, 分到 List<String> 里, 顺序倒置
                    List<String> lenStrList = new ArrayList<>();
                    for (int l = lenStr.length()-2; l >= 0; l -= 2) {
                        lenStrList.add(lenStr.substring(l, l + 2));
                    }
                    List<Integer> lenList = new ArrayList<>();
                    for (int l=0; l < lenStrList.size()-1; l += 1) {
                        String len1 = lenStrList.get(l);
                        String len2 = lenStrList.get(l+1);
                        int len1_10 = Integer.parseInt(len1, 16);
                        int len2_10 = Integer.parseInt(len2, 16);
                        if (padMap.containsKey(l+2)) {
                            len2_10 = len2_10 + padMap.get(l+2) * 256;
                        }
                        lenList.add((len2_10-len1_10)*2);
                    }
                    // 如果 lenList 的长度小于 varNumber, 后面补 0
                    while (lenList.size() < varNumber) {
                        lenList.add(0);
                    }
                    // 求一下 lenList 里面数字的和
                    int sum = 0;
                    for (int l=0; l < lenList.size(); l += 1) {
                        sum += lenList.get(l);
                    }
                    if (nextKJ == varStart+sum) {
                        return lenList;
                    }
                }
            }
        }
        return null;
    }

    public static byte[] parseTimestamp(String s) {
        return parseBinary(s);
    }

    public boolean parseBit(String s) {
        return !"00".equals(s);
    }

    public LocalDateTime parseSmallDatetime(String s) {
        String daysStr = s.substring(0, 4);
        if (sybaseConfig.getEndian() == 1) {
            daysStr = reverseHexString(daysStr);
        }
        Long days = Long.parseLong(daysStr, 16);
        String minutesStr = s.substring(4, 8);
        if (sybaseConfig.getEndian() == 1) {
            minutesStr = reverseHexString(minutesStr);
        }
        Long minutes = Long.parseLong(minutesStr, 16);
        // 开始时间从 1900-01-01 00:00:00 开始
        return LocalDateTime.of(1900,1,1,0,0).plusDays(days).plusMinutes(minutes);
    }

    public String parseDate(String s) {
        String daysStr = s.substring(0, 8);
        if (sybaseConfig.getEndian() == 1) {
            daysStr = reverseHexString(daysStr);
        }
        Long days = parseSignedHex(daysStr, daysStr.length() * 4);
        // 开始时间从 1900-01-01 00:00:00 开始
        LocalDate localDate = LocalDate.of(1900, 1, 1).plusDays(days);
        return localDate.toString();
    }

    public LocalDateTime parseDatetime(String s) {
        String daysStr = s.substring(0, 8);
        if (sybaseConfig.getEndian() == 1) {
            daysStr = reverseHexString(daysStr);
        }
        Long days = parseSignedHex(daysStr, daysStr.length() * 4);
        String secondsStr = s.substring(8, 16);
        if (sybaseConfig.getEndian() == 1) {
            secondsStr = reverseHexString(secondsStr);
        }
        Long millseconds = Long.parseLong(secondsStr, 16) * 1000 / 300;
        Long n = millseconds % 10;
        if (n <= 1) {
            millseconds = millseconds - n;
        }
        if (n >= 2 && n <= 4) {
            millseconds = millseconds - n + 3;
        }
        if (n >= 5 && n <= 8) {
            millseconds = millseconds - n + 6;
        }

        if (n == 9) {
            millseconds = millseconds + 1;
        }

        // 开始时间从 1900-01-01 00:00:00 开始
        LocalDateTime base = LocalDateTime.of(1900, 1, 1, 0, 0);
        return base.plusDays(days).plus(millseconds, ChronoUnit.MILLIS);
    }

    public LocalDateTime parseBigDatetime(String s) {
        String str = s;
        if (sybaseConfig.getEndian() == 1) {
            str = reverseHexString(s);
        }
        BigInteger b = new BigInteger(str, 16);
        b = b.subtract(new BigInteger("62167219200000000", 10));
        b = b.divide(new BigInteger("1000", 10));
        return LocalDateTime.of(1970, 1, 1, 0, 0).plus(b.longValue(), ChronoUnit.MILLIS);
    }

    public Long parseBigTime(String s) {
        String str = s;
        if (sybaseConfig.getEndian() == 1) {
            str = reverseHexString(s);
        }
        BigInteger b = new BigInteger(str, 16);
        b = b.divide(new BigInteger("1000", 10));
        long l = b.longValue() * 1000 * 1000;
        return l;
    }

    public Long parseTime(String s) {
        String str = s;
        if (sybaseConfig.getEndian() == 1) {
            str = reverseHexString(s);
        }
        Long l = Long.parseLong(str, 16);
        Long l2 = l / 300 * 1000 * 1000;
        return l2;
    }

    public int parseInteger(String s) {
        String intStr = s;
        if (sybaseConfig.getEndian() == 1) {
            intStr = reverseHexString(s);
        }
        long l = Long.parseLong(intStr, 16);
        return (int) l;
    }

    public int parseSmallInteger(String s) {
        String intStr = s;
        if (sybaseConfig.getEndian() == 1) {
            intStr = reverseHexString(s);
        }
        // 解析为 long，确保不丢失数据
        long l = Long.parseLong(intStr, 16);
        // 将值限制在 16 位有符号整数范围内（smallint）
        // 使用 0xFFFF 掩码获取低 16 位
        l = l & 0xFFFF;
        // 如果最高位为 1（表示负数），转换为补码
        if ((l & 0x8000) != 0) {
            l = l - 0x10000; // 等价于 -(0x10000 - l)
        }
        return (int) l;
    }

    public static long parseSignedHex(String hexStr, int bitLength) {
        long value = Long.parseUnsignedLong(hexStr, 16);
        long signBit = 1L << (bitLength - 1);

        if ((value & signBit) != 0) {

            value -= 1L << bitLength;
        }

        return value;
    }

    public long parseBigInteger(String s) {
        String intStr = s;
        if (sybaseConfig.getEndian() == 1) {
            intStr = reverseHexString(s);
        }
        BigInteger b = new BigInteger(intStr, 16);
        return b.longValue();
    }

    public long parseUBigInteger(String s) {
        String intStr = s;
        if (sybaseConfig.getEndian() == 1) {
            intStr = reverseHexString(s);
        }
        BigInteger b = new BigInteger(intStr, 16);
        return b.longValue();
    }


    public long parseUInteger(String s) {
        String intStr = s;
        if (sybaseConfig.getEndian() == 1) {
            intStr = reverseHexString(s);
        }
        return Long.parseLong(intStr, 16);
    }

    public static String parseChar(String s) {
        String[] parts = s.split("(?<=\\G.{2})");
        StringBuilder ss = new StringBuilder();
        for (String part : parts) {
            ss.append((char) Integer.parseInt(part, 16));
        }
        return ss.toString();
    }

    public String parseUnichar(String s) {
        // 四个一组, unicode 字符
        String[] parts = s.split("(?<=\\G.{4})");
        StringBuilder ss = new StringBuilder();
        for (String part : parts) {
            // part 四个一组, 高低位交换
            if (sybaseConfig.getEndian() == 1) {
                part = reverseHexString(part);
            }
            ss.append((char) Integer.parseInt(part, 16));
        }
        return ss.toString();
    }

    public String reverseHexString(String hexStr) {
        StringBuilder reversedHex = new StringBuilder();
        for (int i = hexStr.length(); i > 0; i -= 2) {
            reversedHex.append(hexStr.substring(i - 2, i));
        }
        return reversedHex.toString();
    }

    public Double parseSFloat(String s) {
        int intValue = Integer.parseUnsignedInt(s, 16);
        ByteBuffer buffer = ByteBuffer.allocate(Integer.BYTES);
        buffer.putInt(intValue);
        if (sybaseConfig.getEndian() == 0) {
            buffer.order(ByteOrder.BIG_ENDIAN);
        } else {
            buffer.order(ByteOrder.LITTLE_ENDIAN);
        }
        buffer.flip();
        return (double) buffer.getFloat();
    }
    public static void parseText(String s) {
        return;
    }

    public Double parseDFloat(String s) {
        long longValue = Long.parseUnsignedLong(s, 16);
        ByteBuffer buffer = ByteBuffer.allocate(Long.BYTES);
        buffer.putLong(longValue);
        if (sybaseConfig.getEndian() == 0) {
            buffer.order(ByteOrder.BIG_ENDIAN);
        } else {
            buffer.order(ByteOrder.LITTLE_ENDIAN);
        }
        buffer.flip();
        return buffer.getDouble();
    }

    public static Integer getTypeLength(String type, Integer length, Integer scale) {
        // timestamp 类型, 这个类型没有业务意义
        if (type.equals("timestamp")) {
            return 16;
        }
        if ("bit".equals(type)) {
            return 2;
        }

        if ("datetime".equals(type)) {
            return 16;
        }
        if ("smalldatetime".equals(type)) {
            return 8;
        }
        if ("date".equals(type)) {
            return 8;
        }
        if ("time".equals(type)) {
            return 8;
        }
        if ("bigdatetime".equals(type)) {
            return 16;
        }
        if ("bigtime".equals(type)) {
            return 16;
        }

        // 整数类型
        if (type.equals("tinyint")) {
            return 2;
        }
        if (type.equals("smallint") || type.equals("usmallint")) {
            return 4;
        }
        if (type.equals("int") || type.equals("uint")) {
            return 8;
        }
        if (type.equals("bigint") || type.equals("ubigint")) {
            return 16;
        }
        // 浮点数类型
        if (type.equals("float")) {
            // 识别出 float(p) 里面的 p
            if (length == 8) {
                return 16;
            }
            return 8;
        }

        if (type.equals("real")) {
            return 8;
        }

        if (type.equals("double")) {
            return 16;
        }

        if (type.equals("decimal") || type.equals("numeric")) {
            return decimalLength.get(length);
        }

        if (type.equals("char")) {
            return 2*length;
        }
        if (type.equals("nchar")) {
            return 2*length;
        }
        if (type.equals("unichar")) {
            return 2*length;
        }


        if (type.equals("text")) {
            return 32;
        }

        if (type.equals("unitext")) {
            return 32;
        }

        if (type.equals("binary")) {
            return 2*length;
        }

        if (type.equals("image")) {
            return 2*length;
        }

        if (type.equals("smallmoney")) {
            return 8;
        }
        if (type.equals("money")) {
            return 16;
        }
        if (type.equals("unichar")) {
            return 2*length*2;
        }
        return 0;
    }

    public static byte[] parseBinary(String s) {
        // 将字符串, 两个一组转为 byte 数组
        String[] parts = s.split("(?<=\\G.{2})");
        byte[] bytes = new byte[parts.length];
        for (int i = 0; i < parts.length; i++) {
            bytes[i] = (byte) Integer.parseInt(parts[i], 16);
        }
        return bytes;
    }

    public Object parseValue(String v, String type, Integer length, Integer scale) {
        if (debug) {
            tapLogger.info("before v: {}, type: {}, length: {}, scale: {}", v, type, length, scale);
        }

        Object value = null;

        if (type.contains("int")) {
            if (type.equals("smallint")) {
                value = parseSmallInteger(v);
            } else {
                if (type.startsWith("u")) {
                    if (type.contains("big")) {
                        value = parseUBigInteger(v);
                    } else {
                        value = parseUInteger(v);
                    }
                } else {
                    if (type.contains("big")) {
                        value = parseBigInteger(v);
                    } else {
                        value = parseInteger(v);
                    }
                }
            }
        }

        if ("timestamp".equals(type)) {
            value = parseTimestamp(v);
        }

        if ("bit".equals(type)) {
            value = parseBit(v);
        }

        if ("datetime".equals(type)) {
            value = parseDatetime(v);
        }
        if ("smalldatetime".equals(type)) {
            value = parseSmallDatetime(v);
        }
        if ("date".equals(type)) {
            value = parseDate(v);
        }
        if ("bigdatetime".equals(type)) {
            value = parseBigDatetime(v);
        }
        if ("time".equals(type)) {
            value = parseTime(v);
        }
        if ("bigtime".equals(type)) {
            value = parseBigTime(v);
        }

        if ("decimal".equals(type) || "numeric".equals(type)) {
            value = parseDecimal(v, scale);
        }

        if ("char".equals(type)) {
            value = doEncodeAndDecode(type, parseChar(v));
        }
        if ("varchar".equals(type)) {
            value = doEncodeAndDecode(type, parseChar(v));
        }
        if ("unichar".equals(type)) {
            value = doEncodeAndDecode(type, parseUnichar(v));
        }
        if ("univarchar".equals(type)) {
            value = doEncodeAndDecode(type, parseUnichar(v));
        }
        if ("nchar".equals(type)) {
            value = doEncodeAndDecode(type, parseChar(v));
        }
        if ("nvarchar".equals(type)) {
            value = doEncodeAndDecode(type, parseChar(v));
        }


        if ("real".equals(type)) {
            value = parseSFloat(v);
        }

        if ("float".equals(type)) {
            if (length == 4) {
                value = parseSFloat(v);
            } else {
                // double 等于 float 双精度
                value = parseDFloat(v);
            }
        }
        if ("text".equals(type) || "unitext".equals(type)) {
            value = null;
        }

        if ("binary".equals(type)) {
            value = parseBinary(v);
        }

        if (type.contains("varbinary")) {
            value = parseBinary(v);
        }

        if ("image".equals(type)) {
            value = parseBinary(v);
        }

        if ("smallmoney".equals(type)) {
            value = parseSmallMoney(v);
        }

        if ("money".equals(type)) {
            value = parseMoney(v);
        }

        if (debug) {
            tapLogger.info("after v: {}, type: {}, length: {}, scale: {}, value: {}", v, type, length, scale, value);
        }
        return value;
    }

    public Map<String, Object> parseColumn(String s, String type, Integer length, Integer scale) {
        Integer storageLength = getTypeLength(type, length, scale);
        Map<String, Object> result = new HashMap<>();
        Object value = null;
        result.put("i", storageLength);
        String v = s.substring(0, storageLength);
        value = parseValue(v, type, length, scale);
        result.put("value", value);
        return result;
    }

    public Double parseSmallMoney(String s) {
        if (sybaseConfig.getEndian() == 1) {
            s = reverseHexString(s);
        }
        int i = Integer.parseInt(s, 16);
        return (double) i / 10000;
    }

    public Double parseMoney(String s) {
        String s1 = s.substring(0, 8);
        String s2 = s.substring(8, 16);

        if (sybaseConfig.getEndian() == 1) {
            s1 = reverseHexString(s1);
        }
        if (sybaseConfig.getEndian() == 1) {
            s2 = reverseHexString(s2);
        }

        BigDecimal b1 = new BigDecimal(new BigInteger(s1+s2, 16));
        BigDecimal b2 = b1.divide(new BigDecimal("10000"));
        return b2.doubleValue();
    }

    public static BigDecimal parseDecimal(String s, Integer scale) {
        // 十六进制字符串, 转 BigDecimal
        if (s.substring(0, 2).equals("00")) {
            s = s.substring(2);
        }
        if (s.substring(0, 2).equals("01")) {
            s = "-" + s.substring(2);
        }
        BigInteger bigInteger = new BigInteger(s, 16);
        BigDecimal decimal = new BigDecimal(bigInteger, scale);
        return decimal;
    }

    public String parseHash(String tableName, String eventStr, Map<String, Object> dbccLogFormat, Integer skipBytes) {
        List<Map<String, Object>> fixedColumns = (List<Map<String, Object>>) dbccLogFormat.get("fixedColumns");
        List<Map<String, Object>> varColumns = (List<Map<String, Object>>) dbccLogFormat.get("varColumns");
        eventStr = eventStr.replace(" ", "");
        Map<String, Object> event = new HashMap<>();
        String pkHash = tableName + "_";
        int i = 0;
        i += skipBytes; // 开头 4 位不处理

        for (Map<String, Object> column : fixedColumns) {
            String name = column.get("name").toString();
            String type = column.get("type").toString();
            Integer length = 0;
            Integer scale = 0;
            if (column.containsKey("length")) {
                length = Integer.parseInt(column.get("length").toString());
            }
            if (column.containsKey("scale")) {
                scale = Integer.parseInt(column.get("scale").toString());
            }
            Map<String, Object> result;
            result = parseColumn(eventStr.substring(i), type, length, scale);
            event.put(name, result.get("value"));
            if ((Boolean)column.get("pk")) {
                pkHash = pkHash + result.get("value").toString() + "_";
            }
            i += Integer.parseInt(result.get("i").toString());
        }
        return pkHash;
    }


    public Map<String, Object> parseEvent(String tableName, String eventStr, Map<String, Object> dbccLogFormat, Integer skipBytes, boolean isNewFormat) {
        if (debug) {
            tapLogger.info("tableName: {}, eventStr: {}, dbccLogFormat: {}", tableName, eventStr, dbccLogFormat);
        }

        List<Map<String, Object>> fixedColumns = (List<Map<String, Object>>) dbccLogFormat.get("fixedColumns");
        List<Map<String, Object>> varColumns = (List<Map<String, Object>>) dbccLogFormat.get("varColumns");
        eventStr = eventStr.replace(" ", "");
        Map<String, Object> event = new HashMap<>();
        int i = 0;
        i += skipBytes; // 开头 4 位不处理

        for (Map<String, Object> column : fixedColumns) {
            String name = column.get("name").toString();
            String type = column.get("type").toString();
            Integer length = 0;
            Integer scale = 0;
            if (column.containsKey("length")) {
                length = Integer.parseInt(column.get("length").toString());
            }
            if (column.containsKey("scale")) {
                scale = Integer.parseInt(column.get("scale").toString());
            }
            Map<String, Object> result;
            result = parseColumn(eventStr.substring(i), type, length, scale);
            event.put(name, result.get("value"));
            i += Integer.parseInt(result.get("i").toString());
        }

        if (varColumns.isEmpty()) {
            return event;
        }

        i += 4; // 跳过 4 位长度, 不需要处理

        // 有一些日志里, 不记录变长数值
        if (i >= eventStr.length()) {
            for (Map<String, Object> column : varColumns) {
                event.put(column.get("name").toString(), null);
            }
            return event;
        }
        List<Integer> varColumnLengths = new ArrayList<>();
        if (isNewFormat) {
            varColumnLengths = getNewVarColumnLengths(i, eventStr, varColumns.size(), sybaseConfig.getEndian() == 1);
        } else {
            varColumnLengths = getVarColumnLengths(i, eventStr, varColumns.size());
        }
        int j = 0;
        for (Map<String, Object> column : varColumns) {
            if (i >= eventStr.length()) {
                break;
            }
            String name = column.get("name").toString();
            String type = column.get("type").toString();
            Integer length = Integer.parseInt(column.get("length").toString());
            Integer scale = Integer.parseInt(column.get("scale").toString());
            Integer l = varColumnLengths.get(j);
            j += 1;
            if (l == 0) {
                event.put(name, null);
                continue;
            }
            if ("datetime".equals(type) && l != 16) {
                l = 16;
            }
            if (i + l > eventStr.length()) {
                return event;
            }
            String v = eventStr.substring(i, i+l);
            try {
                event.put(name, parseValue(v, type, length, scale));
            } catch (Exception e) {
                tapLogger.warn("varColumnLengths: {}", varColumnLengths);
                tapLogger.warn("parse var column failed, name: {}, type: {}, length: {}, scale: {}, value: {}, error: {}", name, type, length, scale, v, e.getMessage());
                throw e;
            }
            i += l;
        }
        return event;
    }

    private static boolean shouldSkipMessage(String[] parts) {
        String operation = parts[0].replace(" ", "").toLowerCase();
        if (operation.equals("inoop") ||
                operation.equals("delete") ||
                operation.equals("insert") ||
                operation.equals("update") ||
                operation.equals("beginxact") ||
                operation.equals("endxact") ||
                operation.equals("clr") ||
                operation.equals("trunctab")) {
            return false;
        }

        // 数组用 "," 连接成字符串
        String message  = String.join(",", parts);

        if (operation.equals("rowimage") && message.contains("[REPLICATE]")) {
            return false;
        }

        if (operation.equals("dol_insert") && message.contains("[REPLICATE]")) {
            return false;
        }

        if (operation.equals("dol_update") && message.contains("[REPLICATE]")) {
            return false;
        }

        if (operation.equals("dol_delete") && message.contains("[REPLICATE]")) {
            return false;
        }


        return true;
    }


    private List<Map<String, Object>> fetchEvents(SQLWarning sqlWarning, Long startRid2, Long rowId2) throws SQLException {
        scanRecords = 0;
        Map<String, String> innopCache = new HashMap<>();
        List<Map<String, Object>> events = new ArrayList<>();
        // 解析头部描述内容
        while (true) {
            if (sqlWarning == null) {
                break;
            }
            sqlWarning = sqlWarning.getNextWarning();
            if (sqlWarning == null) {
                break;
            }

            String message = sqlWarning.getMessage();
            message = message.replaceAll("^\t+|\t+$", "");

            if ("\n".equals(message)) {
                continue;
            }
            if (message.startsWith("LOG RECORDS:")) {
                break;
            }
        }

        Map<String, Object> event = new HashMap<>();
        String operation = "";
        boolean writeText = false;
        String tableName = "";
        String transId = "";
        Long startRid = 0L;
        Long rowId = 0L;
        Long nextStartRid = 0L;
        Long nextRowId = 0L;
        Long lowTs = 0L;
        Long highTs = 0L;
        String objid = "";
        Long ts = 0L;
        String previousObjid = "";
        SimpleDateFormat formatter = new SimpleDateFormat("MMM dd yyyy h:mm:ss:SSSa", Locale.ENGLISH);
        boolean isNewFormat = false;
        boolean ximageNewFormat = false;

        // 开始解析数据部分
        while (true) {
            if (sqlWarning == null) {
                break;
            }
            sqlWarning = sqlWarning.getNextWarning();
            if (sqlWarning == null) {
                break;
            }

            if (event.containsKey("op") && event.containsKey("before") && event.containsKey("after")) {
                events.add(new HashMap<>(event));
                operation = "";
                writeText = false;
                event.clear();
                isNewFormat = false;
                ximageNewFormat = false;
                event.put("highTs", highTs);
                event.put("lowTs", lowTs);
                event.put("ts", ts);
            }

            String message = sqlWarning.getMessage();
            message = message.replaceAll("^\t+|\t+$", "");
            if (message.contains("Total number of log records")) {
                scanRecords  = Integer.parseInt(message.split("Total number of log records ")[1].split(" ")[0]);
                if (debug) {
                    tapLogger.info("scan {}, {}, records: {}, message: {}", startRid2, rowId2, scanRecords, message);
                }
            }

            if (debug) {
                tapLogger.info("Message: {}", message);
            }
            if (message.startsWith("\t")) {
                message = message.substring(1);
            }
            String[] parts = message.split("\t");
            // 头部内容判定操作类型等信息
            if (message.contains("sessionid=") && (parts.length == 4 || parts.length == 3)) {
                operation = parts[0].replace(" ", "").toLowerCase();
                ximageNewFormat = false;
                transId = parts[2].replace(" ", "").split("=")[1].replace(",", "-");
                transId = transId.split("\\[")[0];
                transId = transId.replace("\n", "");
                startRid = Long.parseLong(transId.split("-")[0]);
                rowId = Long.parseLong(transId.split("-")[1]);
                // parts1 为 (68887,22), 解析出 68887 和 22
                nextStartRid = Long.parseLong(parts[1].split(",")[0].replace("(", "").replace(" ", ""));
                nextRowId = Long.parseLong(parts[1].split(",")[1].replace(")", "").replace(" ", ""));

                if (shouldSkipMessage(parts)) {
                    continue;
                }
            }


            if (message.startsWith("xstat=") && operation.equals("rowimage")) {
                if (message.contains("Query was INSERT")) {
                    operation = "insert";
                }
                if (message.contains("Query was UPDATE Before")) {
                    operation = "update";
                }
                if (message.contains("Query was UPDATE After")) {
                    operation = "update";
                }
                if (message.contains("Query was WRITETEXT")) {
                    operation = "update";
                    writeText = true;
                }
                if (message.contains("DOL formatted")) {
                    ximageNewFormat = true;
                } else {
                    ximageNewFormat = false;
                }
                continue;
            }

            if (message.startsWith("old ts=")) {
                parts = message.split("new ts=");
                String[] parts2 = parts[1].split(" ");
                String lowTsStr = parts2[0].substring(2, 6);
                String highTsStr = parts2[1].substring(2, 10);
                lowTs = Long.parseLong(lowTsStr, 16);
                highTs = Long.parseLong(highTsStr, 16);
            }

            if (message.contains("time=")) {
                try {
                    String tss = message.split("time=")[1];
                    tss = tss.replace("\n", "");
                    // 将 Feb 15 2024  7:09:56:780AM 转换为时间戳
                    Date date = formatter.parse(tss);
                    ts = date.getTime(); // 获取时间戳
                } catch (Exception e) {
                }
                event.put("ts", ts);
            }


            if (!operation.equals("insert") && !operation.equals("update") && !operation.equals("delete") && !operation.equals("rowimage") && !operation.equals("dol_insert") && !operation.equals("dol_update")
                    && !operation.equals("dol_delete") && !operation.equals("inoop") && !operation.equals("beginxact") && !operation.equals("endxact") && !operation.equals("clr") && !operation.equals("trunctab")) {
                continue;
            }

            if (message.startsWith("objid=") || message.startsWith("xobjid=") || message.startsWith("objectid=")) {
                objid = parts[0].split(" ")[0].split("=")[1];
                try {
                    tableName = getTableNameById(objid);
                } catch (SQLException e) {
                    tapLogger.warn(e);
                }

                // 一些糟糕的事情发生了, 出现了 innop 丢数, 尝试查找
                if (event.containsKey("op") && UPDATE.toString().equals(event.get("op")) && event.containsKey("before")) {
                    boolean dataInNoop = true;
                    if ("dol_update".equals(operation) && tableName.equals(event.get("table")) && transId.equals(event.get("transId"))) {
                        dataInNoop = false;
                    }
                    if ("rowimage".equals(operation) && tableName.equals(event.get("table")) && transId.equals(event.get("transId"))) {
                        dataInNoop = false;
                    }
                    if (message.contains("XSTAT_XUPDATE") && tableName.equals(event.get("table")) && "insert".equals(operation) && transId.equals(event.get("transId"))) {
                        dataInNoop = false;
                    }

                    if (dataInNoop) {
                        String hash = String.valueOf(event.get("hash"));
                        String k = "";
                        // 遍历 innopCache, 如果 hash 是 k 的前缀, 则认为是同一个事务
                        for (String key : innopCache.keySet()) {
                            if (key.startsWith(hash)) {
                                k = key;
                                break;
                            }
                        }

                        if (!k.isEmpty()) {
                            String eventStr = innopCache.get(k);
                            try {
                                Map<String, Object> after = parseEvent(event.get("table").toString(), eventStr, getDBCCLogFormat(event.get("table").toString()), 4, false);
                                String pkHash = parseHash(event.get("table").toString(), eventStr, getDBCCLogFormat(event.get("table").toString()), 4);
                                if (innopCache.containsKey(pkHash)) {
                                    innopCache.remove(pkHash);
                                }
                                event.put("after", after);
                                events.add(new HashMap<>(event));
                                event.clear();
                                innopCache.remove(k);
                            } catch (Exception e) {
                                tapLogger.warn("parse event failed, eventStr: {}, table: {}, error: {}", eventStr, event.get("table"), e.getMessage());
                                continue;
                            }
                        } else {
                            String pkHash = String.valueOf(event.get("pkHash"));
                            if (innopCache.containsKey(pkHash)) {
                                tapLogger.info("missed after found in innop cache with pkHash, transId: {}, objid: {}, table: {}, pkHash: {}", transId, objid, event.get("table"), pkHash);
                                String eventStr = innopCache.get(pkHash);
                                try {
                                    Map<String, Object> after = parseEvent(event.get("table").toString(), eventStr, getDBCCLogFormat(event.get("table").toString()), 4, false);
                                    event.put("after", after);
                                    events.add(new HashMap<>(event));
                                    event.clear();
                                    innopCache.remove(pkHash);
                                } catch (Exception e) {
                                    tapLogger.warn("parse event failed, eventStr: {}, table: {}, error: {}", eventStr, event.get("table"), e.getMessage());
                                    continue;
                                }
                            } else {
                                tapLogger.warn("missed after not found in innop cache, transId: {}, objid: {}, event before: {}", transId, objid, event.get("before"));
                                event.put("after", event.get("before"));
                                events.add(new HashMap<>(event));
                                event.clear();
                            }
                        }
                    }
                }


                if (!event.containsKey("op") && message.contains("XSTAT_XUPDATE") && operation.equals("delete")) {
                    operation = "update";
                }

                if (event.containsKey("op") && message.contains("XSTAT_XUPDATE") && operation.equals("insert") && objid.equals(previousObjid)) {
                    operation = "update";
                }

                previousObjid = objid;
                continue;
            }

            if (message.contains("ptnid=") && operation.equals("inoop")) {
                objid = message.split("ptnid=")[1].split("\t")[0];
                try {
                    tableName = getTableNameById(objid);
                } catch (SQLException e) {
                    tapLogger.warn(e);
                }
            }
            if (message.contains("tabid") && "trunctab".equals(operation)) {
                String pattern = "tabid=(\\d+)";
                java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher m = r.matcher(message);
                if (m.find()) {
                    tableName = getTableNameById(m.group(1));
                    Map<String,Object> trunctabEvent=new HashMap<>();
                    trunctabEvent.put("table", tableName);
                    trunctabEvent.put("transId", transId);
                    trunctabEvent.put("startRid", startRid);
                    trunctabEvent.put("rowId", rowId);
                    if (nextStartRid != 0) {
                        trunctabEvent.put("nextStartRid", nextStartRid);
                    }
                    trunctabEvent.put("nextRowId", nextRowId);
                    trunctabEvent.put("lowTs", lowTs);
                    trunctabEvent.put("highTs", highTs);
                    trunctabEvent.put("ts", ts);
                    trunctabEvent.put("op", String.valueOf(TRUNCATE));
                    events.add(trunctabEvent);
                }
            }

            if (message.startsWith("xnoopins:")) {
                String eventStr2 = "";
                while (true) {
                    sqlWarning = sqlWarning.getNextWarning();
                    if ("\n".equals(sqlWarning.getMessage())) {
                        break;
                    }
                    String[] parts22 = sqlWarning.getMessage().split(":  ");
                    if (parts22.length == 1) {
                        break;
                    }
                    String part = parts22[1];
                    String[] part32 = part.split(" ");
                    if (part32.length > 0) {
                        eventStr2 += part32[0];
                    }
                    if (part32.length > 1) {
                        eventStr2 += part32[1];
                    }
                    if (part32.length > 2) {
                        eventStr2 += part32[2];
                    }
                    if (part32.length > 3) {
                        eventStr2 += part32[3];
                    }
                }

                Map<String, Object> dbccLogFormat = getDBCCLogFormat(tableName);
                try {
                    Map<String, Object> e = parseEvent(tableName, eventStr2, dbccLogFormat, 4, false);
                    String pkHash = parseHash(tableName, eventStr2, dbccLogFormat, 4);
                    if (debug) {
                        tapLogger.info("innop pkHash: {}, eventStr: {}, event: {}", pkHash, eventStr2, e);
                    }
                    String k = eventStr2.substring(0, 1) + "0" + eventStr2.substring(2, 4) + eventStr2.substring(4);
                    innopCache.put(k, eventStr2);
                    innopCache.put(pkHash, eventStr2);
                } catch (Exception e2) {
                    tapLogger.warn("parse event failed, eventStr: {}, table: {}, dbccLogFormat: {}, error: {}", eventStr2, tableName, dbccLogFormat, e2.getMessage());
                    continue;
                }

            }

            // 数据段
            if (message.startsWith("xrow:") || message.startsWith("ximage:") || message.startsWith("xdol_row:") || message.startsWith("Undo-only log record") || message.startsWith("Redo-only log record")) {
                String eventStr = "";
                Integer skipLine = 0;
                Integer skipBytes = 0;
                TapTable tapTable =null;
                if (withSchema) {
                    tapTable = tapTableMap.get(sybaseContext.getConfig().getSchema() + "." + tableName);
                } else {
                    tapTable = tapTableMap.get(tableName);
                }
                if (tapTable == null) {
                    continue;
                }
                String storageEngine = tapTable.getStorageEngine();
                if (storageEngine == null) {
                    storageEngine = "allpages";
                }

                if (message.startsWith("xrow:")) {
                    skipLine = 0;
                    skipBytes = 4;
                }

                if (message.startsWith("xdol_row:")) {
                    skipLine = 1;
                    skipBytes = 12;
                    isNewFormat = true;
                }

                if (message.startsWith("Undo-only log record") || message.startsWith("Redo-only log record")) {
                    skipLine = 1;
                    skipBytes = 8;
                    isNewFormat = true;
                }


                if (storageEngine.equalsIgnoreCase("allpages") && !ximageNewFormat && !message.startsWith("xdol_row")) {
                    skipLine = 0;
                    skipBytes = 4;
                    isNewFormat = false;
                }

                if (message.startsWith("ximage:")) {
                    if (ximageNewFormat) {
                        skipLine = 0;
                        skipBytes = 12;
                        isNewFormat = true;
                    } else {
                        skipLine = 0;
                        skipBytes = 4;
                        isNewFormat = false;
                    }
                }

                while (true) {
                    sqlWarning = sqlWarning.getNextWarning();
                    if (skipLine > 0) {
                        skipLine -= 1;
                        continue;
                    }

                    if ("\n".equals(sqlWarning.getMessage())) {
                        break;
                    }

                    String[] parts2 = sqlWarning.getMessage().split(":  ");
                    if (parts2.length == 1) {
                        break;
                    }
                    String part = parts2[1];
                    String[] part3 = part.split(" ");
                    if (part3.length > 0) {
                        eventStr += part3[0];
                    }
                    if (part3.length > 1) {
                        eventStr += part3[1];
                    }
                    if (part3.length > 2) {
                        eventStr += part3[2];
                    }
                    if (part3.length > 3) {
                        eventStr += part3[3];
                    }
                }
                Map<String, Object> e = new HashMap<>();
                String pkHash = "";
                if (operation.equals("insert") || operation.equals("delete") || operation.equals("update") || operation.equals("rowimage") || operation.equals("dol_insert") || operation.equals("dol_update") || operation.equals("dol_delete")) {
                    Map<String, Object> dbccLogFormat = getDBCCLogFormat(tableName);
                    if (dbccLogFormat == null) {
                        continue;
                    }
                    try {
                        e = parseEvent(tableName, eventStr, dbccLogFormat, skipBytes, isNewFormat);
                        pkHash = parseHash(tableName, eventStr, dbccLogFormat, skipBytes);
                    } catch (Exception e1) {
                        tapLogger.warn("parse event failed, eventStr: {}, table: {}, dbccLogFormat: {}, error: {}", eventStr, tableName, dbccLogFormat, e1.getMessage());
                        continue;
                    }
                    String hash = eventStr;
                    event.put("hash", hash);
                    event.put("pkHash", pkHash);
                } else {
                    continue;
                }
                event.put("table", tableName);
                event.put("transId", transId);
                event.put("startRid", startRid);
                event.put("rowId", rowId);
                if (nextStartRid != 0) {
                    event.put("nextStartRid", nextStartRid);
                }
                event.put("nextRowId", nextRowId);
                event.put("lowTs", lowTs);
                event.put("highTs", highTs);
                event.put("ts", ts);

                if ("insert".equals(operation) || "dol_insert".equals(operation)) {
                    event.put("op", String.valueOf(INSERT));
                    event.put("before", null);
                    event.put("after", e);
                    continue;
                }
                if ("delete".equals(operation) || "dol_delete".equals(operation)) {
                    event.put("op", String.valueOf(DELETE));
                    event.put("before", e);
                    event.put("after", null);
                    continue;
                }

                if ("update".equals(operation) || "dol_update".equals(operation)) {
                    event.put("op", String.valueOf(UPDATE));
                    if (writeText) {
                        event.put("before", null);
                        event.put("after", e);
                    } else {
                        if (event.containsKey("before")) {
                            event.put("after", e);
                        } else {
                            event.put("before", e);
                        }
                    }
                    continue;
                }
            }

            if (!"endxact".equals(operation) && !"clr".equals(operation) && !"beginxact".equals(operation)) {
                continue;
            }

            event.put("startRid", startRid);
            event.put("rowId", rowId);
            event.put("transId", transId);
            event.put("table", "");
            event.put("nextStartRid", nextStartRid);
            event.put("nextRowId", nextRowId);
            event.put("lowTs", lowTs);
            event.put("highTs", highTs);
            event.put("ts", ts);

            if ("endxact".equals(operation)) {
                event.put("op", String.valueOf(COMMIT));
                event.put("before", null);
                event.put("after", null);
            }

            if ("clr".equals(operation)) {
                event.put("op", String.valueOf(ROLLBACK));
                event.put("before", null);
                event.put("after", null);
            }

            if ("beginxact".equals(operation)) {
                event.put("op", String.valueOf(BEGIN));
                event.put("before", null);
                event.put("after", null);
            }
        }
        return events;
    }

    private long detectTimezoneOffset() throws Exception {
        long offset = 0L;
        Connection connection1 = sybaseContext.getConnection();
        Statement statement1 = connection1.createStatement();
        statement1.execute("select getdate()");
        ResultSet resultSet = statement1.getResultSet();
        if (!resultSet.next()) {
            return 0L;
        }
        Timestamp timestamp = resultSet.getTimestamp(1);
        offset = System.currentTimeMillis() - timestamp.getTime();
        statement1.close();
        connection1.close();
        return Math.round((float) offset / (float) 3600000) * 3600000L;
    }

    private List<Map<String, Object>> rescanArchiveLog (long startRid, long rowId) throws SQLException {
        Integer autoMinerRescanBatch = sybaseConfig.getAutoMinerRescanBatch();
        long s1 = System.currentTimeMillis();
        Connection connection1 = sybaseContext.getConnection();
        Statement statement1 = connection1.createStatement();
        statement1.execute("dbcc traceon(3604)");
        String sql = String.format("dbcc log(%s, 1, %d, %d, %d, -1, 0)", sybaseConfig.getDatabase(), startRid, rowId, autoMinerRescanBatch);
        statement1.execute(sql);
        SQLWarning sqlWarning = statement1.getWarnings();
        List<Map<String, Object>> events = fetchEvents(sqlWarning, startRid, rowId);
        statement1.close();
        connection1.close();
        long s2 = System.currentTimeMillis();
        tapLogger.info("rescan archive: {}, {}, size: {}, cost: {} ms", startRid, rowId, events.size(), s2 - s1);
        // print events
        if (debug) {
            for (Map<String, Object> event : events) {
                tapLogger.info("rescan event: {}", event);
            }
        }

        return events;
    }

    private List<Map<String, Object>> rescanOnlineLog (long startRid, long rowId) throws SQLException {
        List<Map<String, Object>> events = new ArrayList<>();
        int scan = sybaseConfig.getManualMinerScanBatch();
        int loop = sybaseConfig.getManualMinerScanTimeoutMs()/1000/5;
        if (loop < 2) {
            loop = 2;
        }
        for(int i=1; i <= loop; i++) {
            long s1 = System.currentTimeMillis();
            Connection connection1 = sybaseContext.getConnection();
            Statement statement1 = connection1.createStatement();
            statement1.execute("dbcc traceon(3604)");
            String sql = String.format("dbcc log(%s, -1, %d, %d, %d, -1, 0)", sybaseConfig.getDatabase(), startRid, rowId, scan);
            statement1.execute(sql);
            SQLWarning sqlWarning = statement1.getWarnings();
            events = fetchEvents(sqlWarning, startRid, rowId);
            statement1.close();
            connection1.close();
            long s2 = System.currentTimeMillis();
            tapLogger.info("scan online t: {}, {}, {}, size: {}, cost: {} ms", i, startRid, rowId, events.size(), s2 - s1);
            // print events
            if (debug) {
                for (Map<String, Object> event : events) {
                    tapLogger.info("rescan event: {}", event);
                }
            }
            if (scanRecords == scan) {
                break;
            }
            tapLogger.info("scan online logs not enough: {}, will rescan it until it's enough: {}, or loop: {} to 5", scanRecords, scan, i);
            TapSimplify.sleep(2000);
        }

        return events;
    }

    public String getTableNameById(String id) throws SQLException {
        if (idToTable.containsKey(id)) {
            return idToTable.get(id);
        }
        Connection connection1 = sybaseContext.getConnection();
        Statement statement1 = connection1.createStatement();
        String sql = "select object_name(" + id + ")";
        statement1.execute(sql);
        ResultSet resultSet = statement1.getResultSet();
        if (!resultSet.next()) {
            return null;
        }
        String tableName = resultSet.getString(1);
        idToTable.put(id, tableName);
        statement1.close();
        connection1.close();
        return tableName;
    }

    public int getFloatLength(String tableName, String columnName) {
        String sql = String.format(SELECT_FLOAT_LENGTH, tableName, columnName);
        if (debug) {
            tapLogger.info("getFloatLength sql: {}", sql);
        }
        try {
            Connection connection1 = sybaseContext.getConnection();
            Statement statement1 = connection1.createStatement();
            statement1.execute(sql);
            ResultSet resultSet = statement1.getResultSet();
            if (!resultSet.next()) {
                return 4;
            }
            int length = resultSet.getInt(2);
            if (debug) {
                tapLogger.info("getFloatLength: table: {}, column: {}, length: {}", tableName, columnName, length);
            }
            try {
                connection1.close();
            } catch (Exception e) {
                tapLogger.info(e);
            }
            return length;
        } catch (SQLException e) {
            tapLogger.warn(e);
        }
        return 4;
    }

    public Map<String, Object> getDBCCLogFormat(String tableName) {
        if (dbccColumnsMap.containsKey(tableName)) {
            if (debug) {
                tapLogger.info("tableName: {}, dbccColumnsMap: {} from cache", tableName, dbccColumnsMap.get(tableName));
            }
            return dbccColumnsMap.get(tableName);
        }
        Map<String, Object> logFormat = new HashMap<>();

        TapTable s1 = null;
        if (tapTableMap.containsKey(tableName)) {
            s1 = tapTableMap.get(tableName);
        } else {
            if (tapTableMap.containsKey(sybaseContext.getConfig().getSchema() + "." + tableName)) {
                s1 = tapTableMap.get(sybaseContext.getConfig().getSchema() + "." + tableName);
            }
        }

        if (s1 == null) {
            return null;
        }

        LinkedHashMap<String, TapField> fields = s1.getNameFieldMap();
        List<Map<String, Object>> fixedColumns = new ArrayList<>();
        List<Map<String, Object>> varColumns = new ArrayList<>();
        // 第一步, 选出非 null 的定长类型
        for (Map.Entry<String, TapField> entry : fields.entrySet()) {
            TapField field = entry.getValue();
            Map<String, Object> column = new HashMap<>();
            column.put("length", 0);
            column.put("scale", 0);
            column.put("pk", field.getPrimaryKey());
            if (field.getDataType().contains("int")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }
            if (field.getDataType().startsWith("float")) {
                column.put("name", field.getName());
                column.put("type", "float");
                int length = getFloatLength(tableName, field.getName());
                column.put("length", length);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }
            if (field.getDataType().startsWith("real")) {
                column.put("name", field.getName());
                column.put("type", "real");
                int length = 4;
                column.put("length", length);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }
            if (field.getDataType().startsWith("bit")) {
                column.put("name", field.getName());
                column.put("type", "bit");
                fixedColumns.add(column);
            }

            if (field.getDataType().contains("datetime")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().equals("date")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().equals("time") || field.getDataType().equals("bigtime")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().startsWith("decimal") || field.getDataType().startsWith("numeric")) {
                column.put("name", field.getName());
                if (field.getDataType().startsWith("decimal")) {
                    column.put("type", "decimal");
                } else {
                    column.put("type", "numeric");
                }
                column.put("length", Integer.parseInt(field.getDataType().split("\\(")[1].split(",")[0]));
                column.put("scale", Integer.parseInt(field.getDataType().split("\\(")[1].split(",")[1].split("\\)")[0]));
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }
            if (field.getDataType().startsWith("char")) {
                column.put("name", field.getName());
                column.put("type", "char");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().startsWith("varchar")) {
                column.put("name", field.getName());
                column.put("type", "varchar");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                varColumns.add(column);
            }

            if (field.getDataType().startsWith("unichar")) {
                column.put("name", field.getName());
                column.put("type", "unichar");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }
            if (field.getDataType().startsWith("univarchar")) {
                column.put("name", field.getName());
                column.put("type", "univarchar");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                varColumns.add(column);
            }
            if (field.getDataType().startsWith("nchar")) {
                column.put("name", field.getName());
                column.put("type", "nchar");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().startsWith("nvarchar")) {
                column.put("name", field.getName());
                column.put("type", "nvarchar");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                varColumns.add(column);
            }

            if (field.getDataType().startsWith("text")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                varColumns.add(column);
            }
            if (field.getDataType().startsWith("binary")) {
                column.put("name", field.getName());
                column.put("type", "binary");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }
            if (field.getDataType().startsWith("varbinary")) {
                column.put("name", field.getName());
                column.put("type", "varbinary");
                column.put("length", field.getDataType().split("\\(")[1].split("\\)")[0]);
                column.put("scale", 0);
                varColumns.add(column);
            }
            if (field.getDataType().startsWith("image")) {
                column.put("name", field.getName());
                column.put("type", "image");
                column.put("length", 0);
                column.put("scale", 0);
                varColumns.add(column);
            }

            if (field.getDataType().startsWith("money")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().startsWith("smallmoney")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                if (!field.getNullable()) {
                    fixedColumns.add(column);
                } else {
                    varColumns.add(column);
                }
            }

            if (field.getDataType().startsWith("timestamp")) {
                column.put("name", field.getName());
                column.put("type", field.getDataType());
                column.put("length", 0);
                column.put("scale", 0);
                varColumns.add(column);
            }
        }

        logFormat.put("varColumns", varColumns);
        logFormat.put("fixedColumns", fixedColumns);

        dbccColumnsMap.put(tableName, logFormat);
        if (debug) {
            tapLogger.info("tableName: {}, dbccColumnsMap: {}", tableName, logFormat);
        }
        return logFormat;
    }

    public void startMinerV2() throws Throwable {
        tapLogger.info("sybase cdc work with mode v2: manual rescan");
        final int MAX_ENTRIES = 10000;
        Map<String, Boolean> transFilter = new LinkedHashMap(MAX_ENTRIES + 1, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };

        Map<String, Map<String, Long>> uncommitTrans = new LinkedHashMap(MAX_ENTRIES * 100, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };

        Map<String, Boolean> repeatFilter = new LinkedHashMap(MAX_ENTRIES * 10, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };

        idToTable = new HashMap<>();

        dbccColumnsMap = new HashMap<>();

        SybaseBeforeCdc sybaseBeforeCdc = new SybaseBeforeCdc(sybaseContext, tapLogger);
        if (withSchema) {
            sybaseBeforeCdc.withCdcTables(schemaTableMap);
        } else {
            Map<String, List<String>> cdcTables = new HashMap<>();
            cdcTables.put(sybaseConfig.getSchema(), tableList);
            sybaseBeforeCdc.withCdcTables(cdcTables);
        }

        connection = sybaseContext.getConnection();
        sybaseOffset = sybaseBeforeCdc.doBefore();

        isRunning.set(true);
        initRedoLogQueueAndThread();
        statement = connection.createStatement();
        long lastFlushTime = System.currentTimeMillis();

        statement.close();
        statement.getConnection().close();

        if (sybaseConfig.getDebugLog() == 1) {
            tapLogger.info("sybase cdc debug log is enabled");
            debug = true;
        } else {
            tapLogger.info("sybase cdc debug log is disabled");
        }

        long startRid;
        long rowId;
        long h;
        long l;
        int transTimestampOffset = (int) detectTimezoneOffset();
        tapLogger.info("trans timestamp offset: " + transTimestampOffset);
        long transTimestamp = System.currentTimeMillis();

        long nextStartRid = 0;
        long nextRowId = 0;

        List<Map<String, Object>> events;

        long lastPrintUncommitTrans = 0;
        long previousStartRid = 0;
        long previousRowId = 0;

        while (isRunning.get()) {
            if (EmptyKit.isNotNull(threadException.get())) {
                for (StackTraceElement stackTraceElement : threadException.get().getStackTrace()) {
                    tapLogger.warn(stackTraceElement.toString());
                }
                throw new RuntimeException(threadException.get());
            }

            SybaseOffset sybaseOffset1 = getRescanOffsetSimple(sybaseOffset, uncommitTrans);
            if (sybaseOffset1.getStartRid() == previousStartRid && sybaseOffset1.getRowId() == previousRowId) {
                tapLogger.info("normal rescan, will sleep 1s, and scan from startRid: " + sybaseOffset1.getStartRid() + ", rowId: " + sybaseOffset1.getRowId());
                TapSimplify.sleep((long)sybaseConfig.getAutoMinerNormalSleepMs());
            } else {
                tapLogger.info("continue normal rescan from startRid: " + sybaseOffset1.getStartRid() + ", rowId: " + sybaseOffset1.getRowId());
            }

            previousStartRid = sybaseOffset1.getStartRid();
            previousRowId = sybaseOffset1.getRowId();
            events = rescanOnlineLog(sybaseOffset1.getStartRid(), sybaseOffset1.getRowId());

            if (EmptyKit.isEmpty(events)) {
                continue;
            }

            if (System.currentTimeMillis() - lastPrintUncommitTrans > 10000) {
                lastPrintUncommitTrans = System.currentTimeMillis();
                tapLogger.info("uncommit trans size: " + uncommitTrans.size());
                if (uncommitTrans.size() > 0) {
                    tapLogger.info("uncommit trans: {}", uncommitTrans);
                }
            }

            String transactionId = "";

            boolean commit = false;
            for (Map<String, Object> event : events) {
                String operation = String.valueOf(event.get("op"));
                if (event.containsKey("ts")) {
                    transTimestamp = Long.parseLong(event.get("ts").toString()) + transTimestampOffset;
                } else {
                    transTimestamp = System.currentTimeMillis();
                }
                try {
                    startRid = Integer.parseInt(event.get("startRid").toString());
                } catch (Exception e) {
                    tapLogger.warn(e);
                    // 打印 event
                    for (Map.Entry<String, Object> entry : event.entrySet()) {
                        tapLogger.info("key: {}, value: {}", entry.getKey(), entry.getValue());
                    }
                    continue;
                }
                rowId = Integer.parseInt(event.get("rowId").toString());
                transactionId = startRid + "-" + rowId;

                if (operation.equals("BEGIN")) {
                    Map<String, Long> uncommit = new HashMap<>();
                    uncommit.put("startRid", startRid);
                    uncommit.put("rowId", rowId);
                    uncommit.put("timestamp", transTimestamp);
                    uncommit.put("enTimestamp", System.currentTimeMillis());
                    uncommitTrans.put(transactionId, uncommit);
                }

                h = Long.parseLong(event.get("highTs").toString());
                l = Long.parseLong(event.get("lowTs").toString());
                NormalRedo normalRedo = new NormalRedo();
                normalRedo.setTransactionId(transactionId);
                normalRedo.setCdcSequenceStr(startRid + "-" + rowId + "-" + h + "-" + l);
                normalRedo.setTimestamp(transTimestamp);
                lastEventTimestamp = transTimestamp;
                normalRedo.setOperation(operation);

                if (COMMIT.toString().equals(operation)) {
                    commit = true;
                    uncommitTrans.remove(transactionId);
                    normalRedo.setTimestamp(transTimestamp);
                    nextStartRid = Long.parseLong(event.get("nextStartRid").toString());
                    nextRowId = Long.parseLong(event.get("nextRowId").toString());
                    if ((h > sybaseOffset.getH() || (h == sybaseOffset.getH() && l >= sybaseOffset.getL()))
                            && (
                            (nextStartRid < sybaseOffset.getStartRid() - 1000 || nextStartRid > sybaseOffset.getStartRid()) ||
                                    (nextStartRid == sybaseOffset.getStartRid() && nextRowId > sybaseOffset.getRowId()))) {
                        sybaseOffset.setStartRid(nextStartRid);
                        sybaseOffset.setRowId(nextRowId);
                        sybaseOffset.setH(h);
                        sybaseOffset.setL(l);
                        if ((System.currentTimeMillis() - lastFlushTime > 1000L * sybaseConfig.getDumpLogTimeS())) {
                            lastFlushTime = System.currentTimeMillis();
                            SybaseOffset sybaseOffset2 = getRescanOffsetSimple(sybaseOffset, uncommitTrans);
                            updateLogsHold(sybaseOffset2);
                        }
                    }
                }

                normalRedo.setTableName(String.valueOf(event.get("table")));
                normalRedo.setNameSpace(String.valueOf(sybaseContext.getConfig().getSchema()));

                if (DELETE.toString().equals(operation)) {
                    normalRedo.setRedoRecord((Map<String, Object>) event.get("before"));
                } else {
                    normalRedo.setRedoRecord((Map<String, Object>) event.get("after"));
                }
                normalRedo.setUndoRecord((Map<String, Object>) event.get("before"));

                if (filter(transFilter, repeatFilter, h, l, normalRedo, uncommitTrans)) {
                    continue;
                }
                enqueueRedoLogContent2(normalRedo, transFilter, repeatFilter);
            }

            if (!commit) {
                Long startRid2 = sybaseOffset1.getStartRid();
                Long rowId2 = sybaseOffset1.getRowId();
                tapLogger.info("last rescan not find commit, will rescan it again with 1: {}, {}", startRid2, rowId2);
                List<Map<String, Object>> rescanEvents = rescanArchiveLog(startRid2, rowId2);
                for (Map<String, Object> rescanEvent : rescanEvents) {
                    NormalRedo normalRedo1 = new NormalRedo();
                    Long h2 = Long.parseLong(rescanEvent.get("highTs").toString());
                    Long l2 = Long.parseLong(rescanEvent.get("lowTs").toString());
                    normalRedo1.setTransactionId(String.valueOf(rescanEvent.get("transId")));
                    nextStartRid = Long.parseLong(rescanEvent.get("nextStartRid").toString());
                    nextRowId = Long.parseLong(rescanEvent.get("nextRowId").toString());
                    normalRedo1.setCdcSequenceStr(startRid2 + "-" + rowId2 + "-" + h2 + "-" + l2);
                    normalRedo1.setTimestamp(transTimestamp);
                    normalRedo1.setOperation(String.valueOf(rescanEvent.get("op")));
                    normalRedo1.setTableName(String.valueOf(rescanEvent.get("table")));
                    normalRedo1.setNameSpace(String.valueOf(sybaseContext.getConfig().getSchema()));
                    normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("after"));
                    normalRedo1.setUndoRecord((Map<String, Object>) rescanEvent.get("before"));

                    if (DELETE.toString().equals(normalRedo1.getOperation())) {
                        normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("before"));
                    }

                    if (COMMIT.toString().equals(normalRedo1.getOperation())) {
                        uncommitTrans.remove(normalRedo1.getTransactionId());

                        if ((h2 > sybaseOffset.getH() || (h2 == sybaseOffset.getH() && l2 >= sybaseOffset.getL()))
                                && (
                                (nextStartRid < sybaseOffset.getStartRid() - 1000 || nextStartRid > sybaseOffset.getStartRid()) ||
                                        (nextStartRid == sybaseOffset.getStartRid() && nextRowId > sybaseOffset.getRowId()))) {
                            sybaseOffset.setStartRid(nextStartRid);
                            sybaseOffset.setRowId(nextRowId);
                            sybaseOffset.setH(h2);
                            sybaseOffset.setL(l2);
                            if ((System.currentTimeMillis() - lastFlushTime > 1000L * sybaseConfig.getDumpLogTimeS())) {
                                lastFlushTime = System.currentTimeMillis();
                                SybaseOffset sybaseOffset2 = getRescanOffsetSimple(sybaseOffset, uncommitTrans);
                                updateLogsHold(sybaseOffset2);
                            }
                        }
                    }

                    if (filter(transFilter, repeatFilter, h2, l2, normalRedo1, uncommitTrans)) {
                        continue;
                    }

                    if (debug) {
                        tapLogger.info("enqueue redo log content transId: {}, op: {}, before: {}, after: {}, cdc str: {}", normalRedo1.getTransactionId(), normalRedo1.getOperation(), normalRedo1.getUndoRecord(), normalRedo1.getRedoRecord(), normalRedo1.getCdcSequenceStr());
                    }

                    enqueueRedoLogContent2(normalRedo1, transFilter, repeatFilter);
                }
            }
        }
    }

    @Override
    public void startMiner() throws Throwable {
        tapLogger.info("sybase cdc work with mode v1: auto rescan");
        final int MAX_ENTRIES = 1000;
        Map<String, Boolean> transFilter = new LinkedHashMap(MAX_ENTRIES + 1, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };

        Map<String, Map<String, Long>> uncommitTrans = new LinkedHashMap(MAX_ENTRIES * 100, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };

        Map<String, Boolean> repeatFilter = new LinkedHashMap(MAX_ENTRIES * 10, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };
        Map<String, Boolean> transBroken = new LinkedHashMap(MAX_ENTRIES * 10, .75F, true) {
            public boolean removeEldestEntry(Map.Entry eldest) {
                return size() > MAX_ENTRIES;
            }
        };

        idToTable = new HashMap<>();

        dbccColumnsMap = new HashMap<>();

        SybaseBeforeCdc sybaseBeforeCdc = new SybaseBeforeCdc(sybaseContext, tapLogger);
        if (withSchema) {
            sybaseBeforeCdc.withCdcTables(schemaTableMap);
        } else {
            Map<String, List<String>> cdcTables = new HashMap<>();
            cdcTables.put(sybaseConfig.getSchema(), tableList);
            sybaseBeforeCdc.withCdcTables(cdcTables);
        }

        connection = sybaseContext.getConnection();
        sybaseOffset = sybaseBeforeCdc.doBefore();

        isRunning.set(true);
        initRedoLogQueueAndThread();
        statement = connection.createStatement();
        long lastFlushTime = System.currentTimeMillis();

        statement.close();
        statement.getConnection().close();

        long startRid;
        long rowId;
        long h = 0;
        long l = 0;
        int transTimestampOffset = (int) detectTimezoneOffset();
        tapLogger.info("trans timestamp offset: " + transTimestampOffset);
        long transTimestamp = System.currentTimeMillis();
        boolean normalRescan = true;
        long normalRescanSleep = 0;
        boolean pageBrokenRescan = false;
        long nextStartRid = 0;
        long nextRowId = 0;
        ResultSet resultSet = null;

        long lastPrintUncommitTrans = 0;

        if (sybaseConfig.getDebugLog() == 1) {
            tapLogger.info("sybase cdc debug log is enabled");
            debug = true;
        } else {
            tapLogger.info("sybase cdc debug log is disabled");
        }

        while (isRunning.get()) {
            if (normalRescan) {
                SybaseOffset sybaseOffset1 = getRescanOffset(sybaseOffset, uncommitTrans, false, transFilter, repeatFilter, transTimestampOffset);
                normalRescanSleep += (long)sybaseConfig.getAutoMinerNormalSleepMs();
                if (normalRescanSleep > 3000L) {
                    normalRescanSleep = 3000L;
                }
                tapLogger.info("normal rescan, will sleep " + normalRescanSleep / 1000L + "s, and scan from startRid: " + sybaseOffset1.getStartRid() + ", rowId: " + sybaseOffset1.getRowId());
                TapSimplify.sleep(normalRescanSleep);
                rebuildStatementV2(sybaseOffset1.getStartRid(), sybaseOffset1.getRowId(), sybaseOffset);
            }
            if (pageBrokenRescan) {
                tapLogger.info("page broken rescan, will sleep 1s, and scan from startRid: " + sybaseOffset.getStartRid() + ", rowId: " + sybaseOffset.getRowId()+1);
                getRescanOffset(sybaseOffset, uncommitTrans, true, transFilter, repeatFilter, transTimestampOffset);
                TapSimplify.sleep((long)sybaseConfig.getAutoMinerBrokenSleepMs());
                rebuildStatementV2(sybaseOffset.getStartRid(), sybaseOffset.getRowId(), sybaseOffset);
            }

            normalRescan = false;
            pageBrokenRescan = false;

            while (isRunning.get()) {
                if (EmptyKit.isNotNull(threadException.get())) {
                    for (StackTraceElement stackTraceElement : threadException.get().getStackTrace()) {
                        tapLogger.warn(stackTraceElement.toString());
                    }
                    throw new RuntimeException(threadException.get());
                }

                if (normalRescan || pageBrokenRescan) {
                    break;
                }

                boolean hasMore;
                try {
                    hasMore = statement.getMoreResults(CLOSE_ALL_RESULTS);
                } catch (SQLException e) {
                    if (!e.getMessage().contains("JZ0EM")) {
                        tapLogger.debug("page broken, will rescan it again");
                        pageBrokenRescan = true;
                        break;
                    }
                    hasMore = false;
                }
                if (!hasMore) {
                    normalRescan = true;
                    break;
                }
                resultSet = statement.getResultSet();

                if (!resultSet.next()) {
                    continue;
                }

                normalRescanSleep = 0;
                if (System.currentTimeMillis() - lastPrintUncommitTrans > 10000) {
                    lastPrintUncommitTrans = System.currentTimeMillis();
                    tapLogger.info("uncommit trans size: " + uncommitTrans.size());
                    tapLogger.info("uncommit trans: {}", uncommitTrans);
                }

                // 打印 resultSet 的所有列, 用数字 index
                ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
                int columnCount = resultSetMetaData.getColumnCount();
                for (int i = 1; i <= columnCount; i++) {
                    tapLogger.info("column: {},columnName:{}, value: {}", i,resultSetMetaData.getColumnName(i), resultSet.getObject(i));
                }

                String type = resultSet.getString(1);
                String transactionId = "";
                    switch (type) {
                        case "0":
                            try {
                                transTimestamp = resultSet.getTimestamp(4).getTime() + transTimestampOffset;
                                Object startRidObj = resultSet.getObject(2);
                                Object rowIdObj = resultSet.getObject(3);
                                if (!(startRidObj instanceof Number)) {
                                    continue;
                                } else {
                                    startRid = ((Number) startRidObj).longValue();
                                }
                                if (!(rowIdObj instanceof Number)) {
                                    continue;
                                } else {
                                    rowId = ((Number) rowIdObj).longValue();
                                }
                                transactionId = startRid + "-" + rowId;
                                Map<String, Long> uncommit = new HashMap<>();
                                uncommit.put("startRid", startRid);
                                uncommit.put("rowId", rowId);
                                uncommit.put("timestamp", transTimestamp);
                                uncommit.put("enTimestamp", System.currentTimeMillis());
                                uncommitTrans.put(transactionId, uncommit);
                            } catch (Exception e) {
                                Object startRidObj = resultSet.getObject(2);
                                Object rowIdObj = resultSet.getObject(3);
                                tapLogger.warn("error startRid: {} ",startRidObj);
                                tapLogger.warn("error rowRid: {}", rowIdObj);
                                throw new TapCodeException(SybaseErrorCode.STREAM_READ_GET_INFO_ERROR, e)
                                        .dynamicDescriptionParameters(startRidObj,rowIdObj);
                            }
                            continue;
                        case "4":
                        case "5":
                            h = resultSet.getLong(9);
                            l = resultSet.getLong(10);
                            break;
                        case "26":
                            h = resultSet.getLong(10);
                            l = resultSet.getLong(11);
                            break;
                        case "30":
                            h = resultSet.getLong(7);
                            l = resultSet.getLong(8);
                            break;
                        case "46":
                            try {
                                Object startRidObj = resultSet.getObject(2);
                                Object rowIdObj = resultSet.getObject(3);
                                if (!(startRidObj instanceof Number)) {
                                    continue;
                                } else {
                                    startRid = ((Number) startRidObj).longValue();
                                }
                                if (!(rowIdObj instanceof Number)) {
                                    continue;
                                } else {
                                    rowId = ((Number) rowIdObj).longValue();
                                }
                                transactionId = startRid + "-" + rowId;
                                tapLogger.info("find truncate event will fallback to manual miner to get it , transId: {}, startRid: {}, rowId: {}", transactionId, startRid, rowId);
                            } catch (Exception e) {
                                Object startRidObj = resultSet.getObject(2);
                                Object rowIdObj = resultSet.getObject(3);
                                tapLogger.warn("error startRid: {} ", startRidObj);
                                tapLogger.warn("error rowRid: {}", rowIdObj);
                                throw new TapCodeException(SybaseErrorCode.STREAM_READ_GET_INFO_ERROR, e)
                                        .dynamicDescriptionParameters(startRidObj, rowIdObj);
                            }
                            break;
                        case "36":
                        case "32":
                            try {
                                Object startRidObj = resultSet.getObject(2);
                                Object rowIdObj = resultSet.getObject(3);
                                if (!(startRidObj instanceof Number)) {
                                    continue;
                                } else {
                                    startRid = ((Number) startRidObj).longValue();
                                }
                                if (!(rowIdObj instanceof Number)) {
                                    continue;
                                } else {
                                    rowId = ((Number) rowIdObj).longValue();
                                }
                                transactionId = startRid + "-" + rowId;
                                transBroken.put(transactionId, true);
                                tapLogger.info("find TEXT UPDATE in auto miner, will fallback to manual miner to get it, transId: {}, startRid: {}, rowId: {}", transactionId, startRid, rowId);
                            } catch (Exception e) {
                                Object startRidObj = resultSet.getObject(2);
                                Object rowIdObj = resultSet.getObject(3);
                                tapLogger.warn("error startRid: {} ", startRidObj);
                                tapLogger.warn("error rowRid: {}", rowIdObj);
                                throw new TapCodeException(SybaseErrorCode.STREAM_READ_GET_INFO_ERROR, e)
                                        .dynamicDescriptionParameters(startRidObj, rowIdObj);
                            }
                        default:
                            continue;
                    }


                startRid = resultSet.getLong(2);
                rowId = resultSet.getLong(3);
                NormalRedo normalRedo = new NormalRedo();
                transactionId = startRid + "-" + rowId;
                normalRedo.setTransactionId(transactionId);
                normalRedo.setCdcSequenceStr(startRid + "-" + rowId + "-" + h + "-" + l);
                String schema = "";
                String tableName = "";
                normalRedo.setTimestamp(transTimestamp);
                lastEventTimestamp = transTimestamp;
                switch (type) {
                    case "26":
                    case "30":
                        if (type.equals("26")) {
                            normalRedo.setOperation(String.valueOf(ROLLBACK));
                            normalRedo.setTimestamp(transTimestamp);
                        }
                        if (type.equals("30")) {
                            transTimestamp = resultSet.getTimestamp(9).getTime() + transTimestampOffset;
                            normalRedo.setOperation(String.valueOf(COMMIT));
                            normalRedo.setTimestamp(transTimestamp);
                            nextStartRid = resultSet.getLong(5);
                            nextRowId = resultSet.getLong(6);
                            uncommitTrans.remove(transactionId);
                            if (debug) {
                                tapLogger.info("commit transId: {}, nextStartRid: {}, nextRowId: {}", transactionId, nextStartRid, nextRowId);
                            }
                            if (transBroken.containsKey(transactionId)) {
                                // 这里修正字符串长度 > 255 被截断的问题, 以及一些其他的需要重扫归档的问题
                                tapLogger.info("transBroken find, will rescan: {}, {}", startRid, rowId);
                                List<Map<String, Object>> rescanEvents = rescanArchiveLog(startRid, rowId);
                                for (Map<String, Object> rescanEvent : rescanEvents) {
                                    NormalRedo normalRedo1 = new NormalRedo();
                                    normalRedo1.setTransactionId(String.valueOf(rescanEvent.get("transId")));
                                    normalRedo1.setCdcSequenceStr(startRid + "-" + rowId + "-" + h + "-" + l);
                                    normalRedo1.setTimestamp(transTimestamp);
                                    normalRedo1.setOperation(String.valueOf(rescanEvent.get("op")));
                                    normalRedo1.setTableName(String.valueOf(rescanEvent.get("table")));
                                    normalRedo1.setNameSpace(String.valueOf(sybaseContext.getConfig().getSchema()));
                                    normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("after"));
                                    normalRedo1.setUndoRecord((Map<String, Object>) rescanEvent.get("before"));

                                    if (DELETE.toString().equals(normalRedo1.getOperation())) {
                                        normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("before"));
                                    }

                                    if (COMMIT.toString().equals(normalRedo1.getOperation())) {
                                        uncommitTrans.remove(normalRedo1.getTransactionId());
                                    }

                                    if (filter(transFilter, repeatFilter, h, l, normalRedo1, uncommitTrans)) {
                                        continue;
                                    }

                                    if (debug) {
                                        tapLogger.info("enqueue redo log content transId: {}, op: {}, before: {}, after: {}, cdc str: {}", normalRedo1.getTransactionId(), normalRedo1.getOperation(), normalRedo1.getUndoRecord(), normalRedo1.getRedoRecord(), normalRedo1.getCdcSequenceStr());
                                    }
                                    enqueueRedoLogContent2(normalRedo1, transFilter, repeatFilter);
                                }
                                transBroken.remove(transactionId);
                            }
                        }

                        // 定时更新数据库日志断点
                        if (type.equals("30") &&
                                (h > sybaseOffset.getH() || (h == sybaseOffset.getH() && l >= sybaseOffset.getL()))
                                && (
                                (nextStartRid < sybaseOffset.getStartRid() - 1000 || nextStartRid > sybaseOffset.getStartRid()) ||
                                        (nextStartRid == sybaseOffset.getStartRid() && nextRowId > sybaseOffset.getRowId()))) {
                            if (debug) {
                                tapLogger.info("update sybase offset startRid to: {}, rowId to: {}", nextStartRid, nextRowId);
                            }
                            sybaseOffset.setStartRid(nextStartRid);
                            sybaseOffset.setRowId(nextRowId);
                            sybaseOffset.setH(h);
                            sybaseOffset.setL(l);
                            if ((System.currentTimeMillis() - lastFlushTime > 1000L * sybaseConfig.getDumpLogTimeS())) {
                                lastFlushTime = System.currentTimeMillis();
                                SybaseOffset sybaseOffset2 = getRescanOffset(sybaseOffset, uncommitTrans, false, transFilter, repeatFilter, transTimestampOffset);
                                boolean updateSuccess = updateLogsHold(sybaseOffset2);
                                if (updateSuccess) {
                                    tapLogger.info("update sybase offset startRid to: {}, rowId to: {}, and dump logs", sybaseOffset2.getStartRid(), sybaseOffset2.getRowId());
                                } else {
                                    tapLogger.warn("update sybase offset startRid to: {}, rowId to: {}, and dump logs failed", sybaseOffset2.getStartRid(), sybaseOffset2.getRowId());
                                }
                                normalRescan = true;
                            }
                        }
                        break;
                    case "4":
                        tableName = resultSet.getString(11);
                        schema = resultSet.getString(12);
                        normalRedo.setTableName(tableName);
                        normalRedo.setNameSpace(schema);
                        normalRedo.setOperation(String.valueOf(INSERT));
                        if (statement.getMoreResults(CLOSE_ALL_RESULTS)) {
                            ResultSet resultSet1 = statement.getResultSet();
                            if (resultSet1.next()) {
                                if (generateGlobalMap(resultSet1, schema, tableName)) {
                                    Map<String, Object> r = SybaseDataTypeConvert.filterTimeForDataBase(resultSet1, typeAndNameMap.get(schema + "." + tableName), dateTypeSetMap.get(schema + "." + tableName), needEncode, encode, decode);
                                    for (Map.Entry<String, Object> entry : r.entrySet()) {
                                        if (entry.getValue() instanceof String) {
                                            if (((String) entry.getValue()).length() == 255) {
                                                transBroken.put(transactionId, true);
                                                break;
                                            }
                                        }
                                    }
                                    normalRedo.setRedoRecord(r);
                                    if (debug) {
                                        tapLogger.info("receiver insert on table: {}, transId: {}", tableName, transactionId);
                                        for (Map.Entry<String, Object> entry : r.entrySet()) {
                                            tapLogger.info("{}: {}", entry.getKey(), entry.getValue());
                                        }
                                    }
                                } else {
                                }
                            }
                        } else {
                        }
                        break;
                    case "46":
                        List<Map<String, Object>> truncateRescanEvents = rescanOnlineLog(startRid, rowId);
                        for (Map<String, Object> rescanEvent : truncateRescanEvents) {
                            if (String.valueOf(TRUNCATE).equals(rescanEvent.get("op"))) {
                                NormalRedo truncateNormalRedo = new NormalRedo();
                                truncateNormalRedo.setTransactionId(String.valueOf(rescanEvent.get("transId")));
                                truncateNormalRedo.setCdcSequenceStr(startRid + "-" + rowId + "-" + h + "-" + l);
                                truncateNormalRedo.setTimestamp(transTimestamp);
                                truncateNormalRedo.setOperation(String.valueOf(rescanEvent.get("op")));
                                truncateNormalRedo.setTableName(String.valueOf(rescanEvent.get("table")));
                                truncateNormalRedo.setOperation(String.valueOf(TRUNCATE));
                                truncateNormalRedo.setNameSpace(String.valueOf(sybaseContext.getConfig().getSchema()));
                                enqueueRedoLogContent2(truncateNormalRedo, transFilter, repeatFilter);
                            }
                        }
                        break;
                    case "5":
                        tableName = resultSet.getString(11);
                        schema = resultSet.getString(12);
                        normalRedo.setTableName(tableName);
                        normalRedo.setNameSpace(schema);
                        nextStartRid = resultSet.getLong(7);
                        nextRowId = resultSet.getLong(8);
                        Boolean pageBroken = false;
                        Boolean g = false;
                        try {
                            statement.getMoreResults(CLOSE_ALL_RESULTS);
                            ResultSet resultSet1 = statement.getResultSet();
                            Map<String, Object> before = null;
                            if (resultSet1.next()) {
                                if (generateGlobalMap(resultSet1, schema, tableName)) {
                                    before = SybaseDataTypeConvert.filterTimeForDataBase(resultSet1, typeAndNameMap.get(schema + "." + tableName), dateTypeSetMap.get(schema + "." + tableName), needEncode, encode, decode);
                                    g = true;
                                }
                                if (resultSet1.next()) {
                                    if (g) {
                                        Map<String, Object> after = SybaseDataTypeConvert.filterTimeForDataBase(resultSet1, typeAndNameMap.get(schema + "." + tableName), dateTypeSetMap.get(schema + "." + tableName), needEncode, encode, decode);
                                        for (Map.Entry<String, Object> entry : after.entrySet()) {
                                            if (entry.getValue() instanceof String) {
                                                if (((String) entry.getValue()).length() == 255) {
                                                    transBroken.put(transactionId, true);
                                                    break;
                                                }
                                            }
                                        }
                                        normalRedo.setRedoRecord(after);
                                        normalRedo.setUndoRecord(before);
                                        normalRedo.setOperation(String.valueOf(UPDATE));
                                        if (debug) {
                                            tapLogger.info("receiver update on table: {}, transId: {}, before:", tableName, transactionId);
                                            for (Map.Entry<String, Object> entry : before.entrySet()) {
                                                tapLogger.info("{}: {}", entry.getKey(), entry.getValue());
                                            }

                                            tapLogger.info("receiver update on table: {}, transId: {}, after:", tableName, transactionId);
                                            for (Map.Entry<String, Object> entry : after.entrySet()) {
                                                tapLogger.info("{}: {}", entry.getKey(), entry.getValue());
                                            }
                                        }
                                    } else {
                                    }
                                } else {
                                    if (g) {
                                        for (Map.Entry<String, Object> entry : before.entrySet()) {
                                            if (entry.getValue() instanceof String) {
                                                if (((String) entry.getValue()).length() == 255) {
                                                    transBroken.put(transactionId, true);
                                                    break;
                                                }
                                            }
                                        }
                                        normalRedo.setUndoRecord(before);
                                        normalRedo.setRedoRecord(before);
                                        normalRedo.setOperation(String.valueOf(DELETE));
                                        if (debug) {
                                            tapLogger.info("receiver delete on table: {}, transId: {}", tableName, transactionId);
                                            for (Map.Entry<String, Object> entry : before.entrySet()) {
                                                tapLogger.info("{}: {}", entry.getKey(), entry.getValue());
                                            }
                                        }
                                    } else {
                                    }
                                }
                            }
                        } catch (Exception e) {
                            // 打印
                            pageBroken = true;
                            if (!e.getMessage().contains("the target page is not a data page")) {
                                tapLogger.info(e);
                            }
                        }

                        if (pageBroken) {

                            int pageBrokenSleepMs = sybaseConfig.getAutoMinerBrokenSleepMs() < 1000 ? 1000 : sybaseConfig.getAutoMinerBrokenSleepMs();
                            tapLogger.info("page broken, will sleep {}s, and rescan: {}, {}", pageBrokenSleepMs/1000, startRid, rowId);
                            TapSimplify.sleep(pageBrokenSleepMs);
                            List<Map<String, Object>> rescanEvents = rescanArchiveLog(startRid, rowId);
                            long h2 = 0;
                            long l2 = 0;
                            long nextStartRid2 = 0;
                            long nextRowId2 = 0;
                            for (Map<String, Object> rescanEvent : rescanEvents) {
                                NormalRedo normalRedo1 = new NormalRedo();

                                try {
                                    h2 = Long.parseLong(rescanEvent.get("highTs").toString());
                                } catch (Exception e2) {
                                    tapLogger.warn("rescanEvent highTs: {}", rescanEvent);
                                    continue;
                                }
                                l2 = Long.parseLong(rescanEvent.get("lowTs").toString());
                                normalRedo1.setTransactionId(String.valueOf(rescanEvent.get("transId")));
                                normalRedo1.setCdcSequenceStr(startRid + "-" + rowId + "-" + h2 + "-" + l2);
                                normalRedo1.setTimestamp(transTimestamp);
                                normalRedo1.setOperation(String.valueOf(rescanEvent.get("op")));
                                normalRedo1.setTableName(String.valueOf(rescanEvent.get("table")));
                                normalRedo1.setNameSpace(String.valueOf(sybaseContext.getConfig().getSchema()));

                                normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("after"));
                                normalRedo1.setUndoRecord((Map<String, Object>) rescanEvent.get("before"));

                                if (DELETE.toString().equals(normalRedo1.getOperation())) {
                                    normalRedo1.setRedoRecord((Map<String, Object>) rescanEvent.get("before"));
                                }

                                if (COMMIT.toString().equals(normalRedo1.getOperation())) {
                                    uncommitTrans.remove(normalRedo1.getTransactionId());
                                    nextStartRid2 = Long.parseLong(rescanEvent.get("nextStartRid").toString());
                                    nextRowId2 = Long.parseLong(rescanEvent.get("nextRowId").toString());
                                    tapLogger.info("after rescan, offset change from {}, {} to {}, {}", startRid, rowId, nextStartRid2, nextRowId2);
                                    sybaseOffset.setStartRid(nextStartRid2);
                                    sybaseOffset.setRowId(nextRowId2);
                                }

                                if (filter(transFilter, repeatFilter, h2, l2, normalRedo1, uncommitTrans)) {
                                    continue;
                                }

                                if (debug) {
                                    tapLogger.info("enqueue redo log content transId: {}, op: {}, before: {}, after: {}, cdc str: {}", normalRedo1.getTransactionId(), normalRedo1.getOperation(), normalRedo1.getUndoRecord(), normalRedo1.getRedoRecord(), normalRedo1.getCdcSequenceStr());
                                }
                                enqueueRedoLogContent2(normalRedo1, transFilter, repeatFilter);
                            }


                            long latency = System.currentTimeMillis() - transTimestamp;
                            if (latency < 0) {
                                if (latency / 3600000 > -9 && latency / 3600000 < -5) {
                                    latency = latency + 3600 * 1000 * 8;
                                }
                            }
                            if (latency > 3600000 * 8 && latency < 3600000 * 9) {
                                latency = latency - 3600 * 1000 * 8;
                            }
                            if (latency < 0) {
                                latency = 2000;
                            }

                            // 延迟保持最大 10s
                            if (latency < 10000) {
                                TapSimplify.sleep(latency);
                                tapLogger.info("current latency: {} ms, will sleep {} ms", latency, 10000 - latency);
                            } else {
                                tapLogger.info("current latency: {} ms, will sleep {} ms", latency, 0);
                            }
                            pageBrokenRescan = true;
                        }
                        break;
                    default:
                        continue;
                }

                if (normalRedo.getOperation() == null) {
                    continue;
                }

                if (filter(transFilter, repeatFilter, h, l, normalRedo, uncommitTrans)) {
                    continue;
                }
                if (debug) {
                    tapLogger.info("enqueue redo log content transId: {}, op: {}, before: {}, after: {}, cdc str: {}", normalRedo.getTransactionId(), normalRedo.getOperation(), normalRedo.getUndoRecord(), normalRedo.getRedoRecord(), normalRedo.getCdcSequenceStr());
                }
                enqueueRedoLogContent2(normalRedo, transFilter, repeatFilter);
            }
        }

        if (resultSet != null) {
            try {
                resultSet.close();
            } catch (SQLException ignored) {
            }
        }
        cleanStatement();
    }

    @Override
    protected void processOrBuffRedo(NormalRedo normalRedo, Consumer<Map<String, NormalTransaction>> redoConsumer) {
        Long cdcSequenceId = normalRedo.getCdcSequenceId();
        String cdcSequenceStr = normalRedo.getCdcSequenceStr();
        String transactionId = normalRedo.getTransactionId();
        String operation = normalRedo.getOperation();

        switch (operation) {
            case "BEGIN": {
                NormalTransaction transaction = new NormalTransaction(cdcSequenceId, transactionId, new LinkedList<>());
                transaction.setCdcSequenceStr(cdcSequenceStr);
                transaction.setFirstTimestamp(normalRedo.getTimestamp());
                transaction.setLargeTransactionUpperLimit(largeTransactionUpperLimit);
                transaction.setConnectorId(connectorId);
                transactionBucket.put(transactionId, transaction);
                break;
            }
            case "INSERT":
            case "UPDATE":
            case "DELETE": {
                NormalTransaction transaction = transactionBucket.get(transactionId);
                if (EmptyKit.isNull(transaction)) {
                    transaction = new NormalTransaction(cdcSequenceId, transactionId, new LinkedList<>());
                    transaction.setCdcSequenceStr(cdcSequenceStr);
                    transaction.setFirstTimestamp(normalRedo.getTimestamp());
                    transaction.setLargeTransactionUpperLimit(largeTransactionUpperLimit);
                    transaction.setConnectorId(connectorId);
                    transactionBucket.put(transactionId, transaction);
                }
                transaction.pushRedo(normalRedo);
                long redoSize = transaction.getSize();
                if (redoSize % transaction.getLargeTransactionUpperLimit() == 0) {
                    tapLogger.info(TapLog.CON_LOG_0008.getMsg(), transactionId, redoSize);
                }
                break;
            }
            case "COMMIT":
                if (transactionBucket.containsKey(transactionId)) {
                    NormalTransaction transaction = transactionBucket.get(transactionId);
                    transaction.setLastTimestamp(normalRedo.getTimestamp());
                    commitTransaction(redoConsumer, transaction);
                } else {
                    LinkedList<NormalRedo> redoes = new LinkedList<>();
                    redoes.add(normalRedo);
                    NormalTransaction transaction = new NormalTransaction(cdcSequenceId, transactionId, redoes);
                    transaction.setCdcSequenceStr(cdcSequenceStr);
                    transaction.setLargeTransactionUpperLimit(largeTransactionUpperLimit);
                    transaction.setConnectorId(connectorId);
                    transaction.incrementSize(1);
                    transaction.setLastTimestamp(normalRedo.getTimestamp());
                    Map<String, NormalTransaction> cacheCommitTraction = new HashMap<>();
                    cacheCommitTraction.put(transactionId, transaction);
                    redoConsumer.accept(cacheCommitTraction);
                }
                break;
            case "TRUNCATE": {
                LinkedList<NormalRedo> redoes = new LinkedList<>();
                redoes.add(normalRedo);
                NormalTransaction transaction = new NormalTransaction(cdcSequenceId, transactionId, redoes);
                transaction.setCdcSequenceStr(cdcSequenceStr);
                transaction.setLargeTransactionUpperLimit(largeTransactionUpperLimit);
                transaction.setConnectorId(connectorId);
                transaction.incrementSize(1);
                transaction.setLastTimestamp(normalRedo.getTimestamp());
                Map<String, NormalTransaction> cacheCommitTraction = new HashMap<>();
                cacheCommitTraction.put(transactionId, transaction);
                redoConsumer.accept(cacheCommitTraction);
                break;
            }
            case "ROLLBACK": {
                if (transactionBucket.containsKey(transactionId)) {
                    NormalTransaction transaction = transactionBucket.get(transactionId);
                    if (transaction.isLarge()) {
                        tapLogger.debug("Found large transaction be rolled back: {}", transaction);
                    }
                    transaction.clearRedoes();
                    transactionBucket.remove(transactionId);
                }
                break;
            }
            default:
                break;
        }
    }

    private void enqueueRedoLogContent2(NormalRedo normalRedo, Map<String, Boolean> transFilter, Map<String, Boolean> repeatFilter) {
        if (BEGIN.toString().equals(normalRedo.getOperation())) {
            transFilter.remove(normalRedo.getTransactionId());
            Iterator iterator = repeatFilter.keySet().iterator();
            while (iterator.hasNext()) {
                String key = (String) iterator.next();
                if (key.startsWith(normalRedo.getTransactionId())) {
                    iterator.remove();
                }
            }
        }
        enqueueRedoLogContent(normalRedo);
    }

    private boolean filter(Map<String, Boolean> transFilter, Map<String, Boolean> repeatFilter, long h, long l, NormalRedo normalRedo, Map<String, Map<String, Long>> uncommitTrans) {
        String filterKey = h + "-" + l + "-" + normalRedo.getTransactionId();
        String schema = normalRedo.getNameSpace();
        String tableName = normalRedo.getTableName();
        String op = normalRedo.getOperation();
        if (BEGIN.toString().equals(op)) {
            return false;
        }

        if(ROLLBACK.toString().equals(op)) {
            return false;
        }

        if(COMMIT.toString().equals(op)) {
            transFilter.put(filterKey, true);
            uncommitTrans.remove(normalRedo.getTransactionId());
            return false;
        }

        if (transFilter.containsKey(filterKey)) {
            if (debug) {
                tapLogger.info(
                        "filter op: {}, transId: {}, table: {}, before: {}, after: {}",
                        normalRedo.getOperation(),
                        normalRedo.getTransactionId(),
                        normalRedo.getTableName(),
                        normalRedo.getUndoRecord(),
                        normalRedo.getRedoRecord()
                );
            }
            return true;
        }



        boolean b = INSERT.toString().equals(op) || DELETE.toString().equals(op) || UPDATE.toString().equals(op);
        if (b) {
            if ((!columnNamesMap.containsKey(schema + "." + tableName)) && (!columnNamesMap.containsKey(tableName))) {
                return true;
            }
        }

        StringBuilder cacheKey = new StringBuilder();
        cacheKey.append(normalRedo.getTransactionId());
        cacheKey.append("|");
        cacheKey.append(normalRedo.getOperation());

        if (b) {
            if (normalRedo.getNameSpace() != null) {
                cacheKey.append(normalRedo.getNameSpace());
            }
            cacheKey.append("|");
            if (normalRedo.getTableName() != null) {
                cacheKey.append(normalRedo.getTableName());
            }
            cacheKey.append("|");
            Map<String, Object> undoRecord = normalRedo.getUndoRecord();
            Map<String, Object> sortedUndoRecord = new HashMap<>();
            if (undoRecord != null) {
                undoRecord.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .forEachOrdered(x->sortedUndoRecord.put(x.getKey(),x.getValue()));
            }
            if (sortedUndoRecord != null) {
                for (String key : sortedUndoRecord.keySet()) {
                    Object value = sortedUndoRecord.get(key);
                    if (value != null) {
                        cacheKey.append(value);
                    } else {
                        cacheKey.append("null");
                    }
                    cacheKey.append("|");
                }
            }
            Map<String, Object> redoRecord = normalRedo.getRedoRecord();
            Map<String, Object> sortedRedoRecord = new HashMap<>();
            if (redoRecord != null) {
                redoRecord.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .forEachOrdered(x->sortedRedoRecord.put(x.getKey(),x.getValue()));
            }
            if (sortedRedoRecord != null) {
                for (String key : sortedRedoRecord.keySet()) {
                    Object value = sortedRedoRecord.get(key);
                    if (value != null) {
                        cacheKey.append(value);
                    } else {
                        cacheKey.append("null");
                    }
                    cacheKey.append("|");
                }
            }
        }
        String hashKey = cacheKey.toString();
        String hash = String.format("%s-%d-%d", normalRedo.getTransactionId(), cacheKey.length(), hashKey.hashCode());

        if (!repeatFilter.containsKey(hash)) {
            repeatFilter.put(hash, true);
            return false;
        }

        if (debug) {
            tapLogger.info("filter hashKey is: {}, hash is: {}", hashKey, hash);
        }
        return true;
    }

    protected void initRedoLogQueueAndThread() {
        if (redoLogConsumerThreadPool == null) {
            redoLogConsumerThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
            redoLogConsumerThreadPool.submit(() -> {
                NormalRedo normalRedo;
                while (isRunning.get()) {
                    while (ddlStop.get()) {
                        TapSimplify.sleep(1000);
                    }
                    try {
                        normalRedo = logQueue.poll(1, TimeUnit.SECONDS);
                        if (normalRedo == null) {
                            continue;
                        }
                    } catch (Exception e) {
                        break;
                    }
                    try {
                        // TODO: 2023/12/2 心跳
//                        if ("COMMIT".equals(normalRedo.getOperation())) {
//                            consumer.accept(Collections.singletonList(new HeartbeatEvent().init().referenceTime(System.currentTimeMillis())), informixOffset);
//                        }
                        processOrBuffRedo(normalRedo, this::sendTransaction);

                    } catch (Throwable e) {
                        threadException.set(e);
                        consumer.streamReadEnded();
                    }
                }
            });
            redoLogConsumerThreadPool.submit(() -> {
                try {
                    while (isRunning.get()) {
                        Iterator<String> iterator = transactionBucket.keySet().iterator();
                        while (iterator.hasNext()) {
                            String transactionId = iterator.next();
                            NormalTransaction transaction = transactionBucket.get(transactionId);
                            try {
                                if (transaction == null || EmptyKit.isNull(transaction.getFirstTimestamp())) {
                                    tapLogger.warn("transactionId: {}, transaction: {} firstTimestamp is null, will skip", transactionId, transaction);
                                    continue;
                                }
                            } catch (Exception e) {
                                tapLogger.warn("transactionId: {}, transaction: {}, get first timestamp err: {}", transactionId, transaction, e.getMessage());
                                continue;
                            }
                            if (lastEventTimestamp - transaction.getFirstTimestamp() < 720 * 60 * 1000L) {
                                break;
                            } else {
                                transaction.clearRedoes();
                                iterator.remove();
                            }
                        }
                        int sleep = 3600;
                        try {
                            while (isRunning.get() && (sleep-- > 0)) {
                                TapSimplify.sleep(1000);
                            }
                        } catch (Exception ignore) {
                        }
                    }
                } catch (Exception e) {
                    threadException.set(e);
                }
            });
        }
    }

    private void rebuildConnection() throws Exception {
        try {
            statement.execute(CDC_RELEASE_CONNECTION);
        } catch (Exception ignore) {
        }
        try {
            statement.cancel();
            statement.close();
        } catch (Exception ignore) {
        }
        try {
            ((SybConnection)statement.getConnection()).cancel();
            statement.getConnection().close();
        } catch (Exception ignore) {
        }

        connection = sybaseContext.getConnection();
        statement = connection.createStatement();
    }

    private SybaseOffset rebuildStatementV2(Long startRid, Long rowId, SybaseOffset sybaseOffset) throws Exception {
        try {
            tapLogger.info("rebuild statement with {}, {}", startRid, rowId);
            rebuildConnection();
            statement.execute(CDC_UNIQUE_CONNECTION_LIMIT);
            statement.execute(CDC_QUERY_TIMEOUT);
            statement.execute(String.format(CDC_SET_QUAL, startRid, rowId));
            statement.execute(CDC_SCAN_NORMAL);
        } catch (Exception e) {
            if (e.getMessage().contains("higher number than the last RID")) {
                try {
                    tapLogger.info("higher: rebuild statement with {}, {}, higher number than the last RID, will rescan from: {}, 0", startRid, rowId, startRid + 1);
                    TapSimplify.sleep(5000);
                    rebuildConnection();
                    statement.execute(CDC_UNIQUE_CONNECTION_LIMIT);
                    statement.execute(CDC_QUERY_TIMEOUT);
                    statement.execute(String.format(CDC_SET_QUAL, startRid + 1, 0));
                    statement.execute(CDC_SCAN_NORMAL);
                    sybaseOffset.setStartRid(startRid+1);
                    sybaseOffset.setRowId(0);
                    return sybaseOffset;
                } catch (Exception e1) {
                    if (e1.getMessage().contains("target page is not a data page")) {
                        for (int i = 2; i < 1000; i++) {
                            try {
                                tapLogger.info("skip rebuild statement with {}, {}, target page is not a data page, will rescan from: {}, 0", startRid+1, 0, startRid + i);
                                rebuildConnection();
                                statement.execute(CDC_UNIQUE_CONNECTION_LIMIT);
                                statement.execute(CDC_QUERY_TIMEOUT);
                                statement.execute(String.format(CDC_SET_QUAL, startRid + i, 0));
                                statement.execute(CDC_SCAN_NORMAL);
                                sybaseOffset.setStartRid(startRid+i);
                                sybaseOffset.setRowId(0);
                            } catch (Exception e2) {
                                if (e2.getMessage().contains("target page is not a data page")) {
                                    TapSimplify.sleep(1000);
                                    continue;
                                } else {
                                    throw e2;
                                }
                            }
                        }
                    }
                }
            }
            if (e.getMessage().contains("target page is not a data page")) {
                for (int i = 1; i < 1000; i++) {
                    try {
                        tapLogger.info("skip rebuild statement with {}, {}, target page is not a data page, will rescan from: {}, 0", startRid, rowId, startRid + i);
                        rebuildConnection();
                        statement.execute(CDC_UNIQUE_CONNECTION_LIMIT);
                        statement.execute(CDC_QUERY_TIMEOUT);
                        statement.execute(String.format(CDC_SET_QUAL, startRid + i, 0));
                        statement.execute(CDC_SCAN_NORMAL);
                        sybaseOffset.setStartRid(startRid+i);
                        sybaseOffset.setRowId(0);
                    } catch (Exception e2) {
                        if (e2.getMessage().contains("target page is not a data page")) {
                            TapSimplify.sleep(1000);
                            continue;
                        } else {
                            throw e2;
                        }
                    }
                }
            }
        }
        return sybaseOffset;
    }

    @Override
    protected void ddlFlush() throws Throwable {

    }

    @Override
    protected void createEvent(NormalRedo normalRedo, AtomicReference<List<TapEvent>> eventList, long timestamp) {
        if (!String.valueOf(TRUNCATE).equals(normalRedo.getOperation()) && EmptyKit.isNull(Objects.requireNonNull(normalRedo).getRedoRecord()) ) {
            return;
        }
        TapBaseEvent recordEvent;
        switch (Objects.requireNonNull(normalRedo).getOperation()) {
            case "INSERT": {
                recordEvent = new TapInsertRecordEvent().init()
                        .table(normalRedo.getTableName())
                        .after(normalRedo.getRedoRecord());
                break;
            }
            case "UPDATE": {
                recordEvent = new TapUpdateRecordEvent().init()
                        .table(normalRedo.getTableName())
                        .after(normalRedo.getRedoRecord())
                        .before(normalRedo.getUndoRecord());
                break;
            }
            case "DELETE": {
                recordEvent = new TapDeleteRecordEvent().init()
                        .table(normalRedo.getTableName())
                        .before(normalRedo.getRedoRecord());
                break;
            }
            case "TRUNCATE":
                recordEvent = TapSimplify.clearTableEvent(normalRedo.getTableName());
                break;
            default:
                return;
        }
        recordEvent.setReferenceTime(timestamp);
        if (withSchema) {
            recordEvent.setNamespaces(Arrays.asList(normalRedo.getNameSpace(), normalRedo.getTableName()));
        }
        eventList.get().add(recordEvent);
    }

    @Override
    protected void submitEvent(NormalRedo normalRedo, List<TapEvent> list) {
        SybaseOffset offset = null;
        Iterator<NormalTransaction> iterator = transactionBucket.values().iterator();
        if (iterator.hasNext()) {
            offset = SybaseOffset.parse(iterator.next().getCdcSequenceStr());
        } else {
            if (normalRedo == null) {
                return;
            }
            offset = SybaseOffset.parse(normalRedo.getCdcSequenceStr());
        }
        if (!list.isEmpty()) {
            final String tableFullName = String.format("%s.%s", normalRedo.getNameSpace(), normalRedo.getTableName());
            cdcAccept.accept(tableFullName, list, offset);
        }
    }

    public static String getStackString(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (
                PrintWriter pw = new PrintWriter(sw)
        ) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }

    private void cleanStatement() {
        if (statement == null) {
            return;
        }

        try {
            statement.execute(CDC_RELEASE_CONNECTION);
        } catch (Exception ignore) {
        }

        try {
            statement.close();
        } catch (Exception ignore) {
        }

        try {
            statement.getConnection().close();
        } catch (Exception ignore) {
        }

        if (connection != null) {
            try {
                connection.close();
            } catch (Exception ignore) {
            }
        }
    }

    @Override
    public void stopMiner() throws Throwable {
        super.stopMiner();
        cleanStatement();
    }

    private boolean generateGlobalMap(ResultSet resultSet, String schema, String tableName) throws SQLException {
        if (!tapTableMap.containsKey(schema + "." + tableName) && !tapTableMap.containsKey(tableName)) {
            return false;
        }

        if (typeAndNameMap.containsKey(schema + "." + tableName)) {
            return true;
        }

        try {
            Set<String> dateTypeSet = ConnectorUtil.dateFields(withSchema ? tableMap.get(schema + "." + tableName) : tableMap.get(tableName));
            dateTypeSetMap.put(schema + "." + tableName, dateTypeSet);
            Map<String, String> typeAndNameFromMetaData = SybaseDataTypeConvert.getTypeAndName(resultSet);
            typeAndNameMap.put(schema + "." + tableName, typeAndNameFromMetaData);
        } catch (Exception e) {
            return false;
        }

        return true;
    }
}
