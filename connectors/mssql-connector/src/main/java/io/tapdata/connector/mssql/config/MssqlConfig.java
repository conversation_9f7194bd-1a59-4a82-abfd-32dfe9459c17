package io.tapdata.connector.mssql.config;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.common.util.FileUtil;
import io.tapdata.entity.error.CoreException;
import io.tapdata.entity.logger.Log;
import io.tapdata.kit.EmptyKit;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;

/**
 * Microsoft SQL Server database config.
 *
 * <AUTHOR>
 */
public class MssqlConfig extends CommonDbConfig implements Serializable {
    protected String hostNameInCertificate;

    protected boolean selfCert;
    protected List<Map<String, Object>> sslCertConfig;
    protected String partitionFunctionSuffix = "partition_function";
    protected String partitionSchemaSuffix = "partition_schema";

    private Boolean multiQueryCdc = false;
    private Integer multiQueryThreadSize = 5;

    Log log;

    public MssqlConfig() {
        setDbType("sqlserver");
        setJdbcDriver("com.microsoft.sqlserver.jdbc.SQLServerDriver");
    }

    @Override
    public MssqlConfig load(Map<String, Object> map) {
        assert beanUtils != null;
        beanUtils.mapToBean(map, this);
        Properties properties = new Properties();
        properties.put("database", getDatabase());
        properties.put("SelectMethod", "Cursor");
        setProperties(properties);
        if (EmptyKit.isBlank(timezone)) {
            timezone = "+00:00";
        }
        zoneId = TimeZone.getTimeZone("GMT" + timezone).toZoneId();
        zoneOffsetHour = TimeZone.getTimeZone("GMT" + timezone).getRawOffset() / 3600000;
        if (getUseSSL() && EmptyKit.isNotEmpty(map) && map.containsKey("useSSL")) {
            try {
                generateSSlFile();
            } catch (Exception e) {
                deleteSSlFile();
                throw new IllegalArgumentException("generate ssl file failed, msg: " + e.getMessage());
            }
        }
        return this;
    }

    @Override
    public String getDatabaseUrlPattern() {
        // last %s reserved for extend params
        return "jdbc:" + getDbType() + "://%s:%d;%s";
    }

    @Override
    public String getDatabaseUrl() {
        if (EmptyKit.isNull(this.getExtParams())) {
            this.setExtParams("");
        }
        if (EmptyKit.isNotEmpty(this.getExtParams()) && !this.getExtParams().startsWith("?") && !this.getExtParams().startsWith(":")) {
            this.setExtParams("?" + this.getExtParams());
        }
        return String.format(this.getDatabaseUrlPattern(), this.getHost(), this.getPort(), this.getExtParams());
    }

    /**
     * @document https://learn.microsoft.com/zh-cn/sql/connect/jdbc/connecting-with-ssl-encryption
     * @document https://www.cnblogs.com/zhuwenjoyce/p/12469793.html
     */
    @Override
    public void generateSSlFile() throws IOException, InterruptedException {
        //SSL开启需要设置trustServerCertificate=false, encrypt=true
        properties.put("encrypt", "true");
        properties.put("trustServerCertificate", "false");
        sslRandomPath = UUID.randomUUID().toString().replace("-", "");
        properties.put("trustStore", generateJKS());
        properties.put("trustStorePassword", getSslKeyPassword());
        String hostNameInCertificate = getHostNameInCertificate();
        if (null != hostNameInCertificate && !"".equals(hostNameInCertificate.trim())) {
            properties.put("hostNameInCertificate", getHostNameInCertificate());
        }
    }

    protected static final String CER_CMD = "%s -import -trustcacerts -noprompt -alias %s -file %s -keystore %s -storepass %s";

    protected String generateJKS() throws IOException, InterruptedException {
        String jksCachePath = FileUtil.paths(FileUtil.storeDir(".ssl"), sslRandomPath, "truststore.ks");
        File file = new File(FileUtil.paths(FileUtil.storeDir(".ssl"), sslRandomPath));
        if (!file.exists() || !file.isDirectory()) {
            if (!file.mkdirs()) {
                throw new IOException("Fail to make dirs: " + file.getAbsolutePath() + " which will be used cache ssl files when connect sql server");
            }
        }

        String pwd = getSslKeyPassword();
        String cerBase64Value = getSslCert();
        String sslCerPath = FileUtil.paths(FileUtil.storeDir(".ssl"), sslRandomPath, "mssql.cer");
        FileUtil.save(Base64.getDecoder().decode(cerBase64Value), sslCerPath, true);
        File sslCerFile = new File(sslCerPath);
        if (!sslCerFile.exists() || !sslCerFile.isFile()) {
            throw new CoreException("Can not save cer base64 chars as cer file, path: {}", sslCerFile.getAbsoluteFile());
        }
        String keyToolPath = System.getProperty("java.home");
        int of = keyToolPath.lastIndexOf("jre");
        keyToolPath = keyToolPath.substring(0, of > 0 ? of : keyToolPath.length());

        String cmd = String.format(CER_CMD,
                "".equals(keyToolPath) ? "keytool" : FileUtil.paths(keyToolPath, "bin", "keytool"),
                UUID.randomUUID().toString().replace("-", ""),
                sslCerPath,
                jksCachePath,
                pwd);
        try {
            Runtime.getRuntime().exec(cmd).waitFor();
        } catch (Exception e) {
            throw new CoreException("Generate jks file failed, msg: {}, command: {}", e.getMessage(), cmd);
        }
        return jksCachePath;
    }

    public String getHostNameInCertificate() {
        return hostNameInCertificate;
    }

    public void setHostNameInCertificate(String hostNameInCertificate) {
        this.hostNameInCertificate = hostNameInCertificate;
    }

    public MssqlConfig log(Log log) {
        this.log = log;
        return this;
    }

    public List<Map<String, Object>> getSslCertConfig() {
        return sslCertConfig;
    }

    public void setSslCertConfig(List<Map<String, Object>> sslCertConfig) {
        this.sslCertConfig = sslCertConfig;
    }

    public boolean isSelfCert() {
        return selfCert;
    }

    public void setSelfCert(boolean selfCert) {
        this.selfCert = selfCert;
    }

    public String getPartitionFunctionSuffix() {
        return partitionFunctionSuffix;
    }

    public void setPartitionFunctionSuffix(String partitionFunctionSuffix) {
        this.partitionFunctionSuffix = partitionFunctionSuffix;
    }

    public String getPartitionSchemaSuffix() {
        return partitionSchemaSuffix;
    }

    public void setPartitionSchemaSuffix(String partitionSchemaSuffix) {
        this.partitionSchemaSuffix = partitionSchemaSuffix;
    }

    public Boolean getMultiQueryCdc() {
        return multiQueryCdc;
    }

    public void setMultiQueryCdc(Boolean multiQueryCdc) {
        this.multiQueryCdc = multiQueryCdc;
    }

    public Integer getMultiQueryThreadSize() {
        return multiQueryThreadSize;
    }

    public void setMultiQueryThreadSize(Integer multiQueryThreadSize) {
        this.multiQueryThreadSize = multiQueryThreadSize;
    }
}
