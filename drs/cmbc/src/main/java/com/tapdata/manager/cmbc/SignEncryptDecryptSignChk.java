package com.tapdata.manager.cmbc;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import cfca.sm2.signature.SM2PrivateKey;
import cfca.sm2rsa.common.Mechanism;
import cfca.sm2rsa.common.PKIException;
import cfca.util.EnvelopeUtil;
import cfca.util.SignatureUtil2;
import cfca.util.cipher.lib.JCrypto;
import cfca.util.cipher.lib.Session;
import cfca.x509.certificate.X509Cert;
import cfca.x509.certificate.X509CertHelper;
import com.tapdata.manager.common.http.exception.ClientException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SignEncryptDecryptSignChk {
	private static String context = "{\"summary\":\"子账户备注信息\",\"partnerName\":\"测试子商户1\",\"merSerialNo\":\"90814230616170714556000650879043\",\"accName\":\"子商户联系人\",\"phoneId\":\"18661673206\",\"platformId\":\"03202305230955410001\",\"operType\":\"01\",\"email\":\"<EMAIL>\",\"merchantNo\":\"330006500000073\"}";
	private static Session session;
	
	static {
		try {
			JCrypto.getInstance().initialize(JCrypto.JSOFT_LIB, null);
			session = JCrypto.getInstance().openSession(JCrypto.JSOFT_LIB);
		} catch (PKIException e) {
			e.printStackTrace();
		}
	}
	
	public static void main(String[] args) throws ClientException {
		String sign = getSign(context);
		System.out.println("--------------------------------------");
		System.out.println("签名：");
		System.out.println(sign);

		String signContext = sign(sign, context);
		System.out.println("--------------------------------------");
		System.out.println("加密前：");
		System.out.println(signContext);

		String encryptContext = encrypt(signContext);
		System.out.println("--------------------------------------");
		System.out.println("加密后：");
		System.out.println(encryptContext);

		String dncryptContext = decrypt(encryptContext);
		System.out.println("--------------------------------------");
		System.out.println("解密后：");
		System.out.println(dncryptContext);

		String signChkResult = signCheck(dncryptContext);
		System.out.println("--------------------------------------");
		System.out.println("验证签名结果：");
		System.out.println(signChkResult);
	}

	public static <T extends BaseResponse> String buildRequestBody(BaseRequest<T> request, String transCode) {
		Gson gson = new GsonBuilder().create();
		String content = gson.toJson(request.getBodyParameters());
		String sign = SignEncryptDecryptSignChk.getSign(content);
		String body = sign(sign, content);
		String encryptBody = encrypt(body);
		Map<String, String> map = new HashMap<>();
		map.put("businessContext", encryptBody);
		//map.put("transCode", transCode);
		String sendContent = gson.toJson(map);
		log.debug("\n  Content: {}\n  Sign: {}\n  Before encrypt: {}\n  Encrypt: {}\n  Send content: {}", content, sign, body, encryptBody, sendContent);
		return sendContent;
	}

	public static String decryptAndCheckSign(String encryptContext) throws ClientException {
		String decryptContext = decrypt(encryptContext);
		return signCheck(decryptContext);
	}

	/**
	 * 签名
	 * 
	 * @param sign
	 * @param context
	 * @return
	 */
	public static String sign(String sign, String context) {
		GsonBuilder builder = new GsonBuilder();
		builder.disableHtmlEscaping();
		Gson gson = builder.create();
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("sign", sign);
		paramMap.put("body", context);
		String signInfo = gson.toJson(paramMap); // 待加密字符串
		return signInfo;
	}

	/**
	 * 加密
	 * 
	 * @param signContext
	 *            需要加密的报文
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public static String encrypt(String signContext) {
		X509Cert cert = null;
		try {
			cert = X509CertHelper.parse(Config.getBankPublicKey().getBytes(StandardCharsets.UTF_8));
		} catch (PKIException e) {
			e.printStackTrace();
		}
		X509Cert[] certs = { cert };
		byte[] encryptedData = null;
		try {
			encryptedData = EnvelopeUtil.envelopeMessage(signContext.getBytes("UTF8"), Mechanism.SM4_CBC, certs);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (PKIException e) {
			e.printStackTrace();
		}
		String encodeText = null;
		try {
			encodeText = new String(encryptedData, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return encodeText;
	}

	/**
	 * 解密
	 * 
	 * @param encryptContext
	 *            需要解密的报文
	 * @return
	 */
	public static String decrypt(String encryptContext) throws ClientException {
		String decodeText = null;
		try {
			byte[] merchantPrivateKey = Config.getMerchantPrivateKey().getBytes(StandardCharsets.UTF_8);
			PrivateKey priKey = Utils.getPrivateKeyFromSM2(merchantPrivateKey, Config.getMerchantPassword());
			X509Cert cert = Utils.getCertFromSM2(merchantPrivateKey);
			byte[] sourceData = EnvelopeUtil.openEvelopedMessage(encryptContext.getBytes("UTF8"), priKey, cert, session);
			decodeText = new String(sourceData, "UTF8");
		} catch (Exception e) {
			e.printStackTrace();
			throw new ClientException("DecryptFailed", "Decrypt failed");
		}
		return decodeText;
	}

	/**
	 * 验证签名
	 * 
	 * @param dncryptContext
	 *            需要验证签名的明文
	 * @return
	 */
	public static String signCheck(String dncryptContext) throws ClientException {
		Gson gson = new Gson();
		@SuppressWarnings("unchecked")
		Map<String, Object> paraMap = gson.fromJson(dncryptContext, Map.class);
		String sign = paraMap.get("sign").toString();
		String body = paraMap.get("body").toString();
		boolean isSignOK = false;
		try {
			X509Cert cert = X509CertHelper.parse(Config.getBankPublicKey().getBytes(StandardCharsets.UTF_8));
			PublicKey pubKey = cert.getPublicKey();
			isSignOK = new SignatureUtil2().p1VerifyMessage(Mechanism.SM3_SM2, body.getBytes("UTF8"),
					sign.getBytes(), pubKey, session);
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (isSignOK) {
			return body;
		} else {
			throw new ClientException("VerifySignFailed", "Verify response body sign failed");
		}
	}

	protected static String getSign(String context) {
		String sign = "";
		try {
			byte[] privateKeyData = Config.getMerchantPrivateKey().getBytes(StandardCharsets.UTF_8);
			JCrypto.getInstance().initialize(JCrypto.JSOFT_LIB, null);
			Session session = JCrypto.getInstance().openSession(JCrypto.JSOFT_LIB);
			SM2PrivateKey priKey = Utils.getPrivateKeyFromSM2(privateKeyData, Config.getMerchantPassword());
			sign = new String(
					new SignatureUtil2().p1SignMessage(Mechanism.SM3_SM2, context.getBytes("UTF8"), priKey, session));
		} catch (PKIException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return sign;
	}
}
