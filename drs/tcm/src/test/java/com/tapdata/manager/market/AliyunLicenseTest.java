package com.tapdata.manager.market;

import com.aliyun.market20151101.Client;
import com.aliyun.market20151101.models.*;
import com.aliyun.tea.TeaResponse;
import com.aliyun.tea.interceptor.InterceptorContext;
import com.aliyun.tea.interceptor.ResponseInterceptor;
import com.aliyun.tea.utils.AttributeMap;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.tapdata.manager.agent.dto.Spec;
import com.tapdata.manager.aliyun.market.service.AliCloudMarketService;
import com.tapdata.manager.common.utils.JsonUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;

public class AliyunLicenseTest {

    @Test
    public void parseSku() {
        Spec spec = AliCloudMarketService.parseDisplayNameAsSpec("4C8G");
        Assertions.assertNotNull(spec);
        Assertions.assertEquals(spec.getCpu(), 4);
        Assertions.assertEquals(spec.getMemory(), 8);
        spec = AliCloudMarketService.parseDisplayNameAsSpec("  4C8G  ");
        Assertions.assertNotNull(spec);
        Assertions.assertEquals(spec.getCpu(), 4);
        Assertions.assertEquals(spec.getMemory(), 8);

        spec = AliCloudMarketService.parseDisplayNameAsSpec(" 小规格 4C8G ");
        Assertions.assertNotNull(spec);
        Assertions.assertEquals(spec.getCpu(), 4);
        Assertions.assertEquals(spec.getMemory(), 8);


        spec = AliCloudMarketService.parseDisplayNameAsSpec(" 小规格 4c8G ");
        Assertions.assertNotNull(spec);
        Assertions.assertEquals(spec.getCpu(), 4);
        Assertions.assertEquals(spec.getMemory(), 8);


        spec = AliCloudMarketService.parseDisplayNameAsSpec(" 大规格 8c16G ");
        Assertions.assertNotNull(spec);
        Assertions.assertEquals(spec.getCpu(), 8);
        Assertions.assertEquals(spec.getMemory(), 16);


        spec = AliCloudMarketService.parseDisplayNameAsSpec(" 大规格 16c32G ");
        Assertions.assertNotNull(spec);
        Assertions.assertEquals(spec.getCpu(), 16);
        Assertions.assertEquals(spec.getMemory(), 32);
    }

    @Test
    public void describeProduct() throws Exception {
        DescribeProductRequest describeProductRequest = new DescribeProductRequest()
                .setCode("cmgj00061896");

        Client client = createClient();

        client.addResponseInterceptor(new ResponseInterceptor() {
            @Override
            public TeaResponse modifyResponse(InterceptorContext context, AttributeMap attributes) {

                if ("DescribeProduct".equalsIgnoreCase(context.teaRequest().headers.get("x-acs-action"))) {
                    TeaResponse response = context.teaResponse();
                    Object obj = com.aliyun.teautil.Common.readAsJSON(response.body);
                    if( obj instanceof Map) {
                        Map<String, Object> map = (Map<String, Object>) obj;
                        if (map.containsKey("ProductExtras")) {
                            map.remove("ProductExtras");
                            response.body = new ByteArrayInputStream(Common.toJSONString(map).getBytes(StandardCharsets.UTF_8));
                            context.setTeaResponse(response);
                        }
                    }
                }
                return context.teaResponse();
            }
        });
        RuntimeOptions runtime = new RuntimeOptions();

        DescribeProductResponse response = client.describeProductWithOptions(describeProductRequest, runtime);

        System.out.println(JsonUtil.toJsonUseJackson(response.getBody()));

        DescribeProductResponseBody body = response.getBody();
        Optional<String> displayName = body.getProductSkus().getProductSku().stream()
                .flatMap(s -> s.getModules().getModule().stream())
                .filter(s -> "package_version".equalsIgnoreCase(s.getCode()))
                .flatMap(s -> s.getProperties().getProperty().stream())
                .filter(m -> "package_version".equalsIgnoreCase(m.getKey()))
                .flatMap(m -> m.getPropertyValues().getPropertyValue().stream())
                .filter(m -> "yuncode5589600002".equalsIgnoreCase(m.getValue()))
                .map(DescribeProductResponseBody.DescribeProductResponseBodyProductSkusProductSkuModulesModulePropertiesPropertyPropertyValuesPropertyValue::getDisplayName).findFirst();
        System.out.println(displayName.orElse(null));
    }
    @Test
    public void describeLicense() throws Exception {
        DescribeLicenseRequest describeLicenseRequest = new
                DescribeLicenseRequest()
                .setLicenseCode("G5QE3ZKLR4-WBXJP2DCUNTWSP8MPNDJT8CFTKGTOJC0DKD8Z8SP3ICMQZQIQBFXF");
        RuntimeOptions runtime = new RuntimeOptions();

        Client client = createClient();

        DescribeLicenseResponse response = client.describeLicenseWithOptions(describeLicenseRequest, runtime);
        //JSONObject jsonObject = JSONObject.parseObject()
        System.out.println(JsonUtil.toJsonUseJackson(response.getBody().getLicense()));
    }

    private Client createClient() throws Exception {

        Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId("LTAI5tRs7Bi4t4ngvH94uX17").setAccessKeySecret("******************************");
        config.endpoint = "market.aliyuncs.com";
        return new Client(config);
    }

}
