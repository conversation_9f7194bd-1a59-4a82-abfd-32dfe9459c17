{"result": {"body": [{"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011537363073", "chaGroupName": "单向同步-小规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "4"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "2"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001313", "templateName": "单向同步-小规格"}], "measureDesc": "单向同步-小规格按周期收费-", "measureId": "9202059001", "measureName": "单向同步-小规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013130001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "单向同步-小规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "400", "tempId": 3100001313, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013130002", "paraKind": "", "paraName": "收费周期", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001313, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-小规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001313, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011537363073", "chaGroupName": "单向同步-小规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "4"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "2"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001344", "templateName": "单向同步-小规格"}], "measureDesc": "单向同步-小规格按量收费", "measureId": "9202059032", "measureName": "单向同步-小规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013440002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001344, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013440004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "单向同步-小规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "0.84", "tempId": 3100001344, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013440003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001344, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013440005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001344, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013440006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001344, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013440007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001344, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013440008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "单向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001344, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-小规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001344, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013440001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "单向同步-小规格", "paraType": "5", "paraUnit": "", "paraValue": "11000976", "tempId": 3100001344, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011538453074", "chaGroupName": "单向同步-标准规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "8"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "4"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001328", "templateName": "单向同步-标准规格"}], "measureDesc": "单向同步-标准规格按周期收费", "measureId": "9202059016", "measureName": "单向同步-标准规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013280001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "单向同步-标准规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "850", "tempId": 3100001328, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013280002", "paraKind": "", "paraName": "收费周期", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001328, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-标准规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001328, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011538453074", "chaGroupName": "单向同步-标准规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "8"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "4"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001343", "templateName": "单向同步-标准规格"}], "measureDesc": "单向同步-标准规格按量收费", "measureId": "9202059031", "measureName": "单向同步-标准规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013430002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001343, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013430004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "单向同步-标准规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "1.78", "tempId": 3100001343, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013430003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001343, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013430005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001343, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013430006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001343, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013430007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001343, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013430008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "单向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001343, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-标准规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001343, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013430001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "单向同步-标准规格", "paraType": "5", "paraUnit": "", "paraValue": "11000975", "tempId": 3100001343, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011538473076", "chaGroupName": "单向同步-大规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "32"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "16"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001327", "templateName": "单向同步-大规格按周期收费"}], "measureDesc": "单向同步-大规格按周期收费", "measureId": "9202059015", "measureName": "单向同步-大规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013270001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "单向同步-大规格按周期收费", "paraType": "1", "paraUnit": "元/月", "paraValue": "1780", "tempId": 3100001327, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013270002", "paraKind": "", "paraName": "收费周期", "paraRemark": "单向同步-大规格按周期收费", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001327, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-大规格按周期收费", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001327, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011538473076", "chaGroupName": "单向同步-大规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "32"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "16"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001342", "templateName": "单向同步-大规格"}], "measureDesc": "单向同步-大规格按量收费", "measureId": "9202059030", "measureName": "单向同步-大规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013420002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "单向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001342, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013420004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "单向同步-大规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "3.71", "tempId": 3100001342, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013420003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "单向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001342, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013420005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "单向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001342, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013420006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "单向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001342, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013420007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "单向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001342, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013420008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "单向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001342, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-大规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001342, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013420001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "单向同步-大规格", "paraType": "5", "paraUnit": "", "paraValue": "11000974", "tempId": 3100001342, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011638343094", "chaGroupName": "单向同步-中规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "16"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "8"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001326", "templateName": "单向同步-中规格"}], "measureDesc": "单向同步-中规格按周期收费", "measureId": "9202059014", "measureName": "单向同步-中规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013260001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "单向同步-中规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "1190", "tempId": 3100001326, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013260002", "paraKind": "", "paraName": "收费周期", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001326, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-中规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001326, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011638343094", "chaGroupName": "单向同步-中规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "16"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "单向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "8"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001341", "templateName": "单向同步-中规格"}], "measureDesc": "单向同步-中规格按量收费", "measureId": "9202059029", "measureName": "单向同步-中规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013410002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001341, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013410004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "单向同步-中规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "2.48", "tempId": 3100001341, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013410003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001341, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013410005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001341, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013410006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001341, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013410007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001341, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013410008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "单向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001341, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "单向同步-中规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001341, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013410001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "单向同步-中规格", "paraType": "5", "paraUnit": "", "paraValue": "11000973", "tempId": 3100001341, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011642583103", "chaGroupName": "双向同步-小规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "4"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "2"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001317", "templateName": "双向同步-小规格"}], "measureDesc": "双向同步-小规格按周期收费", "measureId": "9202059005", "measureName": "双向同步-小规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013170001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "双向同步-小规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "800", "tempId": 3100001317, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013170002", "paraKind": "", "paraName": "收费周期", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001317, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-小规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001317, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011642583103", "chaGroupName": "双向同步-小规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "4"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "2"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001332", "templateName": "双向同步-小规格"}], "measureDesc": "双向同步-小规格按量收费", "measureId": "9202059020", "measureName": "双向同步-小规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013320002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001332, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013320004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "双向同步-小规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "1.68", "tempId": 3100001332, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013320003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001332, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013320005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001332, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013320006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001332, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013320007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001332, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013320008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "双向同步-小规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001332, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-小规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001332, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013320001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "双向同步-小规格", "paraType": "5", "paraUnit": "", "paraValue": "11000964", "tempId": 3100001332, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011642593104", "chaGroupName": "双向同步-标准规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "8"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "4"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001316", "templateName": "双向同步-标准规格"}], "measureDesc": "双向同步-标准规格按周期收费", "measureId": "9202059004", "measureName": "双向同步-标准规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013160001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "双向同步-标准规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "1700", "tempId": 3100001316, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013160002", "paraKind": "", "paraName": "收费周期", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001316, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-标准规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001316, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011642593104", "chaGroupName": "双向同步-标准规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "8"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "4"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001331", "templateName": "双向同步-标准规格"}], "measureDesc": "双向同步-标准规格按量收费", "measureId": "9202059019", "measureName": "双向同步-标准规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013310002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001331, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013310004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "双向同步-标准规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "3.56", "tempId": 3100001331, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013310003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001331, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013310005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001331, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013310006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001331, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013310007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001331, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013310008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "双向同步-标准规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001331, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-标准规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001331, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013310001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "双向同步-标准规格", "paraType": "5", "paraUnit": "", "paraValue": "11000963", "tempId": 3100001331, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011643003105", "chaGroupName": "双向同步-大规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "32"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "16"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001315", "templateName": "双向同步-大规格"}], "measureDesc": "双向同步-大规格按周期收费", "measureId": "9202059003", "measureName": "双向同步-大规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013150001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "双向同步-大规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "3560", "tempId": 3100001315, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013150002", "paraKind": "", "paraName": "收费周期", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001315, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-大规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001315, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011643003105", "chaGroupName": "双向同步-大规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "32"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "16"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001330", "templateName": "双向同步-大规格"}], "measureDesc": "双向同步-大规格按量收费", "measureId": "9202059018", "measureName": "双向同步-大规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013300002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001330, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013300004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "双向同步-大规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "7.42", "tempId": 3100001330, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013300003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001330, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013300005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001330, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013300006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001330, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013300007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001330, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013300008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "双向同步-大规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001330, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-大规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001330, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013300001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "双向同步-大规格", "paraType": "5", "paraUnit": "", "paraValue": "11000962", "tempId": 3100001330, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011643023106", "chaGroupName": "双向同步-中规格", "chargeType": "2", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "16"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "8"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000028", "templateId": "3100001314", "templateName": "双向同步-中规格"}], "measureDesc": "双向同步-中规格按周期收费", "measureId": "9202059002", "measureName": "双向同步-中规格按周期收费", "measureType": "1", "measureUnit": "month", "paraList": [{"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "收费金额（元）", "paraId": "31000013140001", "paraKind": "F", "paraName": "收费金额（元）", "paraRemark": "双向同步-中规格", "paraType": "1", "paraUnit": "元/月", "paraValue": "2380", "tempId": 3100001314, "templateParaNumber": "90000101"}, {"defaultValue": "0", "paraDesc": "收费周期", "paraId": "31000013140002", "paraKind": "", "paraName": "收费周期", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001314, "templateParaNumber": "90000102"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-中规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001314, "templateParaNumber": "90000103"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}, {"ebossOfferChaList": [{"canEmpty": "0", "canModify": "1", "characterId": "0000000030", "characterName": "资源池", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000021", "characterName": "审批流程实例ID", "characterValue": ""}, {"canEmpty": "0", "canModify": "1", "characterId": "0000000001", "characterName": "基础产品订购实例ID", "characterValue": ""}, {"canEmpty": "1", "canModify": "1", "characterId": "0000000019", "characterName": "移动云子账号", "characterValue": ""}, {"canEmpty": "1", "canModify": "0", "characterId": "0000000022", "characterName": "移动云产品订购实例ID", "characterValue": ""}], "pays": [{"chaGroupId": "2102011643023106", "chaGroupName": "双向同步-中规格", "chargeType": "1", "configList": [{"configCode": "memory", "configId": "2000000077", "configName": "内存", "configValue": "16"}, {"configCode": "drsNodeCpu", "configId": "2000001012", "configName": "传输方式", "configValue": "双向同步（同区域）"}, {"configCode": "nodeKernelSpecification", "configId": "2000002188", "configName": "CPU核数", "configValue": "8"}, {"configCode": "productOrderNum", "configId": "2000002310", "configName": "数量 x ", "configValue": "1"}], "ladderList": [{"templateCode": "7000000030", "templateId": "3100001329", "templateName": "双向同步-中规格"}], "measureDesc": "双向同步-中规格按量收费", "measureId": "9202059017", "measureName": "双向同步-中规格按量收费", "measureType": "2", "measureUnit": "1", "paraList": [{"defaultValue": "0", "paraDesc": "按哪种类型进行计费", "paraId": "31000013290002", "paraKind": "C", "paraName": "计费量类型", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "2", "tempId": 3100001329, "templateParaNumber": "90000302"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "", "paraId": "31000013290004", "paraKind": "C", "paraName": "单价金额（元）", "paraRemark": "双向同步-中规格", "paraType": "1", "paraUnit": "元/小时", "paraValue": "4.96", "tempId": 3100001329, "templateParaNumber": "90000304"}, {"defaultValue": "0", "paraDesc": "话单中使用的使用量单位", "paraId": "31000013290003", "paraKind": "C", "paraName": "话单中使用量单位", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "11", "tempId": 3100001329, "templateParaNumber": "90000303"}, {"defaultValue": "0", "paraDesc": "实际的使用量单位", "paraId": "31000013290005", "paraKind": "C", "paraName": "单价计费单位", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "4", "tempId": 3100001329, "templateParaNumber": "90000305"}, {"defaultValue": "0", "paraDesc": "配合单价进行计费的单位的数量，如5元/10分钟，单位数量填10", "paraId": "31000013290006", "paraKind": "C", "paraName": "单价计费单位数量", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "1", "tempId": 3100001329, "templateParaNumber": "90000306"}, {"defaultValue": "0", "paraDesc": "使用量按哪种周期进行累计", "paraId": "31000013290007", "paraKind": "C", "paraName": "使用量累计周期", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001329, "templateParaNumber": "90000307"}, {"defaultValue": "0", "paraDesc": "对单价单位数量属性的取整", "paraId": "31000013290008", "paraKind": "C", "paraName": "取整方式", "paraRemark": "双向同步-中规格", "paraType": "", "paraUnit": "", "paraValue": "0", "tempId": 3100001329, "templateParaNumber": "90000308"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "折扣（%）", "paraId": "30032", "paraKind": "D", "paraName": "折扣（%）", "paraRemark": "双向同步-中规格", "paraType": "2", "paraUnit": "%", "paraValue": "100", "tempId": 3100001329, "templateParaNumber": "90000309"}, {"defaultValue": "0", "paraCode": "陆超超 import", "paraDesc": "移动云平台在新场景配置时按序列自动生成，与话单中填写的chargemode保持一致。话单中不填时配置空", "paraId": "31000013290001", "paraKind": "C", "paraName": "话单类型", "paraRemark": "双向同步-中规格", "paraType": "5", "paraUnit": "", "paraValue": "11000961", "tempId": 3100001329, "templateParaNumber": "90000301"}], "templateList": []}], "poolId": "CIDC-RP-33", "poolName": "CIDC-RP-33=华东-上海1", "productType": "eclouddrs", "serviceId": "45f68fcf61034d1d82f72b37251d4642", "serviceListId": "", "serviceName": "数据库复制DRS"}]}, "respDesc": "OK", "respCode": "0"}