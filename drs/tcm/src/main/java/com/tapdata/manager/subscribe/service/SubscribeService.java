package com.tapdata.manager.subscribe.service;

import com.mongodb.client.result.UpdateResult;
import com.stripe.exception.StripeException;
import com.stripe.model.Invoice;
import com.stripe.model.Subscription;
import com.stripe.model.SubscriptionItem;
import com.stripe.model.SubscriptionItemCollection;
import com.tapdata.manager.agent.AgentStatus;
import com.tapdata.manager.agent.AgentType;
import com.tapdata.manager.agent.dto.AgentDto;
import com.tapdata.manager.agent.dto.AggregateTrafficResult;
import com.tapdata.manager.agent.dto.Spec;
import com.tapdata.manager.agent.service.AgentNetworkTrafficService;
import com.tapdata.manager.agent.service.AgentService;
import com.tapdata.manager.base.dto.BaseDto;
import com.tapdata.manager.base.dto.Filter;
import com.tapdata.manager.base.dto.Page;
import com.tapdata.manager.base.dto.Where;
import com.tapdata.manager.base.exception.BizException;
import com.tapdata.manager.base.service.BaseService;
import com.tapdata.manager.billing.service.BillingService;
import com.tapdata.manager.capital.CapitalAccountFlowStatus;
import com.tapdata.manager.capital.dto.CapitalAccountDto;
import com.tapdata.manager.capital.dto.CapitalAccountFlowDto;
import com.tapdata.manager.capital.entity.CapitalAccountFlow;
import com.tapdata.manager.capital.service.CapitalAccountService;
import com.tapdata.manager.config.security.UserDetail;
import com.tapdata.manager.mdb.service.MDBClusterService;
import com.tapdata.manager.paid.entity.PaidCustomer;
import com.tapdata.manager.paid.entity.PaidRecord;
import com.tapdata.manager.paid.service.*;
import com.tapdata.manager.payment.PaymentMethod;
import com.tapdata.manager.order.dto.RenewSubscribeRequestV2DTO;
import com.tapdata.manager.order.dto.SubscribeResponse;
import com.tapdata.manager.order.dto.UnSubscribeRequestV2DTO;
import com.tapdata.manager.paid.constant.*;
import com.tapdata.manager.paid.dto.*;
import com.tapdata.manager.paid.entity.PaidPricePlan;
import com.tapdata.manager.paid.entity.PaidProductPlan;
import com.tapdata.manager.paid.repository.PaidPricePlanRepository;
import com.tapdata.manager.paid.repository.PaidProductPlanRepository;
import com.tapdata.manager.resource.dto.AggregateResourceUsageResult;
import com.tapdata.manager.resource.dto.ResourceUsageDto;
import com.tapdata.manager.resource.service.ResourceUsageService;
import com.tapdata.manager.subscribe.*;
import com.tapdata.manager.subscribe.dto.*;
import com.tapdata.manager.subscribe.entity.Subscribe;
import com.tapdata.manager.subscribe.entity.SubscribeAlert;
import com.tapdata.manager.subscribe.entity.SubscribeAlter;
import com.tapdata.manager.subscribe.repository.SubscribeAlertRepository;
import com.tapdata.manager.subscribe.repository.SubscribeAlterRepository;
import com.tapdata.manager.subscribe.repository.SubscribeRepository;
import com.tapdata.manager.tm.service.TMService;
import com.tapdata.manager.user.service.UserService;
import com.tapdata.tm.client.worker.QueryWorkerExpireResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.tapdata.manager.utils.MongoUtils.toObjectId;
import static java.util.Collections.emptyList;


@Slf4j
@Service
public class SubscribeService extends BaseService<SubscribeDto, Subscribe, ObjectId, SubscribeRepository> implements InitializingBean {

    @Autowired
    StripeService stripeService;

    @Autowired
    MDBClusterService mdbClusterService;

    @Autowired
    AgentService agentService;

    @Autowired
    PaidRecordService paidRecordService;
    @Autowired
    private TMService tmService;

    @Autowired
    PaidPricePlanRepository pricePlanRepository;

    @Autowired
    PaidProductPlanRepository paidProductPlanRepository;

    @Autowired
    private SubscribeAlterRepository subscribeAlterRepository;

    @Autowired
    private SubscribeAlterService subscribeAlterService;

    @Autowired
    private CapitalAccountService capitalAccountService;

    @Autowired
    private UserService userService;

    @Autowired
    private SubscribeAlertRepository subscribeAlertRepository;

    @Autowired
    private AgentNetworkTrafficService agentNetworkTrafficService;
    @Autowired
    private BillingService billingService;
    @Autowired
    private PaidCustomerService paidCustomerService;
    @Autowired
    private ResourceUsageService resourceUsageService;
    @Autowired
    private PaidFreeQuotaService paidFreeQuotaService;

    public SubscribeService(@NonNull SubscribeRepository repository) {
        super(repository, SubscribeDto.class, Subscribe.class);
    }

    @Override
    protected void beforeSave(SubscribeDto dto, UserDetail userDetail) {

    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    /**
     * 创建订阅
     * @param paidPaymentDto
     * @param userDetail
     * @return
     */
    public SubscribeResponse createSubscribe(PaidPaymentDto paidPaymentDto, UserDetail userDetail) {
        SubscribeResponse subscribeResponse = new SubscribeResponse();
        long sumAmount = paidPaymentDto.getSubscribeItems().stream()
                .filter(item -> !ProductType.NetworkTraffic.name().equalsIgnoreCase(item.getProductType()))
                .mapToLong(SubscribeItemReq::getAmount).sum();
        SubscribeDto subscribeDto = new SubscribeDto();
        BeanUtils.copyProperties(paidPaymentDto, subscribeDto);
        subscribeDto.setPeriodUnit(paidPaymentDto.getPeriodUnit());
        subscribeDto.setTotalAmount(sumAmount);
        if (sumAmount == 0) {
            Optional<SubscribeItemReq> fulManagementRes = paidPaymentDto.getSubscribeItems().stream().filter(i -> AgentType.Cloud.name().equals(i.getAgentType())).findFirst();
            if (fulManagementRes.isPresent()) {
                subscribeDto.setEndAt(System.currentTimeMillis() + 1296000000); // 15 天
            }
            subscribeDto.setStartAt(System.currentTimeMillis());
            subscribeDto.setStatus(SubscribeStatus.active.name());
            subscribeResponse.setStatus(SubscribeStatus.active.name());
        } else if (PaymentMethod.GCPMarketplace.name().equals(subscribeDto.getPaymentMethod())) {
            Long startAt = System.currentTimeMillis();
            Long endAt = endAt(subscribeDto.getPeriodUnit(), startAt, 1);
            if (subscribeDto.getStartAt() == null) subscribeDto.setStartAt(startAt);
            if (subscribeDto.getEndAt() == null) subscribeDto.setEndAt(endAt);
            subscribeDto.setStatus(SubscribeStatus.active.name());
            subscribeResponse.setStatus(SubscribeStatus.active.name());
        } else {
            subscribeDto.setStatus(SubscribeStatus.incomplete.name());
            subscribeResponse.setStatus(SubscribeStatus.incomplete.name());
        }

        if (subscribeDto.getStartAt() == null) {
            subscribeDto.setStartAt(System.currentTimeMillis());
        }
        if (subscribeDto.getEndAt() == null) {
            Long endAt = endAt(subscribeDto.getPeriodUnit(), subscribeDto.getStartAt(), 1);
            subscribeDto.setEndAt(endAt);
        }
        subscribeDto = save(subscribeDto, userDetail);
        String subscribeId = subscribeDto.getId().toHexString();
        subscribeResponse.setSubscribe(subscribeId);
        if (sumAmount != 0) {// 非免费实例走支付流程
            if (PaymentMethod.Stripe.name().equalsIgnoreCase(paidPaymentDto.getPaymentMethod())) {
                String payUrl = stripeService.getPaymentLink(paidPaymentDto, userDetail, subscribeId);
                subscribeResponse.setPayUrl(payUrl);
            } else if (PaymentMethod.GCPMarketplace.name().equals(paidPaymentDto.getPaymentMethod())) {
                deploymentResource(subscribeDto, userDetail);
            }
        } else { // 免费实例直接部署资源
            deploymentResource(subscribeDto, userDetail);
        }

        subscribeResponse.setSubscribeItems(subscribeDto.getSubscribeItems());
        subscribeResponse.setId(subscribeDto.getId());

        return subscribeResponse;
    }

    /**
     * 取消连续订阅
     * @param subscribeId
     * @param userDetail
     */
    public void pendingCancellationSubscribe(String subscribeId, UserDetail userDetail) {
        repository.update(Query.query(Criteria.where("_id").is(new ObjectId(subscribeId))),
                Update.update("cancelAtPeriodEnd", true), userDetail);
    }

    /**
     * 恢复取消连续订阅
     * @param subscribeId
     * @param userDetail
     */
    public void cancellationRevertedSubscribe(String subscribeId, UserDetail userDetail) {
        repository.update(Query.query(Criteria.where("_id").is(new ObjectId(subscribeId))),
                Update.update("cancelAtPeriodEnd", false), userDetail);
    }

    public static long endAt(String periodUnit, Long startAt, int period) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startAt);
        if ("year".equalsIgnoreCase(periodUnit)) {
            calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + period);
        } else if ("month".equalsIgnoreCase(periodUnit)) {
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + period);
        } else if ("day".equalsIgnoreCase(periodUnit)) {
            calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + period);
        }
        return calendar.getTimeInMillis();
    }

    /**
     * 订阅生效时，部署用户购买资源
     * @param subscribeDto
     * @param userDetail
     */
    public void deploymentResource(SubscribeDto subscribeDto, UserDetail userDetail) {
        log.info("Deployment for subscribe {}", subscribeDto.getId().toHexString());
        subscribeDto.getSubscribeItems().forEach(item -> {
            if (ProductType.Engine.name().equals(item.getProductType())) {
                // public agent can't deploy
                if (AgentType.Cloud.name().equals(item.getAgentType()) && item.getAmount() == 0L) {
                    String msg = tmService.createWorkerExpire(subscribeDto.getId().toHexString(), userDetail);
                    if (StringUtils.isNotBlank(msg)) {
                        throw new BizException(msg);
                    }
                    return;
                }
                agentService.deploymentAgent(item, subscribeDto, userDetail);
            } else if (ProductType.MongoDB.name().equals(item.getProductType())) {
                mdbClusterService.deploymentMdb(item, subscribeDto, userDetail);
            }
        });
    }

    /**
     * 用户续订订阅时，触发资源续期操作
     * @param subscribeDto
     * @param userDetail
     */
    public void renewResource(SubscribeDto subscribeDto, UserDetail userDetail) {
        log.info("Renew for subscribe {}", subscribeDto.getId().toHexString());
        subscribeDto.getSubscribeItems().forEach(item -> {
            if (ProductType.Engine.name().equals(item.getProductType())) {
                agentService.renewAgent(item, subscribeDto, userDetail);
            }
            if (ProductType.MongoDB.name().equals(item.getProductType())) {
                mdbClusterService.renewMdb(subscribeDto, userDetail);
            }
        });
    }

    public SubscribeDto findById(String subscribeId, UserDetail userDetail) {
        SubscribeDto paidSubscribe = findById(toObjectId(subscribeId), userDetail);
        if (paidSubscribe == null) {
            log.error("paidSubscribe is not exist subscribeId:{}", subscribeId);
            throw new BizException("NotFoundSubscribe", subscribeId);
        }
        return paidSubscribe;
    }

    /**
     * 续订订阅
     * @param requestDTO
     * @param userDetail
     * @return
     */
    public SubscribeResponse renewSubscribe(RenewSubscribeRequestV2DTO requestDTO, UserDetail userDetail) {

        SubscribeDto paidSubscribe = findById(requestDTO.getSubscribeId(), userDetail);

        if (!SubscribeType.one_time.name().equals(paidSubscribe.getSubscribeType())) {
            throw new BizException("RenewSubscribeFailed", "Only one-time subscriptions can be renewed");
        }

        PaidRecordDto paidRecordDto = paidRecordService.renewSubscription(paidSubscribe, userDetail);
        SubscribeResponse subscribeResponse = new SubscribeResponse();
        subscribeResponse.setStatus(SubscribeStatus.incomplete.name());
        String subscribeId = paidSubscribe.getId().toHexString();
        subscribeResponse.setSubscribe(subscribeId);
        subscribeResponse.setId(paidRecordDto.getId());
        return subscribeResponse;

        /*PaidPaymentDto paidPaymentDto = new PaidPaymentDto();
        BeanUtils.copyProperties(requestDTO, paidPaymentDto);

        SubscribeResponse subscribeResponse = new SubscribeResponse();
        subscribeResponse.setStatus(SubscribeStatus.incomplete.name());
        String subscribeId = paidSubscribe.getId().toHexString();
        subscribeResponse.setSubscribe(subscribeId);
        paidPaymentDto.setRenew(true);
        BeanUtils.copyProperties(paidSubscribe, paidPaymentDto);
        paidPaymentDto.getSubscribeItems().stream().forEach(item -> {
            String productType;
            if (ProductType.Engine.name().equals(item.getProductType())) {
                productType = "fullManagement";
                if (AgentType.Local.name().equals(item.getAgentType())) {
                    productType = "selfHost";
                }
            } else {
                productType = "mongoCluster";
            }
            PaidPricePlan price = pricePlanRepository.findOne(Query.query(Criteria.where("priceId").is(item.getPriceId()))).orElse(null);
            if (price == null) {
                log.error("Not found priceId {} SubscribeId:{}", item.getPriceId(), requestDTO.getSubscribeId());
                throw new BizException("NotFoundOriginalPrice", "Not found original price by id " + item.getPriceId());
            }

            String priceId = getActivePriceId(productType, item.getProvider(), item.getSpec(), item.getRegion(),
                    price.getType(), price.getPeriodUnit());
            if(StringUtils.isEmpty(priceId)){
                log.error("Not found priceId SubscribeId:{}",requestDTO.getSubscribeId());
                throw new BizException("NotFoundPrice",priceId);

            }
            item.setPriceId(priceId);
        });
        String payUrl = stripeService.getPaymentLink(paidPaymentDto, userDetail, subscribeId);
        subscribeResponse.setPayUrl(payUrl);
        return subscribeResponse;*/
    }


    public String getActivePriceId(String productType,String cloudProvider, Spec spec,String region,
                                   String type, String periodUnit) {
        // 查询产品
        Query query = Query.query(Criteria.where("productType").is(productType)
                .and("status").is(PaidProductStatus.Active.name()));
        if("mongoCluster".equals(productType)){
            query.addCriteria(Criteria.where("cloudProvider").is(cloudProvider));
        }
        List<PaidProductPlan> paidProductPlanDtoList = paidProductPlanRepository.findAll(query);
        for (PaidProductPlan paidProductPlan : paidProductPlanDtoList) {
            Query queryPrice = Query.query(Criteria.where("productId").is(paidProductPlan.getProductId())
                    .and("status").is(PaidProductStatus.Active.name()));
            if("mongoCluster".equals(productType) && StringUtils.isNotEmpty(region)){
                queryPrice.addCriteria(Criteria.where("region").is(region));
            }
            List<PaidPricePlan> paidPricePlans = pricePlanRepository.findAll(queryPrice);
            for (PaidPricePlan pricePlan : paidPricePlans) {
                if (pricePlan.getType().equals(type) && pricePlan.getPeriodUnit() != null
                        && pricePlan.getPeriodUnit().equals(periodUnit)
                        && pricePlan.getSpec().getCpu() == spec.getCpu()
                        && pricePlan.getSpec().getMemory() == spec.getMemory()
                        && pricePlan.getSpec().getStorageSize() == spec.getStorageSize()) {
                    return pricePlan.getPriceId();
                }
            }
        }
        return null;
    }



    /**
     * 取消订阅
     * @param requestDTO
     * @param userDetail
     */
    public void cancelSubscribe(UnSubscribeRequestV2DTO requestDTO, UserDetail userDetail){
        Validate.notNull(requestDTO, "Parameter requestDto cannot be null.");
        Validate.notNull(userDetail, "Parameter userDetail cannot be null.");
        SubscribeDto paidSubscribe = findById(requestDTO.getSubscribeId(), userDetail);

        SubscribeStatus subscribeStatus = SubscribeStatus.valueOf(paidSubscribe.getStatus());
        if (subscribeStatus != SubscribeStatus.active && subscribeStatus != SubscribeStatus.past_due) {
            throw new BizException("InvalidSubscribeStatus",
                    String.format("The current subscription status(%s) does not allow the operation", subscribeStatus));
        }

        List<SubscribeItemReq> unsubscribeItems = paidSubscribe.getSubscribeItems().stream().filter(item -> {
            if (SubscribeStatus.canceled.name().equals(item.getStatus())) {
                return false;
            }
            if (StringUtils.isEmpty(requestDTO.getResourceId()) ||
                    item.getResourceId().equals(requestDTO.getResourceId())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        for (SubscribeItem item : unsubscribeItems) {
            if (ProductType.MongoDB.name().equalsIgnoreCase(item.getProductType())) {
                mdbClusterService.canUnsubscribe(item.getResourceId());
            } else if (ProductType.Engine.name().equalsIgnoreCase(item.getProductType())) {
                agentService.canUnsubscribe(item.getResourceId());
            }
        }

        PaidPaymentDto paidPaymentDto = new PaidPaymentDto();
        BeanUtils.copyProperties(requestDTO, paidPaymentDto);

        if (!PaymentMethod.GCPMarketplace.name().equals(paidSubscribe.getPaymentMethod())) {
            paidRecordService.refunds(paidSubscribe, requestDTO.getResourceId(), userDetail);
        }
        destroyResource(paidSubscribe,paidPaymentDto,userDetail);

        Query query = Query.query(Criteria.where("_id").is(paidSubscribe.getId().toHexString()));
        Update update = new Update();
        if (StringUtils.isEmpty(requestDTO.getResourceId())) {
            CancellationDetail cancellationDetail = new CancellationDetail();
            cancellationDetail.setSubscribeId(paidPaymentDto.getSubscribeId());
            cancellationDetail.setRefundReason(paidPaymentDto.getRefundReason());
            cancellationDetail.setRefundDescribe(paidPaymentDto.getRefundDescribe());
            update.push("cancellationDetails", cancellationDetail);
        } else {
            CancellationDetail cancellationDetail = new CancellationDetail();
            cancellationDetail.setSubscribeId(paidPaymentDto.getSubscribeId());
            cancellationDetail.setResourceId(requestDTO.getResourceId());
            cancellationDetail.setRefundReason(paidPaymentDto.getRefundReason());
            cancellationDetail.setRefundDescribe(paidPaymentDto.getRefundDescribe());
            update.push("cancellationDetails", cancellationDetail);
        }

        unsubscribeItems.forEach(item -> {
            item.setStatus(SubscribeStatus.canceled.name());
            item.setCancelAt(System.currentTimeMillis());
        });
        update.set("subscribeItems", paidSubscribe.getSubscribeItems());

        long count = paidSubscribe.getSubscribeItems().stream()
                .filter(i -> !SubscribeStatus.canceled.name().equalsIgnoreCase(i.getStatus())).count();

        if (count == 0) {
            update.set("status", SubscribeStatus.canceled.name()).set("endAt", System.currentTimeMillis());
        }
        update(query,update,userDetail);
    }


    public void updateSubscribe(Query query, Update update, UserDetail userDetail){
        update(query,update,userDetail);
    }


    private void destroyResource(SubscribeDto paidSubscribe, PaidPaymentDto paidPaymentDto, UserDetail userDetail) {
        paidSubscribe.getSubscribeItems().forEach(item -> {
            if (SubscribeStatus.canceled.name().equals(item.getStatus())) {
                return;
            }
            if (StringUtils.isEmpty(paidPaymentDto.getResourceId()) ||
                    item.getResourceId().equals(paidPaymentDto.getResourceId())) {

                if (ProductType.Engine.name().equals(item.getProductType())) {
                    if (AgentType.Cloud.name().equals(item.getAgentType()) && paidSubscribe.getTotalAmount() == 0L) {
                        tmService.deleteWorkerExpire(userDetail);
                    } else {
                        agentService.releaseAgent(item.getResourceId(), userDetail, null);
                    }
                } else if (ProductType.MongoDB.name().equals(item.getProductType())) {
                    mdbClusterService.removeMdb(null, item.getResourceId(), userDetail);
                }
            }
        });
    }


    /**
     * 计算退款
     *
     * @param userDetail
     * @return
     */
    public Collection<RefundAmount> calculateRefundAmount(String subscribeId, String resourceId, UserDetail userDetail) {
        SubscribeDto paidSubscribe = findById(subscribeId, userDetail);

        if (paidSubscribe.getUsageType() == UsageType.metered)
            throw new BizException("RejectRefund.UsageType");

        Collection<RefundAmount> list = paidRecordService.calculateRefundPrice(paidSubscribe, resourceId, userDetail);
        list.forEach(item -> {
            if (item.getResource() == null && item.getResourceId() != null) {
                if (ProductType.Engine.name().equalsIgnoreCase(item.getProductType())) {
                    if (StringUtils.isBlank(item.getResourceId()) && item.getActualAmount() == 0L) {
                        AgentDto agentDto = new AgentDto();
                        agentDto.setName("Public Agent");
                        item.setResource(agentDto);
                    } else {
                        item.setResource(agentService.findById(toObjectId(item.getResourceId()), userDetail));
                    }
                } else if (ProductType.MongoDB.name().equalsIgnoreCase(item.getProductType())) {
                    item.setResource(
                            mdbClusterService.findMdbResourceByResourceId(toObjectId(item.getResourceId())));
                }
            }
        });
        return list;
    }


    /**
     * 查询订阅
     */
    public Page<SubscribeDto> findSubscribe(Filter filter, UserDetail userDetail) {
        List<SubscribeDto> subscribeDtoList = new ArrayList<>();
        long total = count(filter.getWhere(), userDetail);
        if (filter.getWhere() == null) {
            filter.setWhere(new Where());
        }
        if (!filter.getWhere().containsKey("status")) {
            filter.getWhere().put("status", new HashMap<String, Object>(){{
                put("$nin", Collections.singletonList("incomplete_expired"));
            }});
        }
        List<Subscribe> paidOrderList = repository.findAll(filter, userDetail);
        for (Subscribe subscribe : paidOrderList) {
            SubscribeDto paidOrderDto = new SubscribeDto();
            BeanUtils.copyProperties(subscribe, paidOrderDto);
            List<PaidRenewDetail> paidDetailList = new ArrayList<>();
            if (PaymentMethod.Stripe.name().equals(paidOrderDto.getPaymentMethod())) {
                Query query = Query.query(Criteria.where("subscribeId").is(subscribe.getId().toHexString())
                        .and("recordType").is(RecordType.pay.name()));
                query.with(Sort.by("createAt").descending());
                PaidRecordDto paidRecord = paidRecordService.findOne(query, userDetail);
                if (paidRecord != null && paidRecord.getStripePay() != null) {
                    if (StringUtils.isNotBlank(paidRecord.getStripePay().getPayUrl())) {
                        paidOrderDto.setPayUrl(paidRecord.getStripePay().getPayUrl());
                    } else {
                        paidOrderDto.setPayUrl(paidRecord.getPaymentUrl());
                    }
                    if (StringUtils.isNotBlank(paidRecord.getStripePay().getPaymentMethodId())) {
                        paidOrderDto.setPaymentMethodId(paidRecord.getStripePay().getPaymentMethodId());
                    }
                }
                paidOrderDto.setPaidRenewDetail(paidDetailList);
            }

            if (subscribe.getPlatform() == null) {
                paidOrderDto.setPlatform(Platform.integration.name());
            }

            if (paidOrderDto.getSubscribeItems() != null) {
                paidOrderDto.getSubscribeItems().forEach(item -> {
                    if (item.getResource() == null && item.getResourceId() != null) {
                        if (ProductType.Engine.name().equalsIgnoreCase(item.getProductType())) {
                            if (AgentType.Cloud.name().equals(item.getAgentType()) && item.getAmount() == 0L) {
                                QueryWorkerExpireResponse.WorkerExpireDto workerExpire = tmService.getWorkerExpire(userDetail);
                                AgentDto agentDto = new AgentDto();
                                if (workerExpire != null && workerExpire.is_deleted()) {
                                    agentDto.setStatus(SubscribeStatus.canceled.name());
                                } else if (workerExpire != null  && new Date().before(workerExpire.getExpireTime())) {
                                    agentDto.setStatus(AgentStatus.Running.name());
                                } else {
                                    agentDto.setStatus("");
                                }
                                item.setResource(agentDto);
                            } else {
                                item.setResource(
                                        agentService.convertToDto(
                                                agentService.findById(toObjectId(item.getResourceId())), AgentDto.class));
                            }
                        } else if (ProductType.MongoDB.name().equalsIgnoreCase(item.getProductType())) {
                            item.setResource(
                                    mdbClusterService.findMdbResourceByResourceId(toObjectId(item.getResourceId())));
                        }
                    }
                });
            }

            subscribeDtoList.add(paidOrderDto);
        }
        return new Page<>(total, subscribeDtoList);
    }

    /**
     * 定时检查过期订阅
     */
    public void checkExpiresSubscribe() {
        // 未支付过期
        Query query = Query.query(Criteria.where("status").is(SubscribeStatus.incomplete.name())
                .and("createAt").lte(new Date(System.currentTimeMillis() - 86400000)));
        repository.update(query, Update.update("status", SubscribeStatus.incomplete_expired.name()));

        // 临近7天到期，且没有发送过7天后到期通知的订阅需要发送通知
        // 临近1天到期，且没有发送过1天后到期通知的订阅需要发送通知
        // 到期时，且没有发送过到期通知的订阅需要发送通知
        long dayOf7 = 7 * 24 * 60 * 60 * 1000;
        long dayOf1 = 24 * 60 * 60 * 1000;
        long currentTime = System.currentTimeMillis();
        long after7Day = currentTime + dayOf7;
        long after1Day = currentTime + dayOf1;
        long timeWindow = 60 * 60 * 1000;
        query = Query.query(Criteria.where("status").in(SubscribeStatus.active.name(), SubscribeStatus.past_due.name()).orOperator(
                Criteria.where("endAt").gte(after7Day - timeWindow).lt(after7Day), // 1-hour window
                Criteria.where("endAt").gte(after1Day - timeWindow).lt(after1Day), // 1-hour window
                Criteria.where("endAt").gte(currentTime - timeWindow).lt(currentTime) // 1-hour window
        ));
        repository.findAll(query).forEach(subscribe -> {
            Long subscribeEndAt = subscribe.getEndAt();
            SubscribeAlertType subscribeAlertType = null;
            if (subscribeEndAt >= after1Day - timeWindow && subscribeEndAt < after1Day) {
                subscribeAlertType = SubscribeAlertType.NOTIFY_1_DAY;
            } else if (subscribeEndAt >= after7Day - timeWindow && subscribeEndAt < after7Day) {
                subscribeAlertType = SubscribeAlertType.NOTIFY_7_DAY;
            } else if (subscribeEndAt >= currentTime - timeWindow && subscribeEndAt < currentTime) {
                subscribeAlertType = SubscribeAlertType.NOTIFY_0_DAY;
            }
            if (subscribeAlertType != null) {
                this.sendImpendingNotify(subscribe, subscribeAlertType);
            }
        });

        // 已支付连续订阅过期，目前依赖Stripe 推送，对接了其他支付渠道时，需要调整实现

        // 已经过期的连续订阅，stripe 不推送事件，需要检查下是否有付款成功的新账单
        // 检查连续包周期订阅是否有新支付订单，如果有就自动续期
        query = Query.query(Criteria.where("status").in(SubscribeStatus.active.name(), SubscribeStatus.past_due.name())
                .and("subscribeType").in(SubscribeType.recurring.name())
                .and("endAt").lte(System.currentTimeMillis()));

        Function<Subscribe, Object> cancelSubscribeFn = (Subscribe subscribe) -> {
            try {
                UnSubscribeRequestV2DTO requestDto = new UnSubscribeRequestV2DTO();
                requestDto.setSubscribeId(subscribe.getId().toHexString());
                this.cancelSubscribe(requestDto, userService.loadUserById(subscribe));
            } catch (Exception e) {
                log.error("Cancel subscribe failed", e);
            }
            return null;
        };
        repository.findAll(query).forEach(subscribe -> {
            final Long subscribeEnd = subscribe.getEndAt();
            if (SubscribeType.recurring.name().equals(subscribe.getSubscribeType())) { // 连续订阅
                // 需要先检查 Stripe 订阅状态
                Subscription stripeSubscribe = stripeService.searchSubscriptionId(subscribe.getId());
                long currentPeriodEndAt = 0;
                String currentSubscribeStatus = null;
                if (stripeSubscribe != null) {
                    currentPeriodEndAt = stripeSubscribe.getCurrentPeriodEnd() * 1000;
                    currentSubscribeStatus = stripeSubscribe.getStatus();
                }

                if ("canceled".equals(currentSubscribeStatus)) { // stripe 订阅状态已经过期
                    cancelSubscribeFn.apply(subscribe);
                    return;
                }

                if (currentPeriodEndAt > subscribeEnd) { // Stripe 过期时间大于当前订阅到期时间，自动续期
                    subscribe.setEndAt(currentPeriodEndAt);
                    repository.update(
                            Query.query(Criteria.where("_id").is(subscribe.getId())),
                            Update.update("endAt", currentPeriodEndAt).set("status", SubscribeStatus.active.name()));
                    if (SubscribeStatus.past_due.name().equals(subscribe.getStatus())) {
                        subscribe.getSubscribeItems().forEach(item -> {
                            if (ProductType.Engine.name().equals(item.getProductType())) {
                                agentService.enableScheduleTask(item.getResourceId());
                            }
                        });
                    }
                } else if (subscribeEnd + 3 * 24 * 60 * 60 * 1000 < System.currentTimeMillis()) { // 超过3天未续期
                    // 自动取消订阅
                    cancelSubscribeFn.apply(subscribe);
                } else { // 过期3天内，更新状态
                    if (!SubscribeStatus.past_due.name().equals(subscribe.getStatus())) {
                        // 刚过期，还未自动续期的
                        repository.update(Query.query(Criteria.where("_id").is(subscribe.getId())),
                                Update.update("status", SubscribeStatus.past_due.name()));
                        subscribe.getSubscribeItems().forEach(item -> {
                            if (ProductType.Engine.name().equals(item.getProductType())) {
                                if (AgentType.Cloud.name().equals(item.getAgentType())) {
                                    UserDetail userDetail = userService.loadUserById(subscribe.getCreateBy());
                                    agentService.releaseAgent(item.getResourceId(), userDetail, null);
                                } else {
                                    agentService.disableScheduleTask(item.getResourceId());
                                }
                            }
                        });
                    }
                }
            } else if (SubscribeType.one_time.name().equals(subscribe.getSubscribeType())) {
                if (subscribeEnd + 3 * 24 * 60 * 60 * 1000 < System.currentTimeMillis()) { // 超过3天自动退订
                    cancelSubscribeFn.apply(subscribe);
                }
            }
        });

    }

    /**
     * 临期订阅发送通知
     *
     * @param subscribe
     * @param subscribeAlertType
     */
    private void sendImpendingNotify(Subscribe subscribe, SubscribeAlertType subscribeAlertType) {

        if (subscribeAlertType == null || subscribe == null ||
                !SubscribeStatus.active.name().equals(subscribe.getStatus())) {
            return; // discard not active subscribe
        }

        UserDetail userDetail = userService.loadUserById(subscribe);

        Query query = Query.query(Criteria.where("subscribeId").is(subscribe.getId())
                .and("endAt").is(subscribe.getEndAt())
                .and("alertType").is(subscribeAlertType)
        );

        Update update = Update.update("lastUpdAt", new Date())
                .setOnInsert("subscribeId", subscribe.getId())
                .setOnInsert("endAt", subscribe.getEndAt())
                .setOnInsert("alertType", subscribeAlertType.name())
                .setOnInsert("status", SubscribeAlertStatus.PENDING.name())
                .setOnInsert("createBy", subscribe.getCreateBy())
                .setOnInsert("createAt", new Date());

        UpdateResult result = subscribeAlertRepository.getMongoOperations().upsert(query, update, SubscribeAlert.class);
        SubscribeAlert subscribeAlert = null;
        if (result.getUpsertedId() == null) {
            // 已经发送过通知，需要检查发送状态是否需要重发
            subscribeAlert = subscribeAlertRepository.findOne(query).orElse(null);
        } else {
            subscribeAlert = subscribeAlertRepository.findById(result.getUpsertedId().asObjectId().getValue(), userDetail)
                    .orElse(null);
        }

        if (subscribeAlert == null || SubscribeAlertStatus.SEND_OUT.name().equals(subscribeAlert.getStatus())) {
            return;
        }

        SubscribeItemReq agent = subscribe.getSubscribeItems().stream()
                .filter(r -> ProductType.Engine.name().equals(r.getProductType())).findFirst().orElse(null);
        if (agent == null) {
            return;
        }
        AgentDto agentDto = agentService.findOne(Query.query(Criteria.where("_id").is(new ObjectId(agent.getResourceId()))), userDetail);
        tmService.createMessage(subscribe, subscribeAlertType, agentDto);
    }

    /**
     * 创建订阅变更:
     *     只有生效状态的订阅和实例才可以变更
     *     不支持变更部署方式：半托管、全托管
     *     不支持变更存储
     *     不支持变更订阅方式：连续订阅 -> 一次性订阅，一次性订阅 -> 连续订阅
     *     免费实例可以变更为付费实例，反之不可以
     *     付费实例可以变更价格（规格），使用 stripe 管理的订阅，费用体现在下个月
     *     一次性订阅需要立即完成支付/退款，支付金额按照新老价格的差额收取当期剩余时间的金额
     * @param requestDTO
     * @param userDetail
     */
    public SubscribeAlterDto changeSubscribe(ChangeSubscribeRequestDto requestDTO, UserDetail userDetail) {
        SubscribeDto paidSubscribe = findById(requestDTO.getSubscribeId(), userDetail);

        // 只有生效状态的订阅和实例才可以变更
        if (!SubscribeStatus.active.name().equals(paidSubscribe.getStatus())) {
            throw new BizException("ChangeSubscribeFailed", "Only active subscriptions can be changed");
        }
        // 免费实例升级为付费实例时，可以选择订阅方式：一次性订阅、连续订阅
        if (paidSubscribe.getTotalAmount() == 0) {
            if (StringUtils.isBlank(requestDTO.getSubscribeType())) {
                throw new BizException("ChangeSubscribeFailed", "Subscription method must be selected for free instance upgrades");
            }
            if (StringUtils.isBlank(requestDTO.getCurrency())) {
                throw new BizException("ChangeSubscribeFailed", "Payment currency must be not null for free instance upgrades");
            }

        } else if (StringUtils.isNotBlank(requestDTO.getSubscribeType()) &&
                !paidSubscribe.getSubscribeType().equals(requestDTO.getSubscribeType())) {
            throw new BizException("ChangeSubscribeFailed", "Not support change subscribe type.");
        }
        long alteringCount = subscribeAlterRepository.count(Query.query(
                Criteria.where("subscribeId").is(requestDTO.getSubscribeId()).and("status").is("altering")), userDetail);
        if (alteringCount > 0) {
            throw new BizException("ChangeSubscribeFailed", "Subscriptions is altering");
        }
        // 不能变更部署方式：半托管、全托管
        Map<String, SubscribeItemReq> originalSubscribeItemMap = paidSubscribe.getSubscribeItems()
                .stream().collect(Collectors.toMap(item -> {
                    if (ProductType.NetworkTraffic.name().equals(item.getProductType())) {
                        return ProductType.NetworkTraffic.name();
                    }
                    return item.getResourceId();
                }, i -> i));

        for (SubscribeItemReq item : requestDTO.getSubscribeItems()) {
            if (StringUtils.isNotBlank(item.getResourceId())) {
                SubscribeItemReq originalItem = originalSubscribeItemMap.get(item.getResourceId());
                if (originalItem == null ) {
                    throw new BizException("InvalidParameter", String.format("Not found subscribe item by %s", item.getResourceId()));
                }
                if (StringUtils.isBlank(item.getProductType()) ) {
                    throw new BizException("ChangeSubscribeFailed", "Product type cannot not be empty.");
                }
                if (ProductType.MongoDB.name().equals(item.getProductType()) ) {
                    throw new BizException("ChangeSubscribeFailed", "Change storage are not supported");
                }
                if (SubscribeStatus.canceled.name().equals(originalItem.getStatus())) {
                    throw new BizException("ChangeSubscribeFailed",
                            String.format("Product %s(%s) has been unsubscribed.", originalItem.getProductType(), item.getResourceId()));
                }
                if (StringUtils.isNotBlank(item.getAgentType()) && !originalItem.getAgentType().equals(item.getAgentType())) {
                    throw new BizException("ChangeSubscribeFailed",
                            String.format("Product %s(%s) cannot change the deployment method.", originalItem.getProductType(), item.getResourceId()));
                }

                if (AgentType.Cloud.name().equalsIgnoreCase(originalItem.getAgentType()) && originalItem.getAmount() == 0) {
                    // 不支持共享实例变更
                    throw new BizException("ChangeSubscribeFailed", "Shared instances do not support changes");
                }

                if (ProductType.Engine.name().equalsIgnoreCase(item.getProductType())) {
                    agentService.checkAgentBeforeAlter(item.getResourceId(), userDetail);
                }
            } else if (ProductType.NetworkTraffic.name().equals(item.getProductType())) {
                // not check network
            } else {
                // 暂时不支持新增订阅项目
                throw new BizException("InvaliParameter", "New subscription products are not supported");
            }
        }

        // 检查价格
        List<String> priceIds = Stream.concat(paidSubscribe.getSubscribeItems().stream(), requestDTO.getSubscribeItems().stream())
                .filter(item -> !SubscribeStatus.canceled.name().equals(item.getStatus())).map(SubscribeItem::getPriceId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, PaidPricePlan> priceMap = pricePlanRepository.findAll(Query.query(Criteria.where("priceId").in(priceIds)))
                .stream().collect(Collectors.toMap(PaidPricePlan::getPriceId, r -> r));

        List<SubscribeItemReq> newSubscribeItems = new ArrayList<>();

        for (SubscribeItemReq item : requestDTO.getSubscribeItems()) {
            if (StringUtils.isBlank(item.getPriceId())) {
                throw new BizException("InvalidParameter", "PriceId cannot be empty.");
            }
            String originalSubscribeItemMapKey = ProductType.NetworkTraffic.name().equals(item.getProductType()) ?
                    ProductType.NetworkTraffic.name() : item.getResourceId();
            SubscribeItemReq originalItem = originalSubscribeItemMap.get(originalSubscribeItemMapKey);
            if (item.getPriceId().equals(originalItem.getPriceId()) &&
                    !ProductType.NetworkTraffic.name().equals(originalSubscribeItemMapKey)) {
                throw new BizException("ChangeSubscribeFailed", "Price has not changed, refuse operation.");
            }

            PaidPricePlan newPrice = priceMap.get(item.getPriceId());
            if (newPrice == null) {
                throw new BizException("ChangeSubscribeFailed", "Not found price by id " + item.getPriceId());
            }
            List<CurrencyOption> currencyOptions = new ArrayList<>();
            if (newPrice.getCurrencyOption() != null) {
                currencyOptions.addAll(newPrice.getCurrencyOption());
            }
            currencyOptions.add(new CurrencyOption(){{
                setAmount(newPrice.getPrice());
                setCurrency(newPrice.getCurrency());
            }});
            String currencyStr = StringUtils.isNotBlank(requestDTO.getCurrency()) ? requestDTO.getCurrency() : paidSubscribe.getCurrency();
            String finalCurrencyStr = StringUtils.isNotBlank(requestDTO.getCurrency()) ? currencyStr : "cny";
            CurrencyOption currency = currencyOptions.stream().filter(c -> finalCurrencyStr.equalsIgnoreCase(c.getCurrency())).findFirst().orElse(null);
            if (currency == null) {
                throw  new BizException("ChangeSubscribeFailed", "Not found amount for currency " + paidSubscribe.getCurrency() + " in price " + item.getPriceId());
            }

            originalItem.setPriceId(newPrice.getPriceId());
            originalItem.setProductId(newPrice.getProductId());
            originalItem.setSpec(newPrice.getSpec());
            originalItem.setAmount(currency.getAmount().longValue());
            originalItem.setFreeNetworkTraffic(item.getFreeNetworkTraffic());
            newSubscribeItems.add(originalItem);
        }

        SubscribeAlter subscribeAlter = new SubscribeAlter();
        BeanUtils.copyProperties(requestDTO, subscribeAlter);
        subscribeAlter.setStatus(SubscribeAlterStatus.incomplete.name());
        subscribeAlter.setSubscribeItems(newSubscribeItems);
        subscribeAlter = subscribeAlterRepository.save(subscribeAlter, userDetail);

        SubscribeAlterDto subscribeAlterDto = new SubscribeAlterDto();
        BeanUtils.copyProperties(subscribeAlter, subscribeAlterDto);

        if (paidSubscribe.getUsageType() != null && UsageType.metered == paidSubscribe.getUsageType()) {
            // 按量计费的直接变更，不需要用户执行支付操作
            PaymentRequestDto paymentRequest = new PaymentRequestDto();
            paymentRequest.setPaymentMethod(PaymentMethod.Stripe.name());
            paymentRequest.setSubscribeAlterId(subscribeAlterDto.getId().toHexString());
            paymentRequest.setEmail(requestDTO.getEmail());
            paymentRequest.setSuccessUrl(requestDTO.getSuccessUrl());
            paymentRequest.setCancelUrl(requestDTO.getCancelUrl());
            paymentRequest.setSubscribeId(paidSubscribe.getId().toHexString());
            subscribeAlterService.paymentForAlterSubscription(paymentRequest, userDetail);
        }

        return subscribeAlterDto;
    }

    /**
     * 变更支付完成，触发实例变更
     * @param subscribeId
     * @param subscribeAlterId
     */
    public void alterPaymentCompleted(String subscribeId, String subscribeAlterId) {
        // 更新变更状态 为变更中
        Query query = Query.query(Criteria.where("_id").is(toObjectId(subscribeAlterId)));

        Subscribe subscribe = repository.findOne(Query.query(Criteria.where("_id").is(toObjectId(subscribeId)))).orElse(null);
        SubscribeAlter subscribeAlter = subscribeAlterRepository.findOne(query).orElse(null);
        if (subscribe == null || subscribeAlter == null) {
            log.error("Not found subscribe ({}) or subscribe alter record ({})", subscribeId, subscribeAlterId);
            return;
        }
        Map<String, SubscribeItemReq> alterResourceMap = subscribeAlter.getSubscribeItems().stream()
                .collect(Collectors.toMap(item -> {
                    if (ProductType.NetworkTraffic.name().equals(item.getProductType()))
                        return ProductType.NetworkTraffic.name();
                    else
                        return item.getResourceId();
                }, item -> item));
        List<SubscribeItemReq> beforeAlterResources = subscribe.getSubscribeItems().stream()
                .filter(item -> {
                    String mapKey = ProductType.NetworkTraffic.name().equals(item.getProductType()) ?
                            ProductType.NetworkTraffic.name() : item.getResourceId();
                    return alterResourceMap.containsKey(mapKey);
                }).collect(Collectors.toList());

        Update update = Update.update("status", SubscribeAlterStatus.altering.name())
                .set("subscribeItems.$[].status", SubscribeAlterStatus.altering.name())
                .set("beforeSubscribeItems", beforeAlterResources);
        if (subscribe.getTotalAmount() == 0) { // 免费实例
            String periodUnit = subscribeAlter.getPeriodUnit();
            long startAt = System.currentTimeMillis();
            long endAt = endAt(periodUnit, startAt, 1);
            update.set("startAt", startAt).set("endAt", endAt);
            repository.update(Query.query(Criteria.where("_id").is(subscribe.getId())),
                    Update.update("startAt", startAt).set("endAt", endAt));
        }

        subscribeAlterRepository.update(query, update);

        // 将这个订阅下面的其他未支付变更记录状态修改为过期
        subscribeAlterRepository.update(Query.query(Criteria
                .where("subscribeId").is(subscribeId)
                        .and("_id").ne(subscribeAlter.getId())
                        .and("status").is(SubscribeAlterStatus.incomplete.name())),
                Update.update("status", SubscribeAlterStatus.incomplete_expired.name()));

        AtomicLong totalAmount = new AtomicLong(0);
        subscribe.getSubscribeItems().forEach(item -> {
            String mapKey = ProductType.NetworkTraffic.name().equals(item.getProductType()) ?
                    ProductType.NetworkTraffic.name() : item.getResourceId();
            if (alterResourceMap.containsKey(mapKey)) {
                SubscribeItemReq alterItem = alterResourceMap.get(mapKey);
                item.setPriceId(alterItem.getPriceId());
                item.setAmount(alterItem.getAmount());
                item.setFreeNetworkTraffic(alterItem.getFreeNetworkTraffic());
                if (alterItem.getSpec() != null) {
                    item.setSpec(alterItem.getSpec());
                }
            }

            if (!SubscribeStatus.canceled.name().equals(item.getStatus())) {
                totalAmount.addAndGet(item.getAmount());
            }
        });

        repository.update(Query.query(Criteria.where("_id").is(subscribe.getId())),
                Update.update("subscribeItems", subscribe.getSubscribeItems())
                        .set("totalAmount", totalAmount.get())
                        // 修复免费实例的订阅类型
                        .set("subscribeType", SubscribeType.recurring.name().equals(subscribe.getSubscribeType()) ?
                                SubscribeType.recurring.name() : SubscribeType.one_time.name()));

        OptionalLong freeNetworkTraffic = subscribe.getSubscribeItems().stream().filter(r -> ProductType.NetworkTraffic.name().equals(r.getProductType()))
                .mapToLong(SubscribeItem::getFreeNetworkTraffic).findFirst();
        if (freeNetworkTraffic.isPresent()) {
            paidFreeQuotaService.alterFreeNetworkTraffic(subscribe.getCreateBy(), freeNetworkTraffic.getAsLong());
        }

        subscribeAlter.getSubscribeItems().forEach(item -> {
            if (ProductType.Engine.name().equalsIgnoreCase(item.getProductType())) {
                agentService.alterAgent(item.getResourceId(), item, subscribeAlter);
            } else {
                // 未实现其他产品的变更
            }
        });
        updateStripeSubscribeItemId(subscribe.getId());
    }

    /**
     * 资源变更完成后调用这个方法修改订阅状态
     * @param resourceId
     * @param userDetail
     */
    public void resourceAlterCompleted(String resourceId, UserDetail userDetail) {
        Query query = Query.query(Criteria.where("subscribeItems.resourceId").is(resourceId)
                .and("status").is(SubscribeAlterStatus.altering.name()));
        query.with(Sort.by(Sort.Order.desc("createAt")));
        SubscribeAlter subscribeAlter = subscribeAlterRepository.findOne(query, userDetail).orElse(null);

        if (subscribeAlter == null) {
            log.error("Not found altering record by resource id {}", resourceId);
            return;
        }

        // 并发可能性极低，直接取出更新后在放回
        AtomicBoolean allComplete = new AtomicBoolean(true);
        subscribeAlter.getSubscribeItems().forEach(item -> {
            if (resourceId.equals(item.getResourceId())) {
                item.setStatus(SubscribeAlterStatus.complete.name());
            }
            if (!SubscribeAlterStatus.complete.name().equals(item.getStatus())) {
                allComplete.set(false);
            }
        });

        Update update = Update.update("subscribeItems", subscribeAlter.getSubscribeItems());
        if (allComplete.get()) {
            update.set("status", SubscribeAlterStatus.complete.name());
        }
        UpdateResult result = subscribeAlterRepository.update(
                Query.query(Criteria.where("_id").is(subscribeAlter.getId())), update, userDetail);
    }

    /**
     * 获取支付链接
     * @return
     */
    public BaseDto getPayment(PaymentRequestDto paymentRequest, UserDetail userDetail) {

        if (StringUtils.isNotBlank(paymentRequest.getSubscribeAlterId())) {// 变更
            return subscribeAlterService.paymentForAlterSubscription(paymentRequest, userDetail);
        } else if (paymentRequest.getRenew() != null && paymentRequest.getRenew()) {// 续订
            return paymentForRenewSubscription(paymentRequest, userDetail);
        } else if (StringUtils.isNotBlank(paymentRequest.getSubscribeId())) { // 订购
            return paymentForSubscription(paymentRequest, userDetail);
        }
        throw new BizException("InvalidParams", "Invalid parameters for payment");
    }

    /**
     * 支付新订阅
     * @param paymentRequest
     * @param userDetail
     * @return
     */
    private BaseDto paymentForSubscription(PaymentRequestDto paymentRequest, UserDetail userDetail) {

        SubscribeDto subscribe = findById(paymentRequest.getSubscribeId(), userDetail);

        if (subscribe == null) {
            throw new BizException("NotFoundSubscription", "Not found subscription by id " + paymentRequest.getSubscribeId());
        }

        if (!SubscribeStatus.incomplete.name().equalsIgnoreCase(subscribe.getStatus())) {
            throw new BizException("NotAllowOperation", "Subscription status(" + subscribe.getStatus() + ") not allow this operation.");
        }

        Update update = Update.update("paymentMethod", paymentRequest.getPaymentMethod());
        if (StringUtils.isNotBlank(paymentRequest.getEmail())) {
            update.set("email", paymentRequest.getEmail());
            subscribe.setEmail(paymentRequest.getEmail());
        }
        subscribe.setPaymentMethod(paymentRequest.getPaymentMethod());
        updateById(toObjectId(paymentRequest.getSubscribeId()), update, userDetail);

        if (PaymentMethod.Stripe.name().equalsIgnoreCase(paymentRequest.getPaymentMethod())) {
            // Stripe 支付
            PaidPaymentDto paidPaymentDto = new PaidPaymentDto();
            BeanUtils.copyProperties(subscribe, paidPaymentDto);
            BeanUtils.copyProperties(paymentRequest, paidPaymentDto);

            String payUrl = stripeService.getPaymentLink(paidPaymentDto, userDetail, paymentRequest.getSubscribeId());
            //subscribeResponse.setPayUrl(payUrl);
            PaymentResponseDto paymentResponseDto = new PaymentResponseDto();
            paymentResponseDto.setPayUrl(payUrl);
            paymentResponseDto.setPaymentUrl(payUrl);
            return paymentResponseDto;
        } else if (PaymentMethod.Balance.name().equalsIgnoreCase(paymentRequest.getPaymentMethod())) {
            // 账户余额支付
            try {
                paymentByBalanceForSubscript(convertToEntity(Subscribe.class, subscribe), userDetail);
            } catch (BizException e) {
                if ("InsufficientBalance".equalsIgnoreCase(e.getErrorCode())) {
                    // 余额不足，通过定时任务检查，账户余额增加时，触发支付与部署
                    log.error("Insufficient balance", e);
                } else {
                    log.error("Payment for using balance failed", e);
                }
            }
        } else {
            throw new BizException("NotSupportPaymentMethod", "Not support payment method " + paymentRequest.getPaymentMethod());
        }

        return null;
    }

    /**
     * 支付续订
     * @param paymentRequest
     * @param userDetail
     * @return
     */
    private BaseDto paymentForRenewSubscription(PaymentRequestDto paymentRequest, UserDetail userDetail) {

        PaidRecordDto renewRecord = paidRecordService.findOne(Query.query(Criteria
                .where("subscribeId").is(paymentRequest.getSubscribeId())
                .and("recordType").is(RecordType.renew.name())), userDetail);

        Subscribe subscribe = repository.findById(toObjectId(paymentRequest.getSubscribeId()), userDetail).orElse(null);

        if (subscribe == null) {
            throw new BizException("NotFoundSubscription", "Not found subscription by id " + paymentRequest.getSubscribeId());
        }

        if (!SubscribeStatus.active.name().equalsIgnoreCase(subscribe.getStatus())) {
            throw new BizException("NotAllowOperation", "Subscription status(" + subscribe.getStatus() + ") not allow this operation.");
        }

        if (PaymentMethod.Stripe.name().equalsIgnoreCase(paymentRequest.getPaymentMethod())) {
            PaidPaymentDto paidPaymentDto = new PaidPaymentDto();
            BeanUtils.copyProperties(subscribe, paidPaymentDto);
            BeanUtils.copyProperties(paymentRequest, paidPaymentDto);

            SubscribeResponse subscribeResponse = new SubscribeResponse();
            subscribeResponse.setStatus(SubscribeStatus.incomplete.name());
            subscribeResponse.setSubscribe(paymentRequest.getSubscribeId());
            //subscribeResponse.setId(paidRecordDto.getId().toHexString());

            paidPaymentDto.setRenew(true);
            paidPaymentDto.getSubscribeItems().stream().forEach(item -> {
                String productType;
                if (ProductType.Engine.name().equals(item.getProductType())) {
                    productType = "fullManagement";
                    if (AgentType.Local.name().equals(item.getAgentType())) {
                        productType = "selfHost";
                    }
                } else {
                    productType = "mongoCluster";
                }
                PaidPricePlan price = pricePlanRepository.findOne(Query.query(Criteria.where("priceId").is(item.getPriceId()))).orElse(null);
                if (price == null) {
                    log.error("Not found priceId {} SubscribeId:{}", item.getPriceId(), paymentRequest.getSubscribeId());
                    throw new BizException("NotFoundOriginalPrice", "Not found original price by id " + item.getPriceId());
                }

                String priceId = getActivePriceId(productType, item.getProvider(), item.getSpec(), item.getRegion(),
                        price.getType(), price.getPeriodUnit());
                if(StringUtils.isEmpty(priceId)){
                    log.error("Not found priceId SubscribeId:{}", paymentRequest.getSubscribeId());
                    throw new BizException("NotFoundPrice",priceId);
                }
                item.setPriceId(priceId);
            });
            String payUrl = stripeService.getPaymentLink(paidPaymentDto, userDetail, paymentRequest.getSubscribeId());
            subscribeResponse.setPayUrl(payUrl);
            subscribeResponse.setPaymentUrl(payUrl);
            return subscribeResponse;
        } else if (PaymentMethod.Balance.name().equalsIgnoreCase(paymentRequest.getPaymentMethod())){
            try {
                paymentByBalanceForRenewSubscript(subscribe, renewRecord, userDetail);
            } catch (Exception e) {
                log.error("Try payment using balance for renew subscription {}", paymentRequest.getSubscribeId());
            }
        }


        return renewRecord;
    }

    /**
     * 检查有未支付订阅，并且账户余额充足，就自动完成付款
     */
    public void onRecharge(CapitalAccountFlow capitalAccountFlow, UserDetail userDetail) {

        log.info("Receive recharge {}, current balance {}, check subscription to payment",
                capitalAccountFlow.getAmount(),
                capitalAccountFlow.getAfterBalance());

        // 续订
        Query query = Query.query(Criteria
                .where("status").is(SubscribeStatus.incomplete.name())
                .and("recordType").is(RecordType.renew.name()));
        PaidRecordDto paidRecord = paidRecordService.findOne(query, userDetail);
        if (paidRecord != null) {
            Subscribe subscribeDto = repository.findById(toObjectId(paidRecord.getSubscribeId()), userDetail).orElse(null);
            if (subscribeDto != null) {
                if (SubscribeStatus.active.name().equalsIgnoreCase(subscribeDto.getStatus())) {
                    try {
                        paymentByBalanceForRenewSubscript(subscribeDto, paidRecord, userDetail);
                    } catch (BizException e) {
                        if ("InsufficientBalance".equalsIgnoreCase(e.getErrorCode())) {
                            log.warn("Payment using balance failed for renew subscribe {} is insufficient balance", subscribeDto.getId());
                        } else {
                            log.error("Payment using balance failed for renew subscribe {}", subscribeDto.getId());
                        }
                    } catch (Exception e) {
                        log.error("Payment using balance failed for renew subscribe {}", subscribeDto.getId());
                    }
                } else {
                    log.warn("Payment using balance failed for renew subscribe {}, subscription status is {}",
                            paidRecord.getSubscribeId(), subscribeDto.getStatus());
                }
            } else {
                log.warn("Payment using balance failed for renew subscribe {}, not found subscription", paidRecord.getSubscribeId());
            }
        }

        // 变更
        subscribeAlterService.onRecharge(capitalAccountFlow, userDetail);

        // 订阅
        query = Query.query(Criteria.where("status").is(SubscribeStatus.incomplete.name()));
        query = query.with(Sort.by(Sort.Order.desc("createAt")));
        List<Subscribe> subscriptions = repository.findAll(query, userDetail);
        if (subscriptions.size() > 0) {
            subscriptions.forEach(subscribe -> {
                if (subscribe.getTotalAmount() > 0) {
                    CapitalAccountDto capitalAccountDto = capitalAccountService.queryBalance(subscribe.getCurrency(), userDetail);
                    if (capitalAccountDto.getBalance() != null && capitalAccountDto.getBalance() >= subscribe.getTotalAmount()) {
                        try {
                            paymentByBalanceForSubscript(subscribe, userDetail);
                        } catch (Exception e) {
                            log.error("Payment using balance failed for subscribe {}", subscribe.getId().toHexString(), e);
                        }

                    }
                }
            });
        }

        // 账单
        billingService.paymentOnRecharge(capitalAccountFlow, userDetail);

    }

    /**
     * 使用账户余额支付新的订阅
     * @param subscribe
     * @param userDetail
     */
    private void paymentByBalanceForSubscript(Subscribe subscribe, UserDetail userDetail) {
        try {
            CapitalAccountFlowDto flowDto = capitalAccountService.payment(
                    subscribe.getTotalAmount(), subscribe.getCurrency(), subscribe.getId().toHexString(),
                    "Payment for subscription", userDetail);
            if (CapitalAccountFlowStatus.Credited.name().equals(flowDto.getStatus())) {
                // 支付成功，部署
                String periodUnit = subscribe.getPeriodUnit();
                long startAt = System.currentTimeMillis();
                long endAt = endAt(periodUnit, startAt, 1);
                updateById(subscribe.getId(), Update.update("status", SubscribeStatus.active.name())
                        .set("paymentMethod", PaymentMethod.Balance.name())
                        .set("startAt", startAt)
                        .set("endAt", endAt), userDetail);

                PaidRecordDto paidRecord = new PaidRecordDto();
                paidRecord.setRecordType(RecordType.pay.name());
                paidRecord.setItems(subscribe.getSubscribeItems());
                paidRecord.setQuantity(1);
                paidRecord.setAmount(subscribe.getTotalAmount());
                paidRecord.setActualAmount(subscribe.getTotalAmount());
                paidRecord.setStatus(SubscribeStatus.active.name());
                paidRecord.setPaymentMethod(PaymentMethod.Balance.name());
                paidRecord.setStartAt(startAt);
                paidRecord.setEndAt(endAt);
                paidRecord.setCurrency(subscribe.getCurrency());
                paidRecord.setSubscribeId(subscribe.getId().toHexString());
                paidRecordService.save(paidRecord, userDetail);

                deploymentResource(findById(subscribe.getId(), userDetail), userDetail);
            } else {
                // 支付失败
                throw new BizException("PaymentFailed", "Payment using balance failed(" + flowDto.getId().toHexString() + ")");
            }

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("PaymentFailed", e.getMessage(), e);
        }
    }

    /**
     * 使用账户余额支付续订
     * @param subscribe
     * @param userDetail
     */
    private void paymentByBalanceForRenewSubscript(Subscribe subscribe, PaidRecordDto paidRecord, UserDetail userDetail) {
        try {
            CapitalAccountFlowDto flowDto = capitalAccountService.payment(
                    subscribe.getTotalAmount(), subscribe.getCurrency(), subscribe.getId().toHexString(),
                    "Payment for subscription", userDetail);
            if (CapitalAccountFlowStatus.Credited.name().equals(flowDto.getStatus())) {
                // 支付成功，更新订阅有效期
                long startAt = subscribe.getEndAt();
                String periodUnit = subscribe.getPeriodUnit();
                long endAt = endAt(periodUnit, startAt, 1);
                updateById(subscribe.getId(), Update.update("endAt", endAt).set("status", SubscribeStatus.active.name()), userDetail);
                if (paidRecord != null) {
                    paidRecordService.update(Query.query(Criteria.where("_id").is(paidRecord.getId())),
                            Update.update("endAt", endAt).set("startAt", startAt).set("status", SubscribeStatus.active.name())
                                    .set("actualAmount", subscribe.getTotalAmount())
                                    .set("paymentMethod", PaymentMethod.Balance.name()), userDetail);
                }
                subscribe.setStatus(SubscribeStatus.active.name());
                subscribe.setEndAt(endAt);
                renewResource(findById(subscribe.getId().toHexString(), userDetail), userDetail);
            } else {
                // 支付失败
                throw new BizException("PaymentFailed", "Payment using balance failed(" + flowDto.getId().toHexString() + ")");
            }

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("PaymentFailed", e.getMessage(), e);
        }
    }

    public List<SubscribeDto> findAll(Query query) {
        return convertToDto(repository.findAll(query), SubscribeDto.class);
    }

    public Subscribe findSubscribeByResource(String resourceId, ProductType productType, UserDetail loginUser) {
        Validate.notNull(resourceId, "Illegal argument resourceId, cannot be null");
        Validate.notNull(productType, "Illegal argument productType, cannot be null");
        Validate.notNull(loginUser, "Illegal argument loginUser, cannot be null");

        Criteria criteria = Criteria.where("subscribeItems").elemMatch(
                Criteria.where("resourceId").is(resourceId).and("productType").is(productType)
        );

        Subscribe entity = repository.findOne(Query.query(criteria), loginUser).orElse(null);
        return entity;
    }

    public Subscribe findEntityById(ObjectId id, UserDetail userDetail) {
        Assert.notNull(id, "Id must not be null!");
        return repository.findById(id, userDetail).orElse(null);
    }

    public List<?> invoice(String subscribeId, UserDetail userDetail) {

        PaidRecord paidRecord = paidRecordService.findRecord(Query.query(Criteria.where("subscribeId").is(subscribeId).and("recordType").is(RecordType.pay)));
        if (paidRecord == null)
            return emptyList();
        if (paidRecord.getStripePay() != null && StringUtils.isNotEmpty(paidRecord.getStripePay().getSubscribeId()))
            return stripeService.getInvoice(paidRecord.getStripePay().getSubscribeId());
        if (paidRecord.getStripePay() != null && StringUtils.isNotEmpty(paidRecord.getStripePay().getCustomerId()))
            return stripeService.getChargeByCustomer(paidRecord.getStripePay().getCustomerId());
        Subscribe subscribe = repository.findOne(Query.query(Criteria.where("_id").is(toObjectId(subscribeId)))).orElse(null);
        if (subscribe == null) {
            return emptyList();
        }
        if (StringUtils.isNotEmpty(subscribe.getEmail())) {
            PaidCustomer customer = paidCustomerService.findStripeCustomerId(subscribe.getEmail(), userDetail);
            if (customer != null) {
                return stripeService.getChargeByCustomer(customer.getStripeCustomerId());
            }
        }
        return emptyList();
    }

    public List<Object> all_invoices(UserDetail userDetail) {
        List<PaidCustomer> customers = paidCustomerService.findAllCustomer(userDetail);
        List<Object> invoices = new ArrayList<>();
        
        for (PaidCustomer customer : customers) {
            invoices.addAll(stripeService.getInvoiceByCustomer(customer.getStripeCustomerId()));
        }
        
        return invoices;
    }

    public Page<ResourceUsageDto> findInvoiceDetail(String subscribeId, Filter filter, UserDetail loginUser) {

        Subscribe subscribe = repository.findById(toObjectId(subscribeId), loginUser).orElse(null);
        if (subscribe == null) {
            throw new BizException("NotFoundSubscribe", "Not found subscribe by id " + subscribeId);
        }

        if (filter == null)
            filter = new Filter();
        if (filter.getWhere() == null) {
            filter.setWhere(new Where());
        }
        filter.getWhere().and("subscribeId", toObjectId(subscribeId));
        Page<ResourceUsageDto> page = resourceUsageService.find(filter);
        page.getItems().forEach(r -> {
            AggregateTrafficResult trafficResult = agentNetworkTrafficService.getNetworkTraffic(r.getResourceId(), Instant.ofEpochMilli(r.getStart()), Instant.ofEpochMilli(r.getEnd()));
            r.setTransmit(trafficResult.getTotalTransmit());
            r.setReceived(trafficResult.getTotalReceived());
            r.setChargeableTraffic(trafficResult.getTotalChargeableTraffic());
            r.setTrafficCost(trafficResult.getTotalCost());
        });

        return page;
    }

    /**
     * 获取订阅的免费流量
     * @param subscribeId
     */
    public Long getFreeNetworkTrafficById(String subscribeId) {
        if (StringUtils.isBlank(subscribeId)) {
            return null;
        }
        Optional<Subscribe> sub = repository.findOne(Query.query(Criteria.where("_id").is(toObjectId(subscribeId))));
        return sub.flatMap(subscribe ->
                        subscribe.getSubscribeItems().stream()
                        .filter(r -> ProductType.NetworkTraffic.name().equals(r.getProductType()))
                        .map(SubscribeItem::getFreeNetworkTraffic)
                        .findFirst())
                .orElse(null);
    }

    /**
     * 订阅用量汇总
     * @param subscribeId
     * @param month
     * @param loginUser
     * @return
     */
    public InvoiceSummaryResult summary(String subscribeId, String month, UserDetail loginUser) {

        if (StringUtils.isBlank(month) || !Pattern.compile("^\\d{4}-\\d{2}$").matcher(month).matches()) {
            throw new BizException("IllegalArgument", "Month format error, it must be like 2025-01");
        }

        Subscribe subscribe = repository.findById(toObjectId(subscribeId), loginUser).orElse(null);
        if (subscribe == null) {
            throw new BizException("NotFoundSubscribe", subscribeId);
        }
        InvoiceSummaryResult.TotalSummary.TotalSummaryBuilder totalSummaryBuilder = InvoiceSummaryResult.TotalSummary.builder();

        PaidFreeQuotaDto freeQuota = paidFreeQuotaService.getFreeQuota(PaidFreeQuotaType.NetworkTraffic, month, loginUser);
        if (freeQuota != null)
            totalSummaryBuilder.freeNetworkTrafficAvailable(freeQuota.getAvailableAmount())
                    .totalFreeNetworkTraffic(freeQuota.getAmount());


        SubscribeItemReq engineItem = subscribe.getSubscribeItems().stream().filter(s -> ProductType.Engine.name().equals(s.getProductType())).findFirst().orElse(null);
        LocalDateTime localDateTime = LocalDateTime.parse(month + "-01T00:00:00", DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                .withDayOfMonth(1)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
        Instant start = localDateTime.toInstant(ZoneOffset.ofHours(8));
        Instant end = localDateTime.plusMonths(1).toInstant(ZoneOffset.ofHours(8));

        if (engineItem != null) {
            AggregateTrafficResult result = agentNetworkTrafficService.getNetworkTraffic(toObjectId(engineItem.getResourceId()), start, end);
            if (result != null) {
                totalSummaryBuilder.totalTransmit(result.getTotalTransmit());
                totalSummaryBuilder.totalReceived(result.getTotalReceived());
                totalSummaryBuilder.networkCost(result.getTotalCost());
            }
        }
        AggregateResourceUsageResult resourceUsageResult = resourceUsageService.getResourceUsage(subscribe.getId(), start, end);
        if (resourceUsageResult != null) {
            totalSummaryBuilder.engine(resourceUsageResult.getTotalUsage())
                    .engineCost(resourceUsageResult.getTotalCost());
        }

        return InvoiceSummaryResult.builder().month(month).subscribeId(subscribeId).total(totalSummaryBuilder.build()).build();
    }

    public void updateStripeSubscribeItemId(ObjectId subscribeId) {
        PaidRecord paidRecord = paidRecordService.findRecord(Query.query(Criteria.where("subscribeId").is(subscribeId.toHexString()).and("recordType").is(RecordType.pay)));
        if ( paidRecord == null || paidRecord.getStripePay() == null || StringUtils.isBlank(paidRecord.getStripePay().getSubscribeId()))
            return;

        Subscribe subscribe = repository.findOne(Query.query(Criteria.where("_id").is(subscribeId))).orElse(null);
        if (subscribe == null)
            return;

        try {
            Subscription subscription = Subscription.retrieve(paidRecord.getStripePay().getSubscribeId());
            if (subscription == null)
                return;

            SubscriptionItemCollection subscriptionItemCollection = subscription.getItems();
            List<SubscriptionItem> subscriptionItems = subscriptionItemCollection != null ?
                    subscriptionItemCollection.getData() :emptyList();
            subscribe.getSubscribeItems().forEach(subscribeItem -> {
                SubscriptionItem subscriptionItem = subscriptionItems.stream().filter(s -> subscribeItem.getPriceId().equals(s.getPrice().getId()))
                        .findFirst().orElse(null);
                if(subscriptionItem != null)
                    subscribeItem.setId(subscriptionItem.getId());
            });
        } catch (StripeException e) {
            log.error("Retrieve subscription info failed {}, {}", subscribeId, paidRecord.getStripePay().getSubscribeId(), e);
        }
    }
 
    public Object getUpcomingInvoice(String subscribeId, UserDetail userDetail) {
        PaidRecord paidRecord = paidRecordService.findRecord(Query.query(
                Criteria.where("subscribeId").is(subscribeId)
                .and("recordType").is(RecordType.pay)));

        if (paidRecord == null) {
            return null;
        }

        if (paidRecord.getStripePay() == null || StringUtils.isBlank(paidRecord.getStripePay().getSubscribeId())) {
            throw new BizException("InvalidSubscribe", "No subscription ID associated with this subscription");
        }

        return stripeService.getUpcomingInvoice(paidRecord.getStripePay().getSubscribeId());
    }
}
