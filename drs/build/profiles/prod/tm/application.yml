spring:
    profiles:
        active: '@profile.active@'
        include: '@env.PRODUCT@,default'

    data:
        mongodb:
            cursorBatchSize: 1000
            default:
                uri: 'mongodb://tmv3:<EMAIL>:3717,dds-2ze990d6029d16e42796.mongodb.rds.aliyuncs.com:3717/dfsTm3?replicaSet=mgset-49010163&authSource=admin'
            obs:
                uri: 'mongodb://tmv3:<EMAIL>:3717,dds-2zeb1b67dc017f242.mongodb.rds.aliyuncs.com:3717/dfsTm_obs?replicaSet=mgset-67490324&authSource=admin'
            log:
                uri: 'mongodb://tmv3:<EMAIL>:3717,dds-2zeb1b67dc017f242.mongodb.rds.aliyuncs.com:3717/dfsTm_log?replicaSet=mgset-67490324&authSource=admin'
    elasticsearch:
        rest:
            uris: http://172.19.137.193:9200,http://172.19.137.194:9200,http://172.19.137.195:9200

tcm:
    url: http://172.19.137.193:30103

logWareHouse: elasticsearch

task:
    log:
        expireDay: 7
        cron: "0 0 0 * * ?"

management:
    server:
        port: 34567
    endpoints:
        web:
            exposure:
                include: prometheus
    metrics:
        tags:
            application: tm-java

aliyun:
    accessKey: LTAI5tRs7Bi4t4ngvH94uX17
    accessSecret: ******************************

gateway:
    secret: Q3HraAbDkmKoPzaBEYzPXB1zJXmWlQ169

weChat:
    mp:
        appId: wx592f9ef879b35634
        appSecret: a73b89a5ddb2f61531c0dae8d6c0e94b
