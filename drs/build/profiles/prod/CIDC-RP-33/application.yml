spring:
    profiles:
        active: '@profile.active@'
        include: '@env.PRODUCT@'

    data:
        mongodb:
            uri: 'mongodb://root:Gotapd8!@10.188.121.51:27017,10.188.121.51:27017,10.188.121.53:27017/tapdata_tcm?replicaSet=rs&authSource=admin'
            cursorBatchSize: 1000

op:
    serverUri: ecloud.10086.cn
    # vpc使用的op环境
    vpcServerUri: 172.16.190.132:18080
    # 产品编号
    productType: eclouddrs
    # 文档数据库产品的系统名称，OP配置
    resourceType: CIDC-RT-DRS
    orderSource: CLOUD_DRS_SUBSYSTEM
    accessKey: a15bf65459964b56beb2300c43627ba6
    secretKey: 7fcbc99f89cb4068ac1ebdc2b16d94f9
    threshold: 90
    poolId: CIDC-RP-33
    capacity:
        type: capacityServiceMongoImpl
        #type: capacityServiceK8sImpl
    vpc:
        vpcPage: 1
        vpcPageSize: 100
        types: vm

mop:
    privateKey: MIIBhAIBADANBgkqhkiG9w0BAQEFAASCAW4wggFqAgEAAkwAso5yWN5C7ggRl3eAubs0ozMb+C+MncEcg9YDuWmYjHQpxT0AIyTGpwWEGkOLlZNcHfZ9sBvNBlYaN5BJa/ZXAgr3csXbOd16RLH9AgMBAAECSyekRHLCda2PEQjFuUiC7X7GHBGPzPJBK2mBnDYP4ZRdPeDSWDNvOPiuc3GGQcTlKW5CTt1WoPpCTWWu8tNB2uByCbfNEoFBQ9wFgQImD4G2syu7qg6G+0tBbUsMlh1O5uQq/1/NNInOYpPySeBbXE2BuQ0CJguDybq/r1QSshDUK647aDPUykXwwx7bJ2JL13tPBlRRCpC1csCxAiYDI22mtaM1t9r54TFMvXO+rg/aCzPom6schweOTAMtkIrtE4K9wQImCiF9Du0vGM0KFXxWoghqeY+yFy7ovPEcCYOopji03OIgZYj+2UECJR52UR8diTprzmHmUDWnKrXT5Niq8aK2FuOQpnX5vwZRpDooaRc=
    publicKey: MGcwDQYJKoZIhvcNAQEBBQADVgAwUwJMALKOcljeQu4IEZd3gLm7NKMzG/gvjJ3BHIPWA7lpmIx0KcU9ACMkxqcFhBpDi5WTXB32fbAbzQZWGjeQSWv2VwIK93LF2zndekSx/QIDAQAB
    appId: 600003
    # 0:沙箱环境（模拟报文返回） 1：正式环境  默认为1
    status: 1
    format: json
    mopUrl: http://ebop.mop.inner.cmecloud.org:8000/emop
    orderSource: paas
    productType: eclouddrs
    # mop mq
    consumer:
        consumerGroupName: tapdata_group_33
        namesrvAddr: mq1.mop.inner.cmecloud.org:9876;mq2.mop.inner.cmecloud.org:9876
        tags: 9202059
        instanceName: tapdata_inatance
    capacity:
        serverUri: ccops-paas.cmecloud.cn/o/resops
        userId: third_res_manager
        password: 2b78d9861e35be95d7aa59b94f0295df
        bizType: "数据库产品"
        typeOrder: 3010000
        type: "数据库复制"
    ftp:
        server: "***********"
        port: 22
        username: "paasftp"
        password: "7ygv&UJM"
        remotePath: "/home/<USER>/incoming/CSMP"

tm:
    domain: *************:30102
    appKey: 60125b2c24a4973ee4420646
    appSecret: 60125b2c24a4973ee44206471
    backendUrl: http://*************:30102/api/

vaso:
    domain: 10.254.2.129:19090 # 生产环境
    userName: VASO_drs_001     # 生产环境vaso账号
    password: VASO_drs_001     # 生产环境vaso密码
    serviceType: drs
    accessKey: 967122f3d0ca44209be82d7787a0c405
    secretKey: 8542a81d81254511a9fb3965b2f33641
