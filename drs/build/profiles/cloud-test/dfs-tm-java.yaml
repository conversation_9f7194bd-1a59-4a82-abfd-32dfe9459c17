apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: dfs-tm-java
    version: -version-
  name: dfs-tm-java
  namespace: cloud-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dfs-tm-java
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: dfs-tm-java
    spec:
      hostAliases:
#        - ip: *************
#          hostnames:
#            - "lb.kubesphere.local"
      imagePullSecrets:
        - name: qingcloud-registry-cred
      containers:
        - image: dockerhub.qingcloud.com/tapdata/dfs-tm-java:-version-
          name: dfs-tm-java
#          command: ["java"]
#          args:
#            - "-jar"
#            - "-server"
#            - "-Xms1g"
#            - "-Xmx8g"
#            - "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"
#            - "-XX:CompressedClassSpaceSize=512m"
#            - "-XX:MetaspaceSize=512m"
#            - "-XX:MaxMetaspaceSize=512m"
#            - "/opt/tm/lib/tm--version-.jar"
#            - "--spring.config.additional-location=file:./conf/"
#            - "--logging.config=file:./conf/logback.xml"
          resources:
#            limits:
#              cpu: "4"
#              memory: "8Gi"
#            requests:
#              cpu: "2"
#              memory: "4Gi"
          ports:
            - containerPort: 3000
              name: tomcat
              protocol: TCP
            - containerPort: 8246
              name: netty
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: tomcat
            initialDelaySeconds: 20
            periodSeconds: 20
          readinessProbe:
            httpGet:
              path: /health
              port: tomcat
            initialDelaySeconds: 10
            periodSeconds: 10
          volumeMounts:
            -   mountPath: /opt/tm/conf
                name: dfs-tm-java-app-config
#            -   mountPath: /opt/tm/logs
#                name: dfs-tm-java-app-logs
      volumes:
#        - name: dfs-tm-java-app-logs
#          hostPath:
#            path: /home/<USER>/logs
#            type: DirectoryOrCreate
        - name: dfs-tm-java-app-config
          configMap:
            name: dfs-tm-java-app-config
            items:
              - key: application.yml
                path: application.yml
              - key: logback.xml
                path: logback.xml
      #nodeSelector:
      #    labelName: node-role.kubernetes.io/master

status: {}

---
apiVersion: v1
kind: Service
metadata:
  name: dfs-tm-java-svc
  labels:
    app: dfs-tm-java-svc
  namespace: cloud-test
spec:
  type: NodePort
  ports:
    - port: 32105
      name: tomcat
      protocol: TCP
      targetPort: 3000
      nodePort: 32105
    - port: 32107
      name: netty
      protocol: TCP
      targetPort: 8246
      nodePort: 32107
#  sessionAffinity: ClientIP
#  sessionAffinityConfig:
#    clientIP:
#      timeoutSeconds: 86400
  selector:
    app: dfs-tm-java
