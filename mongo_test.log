2025-04-30 14:44:46 [pool-1-thread-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver", "version": "4.7.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4eb37baa]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-04-30 14:44:46 [pool-1-thread-1] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-04-30 14:44:46 [cluster-ClusterId{value='6811c6de19f3df3b48746799', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2051}] to localhost:27017
2025-04-30 14:44:46 [cluster-rtt-ClusterId{value='6811c6de19f3df3b48746799', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2052}] to localhost:27017
2025-04-30 14:44:46 [cluster-ClusterId{value='6811c6de19f3df3b48746799', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=39609042, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:44:45 CST 2025, lastUpdateTimeNanos=272451840330083}
2025-04-30 14:44:46 [pool-1-thread-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2053}] to localhost:27017
2025-04-30 14:45:46 [pool-1-thread-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver", "version": "4.7.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4eb37baa]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-04-30 14:45:46 [pool-1-thread-1] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-04-30 14:45:46 [cluster-rtt-ClusterId{value='6811c71a19f3df3b4874679a', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:2055}] to localhost:27017
2025-04-30 14:45:46 [cluster-ClusterId{value='6811c71a19f3df3b4874679a', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:2054}] to localhost:27017
2025-04-30 14:45:46 [cluster-ClusterId{value='6811c71a19f3df3b4874679a', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=33445000, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:45:45 CST 2025, lastUpdateTimeNanos=272511703871167}
2025-04-30 14:45:46 [pool-1-thread-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:2056}] to localhost:27017
2025-04-30 14:46:41 [pool-1-thread-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver", "version": "4.7.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4eb37baa]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-04-30 14:46:41 [pool-1-thread-1] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-04-30 14:46:41 [cluster-ClusterId{value='6811c7513aaf0972e0e6b96d', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2057}] to localhost:27017
2025-04-30 14:46:41 [cluster-rtt-ClusterId{value='6811c7513aaf0972e0e6b96d', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2058}] to localhost:27017
2025-04-30 14:46:41 [cluster-ClusterId{value='6811c7513aaf0972e0e6b96d', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=24493708, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:46:40 CST 2025, lastUpdateTimeNanos=272566229831417}
2025-04-30 14:46:41 [pool-1-thread-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2059}] to localhost:27017
2025-04-30 14:46:41 [pool-1-thread-1] INFO  org.example.MongoDBMonitor - admin
2025-04-30 14:47:41 [pool-1-thread-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver", "version": "4.7.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4eb37baa]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-04-30 14:47:41 [pool-1-thread-1] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-04-30 14:47:41 [cluster-ClusterId{value='6811c78c3aaf0972e0e6b96e', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:2060}] to localhost:27017
2025-04-30 14:47:41 [cluster-ClusterId{value='6811c78c3aaf0972e0e6b96e', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=5995166, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:47:40 CST 2025, lastUpdateTimeNanos=272626072459792}
2025-04-30 14:47:41 [cluster-rtt-ClusterId{value='6811c78c3aaf0972e0e6b96e', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:2061}] to localhost:27017
2025-04-30 14:47:41 [pool-1-thread-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:2062}] to localhost:27017
2025-04-30 14:47:41 [pool-1-thread-1] INFO  org.example.MongoDBMonitor - admin
2025-04-30 14:48:41 [pool-1-thread-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver", "version": "4.7.0"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4eb37baa]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
2025-04-30 14:48:41 [pool-1-thread-1] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-04-30 14:48:41 [cluster-ClusterId{value='6811c7c93aaf0972e0e6b96f', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:2064}] to localhost:27017
2025-04-30 14:48:41 [cluster-ClusterId{value='6811c7c93aaf0972e0e6b96f', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3010875, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:48:40 CST 2025, lastUpdateTimeNanos=272686081242958}
2025-04-30 14:48:41 [cluster-rtt-ClusterId{value='6811c7c93aaf0972e0e6b96f', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:2063}] to localhost:27017
2025-04-30 14:48:41 [pool-1-thread-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:2065}] to localhost:27017
2025-04-30 14:48:41 [pool-1-thread-1] INFO  org.example.MongoDBMonitor - admin
2025-04-30 14:56:56 [main] INFO  o.example.MongoDBMonitorApplication - Starting MongoDBMonitorApplication using Java 17.0.14 on ShihuangdeMacBook-Pro.local with PID 90313 (/Users/<USER>/IdeaProjects/tapdata_v3/spring_demo/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
 2025-04-30 14:56:56 [main] INFO  o.example.MongoDBMonitorApplication - No active profile set, falling back to 1 default profile: "default"
 2025-04-30 14:56:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
 2025-04-30 14:56:57 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 1 MongoDB repository interfaces.
 2025-04-30 14:56:57 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@4d48bd85]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
 2025-04-30 14:56:57 [cluster-ClusterId{value='6811c9b9cee415354c96449d', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2067}] to localhost:27017
 2025-04-30 14:56:57 [cluster-rtt-ClusterId{value='6811c9b9cee415354c96449d', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2066}] to localhost:27017
 2025-04-30 14:56:57 [cluster-ClusterId{value='6811c9b9cee415354c96449d', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=16458708, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:56:48 CST 2025, lastUpdateTimeNanos=273182446293042}
 2025-04-30 14:56:57 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 14:56:57 [main] INFO  o.example.MongoDBMonitorApplication - Started MongoDBMonitorApplication in 1.457 seconds (JVM running for 1.977)
 2025-04-30 14:56:57 [scheduling-1] ERROR o.e.service.MongoDBMonitorService - MongoDB is not available: Value expected to be of type DOCUMENT is of unexpected type STRING
 2025-04-30 14:56:57 [scheduling-1] ERROR o.e.service.MongoDBMonitorService - MongoDB connection failed: ServerStatus{id='null', available=false, timestamp=Wed Apr 30 14:56:57 CST 2025, message='Value expected to be of type DOCUMENT is of unexpected type STRING'}
 2025-04-30 14:59:22 [main] INFO  o.example.MongoDBMonitorApplication - Starting MongoDBMonitorApplication using Java 17.0.14 on ShihuangdeMacBook-Pro.local with PID 90379 (/Users/<USER>/IdeaProjects/tapdata_v3/spring_demo/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
 2025-04-30 14:59:22 [main] INFO  o.example.MongoDBMonitorApplication - No active profile set, falling back to 1 default profile: "default"
 2025-04-30 14:59:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
 2025-04-30 14:59:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 1 MongoDB repository interfaces.
 2025-04-30 14:59:22 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@421a4ee1]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
 2025-04-30 14:59:22 [cluster-ClusterId{value='6811ca4a723c5b22e168dae0', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2069}] to localhost:27017
 2025-04-30 14:59:22 [cluster-rtt-ClusterId{value='6811ca4a723c5b22e168dae0', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2068}] to localhost:27017
 2025-04-30 14:59:22 [cluster-ClusterId{value='6811ca4a723c5b22e168dae0', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=16653416, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 14:59:19 CST 2025, lastUpdateTimeNanos=273327886521750}
 2025-04-30 14:59:23 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 14:59:23 [main] INFO  o.example.MongoDBMonitorApplication - Started MongoDBMonitorApplication in 1.484 seconds (JVM running for 2.08)
 2025-04-30 14:59:23 [scheduling-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2070}] to localhost:27017
 2025-04-30 14:59:23 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - MongoDB is available: ServerStatus{id='6811ca4b723c5b22e168dae1', available=true, timestamp=Wed Apr 30 14:59:23 CST 2025, message='MongoDB is available'}
 2025-04-30 15:01:02 [main] INFO  o.example.MongoDBMonitorApplication - Starting MongoDBMonitorApplication using Java 17.0.14 on ShihuangdeMacBook-Pro.local with PID 90417 (/Users/<USER>/IdeaProjects/tapdata_v3/spring_demo/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
 2025-04-30 15:01:02 [main] INFO  o.example.MongoDBMonitorApplication - No active profile set, falling back to 1 default profile: "default"
 2025-04-30 15:01:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
 2025-04-30 15:01:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 MongoDB repository interfaces.
 2025-04-30 15:01:03 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@3104351d]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
 2025-04-30 15:01:03 [cluster-ClusterId{value='6811caafdeafb40410f72e9d', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2071}] to localhost:27017
 2025-04-30 15:01:03 [cluster-rtt-ClusterId{value='6811caafdeafb40410f72e9d', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2072}] to localhost:27017
 2025-04-30 15:01:03 [cluster-ClusterId{value='6811caafdeafb40410f72e9d', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=17897209, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 15:01:00 CST 2025, lastUpdateTimeNanos=273428569647292}
 2025-04-30 15:01:03 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 15:01:03 [main] INFO  o.example.MongoDBMonitorApplication - Started MongoDBMonitorApplication in 1.412 seconds (JVM running for 1.911)
 2025-04-30 15:01:03 [scheduling-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2073}] to localhost:27017
 2025-04-30 15:01:03 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - MongoDB is available: ServerStatus{id='null', available=true, timestamp=Wed Apr 30 15:01:03 CST 2025, message='MongoDB is available'}
 2025-04-30 15:01:39 [main] INFO  o.example.MongoDBMonitorApplication - Starting MongoDBMonitorApplication using Java 17.0.14 on ShihuangdeMacBook-Pro.local with PID 90429 (/Users/<USER>/IdeaProjects/tapdata_v3/spring_demo/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
 2025-04-30 15:01:39 [main] INFO  o.example.MongoDBMonitorApplication - No active profile set, falling back to 1 default profile: "default"
 2025-04-30 15:01:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
 2025-04-30 15:01:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 MongoDB repository interfaces.
 2025-04-30 15:01:40 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@17814b1c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
 2025-04-30 15:01:40 [cluster-rtt-ClusterId{value='6811cad4042d9700afa76801', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2075}] to localhost:27017
 2025-04-30 15:01:40 [cluster-ClusterId{value='6811cad4042d9700afa76801', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2074}] to localhost:27017
 2025-04-30 15:01:40 [cluster-ClusterId{value='6811cad4042d9700afa76801', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=16837917, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 15:01:30 CST 2025, lastUpdateTimeNanos=273465340463417}
 2025-04-30 15:01:40 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 15:01:40 [main] INFO  o.example.MongoDBMonitorApplication - Started MongoDBMonitorApplication in 1.387 seconds (JVM running for 1.869)
 2025-04-30 15:01:40 [scheduling-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2076}] to localhost:27017
 2025-04-30 15:01:40 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - MongoDB is available: Document{{host=ShihuangdeMacBook-Pro.local, version=6.0.18, process=mongod, pid=542, uptime=669136.0, uptimeMillis=669136319, uptimeEstimate=669136, localTime=Wed Apr 30 15:01:40 CST 2025, activeIndexBuilds=Document{{total=0, phases=Document{{scanCollection=0, drainSideWritesTable=0, drainSideWritesTablePreCommit=0, waitForCommitQuorum=0, drainSideWritesTableOnCommit=0, processConstraintsViolatonTableOnCommit=0, commit=0}}}}, asserts=Document{{regular=0, warning=0, msg=0, user=20446, tripwire=0, rollovers=0}}, batchedDeletes=Document{{batches=0, docs=0, stagedSizeBytes=0, timeMillis=0}}, catalogStats=Document{{collections=5575, capped=0, clustered=0, timeseries=0, views=0, internalCollections=20, internalViews=1}}, connections=Document{{current=34, available=51166, totalCreated=1766, active=23, threaded=34, exhaustIsMaster=0, exhaustHello=21, awaitingTopologyChanges=22}}, defaultRWConcern=Document{{defaultReadConcern=Document{{level=local}}, defaultWriteConcern=Document{{w=majority, wtimeout=0}}, defaultWriteConcernSource=implicit, defaultReadConcernSource=implicit, localUpdateWallClockTime=Tue Apr 22 21:09:48 CST 2025}}, electionMetrics=Document{{stepUpCmd=Document{{called=0, successful=0}}, priorityTakeover=Document{{called=0, successful=0}}, catchUpTakeover=Document{{called=0, successful=0}}, electionTimeout=Document{{called=1, successful=1}}, freezeTimeout=Document{{called=0, successful=0}}, numStepDownsCausedByHigherTerm=0, numCatchUps=0, numCatchUpsSucceeded=0, numCatchUpsAlreadyCaughtUp=0, numCatchUpsSkipped=1, numCatchUpsTimedOut=0, numCatchUpsFailedWithError=0, numCatchUpsFailedWithNewTerm=0, numCatchUpsFailedWithReplSetAbortPrimaryCatchUpCmd=0, averageCatchUpOps=0.0}}, extra_info=Document{{note=fields vary by platform, page_faults=4992}}, featureCompatibilityVersion=Document{{major=6, minor=0, transitioning=0}}, flowControl=Document{{enabled=true, targetRateLimit=1000000000, timeAcquiringMicros=809946, locksPerKiloOp=7175.824175824176, sustainerRate=0, isLagged=false, isLaggedCount=2, isLaggedTimeMicros=2143217}}, globalLock=Document{{totalTime=669137951000, currentQueue=Document{{total=0, readers=0, writers=0}}, activeClients=Document{{total=0, readers=0, writers=0}}}}, indexBulkBuilder=Document{{count=9, resumed=0, filesOpenedForExternalSort=0, filesClosedForExternalSort=0, spilledRanges=0, bytesSpilledUncompressed=0, bytesSpilled=0}}, indexStats=Document{{count=20374, features=Document{{2d=Document{{count=0, accesses=0}}, 2dsphere=Document{{count=0, accesses=0}}, 2dsphere_bucket=Document{{count=0, accesses=0}}, collation=Document{{count=0, accesses=0}}, compound=Document{{count=14006, accesses=636205}}, hashed=Document{{count=0, accesses=0}}, id=Document{{count=5575, accesses=831162}}, normal=Document{{count=14788, accesses=678659}}, partial=Document{{count=42, accesses=0}}, single=Document{{count=793, accesses=42454}}, sparse=Document{{count=92, accesses=158520}}, text=Document{{count=11, accesses=0}}, ttl=Document{{count=211, accesses=7036}}, unique=Document{{count=165, accesses=13786}}, wildcard=Document{{count=0, accesses=0}}}}}}, locks=Document{{ParallelBatchWriterMode=Document{{acquireCount=Document{{r=3542169, W=1}}}}, FeatureCompatibilityVersion=Document{{acquireCount=Document{{r=5734695, w=1753405}}}}, ReplicationStateTransition=Document{{acquireCount=Document{{w=7222655, W=2}}, acquireWaitCount=Document{{w=2}}, timeAcquiringMicros=Document{{w=11572}}}}, Global=Document{{acquireCount=Document{{r=7520311, w=3767679, W=6}}, acquireWaitCount=Document{{r=1}}, timeAcquiringMicros=Document{{r=21}}}}, Database=Document{{acquireCount=Document{{r=12373, w=3609621, W=2}}}}, Collection=Document{{acquireCount=Document{{r=17955, w=3739440, R=8, W=328}}}}, Mutex=Document{{acquireCount=Document{{r=5847098}}}}, oplog=Document{{acquireCount=Document{{r=1, w=2, W=1}}}}}}, logicalSessionRecordCache=Document{{activeSessionsCount=1, sessionsCollectionJobCount=912, lastSessionsCollectionJobDurationMillis=12, lastSessionsCollectionJobTimestamp=Wed Apr 30 15:01:13 CST 2025, lastSessionsCollectionJobEntriesRefreshed=1, lastSessionsCollectionJobEntriesEnded=1, lastSessionsCollectionJobCursorsClosed=0, transactionReaperJobCount=912, lastTransactionReaperJobDurationMillis=0, lastTransactionReaperJobTimestamp=Wed Apr 30 15:01:14 CST 2025, lastTransactionReaperJobEntriesCleanedUp=0, sessionCatalogSize=9}}, network=Document{{bytesIn=1499941284, bytesOut=3206644308, physicalBytesIn=1410156420, physicalBytesOut=3144542749, numSlowDNSOperations=0, numSlowSSLOperations=0, numRequests=2533003, tcpFastOpen=Document{{serverSupported=false, clientSupported=false, accepted=0}}, compression=Document{{snappy=Document{{compressor=Document{{bytesIn=256009915, bytesOut=190798624}}, decompressor=Document{{bytesIn=190577286, bytesOut=255720453}}}}, zstd=Document{{compressor=Document{{bytesIn=0, bytesOut=0}}, decompressor=Document{{bytesIn=0, bytesOut=0}}}}, zlib=Document{{compressor=Document{{bytesIn=0, bytesOut=0}}, decompressor=Document{{bytesIn=0, bytesOut=0}}}}}}, serviceExecutors=Document{{passthrough=Document{{threadsRunning=34, clientsInTotal=34, clientsRunning=34, clientsWaitingForData=0}}, fixed=Document{{threadsRunning=1, clientsInTotal=0, clientsRunning=0, clientsWaitingForData=0}}}}}}, opLatencies=Document{{reads=Document{{latency=2964179380, ops=1633480}}, writes=Document{{latency=1962997588, ops=358590}}, commands=Document{{latency=87507425, ops=540910}}, transactions=Document{{latency=0, ops=0}}}}, opcounters=Document{{insert=10163, query=1344944, update=180022, delete=3041, getmore=110941, command=930071, deprecated=Document{{total=239, insert=0, query=239, update=0, delete=0, getmore=0, killcursors=0}}}}, opcountersRepl=Document{{insert=0, query=0, update=0, delete=0, getmore=0, command=0}}, oplogTruncation=Document{{totalTimeProcessingMicros=31581, processingMethod=sampling, totalTimeTruncatingMicros=832852, truncateCount=43}}, readConcernCounters=Document{{nonTransactionOps=Document{{none=1508933, noneInfo=Document{{CWRC=Document{{local=0, available=0, majority=0}}, implicitDefault=Document{{local=1508933, available=0}}}}, local=0, available=0, majority=13366, snapshot=Document{{withClusterTime=0, withoutClusterTime=0}}, linearizable=0}}, transactionOps=Document{{none=0, noneInfo=Document{{CWRC=Document{{local=0, majority=0}}, implicitDefault=Document{{local=0}}}}, local=0, majority=0, snapshot=Document{{withClusterTime=0, withoutClusterTime=0}}}}}}, readPreferenceCounters=Document{{executedOnPrimary=Document{{primary=Document{{internal=0, external=3214}}, primaryPreferred=Document{{internal=0, external=1508669}}, secondary=Document{{internal=0, external=0}}, secondaryPreferred=Document{{internal=0, external=0}}, nearest=Document{{internal=0, external=13368}}, tagged=Document{{internal=0, external=0}}}}, executedOnSecondary=Document{{primary=Document{{internal=0, external=0}}, primaryPreferred=Document{{internal=0, external=0}}, secondary=Document{{internal=0, external=0}}, secondaryPreferred=Document{{internal=0, external=0}}, nearest=Document{{internal=0, external=1}}, tagged=Document{{internal=0, external=0}}}}}}, repl=Document{{topologyVersion=Document{{processId=680795042a6da540c4c81bdd, counter=6}}, hosts=[127.0.0.1:27017], setName=rs0, setVersion=1, isWritablePrimary=true, secondary=false, primary=127.0.0.1:27017, me=127.0.0.1:27017, electionId=7fffffff000000000000001a, lastWrite=Document{{opTime=Document{{ts=Timestamp{value=7498997823480791041, seconds=1745996490, inc=1}, t=26}}, lastWriteDate=Wed Apr 30 15:01:30 CST 2025, majorityOpTime=Document{{ts=Timestamp{value=7498997823480791041, seconds=1745996490, inc=1}, t=26}}, majorityWriteDate=Wed Apr 30 15:01:30 CST 2025}}, primaryOnlyServices=Document{{TenantMigrationRecipientService=Document{{state=running, numInstances=0}}, ShardSplitDonorService=Document{{state=running, numInstances=0}}, TenantMigrationDonorService=Document{{state=running, numInstances=0}}}}, rbid=18, userWriteBlockMode=1}}, scramCache=Document{{SCRAM-SHA-1=Document{{count=0, hits=0, misses=0}}, SCRAM-SHA-256=Document{{count=0, hits=0, misses=0}}}}, security=Document{{authentication=Document{{saslSupportedMechsReceived=308, mechanisms=Document{{MONGODB-X509=Document{{speculativeAuthenticate=Document{{received=0, successful=0}}, clusterAuthenticate=Document{{received=0, successful=0}}, authenticate=Document{{received=0, successful=0}}}}, SCRAM-SHA-1=Document{{speculativeAuthenticate=Document{{received=0, successful=0}}, clusterAuthenticate=Document{{received=0, successful=0}}, authenticate=Document{{received=0, successful=0}}}}, SCRAM-SHA-256=Document{{speculativeAuthenticate=Document{{received=0, successful=0}}, clusterAuthenticate=Document{{received=0, successful=0}}, authenticate=Document{{received=0, successful=0}}}}}}}}}}, storageEngine=Document{{name=wiredTiger, supportsCommittedReads=true, oldestRequiredTimestampForCrashRecovery=Timestamp{value=7498997750466347009, seconds=1745996473, inc=1}, supportsPendingDrops=true, dropPendingIdents=0, supportsSnapshotReadConcern=true, readOnly=false, persistent=true, backupCursorOpen=false}}, tenantMigrations=Document{{currentMigrationsDonating=0, currentMigrationsReceiving=0, totalSuccessfulMigrationsDonated=0, totalSuccessfulMigrationsReceived=0, totalFailedMigrationsDonated=0, totalFailedMigrationsReceived=0}}, trafficRecording=Document{{running=false}}, transactions=Document{{retriedCommandsCount=0, retriedStatementsCount=0, transactionsCollectionWriteCount=193858, currentActive=0, currentInactive=0, currentOpen=0, totalAborted=0, totalCommitted=0, totalStarted=0, totalPrepared=0, totalPreparedThenCommitted=0, totalPreparedThenAborted=0, currentPrepared=0}}, transportSecurity=Document{{1.0=0, 1.1=0, 1.2=0, 1.3=0, unknown=0}}, twoPhaseCommitCoordinator=Document{{totalCreated=0, totalStartedTwoPhaseCommit=0, totalAbortedTwoPhaseCommit=0, totalCommittedTwoPhaseCommit=0, currentInSteps=Document{{writingParticipantList=0, waitingForVotes=0, writingDecision=0, waitingForDecisionAcks=0, deletingCoordinatorDoc=0}}}}, wiredTiger=Document{{uri=statistics:, autocommit=Document{{retries for readonly operations=0, retries for update operations=0}}, block-cache=Document{{cached blocks updated=0, cached bytes updated=0, evicted blocks=0, file size causing bypass=0, lookups=0, number of blocks not evicted due to overhead=0, number of bypasses because no-write-allocate setting was on=0, number of bypasses due to overhead on put=0, number of bypasses on get=0, number of bypasses on put because file is too small=0, number of eviction passes=0, number of hits=0, number of misses=0, number of put bypasses on checkpoint I/O=0, removed blocks=0, total blocks=0, total blocks inserted on read path=0, total blocks inserted on write path=0, total bytes=0, total bytes inserted on read path=0, total bytes inserted on write path=0}}, block-manager=Document{{blocks pre-loaded=2835, blocks read=110308, blocks written=897882, bytes read=601284608, bytes read via memory map API=0, bytes read via system call API=0, bytes written=12276473856, bytes written for checkpoint=12255801344, bytes written via memory map API=0, bytes written via system call API=0, mapped blocks read=0, mapped bytes read=0, number of times the file was remapped because it changed size via fallocate or truncate=0, number of times the region was remapped via write=0}}, cache=Document{{application threads page read from disk to cache count=13754, application threads page read from disk to cache time (usecs)=2977908, application threads page write from cache to disk count=590493, application threads page write from cache to disk time (usecs)=2037741019, bytes allocated for updates=413978103, bytes belonging to page images in the cache=626679966, bytes belonging to the history store table in the cache=75094941, bytes currently in the cache=1046114850, bytes dirty in the cache cumulative=98168269819, bytes not belonging to page images in the cache=419434884, bytes read into cache=706660537, bytes written from cache=23705615706, checkpoint blocked page eviction=1, checkpoint of history store file blocked non-history store page eviction=0, eviction calls to get a page=177833, eviction calls to get a page found queue empty=50710, eviction calls to get a page found queue empty after locking=175, eviction currently operating in aggressive mode=0, eviction empty score=0, eviction gave up due to detecting an out of order on disk value behind the last update on the chain=0, eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update=0, eviction gave up due to detecting an out of order tombstone ahead of the selected on disk update after validating the update chain=0, eviction gave up due to detecting out of order timestamps on the update chain after the selected on disk update=0, eviction gave up due to needing to remove a record from the history store but checkpoint is running=0, eviction passes of a file=2644, eviction server candidate queue empty when topping up=20, eviction server candidate queue not empty when topping up=7, eviction server evicting pages=0, eviction server skips dirty pages during a running checkpoint=0, eviction server skips metadata pages with history=0, eviction server skips pages that are written with transactions greater than the last running=6, eviction server skips pages that previously failed eviction and likely will again=0, eviction server skips pages that we do not want to evict=9875, eviction server skips tree that we do not want to evict=22953, eviction server skips trees because there are too many active walks=0, eviction server skips trees that are being checkpointed=0, eviction server skips trees that are configured to stick in cache=108, eviction server skips trees that disable eviction=45261, eviction server skips trees that were not useful before=36170, eviction server slept, because we did not make progress with eviction=302970, eviction server unable to reach eviction goal=0, eviction server waiting for a leaf page=931, eviction state=64, eviction walk most recent sleeps for checkpoint handle gathering=0, eviction walk target pages histogram - 0-9=1892, eviction walk target pages histogram - 10-31=728, eviction walk target pages histogram - 128 and higher=0, eviction walk target pages histogram - 32-63=14, eviction walk target pages histogram - 64-128=10, eviction walk target pages reduced due to history store cache pressure=0, eviction walk target strategy both clean and dirty pages=0, eviction walk target strategy only clean pages=0, eviction walk target strategy only dirty pages=2644, eviction walks abandoned=4, eviction walks gave up because they restarted their walk twice=2614, eviction walks gave up because they saw too many pages and found no candidates=5, eviction walks gave up because they saw too many pages and found too few candidates=2, eviction walks random search fails to locate a page, results in a null position=0, eviction walks reached end of tree=5255, eviction walks restarted=0, eviction walks started from root of tree=2627, eviction walks started from saved location in tree=17, eviction worker thread active=4, eviction worker thread created=0, eviction worker thread evicting pages=126141, eviction worker thread removed=0, eviction worker thread stable number=0, files with active eviction walks=0, files with new eviction walks started=2641, force re-tuning of eviction workers once in a while=0, forced eviction - do not retry count to evict pages selected to evict during reconciliation=0, forced eviction - history store pages failed to evict while session has history store cursor open=0, forced eviction - history store pages selected while session has history store cursor open=29, forced eviction - history store pages successfully evicted while session has history store cursor open=29, forced eviction - pages evicted that were clean count=0, forced eviction - pages evicted that were clean time (usecs)=0, forced eviction - pages evicted that were dirty count=377, forced eviction - pages evicted that were dirty time (usecs)=449190, forced eviction - pages selected because of a large number of updates to a single item=0, forced eviction - pages selected because of too many deleted items count=2374, forced eviction - pages selected count=412, forced eviction - pages selected unable to be evicted count=35, forced eviction - pages selected unable to be evicted time=277, hazard pointer blocked page eviction=3391, hazard pointer check calls=126976, hazard pointer check entries walked=13997, hazard pointer maximum array length=1, history store table insert calls=560897, history store table insert calls that returned restart=0, history store table max on-disk size=0, history store table on-disk size=434176, history store table out-of-order resolved updates that lose their durable timestamp=0, history store table out-of-order updates that were fixed up by reinserting with the fixed timestamp=0, history store table reads=0, history store table reads missed=2, history store table reads requiring squashed modifies=0, history store table truncation by rollback to stable to remove an unstable update=0, history store table truncation by rollback to stable to remove an update=1244, history store table truncation to remove an update=127, history store table truncation to remove range of updates due to key being removed from the data page during reconciliation=0, history store table truncation to remove range of updates due to out-of-order timestamp update on data page=0, history store table writes requiring squashed modifies=850, in-memory page passed criteria to be split=3295, in-memory page splits=198, internal pages evicted=14, internal pages queued for eviction=9, internal pages seen by eviction walk=320, internal pages seen by eviction walk that are already queued=9, internal pages split during eviction=0, leaf pages split during eviction=175, locate a random in-mem ref by examining all entries on the root page=0, maximum bytes configured=16642998272, maximum milliseconds spent at a single eviction=0, maximum page size seen at eviction=321, modified pages evicted=119811, modified pages evicted by application threads=0, operations timed out waiting for space in cache=0, overflow pages read into cache=0, page split during eviction deepened the tree=0, page written requiring history store records=13452, pages currently held in the cache=9612, pages evicted by application threads=0, pages evicted in parallel with checkpoint=4403, pages queued for eviction=1429, pages queued for eviction post lru sorting=1216, pages queued for urgent eviction=125221, pages queued for urgent eviction during walk=7, pages queued for urgent eviction from history store due to high dirty content=0, pages read into cache=15870, pages read into cache after truncate=116706, pages read into cache after truncate in prepare state=0, pages removed from the ordinary queue to be queued for urgent eviction=172, pages requested from the cache=47123992, pages seen by eviction walk=16893, pages seen by eviction walk that are already queued=2845, pages selected for eviction unable to be evicted=3433, pages selected for eviction unable to be evicted because of active children on an internal page=7, pages selected for eviction unable to be evicted because of failure in reconciliation=22, pages selected for eviction unable to be evicted because of race between checkpoint and out of order timestamps handling=0, pages walked for eviction=43090, pages written from cache=592951, pages written requiring in-memory restoration=2495, percentage overhead=8, the number of times full update inserted to history store=483547, the number of times reverse modify inserted to history store=77350, total milliseconds spent inside reentrant history store evictions in a reconciliation=0, tracked bytes belonging to internal pages in the cache=1797828, tracked bytes belonging to leaf pages in the cache=1044317022, tracked dirty bytes in the cache=200401, tracked dirty pages in the cache=2, unmodified pages evicted=3750}}, capacity=Document{{background fsync file handles considered=0, background fsync file handles synced=0, background fsync time (msecs)=0, bytes read=214462464, bytes written for checkpoint=9691601304, bytes written for eviction=12073207, bytes written for log=1517288320, bytes written total=11220962831, threshold to call fsync=0, time waiting due to total capacity (usecs)=0, time waiting during checkpoint (usecs)=0, time waiting during eviction (usecs)=0, time waiting during logging (usecs)=0, time waiting during read (usecs)=0}}, checkpoint-cleanup=Document{{pages added for eviction=305591, pages removed=620, pages skipped during tree walk=2647463, pages visited=13069470}}, connection=Document{{auto adjusting condition resets=225159, auto adjusting condition wait calls=1772030, auto adjusting condition wait raced to update timeout and skipped updating=0, detected system time went backwards=0, files currently open=1001, hash bucket array size for data handles=512, hash bucket array size general=512, memory allocations=45736640, memory frees=44845034, memory re-allocations=3890651, pthread mutex condition wait calls=3686464, pthread mutex shared lock read-lock calls=36474675, pthread mutex shared lock write-lock calls=1957615, total fsync I/Os=467901, total read I/Os=117265, total write I/Os=1153205}}, cursor=Document{{Total number of deleted pages skipped during tree walk=27844, Total number of entries skipped by cursor next calls=118371279, Total number of entries skipped by cursor prev calls=5165164, Total number of entries skipped to position the history store cursor=0, Total number of in-memory deleted pages skipped during tree walk=121038, Total number of times a search near has exited due to prefix config=2071, bulk cursor count=0, cached cursor count=338, cursor bulk loaded cursor insert calls=463, cursor close calls that result in cache=9602496, cursor create calls=174336, cursor insert calls=1095767, cursor insert key and value bytes=1870740527, cursor modify calls=463642, cursor modify key and value bytes affected=200360169, cursor modify value bytes modified=18356669, cursor next calls=143108120, cursor next calls that skip due to a globally visible history store tombstone=0, cursor next calls that skip greater than or equal to 100 entries=260808, cursor next calls that skip less than 100 entries=142845135, cursor operation restarted=4425, cursor prev calls=2950933, cursor prev calls that skip due to a globally visible history store tombstone=0, cursor prev calls that skip greater than or equal to 100 entries=14691, cursor prev calls that skip less than 100 entries=2936239, cursor remove calls=117579, cursor remove key bytes removed=2971570, cursor reserve calls=0, cursor reset calls=26138835, cursor search calls=21865009, cursor search history store calls=2, cursor search near calls=4326740, cursor sweep buckets=7118218, cursor sweep cursors closed=541, cursor sweep cursors examined=3463745, cursor sweeps=572448, cursor truncate calls=43, cursor update calls=0, cursor update key and value bytes=0, cursor update value size change=1699252, cursors reused from cache=9601451, open cursor count=6}}, data-handle=Document{{connection data handle size=512, connection data handles currently active=1996, connection sweep candidate became referenced=0, connection sweep dhandles closed=113, connection sweep dhandles removed from hash list=207171, connection sweep time-of-death sets=1718116, connection sweeps=27587, connection sweeps skipped due to checkpoint gathering handles=5, session dhandles swept=139210, session sweep attempts=137085}}, lock=Document{{checkpoint lock acquisitions=4529, checkpoint lock application thread wait time (usecs)=188, checkpoint lock internal thread wait time (usecs)=0, dhandle lock application thread time waiting (usecs)=287806, dhandle lock internal thread time waiting (usecs)=8130, dhandle read lock acquisitions=1265426, dhandle write lock acquisitions=416841, durable timestamp queue lock application thread time waiting (usecs)=0, durable timestamp queue lock internal thread time waiting (usecs)=0, durable timestamp queue read lock acquisitions=0, durable timestamp queue write lock acquisitions=0, metadata lock acquisitions=4528, metadata lock application thread wait time (usecs)=165, metadata lock internal thread wait time (usecs)=0, read timestamp queue lock application thread time waiting (usecs)=0, read timestamp queue lock internal thread time waiting (usecs)=0, read timestamp queue read lock acquisitions=0, read timestamp queue write lock acquisitions=0, schema lock acquisitions=7669, schema lock application thread wait time (usecs)=17047784, schema lock internal thread wait time (usecs)=0, table lock application thread time waiting for the table lock (usecs)=60562, table lock internal thread time waiting for the table lock (usecs)=1501264, table read lock acquisitions=0, table write lock acquisitions=3580, txn global lock application thread time waiting (usecs)=90523, txn global lock internal thread time waiting (usecs)=55785, txn global read lock acquisitions=394987, txn global write lock acquisitions=860373}}, log=Document{{busy returns attempting to switch slots=466080, force log remove time sleeping (usecs)=0, log bytes of payload data=696999043, log bytes written=730261632, log files manually zero-filled=0, log flush operations=1785661, log force write operations=2073343, log force write operations skipped=1831258, log records compressed=61620, log records not compressed=231364, log records too small to compress=242834, log release advances write LSN=5897, log scan operations=4, log scan records requiring two reads=7, log server thread advances write LSN=243987, log server thread write LSN walk skipped=291378, log sync operations=246238, log sync time duration (usecs)=1370618915, log sync_dir operations=7, log sync_dir time duration (usecs)=30966, log write operations=535818, logging bytes consolidated=730259584, maximum log file size=104857600, number of pre-allocated log files to create=2, pre-allocated log files not ready and missed=1, pre-allocated log files prepared=8, pre-allocated log files used=6, records processed by log scan=20, slot close lost race=0, slot close unbuffered waits=0, slot closures=249884, slot join atomic update races=928, slot join calls atomic updates raced=921, slot join calls did not yield=534894, slot join calls found active slot closed=3, slot join calls slept=0, slot join calls yielded=924, slot join found active slot closed=3, slot joins yield time (usecs)=204180, slot transitions unable to find free slot=0, slot unbuffered writes=1918, total in-memory size of compressed records=831528541, total log buffer size=33554432, total size of compressed records=414726638, written slots coalesced=0, yields waiting for previous log file close=0}}, perf=Document{{file system read latency histogram (bucket 1) - 10-49ms=318, file system read latency histogram (bucket 2) - 50-99ms=11, file system read latency histogram (bucket 3) - 100-249ms=3, file system read latency histogram (bucket 4) - 250-499ms=0, file system read latency histogram (bucket 5) - 500-999ms=0, file system read latency histogram (bucket 6) - 1000ms+=0, file system write latency histogram (bucket 1) - 10-49ms=419, file system write latency histogram (bucket 2) - 50-99ms=49, file system write latency histogram (bucket 3) - 100-249ms=8, file system write latency histogram (bucket 4) - 250-499ms=0, file system write latency histogram (bucket 5) - 500-999ms=0, file system write latency histogram (bucket 6) - 1000ms+=1, operation read latency histogram (bucket 1) - 100-249us=44920, operation read latency histogram (bucket 2) - 250-499us=18772, operation read latency histogram (bucket 3) - 500-999us=9077, operation read latency histogram (bucket 4) - 1000-9999us=1562, operation read latency histogram (bucket 5) - 10000us+=321, operation write latency histogram (bucket 1) - 100-249us=7041, operation write latency histogram (bucket 2) - 250-499us=1870, operation write latency histogram (bucket 3) - 500-999us=717, operation write latency histogram (bucket 4) - 1000-9999us=604, operation write latency histogram (bucket 5) - 10000us+=213}}, reconciliation=Document{{approximate byte size of timestamps in pages written=100162304, approximate byte size of transaction IDs in pages written=51644120, fast-path pages deleted=4175, leaf-page overflow keys=0, maximum milliseconds spent in a reconciliation call=1, maximum milliseconds spent in building a disk image in a reconciliation=1, maximum milliseconds spent in moving updates to the history store in a reconciliation=0, page reconciliation calls=665385, page reconciliation calls for eviction=2513, page reconciliation calls that resulted in values with prepared transaction metadata=0, page reconciliation calls that resulted in values with timestamps=56087, page reconciliation calls that resulted in values with transaction ids=217250, pages deleted=233146, pages written including an aggregated newest start durable timestamp =53880, pages written including an aggregated newest stop durable timestamp =40057, pages written including an aggregated newest stop timestamp =19797, pages written including an aggregated newest stop transaction ID=19797, pages written including an aggregated newest transaction ID =108208, pages written including an aggregated oldest start timestamp =17066, pages written including an aggregated prepare=0, pages written including at least one prepare state=0, pages written including at least one start durable timestamp=63613, pages written including at least one start timestamp=63613, pages written including at least one start transaction ID=224776, pages written including at least one stop durable timestamp=42156, pages written including at least one stop timestamp=42160, pages written including at least one stop transaction ID=42160, records written including a prepare state=0, records written including a start durable timestamp=3179120, records written including a start timestamp=3179120, records written including a start transaction ID=3374488, records written including a stop durable timestamp=3081021, records written including a stop timestamp=3081027, records written including a stop transaction ID=3081027, split bytes currently awaiting free=79, split objects currently awaiting free=2}}, session=Document{{attempts to remove a local object and the object is in use=0, flush_tier operation calls=0, flush_tier tables skipped due to no checkpoint=0, flush_tier tables switched=0, local objects removed=0, open session count=15, session query timestamp calls=1, table alter failed calls=0, table alter successful calls=0, table alter triggering checkpoint calls=0, table alter unchanged and skipped=0, table compact failed calls=0, table compact failed calls due to cache pressure=0, table compact running=0, table compact skipped as process would not reduce file size=0, table compact successful calls=0, table compact timeout=0, table create failed calls=0, table create successful calls=864, table drop failed calls=0, table drop successful calls=505, table rename failed calls=0, table rename successful calls=0, table salvage failed calls=0, table salvage successful calls=0, table truncate failed calls=0, table truncate successful calls=43, table verify failed calls=0, table verify successful calls=0, tiered operations dequeued and processed=0, tiered operations scheduled=0, tiered storage local retention time (secs)=0}}, thread-state=Document{{active filesystem fsync calls=0, active filesystem read calls=0, active filesystem write calls=0}}, thread-yield=Document{{application thread snapshot refreshed for eviction=0, application thread time evicting (usecs)=0, application thread time waiting for cache (usecs)=0, connection close blocked waiting for transaction state stabilization=0, connection close yielded for lsm manager shutdown=0, data handle lock yielded=6857, get reference for page index and slot time sleeping (usecs)=0, page access yielded due to prepare state change=0, page acquire busy blocked=153, page acquire eviction blocked=30, page acquire locked blocked=369, page acquire read blocked=52, page acquire time sleeping (usecs)=57800, page delete rollback time sleeping for state change (usecs)=0, page reconciliation yielded due to child modification=0}}, transaction=Document{{Number of prepared updates=0, Number of prepared updates committed=0, Number of prepared updates repeated on the same key=0, Number of prepared updates rolled back=0, a reader raced with a prepared transaction commit and skipped an update or updates=0, checkpoint has acquired a snapshot for its transaction=0, oldest pinned transaction ID rolled back for eviction=0, prepared transactions=0, prepared transactions committed=0, prepared transactions currently active=0, prepared transactions rolled back=0, query timestamp calls=2807975, race to read prepared update retry=0, rollback to stable calls=0, rollback to stable history store records with stop timestamps older than newer records=0, rollback to stable inconsistent checkpoint=0, rollback to stable keys removed=0, rollback to stable keys restored=0, rollback to stable pages visited=4, rollback to stable restored tombstones from history store=0, rollback to stable restored updates from history store=0, rollback to stable skipping delete rle=0, rollback to stable skipping stable rle=0, rollback to stable sweeping history store keys=0, rollback to stable tree walk skipping pages=15, rollback to stable updates aborted=0, rollback to stable updates removed from history store=1244, sessions scanned in each walk of concurrent sessions=191170158, set timestamp calls=462854, set timestamp durable calls=0, set timestamp durable updates=0, set timestamp oldest calls=228663, set timestamp oldest updates=228663, set timestamp stable calls=234191, set timestamp stable updates=228663, transaction begins=6032323, transaction checkpoint currently running=0, transaction checkpoint currently running for history store file=0, transaction checkpoint generation=4529, transaction checkpoint history store file duration (usecs)=786, transaction checkpoint max time (msecs)=989870, transaction checkpoint min time (msecs)=31, transaction checkpoint most recent duration for gathering all handles (usecs)=7307, transaction checkpoint most recent duration for gathering applied handles (usecs)=494, transaction checkpoint most recent duration for gathering skipped handles (usecs)=4031, transaction checkpoint most recent handles applied=15, transaction checkpoint most recent handles skipped=982, transaction checkpoint most recent handles walked=2010, transaction checkpoint most recent time (msecs)=122, transaction checkpoint prepare currently running=0, transaction checkpoint prepare max time (msecs)=408, transaction checkpoint prepare min time (msecs)=0, transaction checkpoint prepare most recent time (msecs)=7, transaction checkpoint prepare total time (msecs)=47566, transaction checkpoint scrub dirty target=0, transaction checkpoint scrub time (msecs)=0, transaction checkpoint stop timing stress active=0, transaction checkpoint total time (msecs)=3771135, transaction checkpoints=4528, transaction checkpoints due to obsolete pages=24566, transaction checkpoints skipped because database was clean=0, transaction fsync calls for checkpoint after allocating the transaction ID=4528, transaction fsync duration for checkpoint after allocating the transaction ID (usecs)=67906, transaction range of IDs currently pinned=0, transaction range of IDs currently pinned by a checkpoint=0, transaction range of timestamps currently pinned=1288490188800, transaction range of timestamps pinned by a checkpoint=7498997823480791041, transaction range of timestamps pinned by the oldest active read timestamp=0, transaction range of timestamps pinned by the oldest timestamp=1288490188800, transaction read timestamp of the oldest active reader=0, transaction rollback to stable currently running=0, transaction walk of concurrent sessions=7220251, transactions committed=522247, transactions rolled back=5510065, update conflicts=1707}}, concurrentTransactions=Document{{write=Document{{out=0, available=128, totalTickets=128}}, read=Document{{out=0, available=128, totalTickets=128}}}}, snapshot-window-settings=Document{{total number of SnapshotTooOld errors=0, minimum target snapshot window size in seconds=300, current available snapshot window size in seconds=300, latest majority snapshot timestamp available=Apr 30 15:01:30:1, oldest majority snapshot timestamp available=Apr 30 14:56:30:1, pinned timestamp requests=0, min pinned timestamp=Timestamp{value=-1, seconds=-1, inc=-1}}}, oplog=Document{{visibility timestamp=Timestamp{value=7498997823480791041, seconds=1745996490, inc=1}}}}}, mem=Document{{bits=64, resident=201, virtual=404891, supported=true}}, metrics=Document{{apiVersions=Document{{MongoDB Compass=[default], =[default]}}, abortExpiredTransactions=Document{{passes=9089}}, aggStageCounters=Document{{$_addReshardingResumeId=0, $_internalAllCollectionStats=0, $_internalApplyOplogUpdate=0, $_internalBoundedSort=0, $_internalChangeStreamAddPostImage=0, $_internalChangeStreamAddPreImage=0, $_internalChangeStreamCheckInvalidate=0, $_internalChangeStreamCheckResumability=0, $_internalChangeStreamCheckTopologyChange=0, $_internalChangeStreamHandleTopologyChange=0, $_internalChangeStreamOplogMatch=0, $_internalChangeStreamTransform=0, $_internalChangeStreamUnwindTransaction=0, $_internalComputeGeoNearDistance=0, $_internalConvertBucketIndexStats=0, $_internalDensify=0, $_internalFindAndModifyImageLookup=0, $_internalInhibitOptimization=0, $_internalReshardingIterateTransaction=0, $_internalReshardingOwnershipMatch=0, $_internalSetWindowFields=0, $_internalShardServerInfo=0, $_internalShredDocuments=0, $_internalSplitPipeline=0, $_internalStreamingGroup=0, $_internalUnpackBucket=0, $_unpackBucket=0, $addFields=174, $bucket=0, $bucketAuto=0, $changeStream=17, $changeStreamSplitLargeEvent=0, $collStats=180, $count=23, $currentOp=1, $densify=0, $documents=0, $facet=278, $fill=0, $geoNear=0, $graphLookup=0, $group=166951, $indexStats=6, $limit=29434, $listCachedAndActiveUsers=0, $listCatalog=0, $listLocalSessions=0, $listSearchIndexes=0, $listSessions=0, $lookup=0, $match=180310, $merge=0, $mergeCursors=0, $operationMetrics=0, $out=1, $planCacheStats=0, $project=21374, $queryStats=0, $queue=0, $redact=0, $replaceRoot=4689, $replaceWith=0, $sample=0, $search=0, $searchMeta=0, $set=4506, $setVariableFromSubPipeline=0, $setWindowFields=0, $shardedDataDistribution=0, $skip=0, $sort=30376, $sortByCount=0, $unionWith=0, $unset=0, $unwind=10550, $vectorSearch=0}}, changeStreams=Document{{largeEventsFailed=0, largeEventsSplit=0}}, commands=Document{{<UNKNOWN>=2, _addShard=Document{{failed=0, total=0}}, _configsvrAbortReshardCollection=Document{{failed=0, total=0}}, _configsvrAddShard=Document{{failed=0, total=0}}, _configsvrAddShardToZone=Document{{failed=0, total=0}}, _configsvrBalancerCollectionStatus=Document{{failed=0, total=0}}, _configsvrBalancerStart=Document{{failed=0, total=0}}, _configsvrBalancerStatus=Document{{failed=0, total=0}}, _configsvrBalancerStop=Document{{failed=0, total=0}}, _configsvrCleanupReshardCollection=Document{{failed=0, total=0}}, _configsvrClearJumboFlag=Document{{failed=0, total=0}}, _configsvrCollMod=Document{{failed=0, total=0}}, _configsvrCommitChunkMigration=Document{{failed=0, total=0}}, _configsvrCommitChunkSplit=Document{{failed=0, total=0}}, _configsvrCommitChunksMerge=Document{{failed=0, total=0}}, _configsvrCommitMovePrimary=Document{{failed=0, total=0}}, _configsvrCommitReshardCollection=Document{{failed=0, total=0}}, _configsvrConfigureCollectionBalancing=Document{{failed=0, total=0}}, _configsvrCreateDatabase=Document{{failed=0, total=0}}, _configsvrEnsureChunkVersionIsGreaterThan=Document{{failed=0, total=0}}, _configsvrMoveChunk=Document{{failed=0, total=0}}, _configsvrMoveRange=Document{{failed=0, total=0}}, _configsvrRefineCollectionShardKey=Document{{failed=0, total=0}}, _configsvrRemoveChunks=Document{{failed=0, total=0}}, _configsvrRemoveShard=Document{{failed=0, total=0}}, _configsvrRemoveShardFromZone=Document{{failed=0, total=0}}, _configsvrRemoveTags=Document{{failed=0, total=0}}, _configsvrRenameCollectionMetadata=Document{{failed=0, total=0}}, _configsvrRepairShardedCollectionChunksHistory=Document{{failed=0, total=0}}, _configsvrReshardCollection=Document{{failed=0, total=0}}, _configsvrRunRestore=Document{{failed=0, total=0}}, _configsvrSetAllowMigrations=Document{{failed=0, total=0}}, _configsvrSetClusterParameter=Document{{failed=0, total=0}}, _configsvrSetUserWriteBlockMode=Document{{failed=0, total=0}}, _configsvrUpdateZoneKeyRange=Document{{failed=0, total=0}}, _flushDatabaseCacheUpdates=Document{{failed=0, total=0}}, _flushDatabaseCacheUpdatesWithWriteConcern=Document{{failed=0, total=0}}, _flushReshardingStateChange=Document{{failed=0, total=0}}, _flushRoutingTableCacheUpdates=Document{{failed=0, total=0}}, _flushRoutingTableCacheUpdatesWithWriteConcern=Document{{failed=0, total=0}}, _getNextSessionMods=Document{{failed=0, total=0}}, _getUserCacheGeneration=Document{{failed=0, total=0}}, _isSelf=Document{{failed=0, total=0}}, _killOperations=Document{{failed=0, total=0}}, _mergeAuthzCollections=Document{{failed=0, total=0}}, _migrateClone=Document{{failed=0, total=0}}, _recvChunkAbort=Document{{failed=0, total=0}}, _recvChunkCommit=Document{{failed=0, total=0}}, _recvChunkReleaseCritSec=Document{{failed=0, total=0}}, _recvChunkStart=Document{{failed=0, total=0}}, _recvChunkStatus=Document{{failed=0, total=0}}, _shardsvrAbortReshardCollection=Document{{failed=0, total=0}}, _shardsvrCleanupReshardCollection=Document{{failed=0, total=0}}, _shardsvrCloneCatalogData=Document{{failed=0, total=0}}, _shardsvrCollMod=Document{{failed=0, total=0}}, _shardsvrCollModParticipant=Document{{failed=0, total=0}}, _shardsvrCommitReshardCollection=Document{{failed=0, total=0}}, _shardsvrCompactStructuredEncryptionData=Document{{failed=0, total=0}}, _shardsvrCreateCollection=Document{{failed=0, total=0}}, _shardsvrCreateCollectionParticipant=Document{{failed=0, total=0}}, _shardsvrDropCollection=Document{{failed=0, total=0}}, _shardsvrDropCollectionIfUUIDNotMatching=Document{{failed=0, total=0}}, _shardsvrDropCollectionParticipant=Document{{failed=0, total=0}}, _shardsvrDropDatabase=Document{{failed=0, total=0}}, _shardsvrDropDatabaseParticipant=Document{{failed=0, total=0}}, _shardsvrDropIndexes=Document{{failed=0, total=0}}, _shardsvrGetStatsForBalancing=Document{{failed=0, total=0}}, _shardsvrJoinMigrations=Document{{failed=0, total=0}}, _shardsvrMovePrimary=Document{{failed=0, total=0}}, _shardsvrMoveRange=Document{{failed=0, total=0}}, _shardsvrParticipantBlock=Document{{failed=0, total=0}}, _shardsvrRefineCollectionShardKey=Document{{failed=0, total=0}}, _shardsvrRenameCollection=Document{{failed=0, total=0}}, _shardsvrRenameCollectionParticipant=Document{{failed=0, total=0}}, _shardsvrRenameCollectionParticipantUnblock=Document{{failed=0, total=0}}, _shardsvrReshardCollection=Document{{failed=0, total=0}}, _shardsvrReshardingOperationTime=Document{{failed=0, total=0}}, _shardsvrSetAllowMigrations=Document{{failed=0, total=0}}, _shardsvrSetClusterParameter=Document{{failed=0, total=0}}, _shardsvrSetUserWriteBlockMode=Document{{failed=0, total=0}}, _transferMods=Document{{failed=0, total=0}}, abortShardSplit=Document{{failed=0, total=0}}, abortTransaction=Document{{failed=0, total=0}}, aggregate=Document{{failed=0, total=180243}}, appendOplogNote=Document{{failed=0, total=0}}, applyOps=Document{{failed=0, total=0}}, authenticate=Document{{failed=0, total=0}}, autoSplitVector=Document{{failed=0, total=0}}, availableQueryOptions=Document{{failed=0, total=0}}, buildInfo=Document{{failed=0, total=249}}, checkShardingIndex=Document{{failed=0, total=0}}, cleanupOrphaned=Document{{failed=0, total=0}}, cloneCollectionAsCapped=Document{{failed=0, total=0}}, clusterAbortTransaction=Document{{failed=0, total=0}}, clusterAggregate=Document{{failed=0, total=0}}, clusterCommitTransaction=Document{{failed=0, total=0}}, clusterDelete=Document{{failed=0, total=0}}, clusterFind=Document{{failed=0, total=0}}, clusterGetMore=Document{{failed=0, total=0}}, clusterInsert=Document{{failed=0, total=0}}, clusterUpdate=Document{{arrayFilters=0, failed=0, pipeline=0, total=0}}, collMod=Document{{failed=0, total=0, validator=Document{{failed=0, jsonSchema=0, total=0}}}}, collStats=Document{{failed=0, total=0}}, commitShardSplit=Document{{failed=0, total=0}}, commitTransaction=Document{{failed=0, total=0}}, compact=Document{{failed=0, total=0}}, compactStructuredEncryptionData=Document{{failed=0, total=0}}, connPoolStats=Document{{failed=0, total=0}}, connPoolSync=Document{{failed=0, total=0}}, connectionStatus=Document{{failed=0, total=2}}, convertToCapped=Document{{failed=0, total=0}}, coordinateCommitTransaction=Document{{failed=0, total=0}}, count=Document{{failed=0, total=74}}, create=Document{{failed=0, total=0, validator=Document{{failed=0, jsonSchema=0, total=0}}}}, createIndexes=Document{{failed=0, total=717}}, createRole=Document{{failed=0, total=0}}, createUser=Document{{failed=0, total=0}}, currentOp=Document{{failed=0, total=0}}, dataSize=Document{{failed=0, total=0}}, dbCheck=Document{{failed=0, total=0}}, dbHash=Document{{failed=0, total=0}}, dbStats=Document{{failed=0, total=22}}, delete=Document{{failed=0, total=2063}}, distinct=Document{{failed=0, total=0}}, donorAbortMigration=Document{{failed=0, total=0}}, donorForgetMigration=Document{{failed=0, total=0}}, donorStartMigration=Document{{failed=0, total=0}}, driverOIDTest=Document{{failed=0, total=0}}, drop=Document{{failed=0, total=114}}, dropAllRolesFromDatabase=Document{{failed=0, total=0}}, dropAllUsersFromDatabase=Document{{failed=0, total=0}}, dropConnections=Document{{failed=0, total=0}}, dropDatabase=Document{{failed=0, total=0}}, dropIndexes=Document{{failed=2, total=15}}, dropRole=Document{{failed=0, total=0}}, dropUser=Document{{failed=0, total=0}}, endSessions=Document{{failed=0, total=166}}, explain=Document{{failed=0, total=17}}, features=Document{{failed=0, total=0}}, filemd5=Document{{failed=0, total=0}}, find=Document{{failed=0, total=1344944}}, findAndModify=Document{{arrayFilters=0, failed=2207, pipeline=0, total=206996}}, flushRouterConfig=Document{{failed=0, total=0}}, forgetShardSplit=Document{{failed=0, total=0}}, fsync=Document{{failed=0, total=0}}, fsyncUnlock=Document{{failed=0, total=0}}, getClusterParameter=Document{{failed=0, total=0}}, getCmdLineOpts=Document{{failed=0, total=0}}, getDatabaseVersion=Document{{failed=0, total=0}}, getDefaultRWConcern=Document{{failed=0, total=0}}, getDiagnosticData=Document{{failed=0, total=0}}, getLastError=Document{{failed=0, total=0}}, getLog=Document{{failed=0, total=0}}, getMore=Document{{failed=0, total=110941}}, getParameter=Document{{failed=0, total=2}}, getShardMap=Document{{failed=0, total=0}}, getShardVersion=Document{{failed=0, total=0}}, getnonce=Document{{failed=0, total=0}}, grantPrivilegesToRole=Document{{failed=0, total=0}}, grantRolesToRole=Document{{failed=0, total=0}}, grantRolesToUser=Document{{failed=0, total=0}}, hello=Document{{failed=530, total=501159}}, hostInfo=Document{{failed=0, total=2}}, insert=Document{{failed=0, total=4408}}, internalRenameIfOptionsAndIndexesMatch=Document{{failed=0, total=0}}, invalidateUserCache=Document{{failed=0, total=0}}, isMaster=Document{{failed=0, total=10697}}, killAllSessions=Document{{failed=0, total=0}}, killAllSessionsByPattern=Document{{failed=0, total=0}}, killCursors=Document{{failed=0, total=1}}, killOp=Document{{failed=0, total=0}}, killSessions=Document{{failed=0, total=0}}, listCollections=Document{{failed=0, total=11}}, listCommands=Document{{failed=0, total=0}}, listDatabases=Document{{failed=0, total=11}}, listIndexes=Document{{failed=54, total=2025}}, lockInfo=Document{{failed=0, total=0}}, logRotate=Document{{failed=0, total=0}}, logout=Document{{failed=0, total=0}}, mapReduce=Document{{failed=0, total=0}}, mergeChunks=Document{{failed=0, total=0}}, ping=Document{{failed=0, total=27536}}, planCacheClear=Document{{failed=0, total=0}}, planCacheClearFilters=Document{{failed=0, total=0}}, planCacheListFilters=Document{{failed=0, total=0}}, planCacheSetFilter=Document{{failed=0, total=0}}, prepareTransaction=Document{{failed=0, total=0}}, profile=Document{{failed=0, total=0}}, reIndex=Document{{failed=0, total=0}}, recipientForgetMigration=Document{{failed=0, total=0}}, recipientSyncData=Document{{failed=0, total=0}}, recipientVoteImportedFiles=Document{{failed=0, total=0}}, refreshSessions=Document{{failed=0, total=0}}, renameCollection=Document{{failed=0, total=0}}, replSetAbortPrimaryCatchUp=Document{{failed=0, total=0}}, replSetFreeze=Document{{failed=0, total=0}}, replSetGetConfig=Document{{failed=0, total=0}}, replSetGetRBID=Document{{failed=0, total=0}}, replSetGetStatus=Document{{failed=0, total=0}}, replSetHeartbeat=Document{{failed=0, total=0}}, replSetInitiate=Document{{failed=0, total=0}}, replSetMaintenance=Document{{failed=0, total=0}}, replSetReconfig=Document{{failed=0, total=0}}, replSetRequestVotes=Document{{failed=0, total=0}}, replSetResizeOplog=Document{{failed=0, total=0}}, replSetStepDown=Document{{failed=0, total=0}}, replSetStepDownWithForce=Document{{failed=0, total=0}}, replSetStepUp=Document{{failed=0, total=0}}, replSetSyncFrom=Document{{failed=0, total=0}}, replSetUpdatePosition=Document{{failed=0, total=0}}, revokePrivilegesFromRole=Document{{failed=0, total=0}}, revokeRolesFromRole=Document{{failed=0, total=0}}, revokeRolesFromUser=Document{{failed=0, total=0}}, rolesInfo=Document{{failed=0, total=0}}, rotateCertificates=Document{{failed=0, total=0}}, saslContinue=Document{{failed=0, total=0}}, saslStart=Document{{failed=0, total=0}}, serverStatus=Document{{failed=0, total=3}}, setClusterParameter=Document{{failed=0, total=0}}, setDefaultRWConcern=Document{{failed=0, total=0}}, setFeatureCompatibilityVersion=Document{{failed=0, total=0}}, setIndexCommitQuorum=Document{{failed=0, total=0}}, setParameter=Document{{failed=0, total=0}}, setProfilingFilterGlobally=Document{{failed=0, total=0}}, setShardVersion=Document{{failed=0, total=0}}, setUserWriteBlockMode=Document{{failed=0, total=0}}, shardingState=Document{{failed=0, total=0}}, shutdown=Document{{failed=0, total=0}}, splitChunk=Document{{failed=0, total=0}}, splitVector=Document{{failed=0, total=0}}, startRecordingTraffic=Document{{failed=0, total=0}}, startSession=Document{{failed=0, total=0}}, stopRecordingTraffic=Document{{failed=0, total=0}}, top=Document{{failed=0, total=1}}, update=Document{{arrayFilters=1, failed=0, pipeline=4506, total=145760}}, updateRole=Document{{failed=0, total=0}}, updateUser=Document{{failed=0, total=0}}, usersInfo=Document{{failed=0, total=0}}, validate=Document{{failed=0, total=0}}, validateDBMetadata=Document{{failed=0, total=0}}, voteCommitIndexBuild=Document{{failed=0, total=8}}, waitForFailPoint=Document{{failed=0, total=0}}, whatsmyuri=Document{{failed=0, total=0}}}}, cursor=Document{{moreThanOneBatch=2090, timedOut=0, totalOpened=182310, lifespan=Document{{greaterThanOrEqual10Minutes=17, lessThan10Minutes=0, lessThan15Seconds=0, lessThan1Minute=0, lessThan1Second=182290, lessThan30Seconds=0, lessThan5Seconds=3}}, open=Document{{noTimeout=0, pinned=0, total=0}}}}, document=Document{{deleted=7798, inserted=10141, returned=1800963, updated=342971}}, dotsAndDollarsFields=Document{{inserts=0, updates=0}}, getLastError=Document{{wtime=Document{{num=359880, totalMillis=357}}, wtimeouts=0, default=Document{{unsatisfiable=0, wtimeouts=0}}}}, mongos=Document{{cursor=Document{{moreThanOneBatch=0, totalOpened=0}}}}, operation=Document{{scanAndOrder=169350, temporarilyUnavailableErrors=0, temporarilyUnavailableErrorsConvertedToWriteConflict=0, temporarilyUnavailableErrorsEscaped=0, transactionTooLargeForCacheErrors=0, transactionTooLargeForCacheErrorsConvertedToWriteConflict=0, writeConflicts=1714}}, operatorCounters=Document{{expressions=Document{{$_internalFindAllValuesAtPath=0, $_internalFleEq=0, $_internalJsEmit=0, $_internalKeyStringValue=0, $abs=0, $acos=0, $acosh=0, $add=0, $allElementsTrue=0, $and=0, $anyElementTrue=0, $arrayElemAt=0, $arrayToObject=0, $asin=0, $asinh=0, $atan=0, $atan2=0, $atanh=0, $avg=0, $binarySize=0, $bsonSize=0, $ceil=0, $cmp=0, $concat=0, $concatArrays=0, $cond=256, $const=0, $convert=0, $cos=0, $cosh=0, $dateAdd=0, $dateDiff=0, $dateFromParts=0, $dateFromString=0, $dateSubtract=0, $dateToParts=0, $dateToString=0, $dateTrunc=0, $dayOfMonth=1407, $dayOfWeek=0, $dayOfYear=0, $degreesToRadians=0, $divide=174, $eq=90, $exp=0, $filter=0, $first=0, $firstN=0, $floor=0, $function=0, $getField=0, $gt=0, $gte=0, $hour=1407, $ifNull=0, $in=0, $indexOfArray=0, $indexOfBytes=0, $indexOfCP=0, $isArray=0, $isNumber=0, $isoDayOfWeek=0, $isoWeek=0, $isoWeekYear=0, $last=0, $lastN=0, $let=0, $literal=0, $ln=0, $log=0, $log10=0, $lt=0, $lte=0, $ltrim=0, $map=0, $max=0, $maxN=0, $mergeObjects=0, $meta=0, $millisecond=0, $min=0, $minN=0, $minute=1407, $mod=0, $month=1407, $multiply=174, $ne=174, $not=0, $objectToArray=6, $or=0, $pow=0, $radiansToDegrees=0, $rand=0, $range=0, $reduce=0, $regexFind=0, $regexFindAll=0, $regexMatch=0, $replaceAll=0, $replaceOne=0, $reverseArray=0, $round=0, $rtrim=0, $second=0, $setDifference=0, $setEquals=0, $setField=0, $setIntersection=0, $setIsSubset=0, $setUnion=0, $sin=0, $sinh=0, $size=0, $slice=0, $sortArray=0, $split=0, $sqrt=0, $stdDevPop=0, $stdDevSamp=0, $strLenBytes=0, $strLenCP=0, $strcasecmp=0, $substr=0, $substrBytes=0, $substrCP=0, $subtract=0, $sum=0, $switch=0, $tan=0, $tanh=0, $toBool=0, $toDate=0, $toDecimal=0, $toDouble=1224, $toHashedIndexKey=0, $toInt=0, $toLong=0, $toLower=0, $toObjectId=0, $toString=41, $toUpper=0, $trim=0, $trunc=0, $tsIncrement=0, $tsSecond=0, $type=0, $unsetField=0, $week=0, $year=1407, $zip=0}}, groupAccumulators=Document{{$_internalJsReduce=0, $accumulator=0, $addToSet=0, $avg=10462, $bottom=0, $bottomN=0, $count=0, $first=4945, $firstN=0, $last=1407, $lastN=0, $max=452, $maxN=0, $mergeObjects=0, $min=0, $minN=0, $push=0, $stdDevPop=0, $stdDevSamp=0, $sum=154992, $top=0, $topN=0}}, match=Document{{$all=0, $alwaysFalse=0, $alwaysTrue=0, $and=120811, $bitsAllClear=0, $bitsAllSet=0, $bitsAnyClear=0, $bitsAnySet=0, $comment=0, $elemMatch=154, $eq=3062740, $exists=221177, $expr=8, $geoIntersects=0, $geoWithin=0, $gt=89734, $gte=82909, $in=225029, $jsonSchema=0, $lt=111106, $lte=361218, $mod=0, $ne=137246, $near=0, $nearSphere=0, $nin=27748, $nor=51, $not=51, $or=407559, $regex=829, $sampleRate=0, $size=63, $text=0, $type=17, $where=0}}, windowAccumulators=Document{{$addToSet=0, $avg=0, $bottom=0, $bottomN=0, $count=0, $covariancePop=0, $covarianceSamp=0, $denseRank=0, $derivative=0, $documentNumber=0, $expMovingAvg=0, $first=0, $firstN=0, $integral=0, $last=0, $lastN=0, $linearFill=0, $locf=0, $max=0, $maxN=0, $min=0, $minN=0, $push=0, $rank=0, $shift=0, $stdDevPop=0, $stdDevSamp=0, $sum=0, $top=0, $topN=0}}}}, query=Document{{allowDiskUseFalse=13921, deleteManyCount=2006, externalRetryableWriteCount=323154, internalRetryableWriteCount=0, planCacheTotalSizeEstimateBytes=1064132, retryableInternalTransactionCount=0, updateDeleteManyDocumentsMaxCount=191, updateDeleteManyDocumentsTotalCount=58464, updateDeleteManyDurationMaxMs=211, updateDeleteManyDurationTotalMs=44577, updateManyCount=63450, updateOneOpStyleBroadcastWithExactIDCount=0, multiPlanner=Document{{classicCount=15248, classicMicros=1049877581, classicWorks=5992629, sbeCount=0, sbeMicros=0, sbeNumReads=0, histograms=Document{{classicMicros=[Document{{lowerBound=0, count=4873}}, Document{{lowerBound=1024, count=6423}}, Document{{lowerBound=4096, count=3922}}, Document{{lowerBound=16384, count=28}}, Document{{lowerBound=65536, count=1}}, Document{{lowerBound=262144, count=0}}, Document{{lowerBound=1048576, count=0}}, Document{{lowerBound=4194304, count=0}}, Document{{lowerBound=16777216, count=0}}, Document{{lowerBound=67108864, count=0}}, Document{{lowerBound=268435456, count=1}}, Document{{lowerBound=1073741824, count=0}}], classicNumPlans=[Document{{lowerBound=0, count=0}}, Document{{lowerBound=2, count=228}}, Document{{lowerBound=4, count=204}}, Document{{lowerBound=8, count=14816}}, Document{{lowerBound=16, count=0}}, Document{{lowerBound=32, count=0}}], classicWorks=[Document{{lowerBound=0, count=5703}}, Document{{lowerBound=128, count=119}}, Document{{lowerBound=256, count=3917}}, Document{{lowerBound=512, count=3608}}, Document{{lowerBound=1024, count=1901}}, Document{{lowerBound=2048, count=0}}, Document{{lowerBound=4096, count=0}}, Document{{lowerBound=8192, count=0}}, Document{{lowerBound=16384, count=0}}, Document{{lowerBound=32768, count=0}}], sbeMicros=[Document{{lowerBound=0, count=0}}, Document{{lowerBound=1024, count=0}}, Document{{lowerBound=4096, count=0}}, Document{{lowerBound=16384, count=0}}, Document{{lowerBound=65536, count=0}}, Document{{lowerBound=262144, count=0}}, Document{{lowerBound=1048576, count=0}}, Document{{lowerBound=4194304, count=0}}, Document{{lowerBound=16777216, count=0}}, Document{{lowerBound=67108864, count=0}}, Document{{lowerBound=268435456, count=0}}, Document{{lowerBound=1073741824, count=0}}], sbeNumPlans=[Document{{lowerBound=0, count=0}}, Document{{lowerBound=2, count=0}}, Document{{lowerBound=4, count=0}}, Document{{lowerBound=8, count=0}}, Document{{lowerBound=16, count=0}}, Document{{lowerBound=32, count=0}}], sbeNumReads=[Document{{lowerBound=0, count=0}}, Document{{lowerBound=128, count=0}}, Document{{lowerBound=256, count=0}}, Document{{lowerBound=512, count=0}}, Document{{lowerBound=1024, count=0}}, Document{{lowerBound=2048, count=0}}, Document{{lowerBound=4096, count=0}}, Document{{lowerBound=8192, count=0}}, Document{{lowerBound=16384, count=0}}, Document{{lowerBound=32768, count=0}}]}}}}, queryFramework=Document{{aggregate=Document{{classicHybrid=180050, classicOnly=6, cqf=0, sbeHybrid=0, sbeOnly=0}}, find=Document{{classic=1344944, cqf=0, sbe=0}}}}}}, queryExecutor=Document{{scanned=14928365, scannedObjects=134086578, collectionScans=Document{{nonTailable=399887, total=399904}}}}, queryStats=Document{{numEvicted=0, numHmacApplicationErrors=0, numQueryStatsStoreWriteErrors=0, numRateLimitedRequests=0, queryStatsStoreSizeEstimateBytes=0}}, repl=Document{{executor=Document{{pool=Document{{inProgressCount=0}}, queues=Document{{networkInProgress=0, sleepers=0}}, unsignaledEvents=0, shuttingDown=false, networkInterface=DEPRECATED: getDiagnosticString is deprecated in NetworkInterfaceTL}}, apply=Document{{attemptsToBecomeSecondary=1, batchSize=2, batches=Document{{num=1, totalMillis=0}}, ops=2}}, buffer=Document{{count=0, maxSizeBytes=268435456, sizeBytes=0}}, initialSync=Document{{completed=0, failedAttempts=0, failures=0}}, network=Document{{bytes=0, getmores=Document{{num=0, totalMillis=0, numEmptyBatches=0}}, notPrimaryLegacyUnacknowledgedWrites=0, notPrimaryUnacknowledgedWrites=0, oplogGetMoresProcessed=Document{{num=0, totalMillis=0}}, ops=0, readersCreated=0, replSetUpdatePosition=Document{{num=0}}}}, reconfig=Document{{numAutoReconfigsForRemovalOfNewlyAddedFields=0}}, stateTransition=Document{{lastStateTransition=stepUp, userOperationsKilled=0, userOperationsRunning=2}}, syncSource=Document{{numSelections=1, numSyncSourceChangesDueToSignificantlyCloserNode=0, numTimesChoseDifferent=0, numTimesChoseSame=0, numTimesCouldNotFind=1}}, waiters=Document{{opTime=0, replication=0}}}}, ttl=Document{{deletedDocuments=4937, passes=4549}}}}, ok=1.0, $clusterTime=Document{{clusterTime=Timestamp{value=7498997823480791041, seconds=1745996490, inc=1}, signature=Document{{hash=org.bson.types.Binary@c98f581, keyId=0}}}}, operationTime=Timestamp{value=7498997823480791041, seconds=1745996490, inc=1}}}
 2025-04-30 15:01:59 [main] INFO  o.example.MongoDBMonitorApplication - Starting MongoDBMonitorApplication using Java 17.0.14 on ShihuangdeMacBook-Pro.local with PID 90437 (/Users/<USER>/IdeaProjects/tapdata_v3/spring_demo/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
 2025-04-30 15:01:59 [main] INFO  o.example.MongoDBMonitorApplication - No active profile set, falling back to 1 default profile: "default"
 2025-04-30 15:01:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
 2025-04-30 15:01:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 MongoDB repository interfaces.
 2025-04-30 15:02:00 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@17814b1c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
 2025-04-30 15:02:00 [cluster-ClusterId{value='6811cae82e244a02bc6bc220', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2078}] to localhost:27017
 2025-04-30 15:02:00 [cluster-rtt-ClusterId{value='6811cae82e244a02bc6bc220', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2077}] to localhost:27017
 2025-04-30 15:02:00 [cluster-ClusterId{value='6811cae82e244a02bc6bc220', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=24636791, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 15:01:50 CST 2025, lastUpdateTimeNanos=273485113465917}
 2025-04-30 15:02:00 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 15:02:00 [main] INFO  o.example.MongoDBMonitorApplication - Started MongoDBMonitorApplication in 1.385 seconds (JVM running for 1.878)
 2025-04-30 15:02:00 [scheduling-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2079}] to localhost:27017
 2025-04-30 15:02:00 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - MongoDB is available: ServerStatus{id='null', available=true, timestamp=Wed Apr 30 15:02:00 CST 2025, message='MongoDB is available'}
 2025-04-30 15:56:56 [main] INFO  o.example.MongoDBMonitorApplication - Starting MongoDBMonitorApplication using Java 17.0.14 on ShihuangdeMacBook-Pro.local with PID 93230 (/Users/<USER>/IdeaProjects/tapdata_v3/spring_demo/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
 2025-04-30 15:56:56 [main] INFO  o.example.MongoDBMonitorApplication - No active profile set, falling back to 1 default profile: "default"
 2025-04-30 15:56:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
 2025-04-30 15:56:56 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 MongoDB repository interfaces.
 2025-04-30 15:56:56 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.6.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "x86_64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@63ec445c]}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
 2025-04-30 15:56:56 [cluster-ClusterId{value='6811d7c8edfa684b77c4cfb6', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:2081}] to localhost:27017
 2025-04-30 15:56:56 [cluster-rtt-ClusterId{value='6811d7c8edfa684b77c4cfb6', description='null'}-localhost:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:2080}] to localhost:27017
 2025-04-30 15:56:56 [cluster-ClusterId{value='6811d7c8edfa684b77c4cfb6', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=19055542, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff000000000000001a, setVersion=1, topologyVersion=TopologyVersion{processId=680795042a6da540c4c81bdd, counter=6}, lastWriteDate=Wed Apr 30 15:56:53 CST 2025, lastUpdateTimeNanos=276781891796833}
 2025-04-30 15:56:57 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 15:56:57 [main] INFO  o.example.MongoDBMonitorApplication - Started MongoDBMonitorApplication in 1.591 seconds (JVM running for 2.167)
 2025-04-30 15:56:57 [scheduling-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:2082}] to localhost:27017
 2025-04-30 15:56:57 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - MongoDB is available: ServerStatus{id='null', available=true, timestamp=Wed Apr 30 15:56:57 CST 2025, message='MongoDB is available'}
 2025-04-30 15:57:57 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - Checking MongoDB availability at: mongodb://localhost:27017/testdb
 2025-04-30 15:57:57 [scheduling-1] INFO  o.e.service.MongoDBMonitorService - MongoDB is available: ServerStatus{id='null', available=true, timestamp=Wed Apr 30 15:57:57 CST 2025, message='MongoDB is available'}
 