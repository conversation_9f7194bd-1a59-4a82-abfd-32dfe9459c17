# 集群定时任务删除解决方案

## 问题描述

在多服务集群部署环境中，检验任务的定时任务只在一个服务实例上运行，但删除操作可能在任何一个服务实例上执行。当前的 `CronUtil.removeJob(id)` 只能删除本地服务实例上的定时任务，无法删除其他服务实例上的定时任务。

## 解决方案

我们实现了一个基于数据库状态同步的集群定时任务管理机制，通过以下组件协同工作：

### 1. 修改后的 CronUtil 类

**文件**: `manager/tm-api/src/main/java/com/tapdata/tm/utils/CronUtil.java`

**主要改动**:
- `removeJob(String id)`: 删除本地任务并标记数据库状态
- `removeJobLocal(String id)`: 只删除本地定时任务
- 增加了任务存在性检查，避免删除不存在的任务时抛出异常

### 2. 集群定时任务管理器

**文件**: `manager/tm-api/src/main/java/com/tapdata/tm/utils/ClusterCronJobManager.java`

**功能**:
- 每10秒检查数据库中标记为删除的检验任务
- 自动删除本地存在的对应定时任务
- 更新数据库状态，避免重复处理

**关键方法**:
- `checkAndRemoveDeletedJobs()`: 定时检查并删除任务
- `markInspectForCronJobRemoval(String inspectId)`: 标记任务需要删除

### 3. 数据库字段扩展

在 `Inspect` 集合中添加以下字段来跟踪定时任务状态：

```javascript
{
  "cronJobRemovalRequested": true,        // 是否请求删除定时任务
  "cronJobRemovalRequestTime": ISODate(), // 删除请求时间
  "cronJobRemoved": true,                 // 定时任务是否已删除
  "cronJobRemovedTime": ISODate()         // 定时任务删除时间
}
```

## 工作流程

1. **删除检验任务时**:
   ```java
   // 在 InspectServiceImpl.delete() 方法中
   CronUtil.removeJob(id);  // 删除本地定时任务并标记数据库状态
   ```

2. **集群同步删除**:
   - `ClusterCronJobManager` 每10秒检查数据库
   - 发现 `is_deleted=true` 且 `cronJobRemoved!=true` 的记录
   - 调用 `CronUtil.removeJobLocal(id)` 删除本地定时任务
   - 更新数据库标记 `cronJobRemoved=true`

3. **状态跟踪**:
   - 避免重复删除同一个定时任务
   - 提供删除操作的审计跟踪
   - 确保所有服务实例最终都会删除对应的定时任务

## 优势

1. **可靠性**: 基于数据库状态，不依赖网络通信
2. **容错性**: 即使某个服务实例暂时不可用，重启后也会自动同步
3. **简单性**: 不需要复杂的消息队列或分布式锁
4. **可观测性**: 通过数据库字段可以跟踪删除状态
5. **向后兼容**: 不影响现有的定时任务创建逻辑

## 部署说明

1. 确保所有服务实例都包含更新后的代码
2. `ClusterCronJobManager` 会自动启动定时检查
3. 无需额外的配置或依赖

## 监控建议

可以通过以下查询监控定时任务删除状态：

```javascript
// 查看待删除的定时任务
db.Inspect.find({
  "is_deleted": true,
  "cronJobRemoved": {$ne: true}
})

// 查看删除历史
db.Inspect.find({
  "cronJobRemoved": true
}).sort({"cronJobRemovedTime": -1})
```

## 注意事项

1. 定时检查间隔为10秒，删除操作可能有轻微延迟
2. 如果需要立即删除，可以手动调用 `CronUtil.removeJobLocal(id)`
3. 建议定期清理已删除检验任务的历史记录
