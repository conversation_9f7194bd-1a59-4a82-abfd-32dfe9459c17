<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='utf-8'>
<style>
	body {margin: 0; padding: 10px; background-color: #ffffff}
	h1 {margin: 5px 0 0 0; font-size: 18px; font-weight: normal; text-align: center}
	header {margin: -24px 0 5px 0; line-height: 24px}
	button {font: 12px sans-serif; cursor: pointer}
	p {margin: 5px 0 5px 0}
	a {color: #0366d6}
	#hl {position: absolute; display: none; overflow: hidden; white-space: nowrap; pointer-events: none; background-color: #ffffe0; outline: 1px solid #ffc000; height: 15px}
	#hl span {padding: 0 3px 0 3px}
	#status {overflow: hidden; white-space: nowrap}
	#match {overflow: hidden; white-space: nowrap; display: none; float: right; text-align: right}
	#reset {cursor: pointer}
	#canvas {width: 100%; height: 1152px}
</style>
</head>
<body style='font: 12px Verdana, sans-serif'>
<h1>CPU profile</h1>
<header style='text-align: left'><button id='reverse' title='Reverse'>&#x1f53b;</button>&nbsp;&nbsp;<button id='search' title='Search'>&#x1f50d;</button></header>
<header style='text-align: right'>Produced by <a href='https://github.com/jvm-profiling-tools/async-profiler'>async-profiler</a></header>
<canvas id='canvas'></canvas>
<div id='hl'><span></span></div>
<p id='match'>Matched: <span id='matchval'></span> <span id='reset' title='Clear'>&#x274c;</span></p>
<p id='status'>&nbsp;</p>
<script>
	// Copyright 2020 Andrei Pangin
	// Licensed under the Apache License, Version 2.0.
	'use strict';
	var root, rootLevel, px, pattern;
	var reverse = false;
	const levels = Array(72);
	for (let h = 0; h < levels.length; h++) {
		levels[h] = [];
	}

	const canvas = document.getElementById('canvas');
	const c = canvas.getContext('2d');
	const hl = document.getElementById('hl');
	const status = document.getElementById('status');

	const canvasWidth = canvas.offsetWidth;
	const canvasHeight = canvas.offsetHeight;
	canvas.style.width = canvasWidth + 'px';
	canvas.width = canvasWidth * (devicePixelRatio || 1);
	canvas.height = canvasHeight * (devicePixelRatio || 1);
	if (devicePixelRatio) c.scale(devicePixelRatio, devicePixelRatio);
	c.font = document.body.style.font;

	const palette = [
		[0xb2e1b2, 20, 20, 20],
		[0x50e150, 30, 30, 30],
		[0x50cccc, 30, 30, 30],
		[0xe15a5a, 30, 40, 40],
		[0xc8c83c, 30, 30, 10],
		[0xe17d00, 30, 30,  0],
		[0xcce880, 20, 20, 20],
	];

	function getColor(p) {
		const v = Math.random();
		return '#' + (p[0] + ((p[1] * v) << 16 | (p[2] * v) << 8 | (p[3] * v))).toString(16);
	}

	function f(level, left, width, type, title, inln, c1, int) {
		levels[level].push({left: left, width: width, color: getColor(palette[type]), title: title,
			details: (int ? ', int=' + int : '') + (c1 ? ', c1=' + c1 : '') + (inln ? ', inln=' + inln : '')
		});
	}

	function samples(n) {
		return n === 1 ? '1 sample' : n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' samples';
	}

	function pct(a, b) {
		return a >= b ? '100' : (100 * a / b).toFixed(2);
	}

	function findFrame(frames, x) {
		let left = 0;
		let right = frames.length - 1;

		while (left <= right) {
			const mid = (left + right) >>> 1;
			const f = frames[mid];

			if (f.left > x) {
				right = mid - 1;
			} else if (f.left + f.width <= x) {
				left = mid + 1;
			} else {
				return f;
			}
		}

		if (frames[left] && (frames[left].left - x) * px < 0.5) return frames[left];
		if (frames[right] && (x - (frames[right].left + frames[right].width)) * px < 0.5) return frames[right];

		return null;
	}

	function search(r) {
		if (r === true && (r = prompt('Enter regexp to search:', '')) === null) {
			return;
		}

		pattern = r ? RegExp(r) : undefined;
		const matched = render(root, rootLevel);
		document.getElementById('matchval').textContent = pct(matched, root.width) + '%';
		document.getElementById('match').style.display = r ? 'inherit' : 'none';
	}

	function render(newRoot, newLevel) {
		if (root) {
			c.fillStyle = '#ffffff';
			c.fillRect(0, 0, canvasWidth, canvasHeight);
		}

		root = newRoot || levels[0][0];
		rootLevel = newLevel || 0;
		px = canvasWidth / root.width;

		const x0 = root.left;
		const x1 = x0 + root.width;
		const marked = [];

		function mark(f) {
			return marked[f.left] >= f.width || (marked[f.left] = f.width);
		}

		function totalMarked() {
			let total = 0;
			let left = 0;
			Object.keys(marked).sort(function(a, b) { return a - b; }).forEach(function(x) {
				if (+x >= left) {
					total += marked[x];
					left = +x + marked[x];
				}
			});
			return total;
		}

		function drawFrame(f, y, alpha) {
			if (f.left < x1 && f.left + f.width > x0) {
				c.fillStyle = pattern && f.title.match(pattern) && mark(f) ? '#ee00ee' : f.color;
				c.fillRect((f.left - x0) * px, y, f.width * px, 15);

				if (f.width * px >= 21) {
					const chars = Math.floor(f.width * px / 7);
					const title = f.title.length <= chars ? f.title : f.title.substring(0, chars - 2) + '..';
					c.fillStyle = '#000000';
					c.fillText(title, Math.max(f.left - x0, 0) * px + 3, y + 12, f.width * px - 6);
				}

				if (alpha) {
					c.fillStyle = 'rgba(255, 255, 255, 0.5)';
					c.fillRect((f.left - x0) * px, y, f.width * px, 15);
				}
			}
		}

		for (let h = 0; h < levels.length; h++) {
			const y = reverse ? h * 16 : canvasHeight - (h + 1) * 16;
			const frames = levels[h];
			for (let i = 0; i < frames.length; i++) {
				drawFrame(frames[i], y, h < rootLevel);
			}
		}

		return totalMarked();
	}

	canvas.onmousemove = function() {
		const h = Math.floor((reverse ? event.offsetY : (canvasHeight - event.offsetY)) / 16);
		if (h >= 0 && h < levels.length) {
			const f = findFrame(levels[h], event.offsetX / px + root.left);
			if (f) {
				if (f != root) getSelection().removeAllRanges();
				hl.style.left = (Math.max(f.left - root.left, 0) * px + canvas.offsetLeft) + 'px';
				hl.style.width = (Math.min(f.width, root.width) * px) + 'px';
				hl.style.top = ((reverse ? h * 16 : canvasHeight - (h + 1) * 16) + canvas.offsetTop) + 'px';
				hl.firstChild.textContent = f.title;
				hl.style.display = 'block';
				canvas.title = f.title + '\n(' + samples(f.width) + f.details + ', ' + pct(f.width, levels[0][0].width) + '%)';
				canvas.style.cursor = 'pointer';
				canvas.onclick = function() {
					if (f != root) {
						render(f, h);
						canvas.onmousemove();
					}
				};
				status.textContent = 'Function: ' + canvas.title;
				return;
			}
		}
		canvas.onmouseout();
	}

	canvas.onmouseout = function() {
		hl.style.display = 'none';
		status.textContent = '\xa0';
		canvas.title = '';
		canvas.style.cursor = '';
		canvas.onclick = '';
	}

	canvas.ondblclick = function() {
		getSelection().selectAllChildren(hl);
	}

	document.getElementById('reverse').onclick = function() {
		reverse = !reverse;
		render();
	}

	document.getElementById('search').onclick = function() {
		search(true);
	}

	document.getElementById('reset').onclick = function() {
		search(false);
	}

	window.onkeydown = function() {
		if (event.ctrlKey && event.keyCode === 70) {
			event.preventDefault();
			search(true);
		} else if (event.keyCode === 27) {
			search(false);
		}
	}

f(0,0,6117,3,'all')
f(1,0,6,3,'[deoptimization]')
f(2,0,2,4,'Deoptimization::fetch_unroll_info(JavaThread*)')
f(3,0,2,4,'Deoptimization::fetch_unroll_info_helper(JavaThread*)')
f(4,0,2,4,'Bytecode_member_ref::result_type() const')
f(5,0,2,4,'SignatureIterator::iterate()')
f(6,0,1,4,'SignatureInfo::do_object(int, int)')
f(6,1,1,4,'SignatureIterator::parse_type()')
f(2,2,4,4,'Deoptimization::unpack_frames(JavaThread*, int)')
f(3,2,2,4,'Deoptimization::cleanup_deopt_info(JavaThread*, vframeArray*)')
f(4,2,1,4,'DeoptResourceMark::reset_to_mark()')
f(5,2,1,4,'Chunk::next_chop()')
f(6,2,1,4,'Chunk::operator delete(void*)')
f(7,2,1,4,'ThreadCritical::~ThreadCritical()')
f(8,2,1,3,'pthread_mutex_unlock')
f(4,3,1,3,'free_small')
f(5,3,1,3,'small_free_list_add_ptr')
f(3,4,1,4,'Events::log(Thread*, char const*, ...)')
f(4,4,1,4,'StringEventLog::logv(Thread*, char const*, __va_list_tag*)')
f(5,4,1,3,'jio_vsnprintf')
f(6,4,1,4,'os::vsnprintf(char*, unsigned long, char const*, __va_list_tag*)')
f(7,4,1,3,'vsnprintf')
f(8,4,1,3,'_vsnprintf')
f(9,4,1,3,'__vfprintf')
f(3,5,1,4,'vframeArray::unpack_to_stack(frame&, int, int)')
f(4,5,1,4,'vframeArrayElement::unpack_on_stack(int, int, int, frame*, bool, bool, int)')
f(5,5,1,4,'frame::patch_pc(Thread*, unsigned char*)')
f(6,5,1,4,'nmethod::get_deopt_original_pc(frame const*)')
f(1,6,31,3,'[unknown_Java]')
f(2,6,1,6,'com/hazelcast/internal/metrics/metricsets/ThreadMetricSet$$Lambda$1546/404787971.get',0,1,0)
f(2,7,29,3,'new_type_array Runtime1 stub')
f(2,36,1,3,'slow_subtype_check Runtime1 stub')
f(1,37,2,3,'call_stub')
f(1,39,1,1,'com/hazelcast/internal/networking/nio/iobalancer/IOBalancerThread.run')
f(2,39,1,1,'com/hazelcast/internal/networking/nio/iobalancer/IOBalancer.rebalance')
f(3,39,1,1,'com/hazelcast/internal/networking/nio/iobalancer/IOBalancer.scheduleMigrationIfNeeded')
f(4,39,1,1,'com/hazelcast/internal/networking/nio/iobalancer/LoadTracker.updateImbalance')
f(5,39,1,1,'com/hazelcast/internal/networking/nio/iobalancer/LoadTracker.clearWorkingImbalance')
f(6,39,1,6,'java/util/HashSet.clear',0,1,0)
f(1,40,1,1,'com/hazelcast/internal/partition/impl/MigrationThread.run')
f(2,40,1,1,'com/hazelcast/internal/partition/impl/MigrationThread.doRun')
f(3,40,1,1,'com/hazelcast/internal/partition/impl/MigrationQueue.poll')
f(4,40,1,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(5,40,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(6,40,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(7,40,1,1,'sun/misc/Unsafe.park')
f(8,40,1,3,'Unsafe_Park')
f(9,40,1,4,'Parker::park(bool, long)')
f(10,40,1,3,'__psynch_cvwait')
f(1,41,56,1,'com/hazelcast/internal/util/executor/HazelcastManagedThread.run')
f(2,41,44,1,'com/hazelcast/internal/util/executor/HazelcastManagedThread.executeRun')
f(3,41,44,1,'java/lang/Thread.run')
f(4,41,44,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(5,41,44,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(6,41,29,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate$Worker.run')
f(7,41,8,1,'com/hazelcast/jet/impl/JobCoordinationService$$Lambda$1646/1876291408.run',0,1,0)
f(8,42,7,1,'com/hazelcast/jet/impl/JobCoordinationService.scanJobs')
f(9,42,6,1,'com/hazelcast/jet/impl/JobCoordinationService.doScanJobs')
f(10,42,1,1,'com/hazelcast/jet/impl/JobRepository.getJobExecutionRecord')
f(11,42,1,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.get')
f(12,42,1,1,'com/hazelcast/map/impl/proxy/MapProxySupport.getInternal')
f(13,42,1,1,'com/hazelcast/map/impl/proxy/MapProxySupport.invokeOperation')
f(14,42,1,1,'com/hazelcast/spi/impl/operationservice/impl/InvocationBuilderImpl.invoke')
f(15,42,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke')
f(16,42,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke0')
f(17,42,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvoke')
f(18,42,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.initInvocationTarget')
f(19,42,1,6,'com/hazelcast/spi/impl/operationservice/impl/PartitionInvocation.toTargetMember',0,1,0)
f(10,43,1,1,'com/hazelcast/jet/impl/JobRepository.getJobRecords')
f(11,43,1,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.values')
f(12,43,1,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.values')
f(13,43,1,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.executePredicate')
f(14,43,1,1,'com/hazelcast/map/impl/proxy/MapProxySupport.executeQueryInternal')
f(15,43,1,1,'com/hazelcast/map/impl/proxy/MapProxySupport.executeQueryInternal')
f(16,43,1,1,'com/hazelcast/map/impl/query/QueryEngineImpl.execute')
f(17,43,1,1,'com/hazelcast/map/impl/query/QueryEngineImpl.runOnGivenPartitions')
f(18,43,1,1,'com/hazelcast/map/impl/query/QueryEngineImpl.doRunOnQueryThreads')
f(19,43,1,1,'com/hazelcast/map/impl/query/QueryEngineImpl.dispatchOnQueryThreads')
f(20,43,1,1,'com/hazelcast/map/impl/query/QueryEngineImpl.dispatchFullQueryOnQueryThread')
f(21,43,1,1,'com/hazelcast/map/impl/query/QueryEngineImpl.dispatchFullQueryOnAllMembersOnQueryThread')
f(22,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationServiceImpl.invokeOnTarget')
f(23,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke')
f(24,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke0')
f(25,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvoke')
f(26,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvokeLocal')
f(27,43,1,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.runOrExecute')
f(28,43,1,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.run')
f(29,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(30,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(31,43,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.call')
f(32,43,1,1,'com/hazelcast/map/impl/query/QueryOperation.call')
f(33,43,1,1,'com/hazelcast/map/impl/query/QueryOperation.callInternal')
f(34,43,1,1,'com/hazelcast/map/impl/query/QueryRunner.runIndexOrPartitionScanQueryOnOwnedPartitions')
f(35,43,1,1,'com/hazelcast/map/impl/query/QueryRunner.runIndexOrPartitionScanQueryOnOwnedPartitions')
f(36,43,1,1,'com/hazelcast/map/impl/query/QueryRunner.runUsingPartitionScanSafely')
f(37,43,1,1,'com/hazelcast/map/impl/query/CallerRunsPartitionScanExecutor.execute')
f(38,43,1,1,'com/hazelcast/map/impl/query/PartitionScanRunner.run')
f(39,43,1,1,'com/hazelcast/map/impl/recordstore/DefaultRecordStore.forEachAfterLoad')
f(40,43,1,1,'com/hazelcast/map/impl/recordstore/DefaultRecordStore.forEach')
f(41,43,1,1,'com/hazelcast/map/impl/recordstore/DefaultRecordStore.forEach')
f(42,43,1,1,'com/hazelcast/map/impl/query/PartitionScanRunner$1.accept')
f(43,43,1,1,'com/hazelcast/map/impl/query/PartitionScanRunner$1.accept')
f(44,43,1,1,'com/hazelcast/query/impl/CachedQueryEntry.init')
f(45,43,1,6,'com/hazelcast/query/impl/CachedQueryEntry.init',0,1,0)
f(10,44,4,1,'com/hazelcast/map/impl/query/QueryResultIterator.next')
f(11,44,4,1,'com/hazelcast/internal/serialization/impl/AbstractSerializationService.toObject')
f(12,44,2,1,'com/hazelcast/internal/serialization/impl/StreamSerializerAdapter.read')
f(13,44,2,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(14,44,2,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(15,44,2,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.readInternal')
f(16,44,2,1,'com/hazelcast/jet/impl/JobRecord.readData')
f(17,44,2,1,'com/hazelcast/internal/serialization/impl/ByteArrayObjectDataInput.readObject')
f(18,44,2,1,'com/hazelcast/internal/serialization/impl/AbstractSerializationService.readObject')
f(19,44,2,1,'com/hazelcast/internal/serialization/impl/StreamSerializerAdapter.read')
f(20,44,2,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(21,44,2,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(22,44,2,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.readInternal')
f(23,44,2,1,'com/hazelcast/jet/config/JobConfig.readData')
f(24,44,2,1,'com/hazelcast/internal/serialization/impl/ByteArrayObjectDataInput.readObject')
f(25,44,2,1,'com/hazelcast/internal/serialization/impl/AbstractSerializationService.readObject')
f(26,44,2,1,'com/hazelcast/internal/serialization/impl/StreamSerializerAdapter.read')
f(27,44,2,1,'com/hazelcast/internal/serialization/impl/defaultserializers/JavaDefaultSerializers$JavaSerializer.read')
f(28,44,2,1,'com/hazelcast/internal/serialization/impl/defaultserializers/JavaDefaultSerializers$JavaSerializer.read')
f(29,44,2,1,'java/io/ObjectInputStream.readObject')
f(30,44,2,1,'java/io/ObjectInputStream.readObject0')
f(31,44,2,1,'java/io/ObjectInputStream.readEnum')
f(32,44,2,1,'java/io/ObjectInputStream.readClassDesc')
f(33,44,2,1,'java/io/ObjectInputStream.readNonProxyDesc')
f(34,44,1,1,'com/hazelcast/internal/nio/IOUtil$ClassLoaderAwareObjectInputStream.resolveClass')
f(35,44,1,6,'com/hazelcast/internal/nio/ClassLoaderUtil.loadClass',0,1,0)
f(34,45,1,1,'sun/reflect/misc/ReflectUtil.checkPackageAccess')
f(35,45,1,6,'sun/reflect/misc/ReflectUtil.isNonPublicProxyClass',0,1,0)
f(12,46,2,6,'com/hazelcast/internal/serialization/impl/bufferpool/BufferPoolThreadLocal.get',0,1,0)
f(13,46,1,2,'com/hazelcast/internal/serialization/impl/bufferpool/BufferPoolFactoryImpl.create',1,0,0)
f(13,47,1,1,'com/hazelcast/internal/util/ConcurrentReferenceHashMap.put')
f(14,47,1,1,'com/hazelcast/internal/util/ConcurrentReferenceHashMap.hashOf')
f(15,47,1,1,'java/lang/Object.hashCode')
f(9,48,1,1,'com/hazelcast/jet/impl/JobCoordinationService.shouldStartJobs')
f(10,48,1,1,'com/hazelcast/internal/partition/impl/PartitionReplicaStateChecker.getPartitionServiceState')
f(11,48,1,1,'com/hazelcast/internal/partition/impl/PartitionReplicaStateChecker.checkAndTriggerReplicaSync')
f(12,48,1,1,'com/hazelcast/internal/partition/impl/PartitionReplicaStateChecker.invokeReplicaSyncOperations')
f(13,48,1,1,'com/hazelcast/internal/partition/PartitionReplica.isIdentical')
f(14,48,1,6,'com/hazelcast/cluster/impl/AbstractMember.getUuid',0,1,0)
f(7,49,17,1,'com/hazelcast/spi/impl/executionservice/impl/DelegateAndSkipOnConcurrentExecutionDecorator$DelegateDecorator.run')
f(8,49,1,6,'com/hazelcast/internal/crdt/CRDTReplicationTask.run',0,1,0)
f(8,50,1,1,'com/hazelcast/internal/eviction/ClearExpiredRecordsTask.run')
f(9,50,1,1,'com/hazelcast/internal/eviction/ClearExpiredRecordsTask.runInternal')
f(10,50,1,1,'com/hazelcast/internal/eviction/ClearExpiredRecordsTask.sendCleanupOperations')
f(11,50,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationServiceImpl.execute')
f(12,50,1,6,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.execute',0,1,0)
f(8,51,15,1,'com/hazelcast/internal/metrics/impl/MetricsService$$Lambda$1589/1413679210.run')
f(9,51,15,1,'com/hazelcast/internal/metrics/impl/MetricsService.collectMetrics')
f(10,51,13,1,'com/hazelcast/internal/metrics/impl/MetricsService.collectMetrics')
f(11,51,13,1,'com/hazelcast/internal/metrics/impl/MetricsRegistryImpl.collect')
f(12,51,6,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectDynamicMetrics',0,1,0)
f(13,52,4,1,'com/hazelcast/jet/impl/JobExecutionService.provideDynamicMetrics')
f(14,52,4,1,'java/util/concurrent/ConcurrentHashMap.forEach')
f(15,52,4,1,'com/hazelcast/jet/impl/JobExecutionService$$Lambda$1778/1058338580.accept')
f(16,52,4,1,'com/hazelcast/jet/impl/JobExecutionService.lambda$provideDynamicMetrics$20')
f(17,52,4,1,'com/hazelcast/jet/impl/execution/ExecutionContext.provideDynamicMetrics')
f(18,52,4,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.provideDynamicMetrics',0,1,0)
f(19,53,1,1,'com/hazelcast/internal/metrics/impl/MetricDescriptorImpl.withTag')
f(20,53,1,6,'com/hazelcast/internal/metrics/impl/MetricDescriptorImpl.withTag',0,1,0)
f(19,54,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle$MetricsContext.collect')
f(20,54,1,1,'com/hazelcast/internal/metrics/impl/PublisherMetricsCollector.collectLong')
f(21,54,1,1,'com/hazelcast/internal/metrics/managementcenter/ManagementCenterPublisher.publishLong')
f(22,54,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.addLong')
f(23,54,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.writeDescriptor')
f(24,54,1,6,'java/io/DataOutputStream.writeInt',0,1,0)
f(25,54,1,3,'vtable stub')
f(19,55,1,6,'com/hazelcast/jet/impl/execution/init/Contexts$MetaSupplierCtx.vertexName',0,1,0)
f(13,56,1,1,'com/hazelcast/map/impl/MapService.provideDynamicMetrics')
f(14,56,1,1,'com/hazelcast/map/impl/MapService.getStats')
f(15,56,1,1,'com/hazelcast/map/impl/MapStatisticsAwareService.getStats')
f(16,56,1,1,'com/hazelcast/map/impl/LocalMapStatsProvider.createAllLocalMapStats')
f(17,56,1,1,'com/hazelcast/map/impl/LocalMapStatsProvider.addStatsOfPrimaryReplica')
f(18,56,1,1,'com/hazelcast/map/impl/recordstore/AbstractEvictableRecordStore.getLockedEntryCount')
f(19,56,1,1,'com/hazelcast/map/impl/recordstore/AbstractRecordStore.getLockedEntryCount')
f(20,56,1,6,'com/hazelcast/internal/locksupport/LockStoreProxy.getLockedEntryCount',0,1,0)
f(12,57,7,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectStaticMetrics')
f(13,57,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectDouble')
f(14,57,1,6,'com/hazelcast/internal/metrics/metricsets/OperatingSystemMetricSet$$Lambda$1541/**********.get',0,1,0)
f(15,57,1,2,'com/hazelcast/internal/metrics/metricsets/OperatingSystemMetricSet.lambda$registerMethod$2',1,0,0)
f(13,58,6,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectLong')
f(14,58,2,1,'com/hazelcast/internal/metrics/impl/MethodProbe$LongMethodProbe.get')
f(15,58,2,1,'java/lang/reflect/Method.invoke')
f(16,58,2,1,'sun/reflect/DelegatingMethodAccessorImpl.invoke')
f(17,58,1,1,'sun/reflect/GeneratedMethodAccessor113.invoke')
f(18,58,1,6,'com/hazelcast/internal/memory/DefaultMemoryStats.getFreeNative',0,1,0)
f(17,59,1,1,'sun/reflect/GeneratedMethodAccessor97.invoke')
f(18,59,1,1,'com/hazelcast/internal/memory/DefaultMemoryStats.getMaxHeap')
f(19,59,1,1,'java/lang/Runtime.maxMemory')
f(14,60,4,1,'com/hazelcast/internal/metrics/impl/PublisherMetricsCollector.collectLong',0,1,0)
f(15,61,1,1,'com/hazelcast/internal/metrics/jmx/JmxPublisher.publishLong')
f(16,61,1,1,'com/hazelcast/internal/metrics/jmx/JmxPublisher.publishNumber')
f(17,61,1,6,'java/util/HashMap.computeIfAbsent',0,1,0)
f(15,62,2,1,'com/hazelcast/internal/metrics/managementcenter/ManagementCenterPublisher.publishLong')
f(16,62,2,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.addLong')
f(17,62,1,6,'com/hazelcast/internal/metrics/impl/MetricsCompressor.writeDescriptor',0,1,0)
f(17,63,1,1,'java/io/DataOutputStream.write')
f(18,63,1,1,'java/util/zip/DeflaterOutputStream.write')
f(19,63,1,1,'java/util/zip/DeflaterOutputStream.deflate')
f(20,63,1,1,'java/util/zip/Deflater.deflate')
f(21,63,1,1,'java/util/zip/Deflater.deflate')
f(22,63,1,1,'java/util/zip/Deflater.deflateBytes')
f(23,63,1,3,'Java_java_util_zip_Deflater_deflateBytes')
f(24,63,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(10,64,2,1,'com/hazelcast/internal/metrics/impl/PublisherMetricsCollector.publishCollectedMetrics')
f(11,64,1,1,'com/hazelcast/internal/metrics/managementcenter/ManagementCenterPublisher.whenComplete')
f(12,64,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getBlobAndReset')
f(13,64,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getRenderedBlob')
f(14,64,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.writeDictionary')
f(15,64,1,1,'java/io/DataOutputStream.write')
f(16,64,1,1,'java/util/zip/DeflaterOutputStream.write')
f(17,64,1,1,'java/util/zip/DeflaterOutputStream.deflate')
f(18,64,1,1,'java/util/zip/Deflater.deflate')
f(19,64,1,1,'java/util/zip/Deflater.deflate')
f(20,64,1,1,'java/util/zip/Deflater.deflateBytes')
f(21,64,1,3,'Java_java_util_zip_Deflater_deflateBytes')
f(22,64,1,3,'deflate')
f(23,64,1,3,'deflateCopy')
f(24,64,1,3,'deflateCopy')
f(11,65,1,1,'com/hazelcast/jet/impl/metrics/JobMetricsPublisher.whenComplete')
f(12,65,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getBlobAndReset')
f(13,65,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getRenderedBlob')
f(14,65,1,1,'java/io/FilterOutputStream.close')
f(15,65,1,1,'java/util/zip/DeflaterOutputStream.close')
f(16,65,1,1,'java/util/zip/DeflaterOutputStream.finish')
f(17,65,1,1,'java/util/zip/DeflaterOutputStream.deflate')
f(18,65,1,1,'java/util/zip/Deflater.deflate')
f(19,65,1,1,'java/util/zip/Deflater.deflate')
f(20,65,1,1,'java/util/zip/Deflater.deflateBytes')
f(21,65,1,3,'Java_java_util_zip_Deflater_deflateBytes')
f(22,65,1,3,'deflate')
f(23,65,1,3,'deflateCopy')
f(24,65,1,3,'inflateCodesUsed')
f(25,65,1,3,'inflateCodesUsed')
f(26,65,1,3,'inflateCodesUsed')
f(7,66,4,1,'java/util/concurrent/LinkedBlockingQueue.poll',0,1,0)
f(8,67,3,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos',0,1,0)
f(9,68,2,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(10,68,2,1,'sun/misc/Unsafe.park')
f(11,68,2,3,'Unsafe_Park')
f(12,68,2,4,'Parker::park(bool, long)')
f(13,68,2,3,'__psynch_cvwait')
f(6,70,3,1,'java/util/concurrent/FutureTask.run')
f(7,70,3,1,'java/util/concurrent/FutureTask.run$$$capture')
f(8,70,3,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(9,70,3,1,'com/hazelcast/spi/impl/operationparker/impl/OperationParkerImpl$ExpirationTask.run')
f(10,70,3,1,'com/hazelcast/spi/impl/operationparker/impl/OperationParkerImpl$ExpirationTask.doRun',0,1,0)
f(11,71,2,6,'java/util/concurrent/DelayQueue.poll',0,2,0)
f(6,73,3,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.run')
f(7,73,3,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301')
f(8,73,3,1,'java/util/concurrent/FutureTask.runAndReset')
f(9,73,1,6,'com/intellij/rt/debugger/agent/CaptureStorage.insertEnter',0,1,0)
f(9,74,2,1,'java/util/concurrent/FutureTask.runAndReset$$$capture')
f(10,74,2,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(11,74,2,1,'com/hazelcast/spi/impl/executionservice/impl/DelegateAndSkipOnConcurrentExecutionDecorator.run')
f(12,74,2,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.execute')
f(13,74,2,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.addNewWorkerIfRequired')
f(14,74,2,1,'java/util/concurrent/ThreadPoolExecutor.execute')
f(15,74,2,1,'java/util/concurrent/SynchronousQueue.offer')
f(16,74,2,6,'java/util/concurrent/SynchronousQueue$TransferStack.transfer',0,2,0)
f(6,76,9,1,'java/util/concurrent/ThreadPoolExecutor.getTask')
f(7,76,3,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(8,76,3,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take',0,1,0)
f(9,77,1,6,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.finishPoll',0,1,0)
f(9,78,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(10,78,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(11,78,1,1,'sun/misc/Unsafe.park')
f(12,78,1,3,'Unsafe_Park')
f(13,78,1,4,'Parker::park(bool, long)')
f(14,78,1,4,'JavaThread::handle_special_suspend_equivalent_condition()')
f(15,78,1,4,'MutexLockerEx::MutexLockerEx(Monitor*, bool)')
f(7,79,6,1,'java/util/concurrent/SynchronousQueue.poll')
f(8,79,6,1,'java/util/concurrent/SynchronousQueue$TransferStack.transfer',0,1,0)
f(9,80,5,1,'java/util/concurrent/SynchronousQueue$TransferStack.awaitFulfill',0,1,0)
f(10,80,1,2,'java/util/concurrent/locks/LockSupport.park',1,0,0)
f(10,81,4,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(11,81,4,1,'sun/misc/Unsafe.park')
f(12,81,4,3,'Unsafe_Park')
f(13,81,4,4,'Parker::park(bool, long)')
f(14,81,1,3,'__gettimeofday')
f(14,82,3,3,'__psynch_cvwait')
f(2,85,12,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.executeRun')
f(3,85,6,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationQueueImpl.take')
f(4,85,6,1,'com/hazelcast/internal/util/concurrent/MPSCQueue.take')
f(5,85,6,1,'com/hazelcast/internal/util/concurrent/MPSCQueue.takeAll',0,1,0)
f(6,85,1,2,'java/lang/Thread.isInterrupted',1,0,0)
f(6,86,5,1,'java/util/concurrent/locks/LockSupport.park')
f(7,86,5,1,'sun/misc/Unsafe.park')
f(8,86,5,3,'Unsafe_Park')
f(9,86,5,4,'Parker::park(bool, long)')
f(10,86,5,3,'__psynch_cvwait')
f(3,91,6,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.process')
f(4,91,6,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.process',0,1,0)
f(5,92,3,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.process')
f(6,92,3,1,'com/hazelcast/internal/partition/impl/PartitionPrimaryReplicaAntiEntropyTask.run')
f(7,92,3,1,'com/hazelcast/internal/partition/impl/AbstractPartitionPrimaryReplicaAntiEntropyTask.retainAndGetNamespaces')
f(8,92,1,1,'com/hazelcast/internal/locksupport/LockSupportServiceImpl.getAllServiceNamespaces')
f(9,92,1,1,'com/hazelcast/internal/locksupport/LockStoreContainer.getAllNamespaces')
f(10,92,1,1,'com/hazelcast/internal/partition/impl/NameSpaceUtil.getAllNamespaces')
f(11,92,1,1,'java/util/concurrent/ConcurrentHashMap$ValueIterator.next')
f(12,92,1,6,'java/util/concurrent/ConcurrentHashMap$Traverser.advance',0,1,0)
f(8,93,1,1,'com/hazelcast/map/impl/MapService.getAllServiceNamespaces')
f(9,93,1,1,'com/hazelcast/spi/impl/CountingMigrationAwareService.getAllServiceNamespaces')
f(10,93,1,1,'com/hazelcast/map/impl/MapMigrationAwareService.getAllServiceNamespaces')
f(11,93,1,1,'com/hazelcast/map/impl/PartitionContainer.getAllNamespaces')
f(12,93,1,1,'com/hazelcast/map/impl/PartitionContainer.getNamespaces')
f(13,93,1,6,'com/hazelcast/internal/partition/impl/NameSpaceUtil.getAllNamespaces',0,1,0)
f(8,94,1,1,'java/util/AbstractCollection.addAll')
f(9,94,1,1,'java/util/HashSet.add')
f(10,94,1,1,'java/util/HashMap.put')
f(11,94,1,1,'java/util/HashMap.putVal')
f(12,94,1,1,'com/hazelcast/internal/services/DistributedObjectNamespace.equals')
f(13,94,1,6,'java/lang/String.equals',0,1,0)
f(5,95,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(6,95,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(7,95,1,6,'com/hazelcast/spi/impl/operationservice/Operation.beforeRun',0,1,0)
f(7,96,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.call')
f(8,96,1,1,'com/hazelcast/spi/impl/operationservice/Operation.call')
f(9,96,1,1,'com/hazelcast/map/impl/operation/MapClearExpiredOperation.run')
f(10,96,1,1,'com/hazelcast/map/impl/recordstore/AbstractEvictableRecordStore.evictExpiredEntries')
f(11,96,1,1,'com/hazelcast/map/impl/recordstore/expiry/ExpirySystemImpl.evictExpiredEntries')
f(12,96,1,1,'com/hazelcast/map/impl/recordstore/expiry/ExpirySystemImpl.findExpiredKeys')
f(13,96,1,1,'com/hazelcast/map/impl/recordstore/expiry/ExpirySystemImpl.hasExpired')
f(14,96,1,6,'com/hazelcast/map/impl/recordstore/expiry/ExpiryMetadataImpl.getExpirationTime',0,1,0)
f(1,97,1,1,'com/hazelcast/spi/impl/operationexecutor/slowoperationdetector/SlowOperationDetector$DetectorThread.run')
f(2,97,1,6,'com/hazelcast/spi/impl/operationexecutor/slowoperationdetector/SlowOperationDetector$DetectorThread.purge',0,1,0)
f(1,98,1,1,'io/tapdata/wsclient/modules/imclient/impls/MonitorThread.run')
f(2,98,1,1,'io/tapdata/wsclient/modules/imclient/impls/websocket/WebsocketPushChannel.ping')
f(3,98,1,1,'io/tapdata/wsclient/modules/imclient/impls/websocket/WebsocketPushChannel.send')
f(4,98,1,1,'io/netty/buffer/Unpooled.directBuffer')
f(5,98,1,1,'io/netty/buffer/AbstractByteBufAllocator.directBuffer')
f(6,98,1,1,'io/netty/buffer/AbstractByteBufAllocator.directBuffer')
f(7,98,1,1,'io/netty/buffer/UnpooledByteBufAllocator.newDirectBuffer')
f(8,98,1,1,'io/netty/buffer/UnpooledByteBufAllocator$InstrumentedUnpooledUnsafeNoCleanerDirectByteBuf.<init>')
f(9,98,1,1,'io/netty/buffer/UnpooledUnsafeNoCleanerDirectByteBuf.<init>')
f(10,98,1,1,'io/netty/buffer/UnpooledUnsafeDirectByteBuf.<init>')
f(11,98,1,6,'io/netty/buffer/UnpooledByteBufAllocator$InstrumentedUnpooledUnsafeNoCleanerDirectByteBuf.allocateDirect',0,1,0)
f(1,99,4212,1,'java/lang/Thread.run')
f(2,99,1,1,'com/alibaba/arthas/deps/io/netty/util/concurrent/FastThreadLocalRunnable.run')
f(3,99,1,1,'com/alibaba/arthas/deps/io/netty/util/internal/ThreadExecutorMap$2.run')
f(4,99,1,1,'com/alibaba/arthas/deps/io/netty/util/concurrent/SingleThreadEventExecutor$4.run')
f(5,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.run')
f(6,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.processSelectedKeys')
f(7,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.processSelectedKeysOptimized')
f(8,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.processSelectedKey')
f(9,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe.read')
f(10,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/DefaultChannelPipeline.fireChannelRead')
f(11,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(12,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(13,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/DefaultChannelPipeline$HeadContext.channelRead')
f(14,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.fireChannelRead')
f(15,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(16,99,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(17,99,1,1,'io/termd/core/telnet/netty/TelnetChannelHandler.channelRead')
f(18,99,1,1,'io/termd/core/telnet/TelnetConnection.receive')
f(19,99,1,1,'io/termd/core/telnet/TelnetConnection.flushDataIfNecessary')
f(20,99,1,1,'io/termd/core/telnet/TelnetConnection.flushData')
f(21,99,1,1,'io/termd/core/telnet/TelnetTtyConnection.onData')
f(22,99,1,1,'io/termd/core/io/BinaryDecoder.write')
f(23,99,1,1,'io/termd/core/io/BinaryDecoder.write')
f(24,99,1,1,'io/termd/core/tty/ReadBuffer.accept')
f(25,99,1,1,'io/termd/core/tty/ReadBuffer.accept')
f(26,99,1,1,'io/termd/core/tty/TtyEventDecoder.accept')
f(27,99,1,1,'io/termd/core/tty/TtyEventDecoder.accept')
f(28,99,1,1,'io/termd/core/readline/Readline$Interaction$2.accept')
f(29,99,1,1,'io/termd/core/readline/Readline$Interaction$2.accept')
f(30,99,1,1,'io/termd/core/readline/Readline.access$500')
f(31,99,1,1,'io/termd/core/readline/Readline.deliver')
f(32,99,1,1,'io/termd/core/readline/Readline$Interaction.access$200')
f(33,99,1,1,'io/termd/core/readline/Readline$Interaction.handle')
f(34,99,1,0,'io/termd/core/readline/Readline$2.apply',0,0,1)
f(35,99,1,4,'InterpreterRuntime::ldc(JavaThread*, bool)')
f(2,100,133,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$CooperativeWorker.run',0,1,0)
f(3,101,120,1,'com/hazelcast/internal/util/concurrent/BackoffIdleStrategy.idle')
f(4,101,120,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(5,101,120,1,'sun/misc/Unsafe.park')
f(6,101,120,3,'Unsafe_Park')
f(7,101,1,4,'JavaThreadParkedState::JavaThreadParkedState(JavaThread*, bool)')
f(7,102,117,4,'Parker::park(bool, long)')
f(8,103,10,3,'__gettimeofday')
f(8,113,102,3,'__psynch_cvwait')
f(8,215,1,3,'_pthread_cond_updateval')
f(8,216,2,3,'_pthread_cond_wait')
f(9,216,1,3,'_pthread_cond_updateval')
f(9,217,1,3,'pthread_testcancel')
f(8,218,1,3,'gettimeofday')
f(9,218,1,3,'__commpage_gettimeofday_internal')
f(10,218,1,3,'mach_absolute_time')
f(7,219,1,4,'YieldingFlexibleGangWorker::~YieldingFlexibleGangWorker()')
f(7,220,1,3,'pthread_cond_init')
f(3,221,1,6,'com/hazelcast/internal/util/counters/SwCounter$UnsafeSwCounter.inc',0,1,0)
f(3,222,2,1,'com/hazelcast/logging/AbstractLogger.isFinestEnabled')
f(4,222,2,1,'com/hazelcast/logging/impl/LoggingServiceImpl$DefaultLogger.isLoggable')
f(5,222,2,1,'com/hazelcast/logging/Log4j2Factory$Log4j2Logger.isLoggable')
f(6,222,2,1,'org/apache/logging/log4j/spi/AbstractLogger.isEnabled')
f(7,222,2,6,'org/apache/logging/log4j/core/Logger.isEnabled',0,2,0)
f(3,224,9,1,'java/util/concurrent/CopyOnWriteArrayList.forEach',0,1,0)
f(4,224,8,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$CooperativeWorker$$Lambda$1607/1577337565.accept',0,1,0)
f(5,225,7,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$CooperativeWorker.runTasklet')
f(6,225,6,1,'com/hazelcast/jet/impl/execution/StoreSnapshotTasklet.call')
f(7,225,6,1,'com/hazelcast/jet/impl/execution/StoreSnapshotTasklet.stateMachineStep',0,1,0)
f(8,226,5,1,'com/hazelcast/jet/impl/execution/ConcurrentInboundEdgeStream$RoundRobinDrain.drainTo',0,2,0)
f(9,228,1,6,'com/hazelcast/jet/impl/execution/ConcurrentInboundEdgeStream$RoundRobinDrain.drainQueue',0,1,0)
f(9,229,1,6,'java/util/ArrayList.forEach',0,1,0)
f(9,230,1,6,'java/util/BitSet.get',0,1,0)
f(6,231,1,6,'java/lang/Thread.setContextClassLoader',0,1,0)
f(4,232,1,3,'itable stub')
f(2,233,4,1,'com/mongodb/internal/connection/DefaultServerMonitor$ServerMonitorRunnable.run')
f(3,233,1,1,'com/mongodb/internal/connection/DefaultServer$DefaultServerStateListener.stateChanged')
f(4,233,1,1,'com/mongodb/internal/connection/SingleServerCluster$DefaultServerStateListener.serverDescriptionChanged')
f(5,233,1,1,'com/mongodb/internal/connection/SingleServerCluster.access$100')
f(6,233,1,1,'com/mongodb/internal/connection/SingleServerCluster.publishDescription')
f(7,233,1,6,'com/mongodb/internal/connection/BaseCluster.updateDescription',0,1,0)
f(3,234,2,1,'com/mongodb/internal/connection/DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription')
f(4,234,2,1,'com/mongodb/internal/connection/CommandHelper.executeCommand')
f(5,234,2,1,'com/mongodb/internal/connection/CommandHelper.sendAndReceive')
f(6,234,2,1,'com/mongodb/internal/connection/InternalStreamConnection.sendAndReceive')
f(7,234,1,1,'com/mongodb/internal/connection/CommandMessage.encode')
f(8,234,1,1,'com/mongodb/internal/connection/RequestMessage.encode')
f(9,234,1,1,'com/mongodb/internal/connection/CommandMessage.encodeMessageBodyWithMetadata')
f(10,234,1,6,'com/mongodb/internal/connection/CommandMessage.getExtraElements',0,1,0)
f(7,235,1,1,'com/mongodb/internal/connection/InternalStreamConnection.receiveCommandMessageResponse')
f(8,235,1,1,'com/mongodb/internal/connection/InternalStreamConnection.updateSessionContext')
f(9,235,1,1,'com/mongodb/internal/connection/ProtocolHelper.getOperationTime')
f(10,235,1,1,'com/mongodb/internal/connection/ProtocolHelper.getField')
f(11,235,1,6,'org/bson/BsonBinaryReader.readBsonType',0,1,0)
f(12,235,1,2,'org/bson/io/ByteBufferBsonInput.skipCString',1,0,0)
f(13,235,1,2,'org/bson/io/ByteBufferBsonInput.readUntilNullByte',1,0,0)
f(14,235,1,2,'org/bson/io/ByteBufferBsonInput.readByte',1,0,0)
f(3,236,1,1,'com/mongodb/internal/connection/DefaultServerMonitor$ServerMonitorRunnable.waitForNext')
f(4,236,1,1,'com/mongodb/internal/connection/DefaultServerMonitor$ServerMonitorRunnable.waitForSignalOrTimeout')
f(5,236,1,6,'java/util/concurrent/locks/ReentrantLock.unlock',0,1,0)
f(2,237,2,1,'io/netty/util/concurrent/FastThreadLocalRunnable.run')
f(3,237,1,1,'io/netty/util/concurrent/SingleThreadEventExecutor$5.run')
f(4,237,1,1,'io/netty/channel/nio/NioEventLoop.run')
f(5,237,1,6,'io/netty/channel/nio/NioEventLoop.select',0,1,0)
f(3,238,1,1,'io/netty/util/internal/ObjectCleaner$1.run')
f(4,238,1,6,'java/lang/ref/ReferenceQueue.remove',0,1,0)
f(2,239,3756,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(3,239,3756,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(4,239,1,1,'com/zaxxer/hikari/pool/HikariPool$$Lambda$2046/967224830.run')
f(5,239,1,1,'com/zaxxer/hikari/pool/HikariPool.lambda$closeConnection$1')
f(6,239,1,1,'com/zaxxer/hikari/pool/PoolBase.quietlyCloseConnection')
f(7,239,1,1,'oracle/jdbc/driver/PhysicalConnection.close')
f(8,239,1,1,'oracle/jdbc/driver/T4CConnection.logoff')
f(9,239,1,1,'oracle/jdbc/driver/T4C7Ocommoncall.doOLOGOFF')
f(10,239,1,1,'oracle/jdbc/driver/T4CTTIfun.doRPC')
f(11,239,1,6,'oracle/jdbc/driver/T4CTTIfun.receive',0,1,0)
f(4,240,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx$$Lambda$2381/1045511461.run')
f(5,240,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx.lambda$start$1')
f(6,240,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx.checkJoin')
f(7,240,1,1,'com/google/common/collect/Queues.drain')
f(8,240,1,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(9,240,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(10,240,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(11,240,1,1,'sun/misc/Unsafe.park')
f(12,240,1,3,'Unsafe_Park')
f(13,240,1,4,'Parker::park(bool, long)')
f(14,240,1,3,'__psynch_cvwait')
f(4,241,3674,1,'java/util/concurrent/FutureTask.run')
f(5,241,3674,1,'java/util/concurrent/FutureTask.run$$$capture')
f(6,241,5,1,'io/tapdata/observable/logging/appender/AppenderFactory$$Lambda$2243/1943097246.call')
f(7,241,5,1,'io/tapdata/observable/logging/appender/AppenderFactory.lambda$new$3')
f(8,241,5,1,'io/tapdata/observable/logging/appender/AppenderFactory.readMessageFromCacheQueue')
f(9,241,5,1,'java/util/concurrent/Semaphore.tryAcquire')
f(10,241,5,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.tryAcquireSharedNanos')
f(11,241,5,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.doAcquireSharedNanos',0,2,0)
f(12,242,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer$Node.predecessor',1,0,0)
f(12,243,3,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(13,243,3,1,'sun/misc/Unsafe.park')
f(14,243,3,3,'Unsafe_Park')
f(15,243,3,4,'Parker::park(bool, long)')
f(16,243,3,3,'__psynch_cvwait')
f(6,246,3,1,'io/tapdata/observable/logging/appender/AppenderFactory$$Lambda$2244/1935465477.call')
f(7,246,3,1,'io/tapdata/observable/logging/appender/AppenderFactory.lambda$new$4')
f(8,246,3,1,'io/tapdata/observable/logging/appender/AppenderFactory.readMessageFromCacheQueue',0,1,0)
f(9,247,2,1,'java/util/concurrent/Semaphore.tryAcquire')
f(10,247,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.tryAcquireSharedNanos')
f(11,247,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.doAcquireSharedNanos')
f(12,247,2,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(13,247,2,1,'sun/misc/Unsafe.park')
f(14,248,1,3,'Unsafe_Park')
f(15,248,1,4,'Parker::park(bool, long)')
f(16,248,1,3,'__psynch_cvwait')
f(6,249,3666,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(7,249,51,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$BlockingWorker.run')
f(8,249,14,1,'com/hazelcast/internal/util/concurrent/BackoffIdleStrategy.idle',0,1,0)
f(9,250,13,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(10,250,13,1,'sun/misc/Unsafe.park')
f(11,250,13,3,'Unsafe_Park')
f(12,250,13,4,'Parker::park(bool, long)')
f(13,250,1,3,'__gettimeofday')
f(13,251,12,3,'__psynch_cvwait')
f(8,263,37,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.call')
f(9,263,37,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.stateMachineStep')
f(10,263,36,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.complete')
f(11,263,36,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.complete',0,1,0)
f(12,264,2,6,'com/google/common/collect/Queues.drain',0,1,0)
f(13,265,1,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(14,265,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(15,265,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.checkInterruptWhileWaiting')
f(16,265,1,1,'java/lang/Thread.interrupted')
f(17,265,1,1,'java/lang/Thread.isInterrupted')
f(12,266,33,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.batchTransformToTapValue')
f(13,266,33,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.transformToTapValue')
f(14,266,33,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.tapRecordToTapValue')
f(15,266,33,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.toTapValue')
f(16,266,33,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.transformToTapValueMap')
f(17,266,33,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.transformToTapValueMap')
f(18,266,2,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.getOrInitTransformToTapValueFieldWrapper')
f(19,266,2,1,'com/google/common/cache/LocalCache$LocalManualCache.get')
f(20,266,2,1,'com/google/common/cache/LocalCache.get')
f(21,266,2,6,'com/google/common/cache/LocalCache$Segment.get',0,1,0)
f(22,267,1,1,'com/google/common/cache/LocalCache$Segment.getLiveValue')
f(23,267,1,1,'com/google/common/cache/LocalCache.isExpired')
f(24,267,1,6,'com/google/common/cache/LocalCache$StrongAccessEntry.getAccessTime',0,1,0)
f(18,268,31,1,'io/tapdata/entity/codec/filter/impl/AllLayerMapIterator.iterate',0,2,0)
f(19,269,26,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced$$Lambda$3113/658150783.filter')
f(20,269,26,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.lambda$transformToTapValueMap$0',0,7,0)
f(21,276,6,1,'io/tapdata/connector/oracle/OracleConnector$$Lambda$2000/445001180.toTapValue')
f(22,276,6,1,'io/tapdata/connector/oracle/OracleConnector.lambda$registerCapabilities$9',0,1,0)
f(23,276,6,1,'oracle/sql/TIMESTAMP.toLocalDateTime',1,0,0)
f(24,276,2,1,'java/sql/Timestamp.toLocalDateTime')
f(25,276,1,1,'java/time/LocalDateTime.of')
f(26,276,1,6,'java/time/LocalTime.of',0,1,0)
f(27,276,1,2,'java/time/temporal/ChronoField.checkValidValue',1,0,0)
f(28,276,1,2,'java/time/temporal/ValueRange.checkValidValue',1,0,0)
f(25,277,1,1,'java/util/Date.getYear')
f(26,277,1,6,'java/util/Date.normalize',0,1,0)
f(24,278,4,1,'oracle/sql/TIMESTAMP.timestampValue',1,0,0)
f(25,279,3,1,'oracle/sql/TIMESTAMP.toTimestamp')
f(26,279,3,1,'oracle/sql/TIMESTAMP.toTimestamp')
f(27,279,1,6,'java/util/Calendar.clear',0,1,0)
f(27,280,1,1,'java/util/Calendar.getInstance')
f(28,280,1,1,'java/util/Calendar.createCalendar')
f(29,280,1,6,'sun/util/locale/provider/JRELocaleProviderAdapter.getCalendarProvider',0,1,0)
f(27,281,1,1,'java/util/Calendar.getTime')
f(28,281,1,1,'java/util/Calendar.getTimeInMillis')
f(29,281,1,1,'java/util/Calendar.updateTime')
f(30,281,1,6,'java/util/GregorianCalendar.computeTime',0,1,0)
f(21,282,2,1,'io/tapdata/entity/codec/TapCodecsRegistry.getCustomToTapValueCodec')
f(22,282,2,6,'java/util/concurrent/ConcurrentHashMap.get',0,2,0)
f(23,283,1,3,'vtable stub')
f(21,284,1,6,'io/tapdata/entity/codec/filter/TapCodecsFilterManager.getValueCodec',0,1,0)
f(21,285,10,1,'io/tapdata/entity/codec/filter/entity/TransformToTapValueFieldWrapper.getField')
f(22,285,3,6,'java/util/ArrayList.get',0,3,0)
f(22,288,7,6,'java/util/ArrayList.size',0,7,0)
f(19,295,1,3,'itable stub')
f(19,296,1,6,'java/util/HashMap$Node.getValue',0,1,0)
f(19,297,1,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(20,297,1,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(21,297,1,6,'java/util/LinkedHashMap$LinkedHashIterator.nextNode',0,1,0)
f(19,298,1,6,'java/util/LinkedHashMap$LinkedHashIterator.hasNext',0,1,0)
f(10,299,1,6,'java/util/ArrayDeque.isEmpty',0,1,0)
f(7,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode$$Lambda$2992/945890163.run')
f(8,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.startSourceRunner')
f(9,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.doSnapshotWithControl')
f(10,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/controller/SnapshotOrderController.runWithControl')
f(11,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$2994/441002518.run')
f(12,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2')
f(13,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.doSnapshot')
f(14,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.doSnapshotInvoke')
f(15,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/HazelcastBaseNode.executeDataFuncAspect')
f(16,300,848,1,'io/tapdata/aspect/utils/AspectUtils.executeDataFuncAspect')
f(17,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3014/998296398.accept')
f(18,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12')
f(19,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invoke')
f(20,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethod')
f(21,300,848,1,'io/tapdata/pdk/core/utils/RetryUtils.autoRetry')
f(22,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3028/980981694.run')
f(23,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$invokePDKMethod$11')
f(24,300,848,1,'io/tapdata/pdk/core/api/Node.applyClassLoaderContext')
f(25,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3029/333708744.run')
f(26,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$null$10')
f(27,300,848,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethodPrivate')
f(28,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3026/2084335851.run')
f(29,300,848,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$null$11')
f(30,300,848,1,'io/tapdata/connector/oracle/OracleConnector$$Lambda$1977/1066631167.batchRead')
f(31,300,848,1,'io/tapdata/common/CommonDbConnector.batchReadWithoutOffset')
f(32,300,848,1,'io/tapdata/common/CommonDbConnector.batchReadWithoutHashSplit')
f(33,300,848,1,'io/tapdata/common/JdbcContext.query',0,0,2)
f(34,300,2,4,'SharedRuntime::ldiv(long, long)')
f(34,302,846,1,'io/tapdata/common/CommonDbConnector$$Lambda$3032/1315683681.accept')
f(35,302,846,1,'io/tapdata/common/CommonDbConnector.lambda$batchReadWithoutHashSplit$30')
f(36,302,84,1,'com/zaxxer/hikari/pool/HikariProxyResultSet.next')
f(37,302,84,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.next')
f(38,302,84,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.absoluteInternal')
f(39,302,84,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.fetchNextRows')
f(40,302,84,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.fetchMoreRows')
f(41,302,84,1,'oracle/jdbc/driver/OracleStatement.fetchMoreRows')
f(42,302,84,1,'oracle/jdbc/driver/T4CStatement.fetch')
f(43,302,84,1,'oracle/jdbc/driver/T4CStatement.doOall8')
f(44,302,84,1,'oracle/jdbc/driver/T4C8Oall.doOALL')
f(45,302,84,1,'oracle/jdbc/driver/T4CTTIfun.doRPC')
f(46,302,84,1,'oracle/jdbc/driver/T4CTTIfun.receive')
f(47,302,82,1,'oracle/jdbc/driver/T4C8Oall.readRXD')
f(48,302,82,1,'oracle/jdbc/driver/T4CTTIrxd.unmarshal')
f(49,302,82,1,'oracle/jdbc/driver/T4CTTIrxd.unmarshal',0,2,0)
f(50,304,77,1,'oracle/jdbc/driver/T4CClobAccessor.unmarshalOneRow')
f(51,304,76,1,'oracle/jdbc/driver/T4CClobAccessor.unmarshalBytes')
f(52,304,3,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalCLR')
f(53,304,3,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalBuffer')
f(54,304,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalNBytes')
f(55,304,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.getNBytes')
f(56,304,2,6,'java/nio/HeapByteBuffer.get',0,2,0)
f(57,305,1,3,'jbyte_disjoint_arraycopy')
f(56,306,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(57,306,1,1,'oracle/net/ns/NIONSDataChannel.readDataFromSocketChannel')
f(58,306,1,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(59,306,1,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(60,306,1,1,'oracle/net/ns/NIOPacket.readPacketFromSocketChannel')
f(61,306,1,1,'oracle/net/ns/NIOPacket.readPayload')
f(62,306,1,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(63,306,1,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(64,306,1,1,'sun/nio/ch/SocketChannelImpl.read')
f(65,306,1,1,'sun/nio/ch/IOUtil.read')
f(66,306,1,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(67,306,1,1,'sun/nio/ch/SocketDispatcher.read')
f(68,306,1,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(69,306,1,3,'read')
f(52,307,73,1,'oracle/jdbc/driver/T4CClobAccessor.unmarshalPrefetchData',0,1,0)
f(53,308,71,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalCLR')
f(54,308,66,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalBuffer')
f(55,308,66,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalNBytes',0,1,0)
f(56,309,65,1,'oracle/jdbc/driver/T4CMAREngineNIO.getNBytes',0,2,0)
f(57,311,11,6,'java/nio/HeapByteBuffer.get',0,9,0)
f(58,312,2,6,'java/nio/Buffer.position',0,2,0)
f(58,314,8,3,'jbyte_disjoint_arraycopy')
f(57,322,52,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(58,322,52,1,'oracle/net/ns/NIONSDataChannel.readDataFromSocketChannel',0,1,0)
f(59,323,51,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(60,323,51,1,'oracle/net/ns/NIOPacket.readFromSocketChannel',0,1,0)
f(61,324,50,1,'oracle/net/ns/NIOPacket.readPacketFromSocketChannel')
f(62,324,3,1,'oracle/net/ns/NIOPacket.readHeader')
f(63,324,3,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(64,324,3,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(65,324,3,1,'sun/nio/ch/SocketChannelImpl.read')
f(66,324,3,1,'sun/nio/ch/IOUtil.read')
f(67,324,3,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(68,324,3,1,'sun/nio/ch/SocketDispatcher.read')
f(69,324,3,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(70,324,3,3,'read')
f(62,327,47,1,'oracle/net/ns/NIOPacket.readPayload')
f(63,327,47,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(64,327,47,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(65,327,1,1,'oracle/net/nt/TimeoutSocketChannel.cancelTimeout')
f(66,327,1,1,'oracle/net/nt/TimeoutInterruptHandler.cancelInterrupt')
f(67,327,1,1,'java/util/Timer.purge')
f(68,327,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(69,327,1,4,'JfrBackend::is_event_enabled(TraceEventId)')
f(65,328,4,1,'oracle/net/nt/TimeoutSocketChannel.scheduleInterrupt')
f(66,328,4,1,'oracle/net/nt/TimeoutInterruptHandler.scheduleInterrupt')
f(67,328,2,1,'java/util/Timer.schedule')
f(68,328,2,1,'java/util/Timer.sched')
f(69,328,1,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(70,328,1,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(71,328,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(69,329,1,6,'java/util/TaskQueue.add',0,1,0)
f(67,330,1,1,'java/util/concurrent/ConcurrentHashMap.get')
f(68,330,1,1,'java/lang/Object.hashCode')
f(69,330,1,3,'JVM_IHashCode')
f(70,330,1,3,'ReadStableMark(oopDesc*)')
f(67,331,1,1,'java/util/concurrent/ConcurrentHashMap.put')
f(68,331,1,6,'java/util/concurrent/ConcurrentHashMap.putVal',0,1,0)
f(65,332,42,1,'sun/nio/ch/SocketChannelImpl.read')
f(66,332,1,6,'java/nio/channels/spi/AbstractInterruptibleChannel.begin',0,1,0)
f(67,332,1,2,'java/lang/Thread.isInterrupted',1,0,0)
f(66,333,1,6,'java/nio/channels/spi/AbstractInterruptibleChannel.end',0,1,0)
f(66,334,40,1,'sun/nio/ch/IOUtil.read')
f(67,334,3,1,'java/nio/HeapByteBuffer.put')
f(68,334,3,1,'java/nio/DirectByteBuffer.get')
f(69,334,2,1,'java/nio/Bits.copyToArray')
f(70,334,2,1,'sun/misc/Unsafe.copyMemory')
f(71,335,1,3,'acl_CopyRight')
f(69,336,1,6,'java/nio/Buffer.position',0,1,0)
f(67,337,35,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(68,337,35,1,'sun/nio/ch/SocketDispatcher.read',0,1,0)
f(69,338,34,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(70,338,34,3,'read')
f(67,372,1,1,'sun/nio/ch/Util.getTemporaryDirectBuffer')
f(68,372,1,6,'sun/nio/ch/Util$BufferCache.get',0,1,0)
f(67,373,1,1,'sun/nio/ch/Util.offerFirstTemporaryDirectBuffer')
f(68,373,1,6,'sun/nio/ch/Util$BufferCache.offerFirst',0,1,0)
f(54,374,5,6,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB1',0,3,0)
f(55,377,2,6,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall',0,2,0)
f(53,379,1,1,'oracle/jdbc/driver/T4CMAREngine.unmarshalSB8')
f(54,379,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.buffer2Value')
f(55,379,1,6,'java/nio/HeapByteBuffer.getShort',0,1,0)
f(51,380,1,6,'oracle/jdbc/driver/T4CClobAccessor.unmarshalColumnMetadata',0,1,0)
f(50,381,2,6,'oracle/jdbc/driver/T4CTTIrxd.copyRowsAsNeeded',0,2,0)
f(50,383,1,1,'oracle/jdbc/driver/T4CVarcharAccessor.unmarshalOneRow')
f(51,383,1,1,'oracle/jdbc/driver/T4CMarshaller$BasicMarshaller.unmarshalOneRow')
f(52,383,1,1,'oracle/jdbc/driver/T4CMarshaller$BasicMarshaller.unmarshalBytes')
f(53,383,1,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalCLR')
f(54,383,1,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalBuffer')
f(55,383,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalNBytes')
f(56,383,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.getNBytes')
f(57,383,1,6,'java/nio/HeapByteBuffer.get',0,1,0)
f(58,383,1,3,'jbyte_disjoint_arraycopy')
f(47,384,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB1')
f(48,384,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(49,384,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.flush')
f(50,384,1,1,'oracle/net/ns/NIONSDataChannel.writeDataToSocketChannel')
f(51,384,1,1,'oracle/net/ns/NIONSDataChannel.writeDataToSocketChannel')
f(52,384,1,1,'oracle/net/ns/NIOPacket.writeToSocketChannel')
f(53,384,1,1,'oracle/net/nt/TimeoutSocketChannel.write')
f(54,384,1,1,'sun/nio/ch/SocketChannelImpl.write')
f(55,384,1,1,'sun/nio/ch/IOUtil.write')
f(56,384,1,1,'sun/nio/ch/IOUtil.writeFromNativeBuffer')
f(57,384,1,1,'sun/nio/ch/SocketDispatcher.write')
f(58,384,1,1,'sun/nio/ch/FileDispatcherImpl.write0')
f(59,384,1,3,'write')
f(47,385,1,1,'oracle/jdbc/driver/T4CTTIoer11.unmarshal')
f(48,385,1,1,'oracle/jdbc/driver/T4CTTIoer11.unmarshalAttributes')
f(49,385,1,6,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalSB1',0,1,0)
f(36,386,331,1,'io/tapdata/connector/oracle/OracleConnector.processDataMap',0,3,0)
f(37,388,1,6,'java/util/HashMap$Node.getValue',0,1,0)
f(37,389,2,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(38,389,2,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(39,389,2,6,'java/util/LinkedHashMap$LinkedHashIterator.nextNode',0,2,0)
f(37,391,326,1,'oracle/sql/CLOB.stringValue',1,0,0)
f(38,392,325,1,'oracle/jdbc/driver/OracleClob.stringValue',0,144,0)
f(39,536,78,1,'java/io/Reader.read')
f(40,536,78,1,'oracle/jdbc/driver/OracleClobReader.read')
f(41,536,78,1,'oracle/jdbc/driver/OracleClobReader.needChars')
f(42,536,8,1,'oracle/jdbc/driver/OracleClob.getChars',0,1,0)
f(43,536,1,1,'oracle/jdbc/driver/OracleClob.getDBAccess')
f(44,536,1,1,'oracle/jdbc/driver/PhysicalConnection.isClosed')
f(45,536,1,6,'oracle/jdbc/internal/Monitor.acquireCloseableLock',0,1,0)
f(46,536,1,3,'itable stub')
f(43,537,6,1,'oracle/jdbc/driver/T4CConnection.getChars')
f(44,537,5,6,'oracle/jdbc/driver/T4CConnection.copyPrefetchedClobChars',0,5,0)
f(45,537,5,3,'jshort_disjoint_arraycopy')
f(44,542,1,1,'oracle/jdbc/internal/Monitor$CloseableLock.close')
f(45,542,1,6,'java/util/concurrent/locks/ReentrantLock.unlock',0,1,0)
f(43,543,1,2,'oracle/sql/DatumWithConnection.getConnectionDuringExceptionHandling',1,0,0)
f(44,543,1,2,'oracle/sql/DatumWithConnection.getConnectionDuringExceptionHandling',1,0,0)
f(42,544,70,1,'oracle/jdbc/driver/PhysicalConnection.getCharBuffer')
f(43,544,70,1,'oracle/jdbc/driver/BufferCache.get')
f(44,544,70,1,'java/lang/reflect/Array.newInstance')
f(45,544,70,1,'java/lang/reflect/Array.newArray')
f(46,545,69,3,'JVM_NewArray')
f(47,545,1,4,'JNIHandles::make_local(JNIEnv_*, oopDesc*)')
f(48,545,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(47,546,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(47,547,67,4,'TypeArrayKlass::allocate_common(int, bool, Thread*)')
f(48,547,67,4,'CollectedHeap::array_allocate(KlassHandle, int, int, Thread*)')
f(49,612,1,4,'CollectedHeap::common_mem_allocate_noinit(KlassHandle, unsigned long, Thread*)')
f(50,612,1,4,'CollectedHeap::allocate_from_tlab_slow(KlassHandle, Thread*, unsigned long)')
f(51,612,1,4,'ThreadLocalAllocBuffer::make_parsable(bool)')
f(52,612,1,4,'CollectedHeap::fill_with_object_impl(HeapWord*, unsigned long, bool)')
f(49,613,1,4,'oopDesc::size()')
f(39,614,96,6,'java/io/StringWriter.<init>',0,96,0)
f(39,710,4,1,'java/lang/StringBuffer.substring')
f(40,710,4,1,'java/lang/StringBuffer.substring')
f(41,710,4,1,'java/lang/AbstractStringBuilder.substring')
f(42,710,4,1,'java/lang/String.<init>')
f(43,710,4,6,'java/util/Arrays.copyOfRange',0,4,0)
f(44,710,4,2,'java/lang/StringBuilder.append',4,0,0)
f(39,714,3,1,'oracle/jdbc/driver/OracleClob.getCharacterStream')
f(40,714,1,6,'oracle/jdbc/driver/OracleClob.canReadBasicLobDataInLocator',0,1,0)
f(40,715,2,1,'oracle/jdbc/driver/OracleClob.getDBAccess')
f(41,715,2,1,'oracle/jdbc/driver/PhysicalConnection.isClosed')
f(42,715,2,1,'oracle/jdbc/internal/Monitor.acquireCloseableLock')
f(43,715,2,1,'oracle/jdbc/internal/Monitor.acquireLock')
f(44,715,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(45,715,1,6,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock',0,1,0)
f(44,716,1,6,'oracle/jdbc/OracleConnectionWrapper.getMonitorLock',0,1,0)
f(36,717,10,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3030/236230606.accept')
f(37,717,10,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$null$7')
f(38,717,4,1,'io/tapdata/aspect/utils/AspectUtils.accept')
f(39,717,4,1,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError')
f(40,717,4,1,'io/tapdata/aspect/utils/AspectUtils$$Lambda$3089/399383540.run')
f(41,717,4,1,'io/tapdata/aspect/utils/AspectUtils.lambda$accept$0')
f(42,717,2,1,'io/tapdata/aspect/BatchReadFuncAspect$$Lambda$3019/1957290875.accept')
f(43,717,2,1,'io/tapdata/aspect/BatchReadFuncAspect.lambda$readCompleteConsumer$1')
f(44,717,1,1,'io/tapdata/observable/logging/LoggingAspectTask$$Lambda$3024/785151411.accept')
f(45,717,1,1,'io/tapdata/observable/logging/LoggingAspectTask.lambda$handleBatchReadFunc$7')
f(46,717,1,1,'io/tapdata/observable/logging/LoggingAspectTask.debug')
f(47,717,1,1,'io/tapdata/observable/logging/LoggingAspectTask.noNeedLog')
f(48,717,1,1,'io/tapdata/observable/logging/TaskLogger.noNeedLog')
f(49,717,1,1,'io/tapdata/observable/logging/LogLevel.shouldLog')
f(50,717,1,1,'io/tapdata/observable/logging/LogLevel.getLogLevel')
f(51,717,1,1,'java/lang/String.toUpperCase')
f(52,717,1,6,'java/lang/String.toUpperCase',0,1,0)
f(44,718,1,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3018/2122329294.accept')
f(45,718,1,1,'io/tapdata/observable/metric/ObservableAspectTask.lambda$handleBatchReadFunc$11')
f(46,718,1,1,'io/tapdata/observable/metric/util/SyncGetMemorySizeHandler.getEventTypeRecorderSyncTapEvent')
f(47,718,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countTapEvent')
f(48,718,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countTapEvent')
f(49,718,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countEventTypeAndGetReferenceTime')
f(50,718,1,6,'io/tapdata/observable/metric/handler/HandlerUtil.setEventTimestamp',0,1,0)
f(42,719,1,1,'io/tapdata/aspect/BatchReadFuncAspect$$Lambda$3021/846856486.accept')
f(43,719,1,1,'io/tapdata/aspect/BatchReadFuncAspect.lambda$processCompleteConsumer$2')
f(44,719,1,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3020/1271801973.accept')
f(45,719,1,1,'io/tapdata/observable/metric/ObservableAspectTask.lambda$handleBatchReadFunc$12')
f(46,719,1,1,'io/tapdata/observable/metric/handler/ObservableAspectTaskUtil.batchReadProcessComplete')
f(47,719,1,1,'java/util/concurrent/CompletableFuture.thenRun')
f(48,719,1,1,'java/util/concurrent/CompletableFuture.uniRunStage')
f(49,719,1,1,'java/util/concurrent/CompletableFuture.uniRun')
f(50,719,1,1,'io/tapdata/observable/metric/handler/ObservableAspectTaskUtil$$Lambda$3102/1179847635.run')
f(51,719,1,6,'io/tapdata/observable/metric/handler/ObservableAspectTaskUtil.lambda$batchReadProcessComplete$7',0,1,0)
f(42,720,1,1,'io/tapdata/aspect/BatchReadFuncAspect$$Lambda$3023/1986673927.accept')
f(43,720,1,1,'io/tapdata/aspect/BatchReadFuncAspect.lambda$enqueuedConsumer$0')
f(44,720,1,6,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3022/1306766843.accept',0,1,0)
f(38,721,2,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.wrapTapdataEvent')
f(39,721,2,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.wrapTapdataEvent',0,1,0)
f(40,722,1,6,'java/util/ArrayList.<init>',0,1,0)
f(38,723,3,1,'io/tapdata/observable/metric/handler/HandlerUtil.sampleMemoryToTapEvent')
f(39,723,2,1,'io/tapdata/observable/metric/handler/RandomSampleEventHandler.sampleMemoryTapEvent')
f(40,723,2,1,'io/tapdata/observable/metric/handler/RandomSampleEventHandler.sizeOfTapEvent')
f(41,723,1,6,'io/tapdata/flow/engine/V2/util/TapEventUtil.getBefore',0,1,0)
f(41,724,1,1,'io/tapdata/observable/metric/handler/RandomSampleEventHandler.sizeOfDataMap')
f(42,724,1,1,'org/apache/lucene/util/RamUsageEstimator.sizeOfMap')
f(43,724,1,1,'org/apache/lucene/util/RamUsageEstimator.sizeOfMap')
f(44,724,1,1,'org/apache/lucene/util/RamUsageEstimator.shallowSizeOf')
f(45,724,1,1,'org/apache/lucene/util/RamUsageEstimator.shallowSizeOfInstance')
f(46,724,1,1,'java/security/AccessController.doPrivileged')
f(47,724,1,3,'JVM_DoPrivileged')
f(48,724,1,4,'JNIHandleBlock::allocate_handle(oopDesc*)')
f(39,725,1,1,'java/util/stream/ReferencePipeline.forEach')
f(40,725,1,1,'java/util/stream/AbstractPipeline.evaluate')
f(41,725,1,1,'java/util/stream/ForEachOps$ForEachOp$OfRef.evaluateSequential')
f(42,725,1,1,'java/util/stream/ForEachOps$ForEachOp.evaluateSequential')
f(43,725,1,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(44,725,1,1,'java/util/stream/AbstractPipeline.copyInto')
f(45,725,1,1,'java/util/ArrayList$ArrayListSpliterator.forEachRemaining')
f(46,725,1,6,'java/util/stream/ReferencePipeline$2$1.accept',0,1,0)
f(47,725,1,3,'itable stub')
f(38,726,1,1,'java/util/ArrayList.forEach')
f(39,726,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3108/954336029.accept')
f(40,726,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.enqueue',0,1,0)
f(36,727,421,1,'io/tapdata/kit/DbKit.getRowFromResultSet')
f(37,727,417,1,'com/zaxxer/hikari/pool/HikariProxyResultSet.getObject')
f(38,727,417,1,'oracle/jdbc/driver/GeneratedScrollableResultSet.getObject')
f(39,727,414,1,'oracle/jdbc/driver/GeneratedStatement.getObject')
f(40,727,1,6,'oracle/jdbc/driver/OracleStatement.physicalRowIndex',0,1,0)
f(40,728,166,1,'oracle/jdbc/driver/T4CClobAccessor.getObject',0,1,0)
f(41,729,165,1,'oracle/jdbc/driver/ClobAccessor.getObject')
f(42,729,165,1,'oracle/jdbc/driver/ClobAccessor.getCLOB')
f(43,729,4,1,'oracle/jdbc/driver/Accessor.getBytesInternal')
f(44,729,4,1,'oracle/jdbc/driver/ByteArray.get')
f(45,729,4,6,'oracle/jdbc/driver/DynamicByteArray.get',0,4,0)
f(46,730,3,3,'jbyte_disjoint_arraycopy')
f(43,733,161,1,'oracle/jdbc/driver/ClobAccessor.getCLOB_')
f(44,733,161,1,'oracle/jdbc/driver/ClobAccessor.getPrefetchedCharData')
f(45,733,161,1,'oracle/jdbc/driver/DynamicByteArray.getChars',0,9,0)
f(46,740,2,3,'jshort_disjoint_arraycopy')
f(46,742,152,1,'oracle/jdbc/driver/DynamicByteArray.getString',0,1,0)
f(47,743,151,1,'oracle/jdbc/driver/DynamicByteArray.getStringFromAL16UTF16')
f(48,743,5,1,'java/lang/String.<init>')
f(49,743,5,6,'java/util/Arrays.copyOfRange',0,5,0)
f(50,743,5,2,'java/lang/StringBuilder.append',5,0,0)
f(51,746,2,3,'jshort_disjoint_arraycopy')
f(48,748,146,1,'oracle/jdbc/driver/DynamicByteArray.getCharsFromAL16UTF16',0,22,0)
f(49,770,123,6,'oracle/jdbc/driver/DynamicByteArray.next',0,123,0)
f(49,893,1,6,'oracle/jdbc/driver/DynamicByteArray.setGlobals',0,1,0)
f(50,893,1,4,'SharedRuntime::ldiv(long, long)')
f(40,894,2,6,'oracle/jdbc/driver/T4CTimestampAccessor.getObject',0,1,0)
f(41,895,1,1,'oracle/jdbc/driver/TimestampAccessor.getObject')
f(42,895,1,1,'oracle/jdbc/driver/DateTimeCommonAccessor.getTIMESTAMP')
f(43,895,1,1,'oracle/jdbc/driver/Accessor.getBytesInternal')
f(44,895,1,1,'oracle/jdbc/driver/ByteArray.get')
f(45,895,1,6,'oracle/jdbc/driver/DynamicByteArray.get',0,1,0)
f(40,896,245,1,'oracle/jdbc/driver/T4CVarcharAccessor.getObject',0,1,0)
f(41,897,244,1,'oracle/jdbc/driver/CharCommonAccessor.getObject')
f(42,897,244,1,'oracle/jdbc/driver/CharCommonAccessor.getString')
f(43,897,244,1,'oracle/jdbc/driver/DynamicByteArray.getString')
f(44,897,244,6,'oracle/jdbc/driver/DynamicByteArray.getStringFromAL32UTF8',0,229,0)
f(45,1126,14,1,'java/lang/String.<init>')
f(46,1126,14,6,'java/util/Arrays.copyOfRange',0,14,0)
f(47,1126,14,2,'java/lang/StringBuilder.append',14,0,0)
f(48,1137,3,3,'jshort_disjoint_arraycopy')
f(45,1140,1,6,'oracle/jdbc/driver/DynamicByteArray.next',0,1,0)
f(39,1141,3,6,'oracle/jdbc/internal/Monitor.acquireCloseableLock',0,2,0)
f(40,1141,2,3,'itable stub')
f(40,1143,1,6,'oracle/jdbc/internal/Monitor.acquireLock',0,1,0)
f(41,1143,1,3,'itable stub')
f(37,1144,1,1,'com/zaxxer/hikari/pool/HikariProxyResultSet.getRow')
f(38,1144,1,6,'oracle/jdbc/driver/InsensitiveScrollableResultSet.getRow',0,1,0)
f(39,1144,1,2,'oracle/jdbc/internal/Monitor$CloseableLock.close',1,0,0)
f(37,1145,3,1,'java/util/HashMap.put')
f(38,1145,3,1,'java/util/HashMap.putVal',0,1,0)
f(39,1145,1,6,'java/util/LinkedHashMap.afterNodeInsertion',0,1,0)
f(39,1146,1,6,'java/util/LinkedHashMap.newNode',0,1,0)
f(39,1147,1,3,'vtable stub')
f(7,1148,6,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2831/548323019.run')
f(8,1148,6,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.queueConsume')
f(9,1148,6,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.drainAndRun')
f(10,1148,2,1,'com/google/common/collect/Queues.drain')
f(11,1148,2,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(12,1148,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(13,1148,2,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(14,1148,2,1,'sun/misc/Unsafe.park')
f(15,1148,2,3,'Unsafe_Park')
f(16,1148,2,4,'Parker::park(bool, long)')
f(17,1148,2,3,'__psynch_cvwait')
f(10,1150,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2835/1970175363.run')
f(11,1150,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.processTargetEvents',0,1,0)
f(12,1151,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.fromTapValue',0,1,0)
f(12,1152,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.enqueue',0,1,0)
f(12,1153,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.replaceIllegalDateWithNullIfNeed',0,1,0)
f(7,1154,8,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2832/863808055.run')
f(8,1154,8,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.processQueueConsume')
f(9,1154,8,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.drainAndRun')
f(10,1154,3,1,'com/google/common/collect/Queues.drain')
f(11,1154,1,1,'java/util/concurrent/LinkedBlockingQueue.drainTo')
f(12,1154,1,6,'java/util/ArrayList.add',0,1,0)
f(13,1154,1,2,'java/util/ArrayList.ensureCapacityInternal',1,0,0)
f(14,1154,1,2,'java/util/ArrayList.ensureExplicitCapacity',1,0,0)
f(11,1155,2,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(12,1155,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(13,1155,2,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(14,1155,2,1,'sun/misc/Unsafe.park')
f(15,1155,2,3,'Unsafe_Park')
f(16,1155,2,4,'Parker::park(bool, long)')
f(17,1155,2,3,'__psynch_cvwait')
f(10,1157,5,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2839/399876831.run',0,1,0)
f(11,1158,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13')
f(12,1158,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.dispatchTapdataEvents')
f(13,1158,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$3082/909784505.accept')
f(14,1158,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.lambda$null$12')
f(15,1158,3,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.initialProcessEvents')
f(16,1158,3,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.process')
f(17,1158,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.generateWatermarkEvent')
f(18,1158,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.addToAllPartitions')
f(19,1158,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.offer2QueueIfRunning',0,1,0)
f(20,1158,1,2,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.isRunning',1,0,0)
f(21,1158,1,2,'java/util/concurrent/atomic/AtomicBoolean.get',1,0,0)
f(17,1159,2,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.processDML')
f(18,1159,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$2.partition',0,1,0)
f(18,1160,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.offer2QueueIfRunning',0,1,0)
f(15,1161,1,6,'java/util/ArrayList$Itr.hasNext',0,1,0)
f(7,1162,2750,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor$$Lambda$3397/860801381.run')
f(8,1162,2750,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.lambda$start$3')
f(9,1162,2750,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.partitionConsumer')
f(10,1162,5,1,'com/google/common/collect/Queues.drain')
f(11,1162,5,6,'java/util/concurrent/LinkedBlockingQueue.drainTo',0,4,0)
f(12,1166,1,1,'java/util/ArrayList.add')
f(13,1166,1,1,'java/util/ArrayList.ensureCapacityInternal')
f(14,1166,1,1,'java/util/ArrayList.ensureExplicitCapacity')
f(15,1166,1,1,'java/util/ArrayList.grow')
f(16,1166,1,1,'java/util/Arrays.copyOf')
f(17,1166,1,6,'java/util/Arrays.copyOf',0,1,0)
f(10,1167,2745,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/concurrent/PartitionConcurrentProcessor.processPartitionEvents')
f(11,1167,2745,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$3389/2092534763.accept')
f(12,1167,2745,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEvents')
f(13,1167,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEvent')
f(14,1167,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEvent')
f(15,1167,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEventDML')
f(16,1167,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.initAndGetExactlyOnceWriteLookupList')
f(17,1167,1,6,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent',0,1,0)
f(13,1168,2744,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.processTapEvents')
f(14,1168,2744,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.processEvents')
f(15,1168,2744,1,'java/util/HashMap.forEach')
f(16,1168,2744,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3116/476499187.accept')
f(17,1168,2744,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$processEvents$26')
f(18,1168,2744,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.writeRecord')
f(19,1168,2742,1,'io/tapdata/flow/engine/V2/node/hazelcast/HazelcastBaseNode.executeDataFuncAspect')
f(20,1168,2742,1,'io/tapdata/aspect/utils/AspectUtils.executeDataFuncAspect')
f(21,1168,2742,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3121/448406304.accept')
f(22,1168,2742,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$writeRecord$59')
f(23,1168,2742,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invoke')
f(24,1168,2742,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethod')
f(25,1168,2742,1,'io/tapdata/pdk/core/utils/RetryUtils.autoRetry')
f(26,1168,2742,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3028/980981694.run')
f(27,1168,2742,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$invokePDKMethod$11')
f(28,1168,2742,1,'io/tapdata/pdk/core/api/Node.applyClassLoaderContext')
f(29,1168,2742,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3029/333708744.run',0,1,0)
f(30,1169,2741,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$null$10')
f(31,1169,2741,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethodPrivate')
f(32,1169,2741,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3134/1430190202.run')
f(33,1169,2741,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$null$58')
f(34,1169,2741,1,'io/tapdata/flow/engine/V2/policy/PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl')
f(35,1169,2741,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3138/1548007300.apply')
f(36,1169,2741,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$null$57')
f(37,1169,2741,1,'io/tapdata/connector/kafka/KafkaConnector$$Lambda$2065/145413703.writeRecord')
f(38,1169,2741,1,'io/tapdata/connector/kafka/KafkaConnector.writeRecord')
f(39,1169,2741,1,'io/tapdata/connector/kafka/KafkaService.produce',0,2,0)
f(40,1171,1,6,'io/tapdata/connector/kafka/KafkaConnector$$Lambda$3139/353633961.get',0,1,0)
f(41,1171,1,2,'io/tapdata/base/ConnectorBase.isAlive',1,0,0)
f(42,1171,1,2,'java/lang/Thread.isInterrupted',1,0,0)
f(43,1171,1,2,'java/lang/Thread.isInterrupted',1,0,0)
f(44,1171,1,3,'JVM_IsInterrupted')
f(40,1172,15,1,'io/tapdata/connector/kafka/KafkaService.getKafkaMessageKey')
f(41,1172,9,1,'io/tapdata/pdk/core/api/impl/JsonParserImpl.toJsonBytes')
f(42,1172,9,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(43,1172,9,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(44,1172,9,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(45,1172,9,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(46,1172,9,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(47,1172,9,1,'com/alibaba/fastjson/JSON.toJSONBytes',0,1,0)
f(48,1173,6,1,'com/alibaba/fastjson/serializer/JSONSerializer.write',0,1,0)
f(49,1173,2,1,'com/alibaba/fastjson/serializer/JSONSerializer.getObjectWriter')
f(50,1173,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(51,1173,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(52,1173,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.get')
f(53,1173,2,1,'com/alibaba/fastjson/util/IdentityHashMap.get')
f(54,1173,2,1,'java/lang/System.identityHashCode')
f(55,1174,1,3,'JVM_IHashCode')
f(56,1174,1,4,'ObjectSynchronizer::FastHashCode(Thread*, oopDesc*)')
f(57,1174,1,3,'ReadStableMark(oopDesc*)')
f(49,1175,3,1,'com/alibaba/fastjson/serializer/StringCodec.write')
f(50,1175,3,1,'com/alibaba/fastjson/serializer/StringCodec.write')
f(51,1175,3,1,'com/alibaba/fastjson/serializer/SerializeWriter.writeString')
f(52,1175,3,6,'com/alibaba/fastjson/serializer/SerializeWriter.writeStringWithDoubleQuote',0,3,0)
f(49,1178,1,3,'itable stub')
f(48,1179,2,1,'com/alibaba/fastjson/serializer/SerializeWriter.toBytes')
f(49,1179,2,1,'com/alibaba/fastjson/serializer/SerializeWriter.encodeToUTF8Bytes')
f(50,1179,1,6,'com/alibaba/fastjson/util/IOUtils.encodeUTF8',0,1,0)
f(50,1180,1,1,'java/lang/ThreadLocal.get')
f(51,1180,1,6,'java/lang/ThreadLocal.getMap',0,1,0)
f(41,1181,2,6,'java/util/Collection.stream',0,2,0)
f(42,1182,1,3,'itable stub')
f(41,1183,4,1,'java/util/stream/ReferencePipeline.collect')
f(42,1183,3,1,'java/util/stream/AbstractPipeline.evaluate')
f(43,1183,3,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(44,1183,3,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(45,1183,2,1,'java/util/stream/AbstractPipeline.copyInto')
f(46,1183,1,1,'java/util/ArrayList$ArrayListSpliterator.forEachRemaining')
f(47,1183,1,1,'java/util/stream/ReferencePipeline$3$1.accept')
f(48,1183,1,1,'io/tapdata/connector/kafka/KafkaService$$Lambda$3141/1660881738.apply')
f(49,1183,1,1,'io/tapdata/connector/kafka/KafkaService.lambda$getKafkaMessageKey$8')
f(50,1183,1,6,'java/util/LinkedHashMap.get',0,1,0)
f(46,1184,1,1,'java/util/Spliterator.getExactSizeIfKnown')
f(47,1184,1,1,'java/util/ArrayList$ArrayListSpliterator.estimateSize')
f(48,1184,1,6,'java/util/ArrayList$ArrayListSpliterator.getFence',0,1,0)
f(45,1185,1,6,'java/util/stream/AbstractPipeline.wrapSink',0,1,0)
f(46,1185,1,3,'vtable stub')
f(42,1186,1,1,'java/util/stream/Collectors$$Lambda$28/691691381.apply')
f(43,1186,1,1,'java/util/StringJoiner.toString')
f(44,1186,1,1,'java/lang/StringBuilder.toString')
f(45,1186,1,1,'java/lang/String.<init>')
f(46,1186,1,6,'java/util/Arrays.copyOfRange',0,1,0)
f(47,1186,1,2,'java/lang/StringBuilder.append',1,0,0)
f(48,1186,1,3,'jshort_disjoint_arraycopy')
f(40,1187,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3135/1197100591.accept')
f(41,1187,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$null$53')
f(42,1187,1,1,'io/tapdata/aspect/utils/AspectUtils.accept')
f(43,1187,1,1,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError')
f(44,1187,1,1,'io/tapdata/aspect/utils/AspectUtils$$Lambda$3044/1800367162.run')
f(45,1187,1,1,'io/tapdata/aspect/utils/AspectUtils.lambda$accept$1')
f(46,1187,1,1,'io/tapdata/aspect/WriteRecordFuncAspect$$Lambda$3132/704833469.accept')
f(47,1187,1,1,'io/tapdata/aspect/WriteRecordFuncAspect.lambda$consumer$0')
f(48,1187,1,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3131/1979614046.accept')
f(49,1187,1,1,'io/tapdata/observable/metric/ObservableAspectTask.lambda$handleWriteRecordFunc$29')
f(50,1187,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx.runAsync')
f(51,1187,1,6,'io/tapdata/observable/metric/util/TapCompletableFutureEx.enqueue',0,1,0)
f(40,1188,1322,1,'io/tapdata/pdk/core/api/impl/JsonParserImpl.toJsonBytes')
f(41,1188,1322,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(42,1188,1322,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(43,1188,1322,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(44,1188,1322,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(45,1188,1322,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(46,1188,1322,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(47,1188,1007,1,'com/alibaba/fastjson/serializer/JSONSerializer.write',0,1,0)
f(48,1188,2,1,'com/alibaba/fastjson/serializer/JSONSerializer.getObjectWriter')
f(49,1188,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(50,1188,2,6,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter',0,1,0)
f(51,1189,1,1,'com/alibaba/fastjson/serializer/SerializeConfig.get')
f(52,1189,1,1,'com/alibaba/fastjson/util/IdentityHashMap.get')
f(53,1189,1,1,'java/lang/System.identityHashCode')
f(54,1189,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(48,1190,1004,1,'com/alibaba/fastjson/serializer/MapSerializer.write',0,1,0)
f(49,1191,1003,1,'com/alibaba/fastjson/serializer/MapSerializer.write',0,6,0)
f(50,1194,2,6,'com/alibaba/fastjson/serializer/DateCodec.write',0,1,0)
f(51,1195,1,6,'java/sql/Timestamp.getTime',0,1,0)
f(50,1196,1,6,'com/alibaba/fastjson/serializer/JSONSerializer.containsReference',0,1,0)
f(50,1197,4,2,'com/alibaba/fastjson/serializer/JSONSerializer.getObjectWriter',2,0,0)
f(51,1197,4,2,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter',2,0,0)
f(52,1199,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(53,1199,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.get')
f(54,1199,2,1,'com/alibaba/fastjson/util/IdentityHashMap.get')
f(55,1199,2,1,'java/lang/System.identityHashCode')
f(56,1199,2,3,'Java_java_lang_System_identityHashCode')
f(50,1201,1,1,'com/alibaba/fastjson/serializer/JSONSerializer.setContext')
f(51,1201,1,6,'com/alibaba/fastjson/serializer/JSONSerializer.setContext',0,1,0)
f(50,1202,2,6,'com/alibaba/fastjson/serializer/SerializeFilterable.processValue',0,2,0)
f(51,1203,1,2,'com/alibaba/fastjson/JSON.parse',1,0,0)
f(52,1203,1,2,'com/alibaba/fastjson/JSON.parse',1,0,0)
f(50,1204,1,6,'com/alibaba/fastjson/serializer/SerializeWriter.write',0,1,0)
f(50,1205,5,1,'com/alibaba/fastjson/serializer/SerializeWriter.writeFieldName')
f(51,1205,5,6,'com/alibaba/fastjson/serializer/SerializeWriter.writeStringWithDoubleQuote',0,4,0)
f(52,1209,1,6,'java/lang/String.getChars',0,1,0)
f(53,1209,1,3,'jshort_disjoint_arraycopy')
f(50,1210,971,1,'com/alibaba/fastjson/serializer/StringCodec.write')
f(51,1210,971,1,'com/alibaba/fastjson/serializer/StringCodec.write')
f(52,1210,971,1,'com/alibaba/fastjson/serializer/SerializeWriter.writeString')
f(53,1210,971,6,'com/alibaba/fastjson/serializer/SerializeWriter.writeStringWithDoubleQuote',0,913,0)
f(54,2123,58,6,'java/lang/String.getChars',0,58,0)
f(55,2123,58,3,'jshort_disjoint_arraycopy')
f(50,2181,1,3,'itable stub')
f(50,2182,1,6,'java/util/HashMap$Node.getValue',0,1,0)
f(50,2183,10,1,'java/util/LinkedHashMap$LinkedEntryIterator.next',0,1,0)
f(51,2184,9,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(52,2184,9,6,'java/util/LinkedHashMap$LinkedHashIterator.nextNode',0,9,0)
f(50,2193,1,6,'java/util/LinkedHashMap$LinkedEntrySet.iterator',0,1,0)
f(48,2194,1,3,'itable stub')
f(47,2195,3,1,'com/alibaba/fastjson/serializer/SerializeWriter.<init>')
f(48,2195,1,6,'com/alibaba/fastjson/serializer/SerializeWriter.computeFeatures',0,1,0)
f(48,2196,2,1,'java/lang/ThreadLocal.get')
f(49,2196,2,6,'java/lang/ThreadLocal.getMap',0,2,0)
f(47,2198,312,1,'com/alibaba/fastjson/serializer/SerializeWriter.toBytes')
f(48,2198,312,1,'com/alibaba/fastjson/serializer/SerializeWriter.encodeToUTF8Bytes',0,55,0)
f(49,2229,257,6,'com/alibaba/fastjson/util/IOUtils.encodeUTF8',0,257,0)
f(49,2486,24,2,'java/lang/ThreadLocal.set',24,0,0)
f(50,2486,24,2,'java/lang/ThreadLocal$ThreadLocalMap.access$100',24,0,0)
f(51,2501,9,3,'jbyte_disjoint_arraycopy')
f(40,2510,1,1,'java/lang/String.getBytes')
f(41,2510,1,1,'java/lang/StringCoding.encode')
f(42,2510,1,1,'java/lang/StringCoding.encode')
f(43,2510,1,6,'java/lang/StringCoding$StringEncoder.encode',0,1,0)
f(44,2510,1,3,'jbyte_disjoint_arraycopy')
f(40,2511,1,6,'java/util/ArrayList$Itr.hasNext',0,1,0)
f(40,2512,1398,1,'org/apache/kafka/clients/producer/KafkaProducer.send')
f(41,2512,1396,1,'org/apache/kafka/clients/producer/KafkaProducer.doSend')
f(42,2512,3,1,'org/apache/kafka/clients/producer/KafkaProducer.waitOnMetadata')
f(43,2512,1,1,'java/util/Collections$UnmodifiableCollection.contains')
f(44,2512,1,1,'java/util/HashSet.contains')
f(45,2512,1,6,'java/util/HashMap.containsKey',0,1,0)
f(43,2513,1,1,'org/apache/kafka/clients/Metadata.fetch')
f(44,2513,1,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(45,2513,1,4,'ObjectMonitor::enter(Thread*)')
f(46,2513,1,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(43,2514,1,1,'org/apache/kafka/common/Cluster.partitionCountForTopic')
f(44,2514,1,1,'java/util/Collections$UnmodifiableCollection.size')
f(45,2514,1,6,'java/util/ArrayList.size',0,1,0)
f(42,2515,3,1,'org/apache/kafka/clients/producer/internals/DefaultPartitioner.onNewBatch')
f(43,2515,3,1,'org/apache/kafka/clients/producer/internals/StickyPartitionCache.nextPartition',0,1,0)
f(44,2516,2,1,'java/util/concurrent/ConcurrentHashMap.replace')
f(45,2516,2,6,'java/util/concurrent/ConcurrentHashMap.replaceNode',0,2,0)
f(42,2518,1377,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.append',0,3,0)
f(43,2523,77,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(44,2524,74,4,'ObjectMonitor::enter(Thread*)')
f(45,2525,67,4,'ObjectMonitor::EnterI(Thread*)')
f(46,2525,2,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(46,2527,3,4,'ObjectMonitor::TryLock(Thread*)')
f(46,2530,10,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(47,2535,5,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(46,2540,27,4,'os::PlatformEvent::park()')
f(47,2540,27,3,'__psynch_cvwait')
f(46,2567,25,4,'os::PlatformEvent::park(long)')
f(47,2567,24,3,'__psynch_cvwait')
f(47,2591,1,3,'_pthread_cond_wait')
f(48,2591,1,3,'_pthread_mutex_firstfit_lock_slow')
f(49,2591,1,3,'__psynch_mutexwait')
f(45,2592,1,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(45,2593,5,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(46,2594,3,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(46,2597,1,4,'ObjectMonitor::TryLock(Thread*)')
f(44,2598,1,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(45,2598,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(44,2599,1,4,'ThreadStateTransition::trans(JavaThreadState, JavaThreadState)')
f(43,2600,9,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(44,2600,9,4,'ObjectMonitor::ExitEpilog(Thread*, ObjectWaiter*)')
f(45,2600,9,4,'os::PlatformEvent::unpark()')
f(46,2600,9,3,'__psynch_cvsignal')
f(43,2609,117,1,'org/apache/kafka/clients/producer/internals/BufferPool.allocate')
f(44,2609,96,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.await',0,1,0)
f(45,2609,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireQueued')
f(46,2609,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.parkAndCheckInterrupt')
f(47,2609,1,1,'java/util/concurrent/locks/LockSupport.park')
f(48,2609,1,1,'sun/misc/Unsafe.park')
f(49,2609,1,3,'Unsafe_Park')
f(50,2609,1,4,'Parker::park(bool, long)')
f(51,2609,1,3,'__psynch_cvwait')
f(45,2610,94,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(46,2610,94,1,'sun/misc/Unsafe.park')
f(47,2610,94,3,'Unsafe_Park')
f(48,2610,94,4,'Parker::park(bool, long)')
f(49,2610,1,4,'Monitor::unlock()')
f(49,2611,2,3,'__gettimeofday')
f(49,2613,88,3,'__psynch_cvwait')
f(49,2701,3,3,'_pthread_cond_wait')
f(50,2701,3,3,'_pthread_mutex_firstfit_lock_slow')
f(51,2701,3,3,'__psynch_mutexwait')
f(45,2704,1,4,'os::javaTimeNanos()')
f(46,2704,1,3,'mach_absolute_time')
f(44,2705,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(45,2705,1,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock')
f(46,2705,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquire')
f(47,2705,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireQueued')
f(48,2705,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.parkAndCheckInterrupt')
f(49,2705,1,1,'java/util/concurrent/locks/LockSupport.park')
f(50,2705,1,1,'sun/misc/Unsafe.park')
f(51,2705,1,3,'Unsafe_Park')
f(52,2705,1,4,'Parker::park(bool, long)')
f(53,2705,1,3,'__psynch_cvwait')
f(44,2706,4,1,'java/util/concurrent/locks/ReentrantLock.unlock')
f(45,2706,4,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release',0,1,0)
f(46,2706,3,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.unparkSuccessor')
f(47,2706,3,1,'java/util/concurrent/locks/LockSupport.unpark')
f(48,2706,3,1,'sun/misc/Unsafe.unpark')
f(49,2706,3,3,'Unsafe_Unpark')
f(50,2706,3,4,'Parker::unpark()')
f(51,2706,3,3,'__psynch_cvsignal')
f(46,2709,1,3,'vtable stub')
f(44,2710,1,1,'org/apache/kafka/clients/producer/internals/BufferPool.recordWaitTime')
f(45,2710,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(46,2710,1,6,'org/apache/kafka/common/metrics/Sensor.recordInternal',0,1,0)
f(47,2710,1,2,'org/apache/kafka/common/metrics/Sensor$StatAndConfig.config',1,0,0)
f(48,2710,1,3,'itable stub')
f(44,2711,14,6,'org/apache/kafka/clients/producer/internals/BufferPool.safeAllocateByteBuffer',0,14,0)
f(45,2711,14,2,'org/apache/kafka/clients/producer/internals/BufferPool.allocateByteBuffer',14,0,0)
f(46,2711,14,2,'java/nio/ByteBuffer.allocate',14,0,0)
f(47,2711,14,2,'java/nio/HeapByteBuffer.<init>',14,0,0)
f(48,2724,1,3,'new_type_array Runtime1 stub')
f(44,2725,1,6,'org/apache/kafka/common/utils/SystemTime.nanoseconds',0,1,0)
f(45,2725,1,4,'os::javaTimeNanos()')
f(46,2725,1,3,'mach_absolute_time')
f(43,2726,1,1,'org/apache/kafka/clients/producer/internals/IncompleteBatches.add')
f(44,2726,1,1,'java/util/HashSet.add')
f(45,2726,1,1,'java/util/HashMap.put')
f(46,2726,1,6,'java/util/HashMap.putVal',0,1,0)
f(43,2727,5,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.<init>')
f(44,2727,5,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.<init>',0,2,0)
f(45,2727,5,2,'org/apache/kafka/common/record/CompressionRatioEstimator.estimation',2,0,0)
f(46,2729,3,1,'org/apache/kafka/common/record/CompressionRatioEstimator.getAndCreateEstimationIfAbsent')
f(47,2729,3,6,'java/util/concurrent/ConcurrentHashMap.get',0,2,0)
f(48,2729,1,6,'java/lang/String.equals',0,1,0)
f(48,2730,2,3,'vtable stub')
f(43,2732,892,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.tryAppend',0,1,0)
f(44,2732,1,6,'org/apache/kafka/clients/producer/internals/FutureRecordMetadata.<init>',0,1,0)
f(44,2733,1,2,'org/apache/kafka/common/record/AbstractRecords.estimateSizeInBytesUpperBound',1,0,0)
f(45,2733,1,2,'org/apache/kafka/common/utils/Utils.wrapNullable',1,0,0)
f(46,2733,1,2,'java/nio/ByteBuffer.wrap',1,0,0)
f(47,2733,1,2,'java/nio/ByteBuffer.wrap',1,0,0)
f(48,2733,1,2,'java/nio/HeapByteBuffer.<init>',1,0,0)
f(44,2734,889,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.append')
f(45,2734,889,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.append')
f(46,2734,889,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.appendWithOffset')
f(47,2734,889,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.appendWithOffset')
f(48,2734,889,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.appendDefaultRecord')
f(49,2734,889,1,'org/apache/kafka/common/record/DefaultRecord.writeTo')
f(50,2734,1,1,'java/io/DataOutputStream.write')
f(51,2734,1,6,'java/io/BufferedOutputStream.write',0,1,0)
f(50,2735,1,1,'java/io/FilterOutputStream.write')
f(51,2735,1,1,'java/io/DataOutputStream.write')
f(52,2735,1,6,'java/io/BufferedOutputStream.write',0,1,0)
f(50,2736,2,6,'org/apache/kafka/common/record/DefaultRecord.sizeOfBodyInBytes',0,1,0)
f(51,2737,1,1,'org/apache/kafka/common/record/DefaultRecord.sizeOfBodyInBytes')
f(52,2737,1,1,'org/apache/kafka/common/record/DefaultRecord.sizeOf')
f(53,2737,1,1,'org/apache/kafka/common/utils/Utils.utf8Length')
f(54,2737,1,6,'java/lang/String.length',0,1,0)
f(50,2738,885,1,'org/apache/kafka/common/utils/Utils.writeTo')
f(51,2738,885,1,'java/io/DataOutputStream.write',0,1,0)
f(52,2738,884,1,'java/io/BufferedOutputStream.write')
f(53,2738,2,1,'java/io/BufferedOutputStream.flushBuffer')
f(54,2738,2,1,'java/util/zip/GZIPOutputStream.write')
f(55,2738,2,1,'java/util/zip/DeflaterOutputStream.write')
f(56,2738,2,1,'java/util/zip/DeflaterOutputStream.deflate')
f(57,2738,2,1,'java/util/zip/Deflater.deflate')
f(58,2738,2,1,'java/util/zip/Deflater.deflate')
f(59,2738,2,1,'java/util/zip/Deflater.deflateBytes')
f(60,2738,1,3,'Java_java_util_zip_Deflater_deflateBytes')
f(61,2738,1,3,'jni_GetObjectField')
f(62,2738,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(60,2739,1,3,'jni_GetPrimitiveArrayCritical')
f(53,2740,882,1,'java/util/zip/GZIPOutputStream.write')
f(54,2740,7,6,'java/util/zip/CRC32.update',0,7,0)
f(55,2740,7,3,'updateBytesCRC32')
f(54,2747,875,1,'java/util/zip/DeflaterOutputStream.write')
f(55,2747,875,1,'java/util/zip/DeflaterOutputStream.deflate')
f(56,2747,875,1,'java/util/zip/Deflater.deflate')
f(57,2747,875,1,'java/util/zip/Deflater.deflate',0,1,0)
f(58,2748,874,1,'java/util/zip/Deflater.deflateBytes')
f(59,2748,874,3,'Java_java_util_zip_Deflater_deflateBytes')
f(60,2748,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(60,2749,872,3,'deflate')
f(61,2749,872,3,'deflateCopy')
f(62,3383,9,3,'crc32_combine')
f(63,3383,9,3,'deflateCopy')
f(64,3383,9,3,'_platform_memmove$VARIANT$Rosetta')
f(62,3392,229,3,'deflateCopy')
f(60,3621,1,3,'jni_GetBooleanField')
f(52,3622,1,3,'vtable stub')
f(44,3623,1,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.hasRoomFor')
f(45,3623,1,1,'org/apache/kafka/common/utils/Utils.wrapNullable')
f(46,3623,1,1,'java/nio/ByteBuffer.wrap')
f(47,3623,1,1,'java/nio/ByteBuffer.wrap')
f(48,3623,1,1,'java/nio/HeapByteBuffer.<init>')
f(49,3623,1,1,'java/nio/ByteBuffer.<init>')
f(50,3623,1,1,'java/nio/Buffer.<init>')
f(51,3623,1,6,'java/nio/Buffer.position',0,1,0)
f(43,3624,2,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.getOrCreateDeque')
f(44,3624,2,1,'org/apache/kafka/common/utils/CopyOnWriteMap.get')
f(45,3624,2,1,'java/util/Collections$UnmodifiableMap.get')
f(46,3624,2,1,'java/util/HashMap.get')
f(47,3624,1,6,'java/util/HashMap.getNode',0,1,0)
f(47,3625,1,1,'java/util/HashMap.hash')
f(48,3625,1,6,'org/apache/kafka/common/TopicPartition.hashCode',0,1,0)
f(43,3626,76,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.recordsBuilder')
f(44,3626,76,1,'org/apache/kafka/common/record/MemoryRecords.builder')
f(45,3626,76,1,'org/apache/kafka/common/record/MemoryRecords.builder')
f(46,3626,76,1,'org/apache/kafka/common/record/MemoryRecords.builder',0,2,0)
f(47,3626,76,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.<init>',2,0,0)
f(48,3626,76,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.<init>',2,0,0)
f(49,3628,74,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.<init>',0,1,0)
f(50,3628,73,1,'org/apache/kafka/common/record/CompressionType$2.wrapForOutput',0,8,0)
f(51,3636,65,1,'java/util/zip/GZIPOutputStream.<init>')
f(52,3636,65,1,'java/util/zip/GZIPOutputStream.<init>',0,1,0)
f(53,3637,61,1,'java/util/zip/Deflater.<init>')
f(54,3637,4,4,'SharedRuntime::register_finalizer(JavaThread*, oopDesc*)')
f(55,3637,4,4,'InstanceKlass::register_finalizer(instanceOopDesc*, Thread*)')
f(56,3638,1,4,'JavaCallArguments::parameters()')
f(56,3639,2,4,'JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*)')
f(57,3640,1,4,'GrowableArray<Metadata*>::append(Metadata* const&)')
f(54,3641,1,1,'java/lang/Object.<init>')
f(55,3641,1,1,'java/lang/ref/Finalizer.register')
f(56,3641,1,1,'java/lang/ref/Finalizer.<init>')
f(57,3641,1,6,'java/lang/ref/Finalizer.add',0,1,0)
f(54,3642,56,1,'java/util/zip/Deflater.init')
f(55,3643,54,3,'Java_java_util_zip_Deflater_init')
f(56,3643,8,3,'_platform_memset$VARIANT$Rosetta')
f(56,3651,45,3,'deflateInit2_')
f(57,3651,42,3,'_platform_memset$VARIANT$Rosetta')
f(57,3693,3,3,'zError')
f(58,3693,3,3,'szone_malloc_should_clear')
f(59,3693,1,3,'medium_malloc_from_free_list')
f(59,3694,2,3,'medium_malloc_should_clear')
f(60,3694,2,3,'medium_malloc_from_free_list')
f(61,3695,1,3,'medium_free_list_remove_ptr_no_clear')
f(56,3696,1,3,'zError')
f(55,3697,1,3,'calloc')
f(53,3698,3,6,'java/util/zip/DeflaterOutputStream.<init>',0,3,0)
f(50,3701,1,2,'org/apache/kafka/common/utils/ByteBufferOutputStream.position',1,0,0)
f(51,3701,1,2,'org/apache/kafka/common/utils/ByteBufferOutputStream.ensureRemaining',1,0,0)
f(43,3702,190,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.tryAppend')
f(44,3702,1,6,'java/util/ArrayDeque.peekLast',0,1,0)
f(44,3703,185,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.closeForRecordAppends')
f(45,3703,185,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.closeForRecordAppends')
f(46,3703,185,1,'java/io/FilterOutputStream.close')
f(47,3703,5,1,'java/io/DataOutputStream.flush')
f(48,3703,5,1,'java/io/BufferedOutputStream.flush')
f(49,3703,5,1,'java/io/BufferedOutputStream.flushBuffer')
f(50,3703,5,1,'java/util/zip/GZIPOutputStream.write',0,1,0)
f(51,3704,1,6,'java/util/zip/CRC32.update',0,1,0)
f(52,3704,1,3,'updateBytesCRC32')
f(51,3705,3,1,'java/util/zip/DeflaterOutputStream.write')
f(52,3705,3,1,'java/util/zip/DeflaterOutputStream.deflate')
f(53,3705,3,1,'java/util/zip/Deflater.deflate')
f(54,3705,3,1,'java/util/zip/Deflater.deflate')
f(55,3705,3,1,'java/util/zip/Deflater.deflateBytes')
f(56,3705,3,3,'Java_java_util_zip_Deflater_deflateBytes')
f(57,3705,1,3,'deflate')
f(57,3706,1,3,'jni_GetIntField')
f(58,3706,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(57,3707,1,3,'jni_SetIntField')
f(58,3707,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(47,3708,180,1,'java/io/FilterOutputStream.close')
f(48,3708,180,1,'java/util/zip/DeflaterOutputStream.close')
f(49,3708,1,6,'java/io/OutputStream.close',0,1,0)
f(49,3709,6,1,'java/util/zip/Deflater.end')
f(50,3709,6,1,'java/util/zip/Deflater.end')
f(51,3709,4,3,'Java_java_util_zip_Deflater_end')
f(52,3709,4,3,'deflateEnd')
f(53,3709,3,3,'free_medium')
f(54,3711,1,3,'madvise')
f(53,3712,1,3,'free_small')
f(51,3713,2,3,'_nanov2_free')
f(49,3715,173,1,'java/util/zip/GZIPOutputStream.finish')
f(50,3715,172,1,'java/util/zip/Deflater.deflate')
f(51,3715,172,1,'java/util/zip/Deflater.deflate',0,1,0)
f(52,3716,171,1,'java/util/zip/Deflater.deflateBytes')
f(53,3716,171,3,'Java_java_util_zip_Deflater_deflateBytes')
f(54,3716,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(54,3717,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(54,3718,169,3,'deflate')
f(55,3718,169,3,'deflateCopy')
f(56,3721,1,3,'crc32_combine')
f(56,3722,2,3,'deflateCopy')
f(56,3724,2,3,'deflateTune')
f(57,3724,2,3,'_platform_memmove$VARIANT$Rosetta')
f(56,3726,161,3,'inflateCodesUsed')
f(57,3728,159,3,'inflateCodesUsed')
f(58,3859,28,3,'inflateCodesUsed')
f(50,3887,1,1,'org/apache/kafka/common/utils/ByteBufferOutputStream.write')
f(51,3887,1,1,'java/nio/HeapByteBuffer.put')
f(52,3887,1,6,'java/nio/Buffer.position',0,1,0)
f(44,3888,4,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.tryAppend')
f(45,3888,4,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.hasRoomFor')
f(46,3888,1,6,'org/apache/kafka/common/record/MemoryRecordsBuilder.hasRoomFor',0,1,0)
f(47,3888,1,2,'org/apache/kafka/common/record/DefaultRecord.sizeInBytes',1,0,0)
f(46,3889,3,1,'org/apache/kafka/common/utils/Utils.wrapNullable')
f(47,3889,3,1,'java/nio/ByteBuffer.wrap')
f(48,3889,3,1,'java/nio/ByteBuffer.wrap')
f(49,3889,3,1,'java/nio/HeapByteBuffer.<init>')
f(50,3889,3,1,'java/nio/ByteBuffer.<init>',0,1,0)
f(51,3890,2,6,'java/nio/Buffer.<init>',0,1,0)
f(52,3891,1,6,'java/nio/Buffer.limit',0,1,0)
f(43,3892,3,2,'org/apache/kafka/common/record/AbstractRecords.estimateSizeInBytesUpperBound',3,0,0)
f(44,3892,3,2,'org/apache/kafka/common/utils/Utils.wrapNullable',3,0,0)
f(45,3892,3,2,'java/nio/ByteBuffer.wrap',3,0,0)
f(46,3892,3,2,'java/nio/ByteBuffer.wrap',3,0,0)
f(47,3892,3,2,'java/nio/HeapByteBuffer.<init>',3,0,0)
f(42,3895,8,1,'org/apache/kafka/clients/producer/internals/Sender.wakeup')
f(43,3895,8,1,'org/apache/kafka/clients/NetworkClient.wakeup')
f(44,3895,8,1,'org/apache/kafka/common/network/Selector.wakeup')
f(45,3895,8,1,'sun/nio/ch/KQueueSelectorImpl.wakeup')
f(46,3895,2,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(47,3895,2,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(48,3895,2,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(46,3897,6,1,'sun/nio/ch/KQueueArrayWrapper.interrupt')
f(47,3897,6,1,'sun/nio/ch/KQueueArrayWrapper.interrupt')
f(48,3897,6,3,'write')
f(42,3903,2,6,'org/apache/kafka/common/header/internals/RecordHeaders.toArray',0,1,0)
f(43,3904,1,1,'java/util/ArrayList.toArray')
f(44,3904,1,1,'java/util/Arrays.copyOf')
f(45,3904,1,1,'java/lang/reflect/Array.newInstance')
f(46,3904,1,1,'java/lang/reflect/Array.newArray')
f(47,3904,1,3,'JVM_NewArray')
f(48,3904,1,4,'InstanceKlass::array_klass_impl(instanceKlassHandle, bool, int, Thread*)')
f(42,3905,1,1,'org/apache/kafka/common/record/AbstractRecords.estimateSizeInBytesUpperBound')
f(43,3905,1,1,'org/apache/kafka/common/utils/Utils.wrapNullable')
f(44,3905,1,1,'java/nio/ByteBuffer.wrap')
f(45,3905,1,1,'java/nio/ByteBuffer.wrap')
f(46,3905,1,1,'java/nio/HeapByteBuffer.<init>')
f(47,3905,1,6,'java/nio/ByteBuffer.<init>',0,1,0)
f(42,3906,1,1,'org/apache/kafka/common/serialization/Serializer.serialize')
f(43,3906,1,6,'org/apache/kafka/common/serialization/ByteArraySerializer.serialize',0,1,0)
f(42,3907,1,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,1,0)
f(41,3908,2,6,'org/apache/kafka/clients/producer/internals/ProducerInterceptors.onSend',0,2,0)
f(19,3910,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.createPdkMethodInvoker')
f(20,3910,1,1,'io/tapdata/flow/engine/V2/task/retry/task/TaskRetryService.getMethodRetryDurationMinutes')
f(21,3910,1,6,'io/tapdata/flow/engine/V2/task/retry/task/TaskRetryService.getMethodRetryDurationMs',0,1,0)
f(22,3910,1,2,'java/lang/Math.min',1,0,0)
f(19,3911,1,1,'java/util/ArrayList.forEach')
f(20,3911,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3117/1246754272.accept',0,1,0)
f(7,3912,1,1,'io/tapdata/flow/engine/V2/schedule/TapdataTaskScheduler$$Lambda$1713/1924480807.run')
f(8,3912,1,1,'io/tapdata/flow/engine/V2/schedule/TapdataTaskScheduler.lambda$init$2')
f(9,3912,1,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(10,3912,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos',0,1,0)
f(11,3912,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.isOnSyncQueue',1,0,0)
f(12,3912,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.findNodeFromTail',1,0,0)
f(7,3913,2,1,'io/tapdata/observable/logging/appender/ObsHttpTMLog4jAppender$$Lambda$2254/1620513994.run')
f(8,3913,2,0,'io/tapdata/observable/logging/appender/ObsHttpTMLog4jAppender.consumeAndInsertLogs',0,0,1)
f(9,3914,1,1,'com/google/common/collect/Queues.drain')
f(10,3914,1,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(11,3914,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(12,3914,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(13,3914,1,1,'sun/misc/Unsafe.park')
f(14,3914,1,3,'Unsafe_Park')
f(15,3914,1,4,'JavaThread::handle_special_suspend_equivalent_condition()')
f(4,3915,75,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.run')
f(5,3915,19,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201')
f(6,3915,19,1,'java/util/concurrent/FutureTask.run')
f(7,3915,19,1,'java/util/concurrent/FutureTask.run$$$capture',0,1,0)
f(8,3916,18,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(9,3916,18,1,'org/springframework/scheduling/concurrent/ReschedulingRunnable.run',0,1,0)
f(10,3917,17,1,'org/springframework/scheduling/support/DelegatingErrorHandlingRunnable.run')
f(11,3917,17,1,'io/tapdata/task/TapdataTaskScheduler$$Lambda$390/303946916.run')
f(12,3917,17,1,'io/tapdata/task/TapdataTaskScheduler.lambda$start$0')
f(13,3917,17,1,'io/tapdata/task/TapdataTaskScheduler.taskListen')
f(14,3917,15,1,'com/tapdata/mongo/HttpClientMongoOperator.findAndModify')
f(15,3917,15,1,'com/tapdata/mongo/HttpClientMongoOperator.findAndModifyByCollection')
f(16,3917,15,1,'com/tapdata/mongo/HttpClientMongoOperator.findAndModifTask')
f(17,3917,15,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(18,3917,8,1,'com/tapdata/mongo/HttpClientMongoOperator.handleParams')
f(19,3917,7,1,'org/bson/Document.toJson')
f(20,3917,7,1,'org/bson/Document.toJson')
f(21,3917,1,1,'org/bson/Document.toJson')
f(22,3917,1,1,'org/bson/codecs/DocumentCodec.encode')
f(23,3917,1,1,'org/bson/codecs/DocumentCodec.encode')
f(24,3917,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(25,3917,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(26,3917,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(27,3917,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(28,3917,1,1,'org/bson/codecs/DocumentCodec.writeIterable')
f(29,3917,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(30,3917,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(31,3917,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(32,3917,1,1,'org/bson/codecs/DocumentCodec.writeIterable')
f(33,3917,1,6,'org/bson/AbstractBsonWriter.writeEndArray',0,1,0)
f(21,3918,6,1,'org/bson/codecs/DocumentCodec.<init>')
f(22,3918,6,1,'org/bson/codecs/DocumentCodec.<init>')
f(23,3918,6,1,'org/bson/codecs/DocumentCodec.<init>')
f(24,3918,6,1,'org/bson/codecs/DocumentCodec.<init>')
f(25,3918,6,1,'org/bson/codecs/BsonTypeCodecMap.<init>',0,0,1)
f(26,3918,1,1,'I2C/C2I adapters')
f(26,3919,5,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(27,3919,5,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(28,3919,1,4,'Runtime1::exception_handler_for_pc(JavaThread*)')
f(29,3919,1,4,'frame::sender(RegisterMap*) const')
f(30,3919,1,4,'frame::sender_for_compiled_frame(RegisterMap*) const')
f(31,3919,1,4,'OopMapSet::update_register_map(frame const*, RegisterMap*)')
f(32,3919,1,4,'OopMapStream::find_next()')
f(28,3920,1,1,'org/bson/codecs/configuration/CodecCache.containsKey')
f(29,3920,1,1,'java/util/concurrent/ConcurrentHashMap.containsKey')
f(30,3920,1,6,'java/util/concurrent/ConcurrentHashMap.get',0,1,0)
f(28,3921,3,1,'org/bson/codecs/configuration/CodecCache.getOrThrow')
f(29,3921,2,1,'java/lang/String.format')
f(30,3921,1,1,'java/util/Formatter.<init>')
f(31,3921,1,1,'java/util/Formatter.<init>')
f(32,3921,1,1,'java/util/Formatter.getZero')
f(33,3921,1,6,'java/util/Locale.equals',0,1,0)
f(30,3922,1,1,'java/util/Formatter.format')
f(31,3922,1,1,'java/util/Formatter.format')
f(32,3922,1,1,'java/util/Formatter.parse')
f(33,3922,1,1,'java/util/Formatter$FormatSpecifier.<init>')
f(34,3922,1,1,'java/util/regex/Matcher.group')
f(35,3922,1,1,'java/util/regex/Matcher.getSubSequence')
f(36,3922,1,6,'java/lang/String.subSequence',0,1,0)
f(29,3923,1,6,'org/bson/codecs/configuration/Optional$Some.isEmpty',0,1,0)
f(19,3924,1,1,'org/springframework/data/mongodb/core/query/Query.getQueryObject')
f(20,3924,1,1,'org/springframework/data/mongodb/core/query/Criteria.getCriteriaObject')
f(21,3924,1,6,'org/springframework/data/mongodb/core/query/Criteria.getSingleCriteriaObject',0,1,0)
f(22,3924,1,2,'org/bson/Document.<init>',1,0,0)
f(18,3925,7,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(19,3925,7,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(20,3925,7,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(21,3925,7,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$339/424832797.tryFunc')
f(22,3925,7,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getBatch$10')
f(23,3925,1,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.getURI')
f(24,3925,1,1,'org/springframework/web/util/HierarchicalUriComponents.toUri')
f(25,3925,1,1,'java/net/URI.<init>')
f(26,3925,1,1,'java/net/URI$Parser.parse')
f(27,3925,1,1,'java/net/URI$Parser.parseHierarchical')
f(28,3925,1,1,'java/net/URI$Parser.scan')
f(29,3925,1,1,'java/lang/String.indexOf')
f(30,3925,1,6,'java/lang/String.indexOf',0,1,0)
f(23,3926,6,1,'org/springframework/web/client/RestTemplate.exchange')
f(24,3926,6,1,'org/springframework/web/client/RestTemplate.execute')
f(25,3926,6,1,'org/springframework/web/client/RestTemplate.doExecute')
f(26,3926,4,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(27,3926,4,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal',0,1,0)
f(28,3926,1,2,'org/springframework/http/HttpHeaders.getContentLength',1,0,0)
f(28,3927,3,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(29,3927,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(30,3927,3,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(31,3927,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(32,3927,3,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(33,3927,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(34,3927,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(35,3927,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(36,3927,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(37,3927,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(38,3927,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(39,3927,2,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(40,3927,2,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(41,3927,2,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(42,3927,2,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(43,3927,1,1,'org/apache/http/impl/client/DefaultConnectionKeepAliveStrategy.getKeepAliveDuration')
f(44,3927,1,1,'org/apache/http/message/BasicHeaderElementIterator.hasNext')
f(45,3927,1,6,'org/apache/http/message/BasicHeaderElementIterator.parseNextElement',0,1,0)
f(43,3928,1,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(44,3928,1,1,'org/apache/http/protocol/HttpRequestExecutor.doSendRequest')
f(45,3928,1,1,'org/apache/http/impl/conn/CPoolProxy.sendRequestHeader')
f(46,3928,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.sendRequestHeader')
f(47,3928,1,1,'org/apache/http/impl/io/AbstractMessageWriter.write')
f(48,3928,1,1,'org/apache/http/message/BasicLineFormatter.formatHeader')
f(49,3928,1,1,'org/apache/http/message/BasicLineFormatter.doFormatHeader')
f(50,3928,1,6,'org/apache/http/util/CharArrayBuffer.append',0,1,0)
f(34,3929,1,6,'org/springframework/http/client/HttpComponentsClientHttpRequestFactory.createRequest',0,1,0)
f(26,3930,1,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(27,3930,1,1,'org/springframework/web/client/RestTemplate$AcceptHeaderRequestCallback.doWithRequest')
f(28,3930,1,6,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.canRead',0,1,0)
f(26,3931,1,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(27,3931,1,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(28,3931,1,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(29,3931,1,6,'org/springframework/web/client/MessageBodyClientHttpResponseWrapper.hasEmptyMessageBody',0,1,0)
f(14,3932,1,6,'io/tapdata/task/TapdataTaskScheduler.stopTask',0,1,0)
f(14,3933,1,1,'org/springframework/data/mongodb/core/query/Criteria.orOperator')
f(15,3933,1,1,'org/springframework/data/mongodb/core/query/Criteria.createCriteriaList')
f(16,3933,1,1,'org/springframework/data/mongodb/core/query/Criteria.getCriteriaObject')
f(17,3933,1,6,'org/springframework/data/mongodb/core/query/Criteria.getSingleCriteriaObject',0,1,0)
f(5,3934,56,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301')
f(6,3934,56,1,'java/util/concurrent/FutureTask.runAndReset')
f(7,3934,1,6,'com/intellij/rt/debugger/agent/CaptureStorage.insertExit',0,1,0)
f(7,3935,55,1,'java/util/concurrent/FutureTask.runAndReset$$$capture')
f(8,3935,55,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(9,3935,2,1,'com/zaxxer/hikari/pool/HikariPool$KeepaliveTask.run')
f(10,3935,2,1,'com/zaxxer/hikari/pool/PoolBase.isConnectionAlive')
f(11,3935,2,1,'oracle/jdbc/driver/PhysicalConnection.isValid')
f(12,3935,2,1,'oracle/jdbc/driver/PhysicalConnection.isValid')
f(13,3935,2,1,'oracle/jdbc/driver/PhysicalConnection.pingDatabase')
f(14,3935,1,1,'java/lang/Thread.<init>')
f(15,3935,1,1,'java/lang/Thread.init')
f(16,3935,1,1,'java/lang/Thread.init')
f(17,3935,1,6,'java/lang/Thread.setPriority',0,1,0)
f(14,3936,1,1,'java/lang/Thread.start')
f(15,3936,1,6,'java/lang/ThreadGroup.add',0,1,0)
f(9,3937,1,6,'io/tapdata/Schedule/LogConfigurationWatcherManager$$Lambda$397/1345063038.run',0,1,0)
f(9,3938,6,1,'io/tapdata/common/sample/CollectorFactory$$Lambda$1790/133321775.run')
f(10,3938,6,1,'io/tapdata/common/sample/CollectorFactory.lambda$start$1')
f(11,3938,3,1,'io/tapdata/common/sample/CollectorFactory.wrapSampleRequest')
f(12,3938,3,1,'io/tapdata/common/sample/SampleCollector.calculateInPeriod')
f(13,3938,1,1,'io/tapdata/observable/metric/handler/TableSampleHandler$$Lambda$3051/1892290239.value')
f(14,3938,1,1,'io/tapdata/observable/metric/handler/TableSampleHandler.lambda$doInit$1')
f(15,3938,1,1,'java/math/BigDecimal.divide')
f(16,3938,1,1,'java/math/BigDecimal.divide')
f(17,3938,1,1,'java/math/BigDecimal.divide')
f(18,3938,1,1,'java/math/BigDecimal.longMultiplyPowerTen')
f(19,3938,1,6,'java/lang/Math.abs',0,1,0)
f(13,3939,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler$$Lambda$2360/1043307790.value')
f(14,3939,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler.lambda$doInit$3')
f(15,3939,1,1,'java/util/stream/ReferencePipeline$Head.forEach')
f(16,3939,1,1,'java/util/stream/Streams$ConcatSpliterator.forEachRemaining')
f(17,3939,1,1,'java/util/HashMap$ValueSpliterator.forEachRemaining')
f(18,3939,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler$$Lambda$2373/1438541145.accept')
f(19,3939,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler.lambda$null$2')
f(20,3939,1,1,'java/util/Optional.ifPresent')
f(21,3939,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler$$Lambda$3059/1061661489.accept')
f(22,3939,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler.lambda$null$1')
f(23,3939,1,6,'io/tapdata/common/sample/sampler/NumberSampler.value',0,1,0)
f(13,3940,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler$$Lambda$2368/1666299359.value')
f(14,3940,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler.lambda$doInit$16')
f(15,3940,1,1,'java/util/Optional.ifPresent')
f(16,3940,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler$$Lambda$2369/1589424987.accept')
f(17,3940,1,1,'io/tapdata/observable/metric/handler/TaskSampleHandler.lambda$null$15')
f(18,3940,1,1,'io/tapdata/common/sample/sampler/SpeedSampler.getAvgValue')
f(19,3940,1,1,'java/util/stream/DoublePipeline.average')
f(20,3940,1,1,'java/util/stream/DoublePipeline.collect')
f(21,3940,1,1,'java/util/stream/AbstractPipeline.evaluate')
f(22,3940,1,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(23,3940,1,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(24,3940,1,1,'java/util/stream/AbstractPipeline.copyInto')
f(25,3940,1,1,'java/util/stream/Sink$ChainedReference.begin')
f(26,3940,1,1,'java/util/stream/ReduceOps$13ReducingSink.begin')
f(27,3940,1,6,'java/util/stream/DoublePipeline$$Lambda$3177/967432241.get',0,1,0)
f(11,3941,3,1,'io/tapdata/observable/metric/TaskSampleReporter.execute')
f(12,3941,3,1,'com/tapdata/mongo/HttpClientMongoOperator.insertOne')
f(13,3941,3,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(14,3941,3,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(15,3941,3,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(16,3941,3,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$1764/485421119.tryFunc')
f(17,3941,3,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$3')
f(18,3941,3,1,'org/springframework/web/client/RestTemplate.postForEntity')
f(19,3941,3,1,'org/springframework/web/client/RestTemplate.execute')
f(20,3941,3,1,'org/springframework/web/client/RestTemplate.doExecute')
f(21,3941,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(22,3941,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(23,3941,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(24,3941,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(25,3941,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(26,3941,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(27,3941,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(28,3941,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(29,3941,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(30,3941,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(31,3941,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(32,3941,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(33,3941,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(34,3941,2,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(35,3941,2,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(36,3941,1,6,'org/apache/http/impl/client/DefaultRedirectStrategy.isRedirected',0,1,0)
f(36,3942,1,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(37,3942,1,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(38,3942,1,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(39,3942,1,1,'org/apache/http/protocol/HttpRequestExecutor.doSendRequest')
f(40,3942,1,1,'org/apache/http/impl/conn/CPoolProxy.sendRequestEntity')
f(41,3942,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.sendRequestEntity')
f(42,3942,1,1,'org/apache/http/client/entity/GzipCompressingEntity.writeTo')
f(43,3942,1,1,'java/util/zip/DeflaterOutputStream.close')
f(44,3942,1,1,'java/util/zip/GZIPOutputStream.finish')
f(45,3942,1,4,'SharedRuntime::resolve_opt_virtual_call_C(JavaThread*)')
f(46,3942,1,4,'SharedRuntime::resolve_helper(JavaThread*, bool, bool, Thread*)')
f(47,3942,1,4,'SharedRuntime::resolve_sub_helper(JavaThread*, bool, bool, Thread*)')
f(48,3942,1,4,'CompiledIC::set_to_monomorphic(CompiledICInfo&)')
f(49,3942,1,4,'CompiledIC::internal_set_ic_destination(unsigned char*, bool, void*, bool)')
f(50,3942,1,4,'NativeCall::set_destination_mt_safe(unsigned char*)')
f(21,3943,1,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(22,3943,1,1,'org/springframework/http/converter/AbstractGenericHttpMessageConverter.write')
f(23,3943,1,6,'org/springframework/http/converter/AbstractHttpMessageConverter.addDefaultHeaders',0,1,0)
f(24,3943,1,2,'org/springframework/http/MediaType.<init>',1,0,0)
f(25,3943,1,2,'org/springframework/util/MimeType.<init>',1,0,0)
f(26,3943,1,2,'org/springframework/util/MimeType.addCharsetParameter',1,0,0)
f(27,3943,1,2,'java/util/LinkedHashMap.<init>',1,0,0)
f(9,3944,4,1,'io/tapdata/flow/engine/V2/monitor/impl/JetJobStatusMonitor$$Lambda$2773/1619464370.run')
f(10,3944,4,1,'io/tapdata/flow/engine/V2/monitor/impl/JetJobStatusMonitor.lambda$start$0')
f(11,3944,4,1,'com/hazelcast/jet/impl/AbstractJobProxy.getStatus')
f(12,3944,4,1,'com/hazelcast/jet/impl/JobProxy.getStatus0')
f(13,3944,4,1,'com/hazelcast/jet/impl/JobProxy.invokeOp')
f(14,3944,4,1,'com/hazelcast/spi/impl/operationservice/impl/InvocationBuilderImpl.invoke')
f(15,3944,4,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke')
f(16,3944,4,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke0',0,1,0)
f(17,3945,3,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvoke')
f(18,3945,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvokeLocal')
f(19,3945,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.runOrExecute')
f(20,3945,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.run')
f(21,3945,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(22,3945,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(23,3945,1,6,'com/hazelcast/internal/util/LatencyDistribution.recordNanos',0,1,0)
f(23,3946,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.call')
f(24,3946,1,1,'com/hazelcast/spi/impl/operationservice/Operation.call')
f(25,3946,1,6,'com/hazelcast/jet/impl/operation/AsyncOperation.run',0,1,0)
f(26,3946,1,3,'vtable stub')
f(18,3947,1,6,'com/hazelcast/spi/impl/operationservice/impl/Invocation.initInvocationTarget',0,1,0)
f(19,3947,1,2,'com/hazelcast/spi/impl/operationservice/Operations.isJoinOperation',1,0,0)
f(20,3947,1,2,'java/lang/Class.getClassLoader',1,0,0)
f(9,3948,3,1,'io/tapdata/flow/engine/V2/monitor/impl/TaskPingTimeMonitor$$Lambda$2390/1359141143.run')
f(10,3948,3,1,'io/tapdata/flow/engine/V2/monitor/impl/TaskPingTimeMonitor.lambda$start$0')
f(11,3948,3,1,'io/tapdata/flow/engine/V2/monitor/impl/TaskPingTimeMonitor.taskPingTimeUseHttp')
f(12,3948,3,1,'com/tapdata/mongo/HttpClientMongoOperator.update')
f(13,3948,3,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(14,3948,3,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(15,3948,2,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$352/1794607688.tryFunc')
f(16,3948,2,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$8')
f(17,3948,2,1,'org/springframework/web/client/RestTemplate.postForObject')
f(18,3948,2,1,'org/springframework/web/client/RestTemplate.execute')
f(19,3948,2,1,'org/springframework/web/client/RestTemplate.doExecute')
f(20,3948,1,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(21,3948,1,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(22,3948,1,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(23,3948,1,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(24,3948,1,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(25,3948,1,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(26,3948,1,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(27,3948,1,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(28,3948,1,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(29,3948,1,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(30,3948,1,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(31,3948,1,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(32,3948,1,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(33,3948,1,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(34,3948,1,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(35,3948,1,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(36,3948,1,1,'org/apache/http/impl/execchain/ProtocolExec.rewriteRequestURI')
f(37,3948,1,1,'org/apache/http/client/utils/URIUtils.rewriteURIForRoute')
f(38,3948,1,1,'org/apache/http/client/utils/URIUtils.rewriteURI')
f(39,3948,1,1,'org/apache/http/client/utils/URIBuilder.build')
f(40,3948,1,1,'java/net/URI.<init>')
f(41,3948,1,1,'java/net/URI$Parser.parse')
f(42,3948,1,1,'java/net/URI$Parser.parseHierarchical')
f(43,3948,1,1,'java/net/URI$Parser.scan')
f(44,3948,1,1,'java/lang/String.indexOf')
f(45,3948,1,6,'java/lang/String.indexOf',0,1,0)
f(20,3949,1,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(21,3949,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.read')
f(22,3949,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.readJavaType')
f(23,3949,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(24,3949,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(25,3949,1,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(26,3949,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(27,3949,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserializeFromObject')
f(28,3949,1,1,'com/fasterxml/jackson/databind/deser/impl/MethodProperty.deserializeAndSet')
f(29,3949,1,1,'com/fasterxml/jackson/databind/deser/std/NumberDeserializers$LongDeserializer.deserialize')
f(30,3949,1,1,'com/fasterxml/jackson/databind/deser/std/NumberDeserializers$LongDeserializer.deserialize')
f(31,3949,1,1,'com/fasterxml/jackson/core/base/ParserBase.getLongValue')
f(32,3949,1,6,'com/fasterxml/jackson/core/base/ParserBase._parseNumericValue',0,1,0)
f(15,3950,1,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.<init>')
f(16,3950,1,1,'java/util/UUID.randomUUID')
f(17,3950,1,1,'java/security/SecureRandom.nextBytes')
f(18,3950,1,1,'sun/security/provider/NativePRNG.engineNextBytes')
f(19,3950,1,1,'sun/security/provider/NativePRNG$RandomIO.access$400')
f(20,3950,1,1,'sun/security/provider/NativePRNG$RandomIO.implNextBytes')
f(21,3950,1,1,'sun/security/provider/SecureRandom.engineNextBytes')
f(22,3950,1,1,'java/security/MessageDigest.digest')
f(23,3950,1,1,'java/security/MessageDigest$Delegate.engineDigest')
f(24,3950,1,1,'sun/security/provider/DigestBase.engineDigest')
f(25,3950,1,1,'sun/security/provider/DigestBase.engineDigest')
f(26,3950,1,1,'sun/security/provider/SHA.implDigest')
f(27,3950,1,6,'sun/security/provider/SHA.implCompress',0,1,0)
f(9,3951,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2836/**********.run')
f(10,3951,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.saveToSnapshot')
f(11,3951,1,1,'com/tapdata/mongo/HttpClientMongoOperator.insertOne')
f(12,3951,1,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(13,3951,1,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(14,3951,1,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(15,3951,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$1764/485421119.tryFunc')
f(16,3951,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$3')
f(17,3951,1,1,'org/springframework/web/client/RestTemplate.postForEntity')
f(18,3951,1,1,'org/springframework/web/client/RestTemplate.execute')
f(19,3951,1,1,'org/springframework/web/client/RestTemplate.doExecute')
f(20,3951,1,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(21,3951,1,1,'org/springframework/http/converter/AbstractGenericHttpMessageConverter.write')
f(22,3951,1,6,'org/springframework/http/converter/AbstractHttpMessageConverter.addDefaultHeaders',0,1,0)
f(9,3952,5,1,'io/tapdata/milestone/MilestoneAspectTask$$Lambda$2312/1622925019.run')
f(10,3952,5,1,'io/tapdata/milestone/MilestoneAspectTask.storeMilestone')
f(11,3952,5,1,'com/tapdata/mongo/HttpClientMongoOperator.update')
f(12,3952,4,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(13,3952,4,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(14,3952,4,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$352/1794607688.tryFunc')
f(15,3952,4,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$8')
f(16,3952,1,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.getURI')
f(17,3952,1,1,'org/springframework/web/util/HierarchicalUriComponents.toUri')
f(18,3952,1,1,'java/net/URI.<init>')
f(19,3952,1,1,'java/net/URI$Parser.parse')
f(20,3952,1,1,'java/net/URI$Parser.checkChar')
f(21,3952,1,6,'java/net/URI$Parser.checkChars',0,1,0)
f(16,3953,3,1,'org/springframework/web/client/RestTemplate.postForObject')
f(17,3953,3,1,'org/springframework/web/client/RestTemplate.execute')
f(18,3953,3,1,'org/springframework/web/client/RestTemplate.doExecute')
f(19,3953,3,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(20,3953,3,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(21,3953,3,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(22,3953,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(23,3953,3,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(24,3953,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(25,3953,3,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(26,3953,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(27,3953,3,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(28,3953,3,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(29,3953,3,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(30,3953,3,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(31,3953,3,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(32,3953,3,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(33,3953,3,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(34,3953,3,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(35,3953,2,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(36,3953,2,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(37,3953,1,1,'org/apache/http/protocol/HttpRequestExecutor.doReceiveResponse')
f(38,3953,1,1,'org/apache/http/impl/conn/CPoolProxy.receiveResponseHeader')
f(39,3953,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.receiveResponseHeader')
f(40,3953,1,1,'org/apache/http/impl/io/AbstractMessageParser.parse')
f(41,3953,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(42,3953,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(43,3953,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.readLine')
f(44,3953,1,6,'org/apache/http/impl/io/SessionInputBufferImpl.fillBuffer',0,1,0)
f(45,3953,1,2,'org/apache/http/impl/io/SessionInputBufferImpl.streamRead',1,0,0)
f(37,3954,1,1,'org/apache/http/protocol/HttpRequestExecutor.doSendRequest')
f(38,3954,1,1,'org/apache/http/impl/conn/CPoolProxy.sendRequestEntity')
f(39,3954,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.sendRequestEntity')
f(40,3954,1,1,'org/apache/http/client/entity/GzipCompressingEntity.writeTo')
f(41,3954,1,1,'java/util/zip/DeflaterOutputStream.close')
f(42,3954,1,6,'org/apache/http/impl/io/ChunkedOutputStream.close',0,1,0)
f(35,3955,1,1,'org/apache/http/impl/execchain/ProtocolExec.rewriteRequestURI')
f(36,3955,1,1,'org/apache/http/client/utils/URIUtils.rewriteURIForRoute')
f(37,3955,1,1,'org/apache/http/client/utils/URIUtils.rewriteURI')
f(38,3955,1,1,'org/apache/http/client/utils/URIBuilder.<init>')
f(39,3955,1,1,'org/apache/http/client/utils/URIBuilder.digestURI')
f(40,3955,1,1,'org/apache/http/client/utils/URIBuilder.parseQuery')
f(41,3955,1,1,'org/apache/http/client/utils/URLEncodedUtils.parse')
f(42,3955,1,1,'org/apache/http/client/utils/URLEncodedUtils.parse')
f(43,3955,1,1,'org/apache/http/message/TokenParser.parseValue')
f(44,3955,1,6,'org/apache/http/message/TokenParser.copyUnquotedContent',0,1,0)
f(12,3956,1,1,'org/bson/Document.toJson')
f(13,3956,1,1,'org/bson/Document.toJson')
f(14,3956,1,1,'org/bson/Document.toJson')
f(15,3956,1,1,'org/bson/codecs/DocumentCodec.encode')
f(16,3956,1,1,'org/bson/codecs/DocumentCodec.encode')
f(17,3956,1,6,'org/bson/codecs/DocumentCodec.writeMap',0,1,0)
f(9,3957,2,1,'io/tapdata/observable/logging/ObsLoggerFactory$$Lambda$1892/107607915.run')
f(10,3957,2,1,'io/tapdata/observable/logging/ObsLoggerFactory.renewTaskLogSetting')
f(11,3957,1,1,'java/util/concurrent/ConcurrentHashMap.computeIfPresent')
f(12,3957,1,1,'io/tapdata/observable/logging/ObsLoggerFactory$$Lambda$3175/2136446632.apply')
f(13,3957,1,1,'io/tapdata/observable/logging/ObsLoggerFactory.lambda$renewTaskLogSetting$0')
f(14,3957,1,4,'SharedRuntime::resolve_opt_virtual_call_C(JavaThread*)')
f(15,3957,1,4,'SharedRuntime::resolve_helper(JavaThread*, bool, bool, Thread*)')
f(16,3957,1,4,'SharedRuntime::resolve_sub_helper(JavaThread*, bool, bool, Thread*)')
f(17,3957,1,4,'nmethodLocker::lock_nmethod(nmethod*, bool)')
f(11,3958,1,1,'org/bson/types/ObjectId.<init>')
f(12,3958,1,1,'org/bson/types/ObjectId.parseHexString')
f(13,3958,1,6,'org/bson/types/ObjectId.isValid',0,1,0)
f(9,3959,1,1,'io/tapdata/observable/logging/ObsLoggerFactory$$Lambda$1893/1234375661.run')
f(10,3959,1,1,'io/tapdata/observable/logging/ObsLoggerFactory.removeTaskLogger')
f(11,3959,1,6,'java/util/concurrent/ConcurrentHashMap$KeySetView.iterator',0,1,0)
f(9,3960,1,1,'io/tapdata/websocket/ManagementWebsocketHandler$$Lambda$1716/392488030.run')
f(10,3960,1,1,'io/tapdata/websocket/ManagementWebsocketHandler.lambda$init$2')
f(11,3960,1,1,'io/tapdata/websocket/ManagementWebsocketHandler.sendMessage')
f(12,3960,1,1,'io/tapdata/websocket/ManagementWebsocketHandler$SessionOption.sendMessage')
f(13,3960,1,1,'org/springframework/web/socket/adapter/AbstractWebSocketSession.sendMessage')
f(14,3960,1,1,'org/springframework/web/socket/adapter/standard/StandardWebSocketSession.sendTextMessage')
f(15,3960,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointBasic.sendText')
f(16,3960,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendPartialString')
f(17,3960,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendMessageBlock')
f(18,3960,1,6,'java/nio/charset/CoderResult.isOverflow',0,1,0)
f(9,3961,29,1,'org/springframework/scheduling/support/DelegatingErrorHandlingRunnable.run')
f(10,3961,29,1,'org/springframework/scheduling/support/ScheduledMethodRunnable.run')
f(11,3961,29,1,'java/lang/reflect/Method.invoke')
f(12,3961,29,1,'sun/reflect/DelegatingMethodAccessorImpl.invoke')
f(13,3961,6,1,'sun/reflect/GeneratedMethodAccessor140.invoke')
f(14,3961,6,1,'io/tapdata/Schedule/TransformerManager.workerHeartBeat')
f(15,3961,5,1,'com/tapdata/mongo/HttpClientMongoOperator.insertOne')
f(16,3961,5,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(17,3961,5,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(18,3961,5,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(19,3961,5,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$1764/485421119.tryFunc')
f(20,3961,5,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$3')
f(21,3961,1,1,'com/tapdata/mongo/RestTemplateOperator.getBodyMapOrList')
f(22,3961,1,1,'com/tapdata/constant/JSONUtil.json2POJO')
f(23,3961,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(24,3961,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(25,3961,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(26,3961,1,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(27,3961,1,1,'com/fasterxml/jackson/databind/deser/std/MapDeserializer.deserialize')
f(28,3961,1,1,'com/fasterxml/jackson/databind/deser/std/MapDeserializer.deserialize')
f(29,3961,1,6,'com/fasterxml/jackson/databind/deser/std/MapDeserializer._readAndBindStringKeyMap',0,1,0)
f(21,3962,4,1,'org/springframework/web/client/RestTemplate.postForEntity')
f(22,3962,4,1,'org/springframework/web/client/RestTemplate.execute')
f(23,3962,4,1,'org/springframework/web/client/RestTemplate.doExecute')
f(24,3962,3,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(25,3962,3,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(26,3962,3,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(27,3962,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(28,3962,3,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(29,3962,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(30,3962,3,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(31,3962,3,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(32,3962,3,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(33,3962,3,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(34,3962,3,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(35,3962,3,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(36,3962,3,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(37,3962,3,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(38,3962,3,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(39,3962,3,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(40,3962,2,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(41,3962,2,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(42,3962,1,1,'org/apache/http/protocol/HttpRequestExecutor.doReceiveResponse')
f(43,3962,1,1,'org/apache/http/impl/conn/CPoolProxy.receiveResponseHeader')
f(44,3962,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.receiveResponseHeader')
f(45,3962,1,1,'org/apache/http/impl/io/AbstractMessageParser.parse')
f(46,3962,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(47,3962,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(48,3962,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.readLine')
f(49,3962,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.fillBuffer')
f(50,3962,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.streamRead')
f(51,3962,1,6,'java/net/SocketInputStream.read',0,1,0)
f(42,3963,1,1,'org/apache/http/protocol/HttpRequestExecutor.doSendRequest')
f(43,3963,1,1,'org/apache/http/impl/conn/CPoolProxy.sendRequestEntity')
f(44,3963,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.sendRequestEntity')
f(45,3963,1,1,'org/apache/http/impl/BHttpConnectionBase.prepareOutput')
f(46,3963,1,6,'org/apache/http/impl/BHttpConnectionBase.createOutputStream',0,1,0)
f(40,3964,1,1,'org/apache/http/protocol/ImmutableHttpProcessor.process')
f(41,3964,1,6,'org/apache/http/client/protocol/RequestDefaultHeaders.process',0,1,0)
f(42,3964,1,3,'itable stub')
f(24,3965,1,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(25,3965,1,1,'org/springframework/web/client/RestTemplate$AcceptHeaderRequestCallback.doWithRequest')
f(26,3965,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.canRead')
f(27,3965,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.canRead')
f(28,3965,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.getJavaType')
f(29,3965,1,1,'com/fasterxml/jackson/databind/type/TypeFactory.constructType')
f(30,3965,1,1,'com/fasterxml/jackson/databind/type/TypeFactory._fromAny')
f(31,3965,1,6,'com/fasterxml/jackson/databind/type/TypeFactory._fromClass',0,1,0)
f(15,3966,1,1,'java/lang/String.format')
f(16,3966,1,1,'java/util/Formatter.format')
f(17,3966,1,1,'java/util/Formatter.format')
f(18,3966,1,6,'java/util/Formatter.parse',0,1,0)
f(13,3967,8,1,'sun/reflect/GeneratedMethodAccessor141.invoke')
f(14,3967,8,1,'io/tapdata/Schedule/ConnectorManager.workerHeartBeat')
f(15,3967,4,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(16,3967,4,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(17,3967,4,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(18,3967,4,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(19,3967,4,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(20,3967,4,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$339/424832797.tryFunc')
f(21,3967,4,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getBatch$10')
f(22,3967,4,1,'org/springframework/web/client/RestTemplate.exchange')
f(23,3967,4,1,'org/springframework/web/client/RestTemplate.execute')
f(24,3967,4,1,'org/springframework/web/client/RestTemplate.doExecute')
f(25,3967,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(26,3967,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(27,3967,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(28,3967,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(29,3967,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(30,3967,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(31,3967,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(32,3967,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(33,3967,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(34,3967,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(35,3967,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(36,3967,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(37,3967,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(38,3967,2,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(39,3967,2,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(40,3967,2,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(41,3967,2,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(42,3967,1,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager$1.get')
f(43,3967,1,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager.leaseConnection')
f(44,3967,1,1,'org/apache/http/pool/AbstractConnPool$2.get')
f(45,3967,1,1,'org/apache/http/pool/AbstractConnPool$2.get')
f(46,3967,1,1,'org/apache/http/impl/conn/CPool.validate')
f(47,3967,1,1,'org/apache/http/impl/conn/CPool.validate')
f(48,3967,1,1,'org/apache/http/impl/BHttpConnectionBase.isStale')
f(49,3967,1,1,'org/apache/http/impl/BHttpConnectionBase.fillInputBuffer')
f(50,3967,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.fillBuffer')
f(51,3967,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.streamRead')
f(52,3967,1,1,'java/net/SocketInputStream.read')
f(53,3967,1,1,'java/net/SocketInputStream.read')
f(54,3967,1,1,'java/net/SocketInputStream.socketRead')
f(55,3967,1,1,'java/net/SocketInputStream.socketRead0')
f(56,3967,1,1,'java/net/SocketTimeoutException.<init>')
f(57,3967,1,1,'java/io/InterruptedIOException.<init>')
f(58,3967,1,1,'java/io/IOException.<init>')
f(59,3967,1,1,'java/lang/Exception.<init>')
f(60,3967,1,1,'java/lang/Throwable.<init>')
f(61,3967,1,1,'java/lang/Throwable.fillInStackTrace')
f(62,3967,1,1,'java/lang/Throwable.fillInStackTrace')
f(63,3967,1,3,'Java_java_lang_Throwable_fillInStackTrace')
f(64,3967,1,3,'JVM_FillInStackTrace')
f(65,3967,1,4,'java_lang_Throwable::fill_in_stack_trace(Handle, methodHandle)')
f(66,3967,1,4,'java_lang_Throwable::fill_in_stack_trace(Handle, methodHandle, Thread*)')
f(67,3967,1,4,'frame::sender(RegisterMap*) const')
f(68,3967,1,4,'frame::sender_for_compiled_frame(RegisterMap*) const')
f(69,3967,1,4,'CodeCache::find_blob(void*)')
f(70,3967,1,4,'CodeCache::find_blob_unsafe(void*)')
f(71,3967,1,4,'CodeHeap::find_start(void*) const')
f(42,3968,1,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(43,3968,1,1,'org/apache/http/protocol/HttpRequestExecutor.doReceiveResponse')
f(44,3968,1,1,'org/apache/http/impl/conn/CPoolProxy.receiveResponseHeader')
f(45,3968,1,6,'org/apache/http/impl/DefaultBHttpClientConnection.receiveResponseHeader',0,1,0)
f(25,3969,2,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(26,3969,2,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(27,3969,2,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(28,3969,2,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.read')
f(29,3969,2,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.readJavaType')
f(30,3969,2,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(31,3969,1,1,'com/fasterxml/jackson/core/JsonFactory.createParser')
f(32,3969,1,1,'com/fasterxml/jackson/core/JsonFactory._createParser')
f(33,3969,1,1,'com/fasterxml/jackson/core/json/ByteSourceJsonBootstrapper.constructParser')
f(34,3969,1,1,'com/fasterxml/jackson/core/json/ByteSourceJsonBootstrapper.detectEncoding')
f(35,3969,1,1,'com/fasterxml/jackson/core/json/ByteSourceJsonBootstrapper.ensureLoaded')
f(36,3969,1,1,'java/io/PushbackInputStream.read')
f(37,3969,1,1,'java/io/FilterInputStream.read')
f(38,3969,1,1,'org/apache/http/client/entity/LazyDecompressingInputStream.read')
f(39,3969,1,1,'java/util/zip/GZIPInputStream.read')
f(40,3969,1,1,'java/util/zip/InflaterInputStream.read')
f(41,3969,1,1,'java/util/zip/Inflater.inflate')
f(42,3969,1,1,'java/util/zip/Inflater.inflateBytes')
f(43,3969,1,3,'Java_java_util_zip_Inflater_inflateBytes')
f(44,3969,1,3,'inflate')
f(31,3970,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(32,3970,1,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(33,3970,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(34,3970,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserializeFromObject')
f(35,3970,1,1,'com/fasterxml/jackson/databind/deser/impl/MethodProperty.deserializeAndSet')
f(36,3970,1,6,'com/fasterxml/jackson/databind/deser/std/StringDeserializer.deserialize',0,1,0)
f(15,3971,4,1,'io/tapdata/Schedule/ConnectorManager.checkAndExit',0,1,0)
f(16,3972,2,1,'io/tapdata/Schedule/ConnectorManager$$Lambda$1765/1189437467.accept')
f(17,3972,2,1,'io/tapdata/Schedule/WorkerHeatBeatReports.report')
f(18,3972,2,1,'com/tapdata/mongo/HttpClientMongoOperator.insertOne')
f(19,3972,2,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(20,3972,2,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(21,3972,2,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(22,3972,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$1764/485421119.tryFunc')
f(23,3972,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$3')
f(24,3972,1,1,'org/springframework/web/client/RestTemplate.postForEntity')
f(25,3972,1,1,'org/springframework/web/client/RestTemplate.execute')
f(26,3972,1,1,'org/springframework/web/client/RestTemplate.doExecute')
f(27,3972,1,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(28,3972,1,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(29,3972,1,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(30,3972,1,1,'org/springframework/web/client/MessageBodyClientHttpResponseWrapper.hasMessageBody')
f(31,3972,1,1,'org/springframework/web/client/MessageBodyClientHttpResponseWrapper.getHeaders')
f(32,3972,1,1,'org/springframework/http/client/HttpComponentsClientHttpResponse.getHeaders')
f(33,3972,1,1,'org/springframework/http/HttpHeaders.<init>')
f(34,3972,1,6,'org/springframework/http/HttpHeaders.<init>',0,1,0)
f(35,3972,1,2,'java/util/Collections.unmodifiableList',1,0,0)
f(36,3972,1,2,'java/util/Collections$UnmodifiableList.<init>',1,0,0)
f(37,3972,1,2,'java/util/Collections$UnmodifiableCollection.<init>',1,0,0)
f(22,3973,1,1,'java/util/Optional.map')
f(23,3973,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$303/490475818.apply')
f(24,3973,1,1,'io/tapdata/Schedule/ConnectorManager$$Lambda$191/1927452108.get')
f(25,3973,1,1,'io/tapdata/Schedule/ConnectorManager.lambda$new$0')
f(26,3973,1,1,'io/tapdata/pdk/core/utils/CommonUtils.getPropertyLong')
f(27,3973,1,1,'io/tapdata/pdk/core/utils/CommonUtils.getProperty')
f(28,3973,1,1,'io/tapdata/pdk/core/utils/CommonUtils.getenv')
f(29,3973,1,1,'java/lang/System.getenv')
f(30,3973,1,1,'java/lang/ProcessEnvironment.getenv')
f(31,3973,1,1,'java/util/Collections$UnmodifiableMap.get')
f(32,3973,1,1,'java/lang/ProcessEnvironment$StringEnvironment.get')
f(33,3973,1,1,'java/lang/ProcessEnvironment$StringEnvironment.get')
f(34,3973,1,1,'java/util/HashMap.get')
f(35,3973,1,6,'java/util/HashMap.getNode',0,1,0)
f(16,3974,1,1,'io/tapdata/Schedule/ConnectorManager.checkLicenseEngineLimit')
f(17,3974,1,1,'com/tapdata/mongo/HttpClientMongoOperator.findOne')
f(18,3974,1,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(19,3974,1,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(20,3974,1,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(21,3974,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$316/**********.tryFunc')
f(22,3974,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getOne$11')
f(23,3974,1,1,'org/springframework/web/client/RestTemplate.exchange')
f(24,3974,1,1,'org/springframework/web/client/RestTemplate.execute')
f(25,3974,1,1,'org/springframework/web/client/RestTemplate.doExecute')
f(26,3974,1,1,'org/springframework/web/client/RestTemplate.handleResponse')
f(27,3974,1,6,'org/springframework/web/client/ResponseErrorHandler.handleError',0,1,0)
f(13,3975,1,1,'sun/reflect/GeneratedMethodAccessor180.invoke')
f(14,3975,1,1,'io/tapdata/Schedule/ConnectorManager.jobListener')
f(15,3975,1,1,'java/lang/String.format')
f(16,3975,1,1,'java/util/Formatter.<init>')
f(17,3975,1,1,'java/util/Formatter.<init>')
f(18,3975,1,1,'java/util/Formatter.getZero')
f(19,3975,1,1,'java/text/DecimalFormatSymbols.getInstance')
f(20,3975,1,1,'sun/util/locale/provider/DecimalFormatSymbolsProviderImpl.getInstance')
f(21,3975,1,1,'java/text/DecimalFormatSymbols.<init>')
f(22,3975,1,6,'java/text/DecimalFormatSymbols.initialize',0,1,0)
f(13,3976,1,1,'sun/reflect/GeneratedMethodAccessor631.invoke')
f(14,3976,1,1,'io/tapdata/Schedule/ConnectorManager.loadSettings')
f(15,3976,1,1,'io/tapdata/common/SettingService.loadSettings')
f(16,3976,1,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(17,3976,1,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(18,3976,1,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(19,3976,1,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(20,3976,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$339/424832797.tryFunc')
f(21,3976,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getBatch$10')
f(22,3976,1,1,'com/tapdata/mongo/RestTemplateOperator.getListBody')
f(23,3976,1,1,'com/tapdata/constant/JSONUtil.obj2Json')
f(24,3976,1,1,'com/fasterxml/jackson/databind/ObjectMapper.writeValueAsString')
f(25,3976,1,1,'com/fasterxml/jackson/databind/ObjectMapper.createGenerator')
f(26,3976,1,1,'com/fasterxml/jackson/core/JsonFactory.createGenerator')
f(27,3976,1,1,'com/fasterxml/jackson/core/JsonFactory._createGenerator')
f(28,3976,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator.<init>')
f(29,3976,1,6,'com/fasterxml/jackson/core/json/JsonGeneratorImpl.<init>',0,1,0)
f(13,3977,12,1,'sun/reflect/GeneratedMethodAccessor72.invoke')
f(14,3977,12,1,'io/tapdata/Schedule/ConnectorManager.refreshToken')
f(15,3977,10,1,'com/tapdata/mongo/HttpClientMongoOperator.findOne')
f(16,3977,10,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(17,3977,10,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(18,3977,10,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(19,3977,10,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$316/**********.tryFunc')
f(20,3977,10,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getOne$11')
f(21,3977,2,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.getURI')
f(22,3977,2,1,'org/springframework/web/util/UriComponentsBuilder.build')
f(23,3977,2,6,'org/springframework/web/util/HierarchicalUriComponents.<init>',0,1,0)
f(24,3978,1,1,'org/springframework/web/util/HierarchicalUriComponents.verify')
f(25,3978,1,1,'org/springframework/web/util/HierarchicalUriComponents$FullPathComponent.verify')
f(26,3978,1,1,'org/springframework/web/util/HierarchicalUriComponents.access$100')
f(27,3978,1,1,'org/springframework/web/util/HierarchicalUriComponents.verifyUriComponent')
f(28,3978,1,6,'org/springframework/web/util/HierarchicalUriComponents$Type$7.isAllowed',0,1,0)
f(21,3979,3,1,'com/tapdata/mongo/RestTemplateOperator.getBody')
f(22,3979,1,1,'com/tapdata/constant/JSONUtil.json2POJO')
f(23,3979,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(24,3979,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(25,3979,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(26,3979,1,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(27,3979,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(28,3979,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.vanillaDeserialize')
f(29,3979,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializerBase.handleUnknownVanilla')
f(30,3979,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializerBase.handleUnknownProperty')
f(31,3979,1,1,'com/fasterxml/jackson/databind/deser/std/StdDeserializer.handleUnknownProperty')
f(32,3979,1,1,'com/fasterxml/jackson/databind/DeserializationContext.handleUnknownProperty')
f(33,3979,1,1,'com/fasterxml/jackson/core/base/ParserMinimalBase.skipChildren')
f(34,3979,1,1,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser.nextToken')
f(35,3979,1,6,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser._skipWSOrEnd',0,1,0)
f(22,3980,2,1,'com/tapdata/constant/JSONUtil.obj2Json')
f(23,3980,2,1,'com/fasterxml/jackson/databind/ObjectMapper.writeValueAsString')
f(24,3980,2,1,'com/fasterxml/jackson/databind/ObjectMapper._writeValueAndClose')
f(25,3980,2,1,'com/fasterxml/jackson/databind/ser/DefaultSerializerProvider.serializeValue')
f(26,3980,2,1,'com/fasterxml/jackson/databind/ser/DefaultSerializerProvider._serialize')
f(27,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(28,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(29,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(30,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(31,3980,2,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(32,3980,2,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(33,3980,2,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serializeContents')
f(34,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(35,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(36,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(37,3980,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(38,3980,1,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(39,3980,1,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(40,3980,1,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serializeContents')
f(41,3980,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(42,3980,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(43,3980,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(44,3980,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(45,3980,1,6,'com/fasterxml/jackson/databind/ser/std/StringSerializer.serialize',0,1,0)
f(46,3980,1,3,'vtable stub')
f(38,3981,1,1,'com/fasterxml/jackson/databind/ser/std/StdKeySerializers$Dynamic.serialize')
f(39,3981,1,1,'com/fasterxml/jackson/databind/ser/std/StdKeySerializers$StringKeySerializer.serialize')
f(40,3981,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator.writeFieldName')
f(41,3981,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator._writeFieldName')
f(42,3981,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator._writeString')
f(43,3981,1,6,'java/lang/String.getChars',0,1,0)
f(44,3981,1,3,'jshort_disjoint_arraycopy')
f(21,3982,5,1,'org/springframework/web/client/RestTemplate.exchange')
f(22,3982,5,1,'org/springframework/web/client/RestTemplate.execute')
f(23,3982,5,1,'org/springframework/web/client/RestTemplate.doExecute')
f(24,3982,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(25,3982,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(26,3982,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(27,3982,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(28,3982,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(29,3982,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(30,3982,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(31,3982,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(32,3982,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(33,3982,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(34,3982,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(35,3982,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(36,3982,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(37,3982,2,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(38,3982,2,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(39,3982,2,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(40,3982,2,1,'org/apache/http/impl/execchain/ProtocolExec.rewriteRequestURI')
f(41,3982,2,1,'org/apache/http/client/utils/URIUtils.rewriteURIForRoute')
f(42,3982,2,6,'org/apache/http/client/utils/URIUtils.rewriteURI',0,1,0)
f(43,3983,1,1,'org/apache/http/client/utils/URIBuilder.<init>')
f(44,3983,1,1,'org/apache/http/client/utils/URIBuilder.digestURI')
f(45,3983,1,1,'org/apache/http/client/utils/URIBuilder.parseQuery')
f(46,3983,1,1,'org/apache/http/client/utils/URLEncodedUtils.parse')
f(47,3983,1,1,'org/apache/http/client/utils/URLEncodedUtils.parse')
f(48,3983,1,1,'org/apache/http/client/utils/URLEncodedUtils.decodeFormFields')
f(49,3983,1,6,'org/apache/http/client/utils/URLEncodedUtils.urlDecode',0,1,0)
f(50,3983,1,2,'java/nio/ByteBuffer.allocate',1,0,0)
f(51,3983,1,2,'java/nio/HeapByteBuffer.<init>',1,0,0)
f(24,3984,3,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(25,3984,3,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(26,3984,3,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(27,3984,3,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.read')
f(28,3984,3,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.readJavaType')
f(29,3984,3,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(30,3984,3,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(31,3984,3,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(32,3984,3,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(33,3984,3,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserializeFromObject')
f(34,3984,3,1,'com/fasterxml/jackson/databind/deser/impl/MethodProperty.deserializeAndSet')
f(35,3984,3,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(36,3984,3,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(37,3984,3,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapObject')
f(38,3984,3,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(39,3984,3,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapArray')
f(40,3984,3,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize',0,1,0)
f(41,3984,2,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapObject')
f(42,3984,2,6,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser.nextFieldName',0,1,0)
f(43,3985,1,6,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser._parseName',0,1,0)
f(44,3985,1,2,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser.parseName',1,0,0)
f(41,3986,1,3,'vtable stub')
f(15,3987,2,1,'java/lang/String.format')
f(16,3987,1,1,'java/util/Formatter.<init>')
f(17,3987,1,1,'java/util/Formatter.<init>')
f(18,3987,1,1,'java/util/Formatter.getZero')
f(19,3987,1,6,'java/text/DecimalFormatSymbols.getInstance',0,1,0)
f(16,3988,1,1,'java/util/Formatter.format')
f(17,3988,1,1,'java/util/Formatter.format')
f(18,3988,1,1,'java/util/Formatter.parse')
f(19,3988,1,1,'java/util/regex/Matcher.find')
f(20,3988,1,1,'java/util/regex/Matcher.search')
f(21,3988,1,1,'java/util/regex/Pattern$Start.match')
f(22,3988,1,1,'java/util/regex/Pattern$BmpCharProperty.match')
f(23,3988,1,1,'java/util/regex/Pattern$Branch.match')
f(24,3988,1,1,'java/util/regex/Pattern$Branch.match')
f(25,3988,1,1,'java/util/regex/Pattern$GroupHead.match')
f(26,3988,1,1,'java/util/regex/Pattern$Curly.match')
f(27,3988,1,6,'java/util/regex/Pattern$Curly.match0',0,1,0)
f(13,3989,1,1,'sun/reflect/GeneratedMethodAccessor90.invoke')
f(14,3989,1,1,'io/tapdata/flow/engine/V2/schedule/TapdataTaskScheduler.errorOrStopTask')
f(15,3989,1,1,'java/lang/String.format')
f(16,3989,1,1,'java/util/Formatter.<init>')
f(17,3989,1,1,'java/util/Formatter.<init>')
f(18,3989,1,1,'java/util/Formatter.getZero')
f(19,3989,1,6,'java/util/Locale.equals',0,1,0)
f(4,3990,4,1,'java/util/concurrent/ThreadPoolExecutor.getTask')
f(5,3990,4,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(6,3990,4,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(7,3990,4,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.await',0,1,0)
f(8,3990,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireQueued',0,1,0)
f(8,3991,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.isOnSyncQueue',1,0,0)
f(9,3991,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.findNodeFromTail',1,0,0)
f(8,3992,2,1,'java/util/concurrent/locks/LockSupport.park')
f(9,3992,2,1,'sun/misc/Unsafe.park')
f(10,3992,2,3,'Unsafe_Park')
f(11,3992,1,4,'Parker::park(bool, long)')
f(12,3992,1,3,'__psynch_cvwait')
f(11,3993,1,4,'java_lang_Thread::set_thread_status(oopDesc*, java_lang_Thread::ThreadStatus)')
f(4,3994,1,1,'sun/nio/ch/AsynchronousChannelGroupImpl$1.run')
f(5,3994,1,1,'sun/nio/ch/Invoker$2.run')
f(6,3994,1,1,'sun/nio/ch/Invoker.invokeUnchecked')
f(7,3994,1,1,'org/apache/tomcat/websocket/WsFrameClient$WsFrameClientCompletionHandler.completed')
f(8,3994,1,1,'org/apache/tomcat/websocket/WsFrameClient$WsFrameClientCompletionHandler.completed')
f(9,3994,1,1,'org/apache/tomcat/websocket/WsFrameClient$WsFrameClientCompletionHandler.doResumeProcessing')
f(10,3994,1,1,'org/apache/tomcat/websocket/WsFrameClient.access$300')
f(11,3994,1,1,'org/apache/tomcat/websocket/WsFrameClient.resumeProcessing')
f(12,3994,1,1,'org/apache/tomcat/websocket/WsFrameClient.processSocketRead')
f(13,3994,1,1,'org/apache/tomcat/websocket/WsFrameBase.processInputBuffer')
f(14,3994,1,1,'org/apache/tomcat/websocket/WsFrameBase.processData')
f(15,3994,1,1,'org/apache/tomcat/websocket/WsFrameBase.processDataControl')
f(16,3994,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointBase.sendPong')
f(17,3994,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendPong')
f(18,3994,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendMessageBlock')
f(19,3994,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendMessageBlock')
f(20,3994,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.writeMessagePart')
f(21,3994,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase$OutputBufferSendHandler.write')
f(22,3994,1,6,'org/apache/tomcat/websocket/WsRemoteEndpointImplClient.doWrite',0,1,0)
f(2,3995,314,1,'org/apache/kafka/clients/producer/internals/Sender.run',0,1,0)
f(3,3996,313,1,'org/apache/kafka/clients/producer/internals/Sender.runOnce')
f(4,3996,171,1,'org/apache/kafka/clients/NetworkClient.poll')
f(5,3996,1,1,'org/apache/kafka/clients/NetworkClient$DefaultMetadataUpdater.maybeUpdate')
f(6,3996,1,1,'org/apache/kafka/clients/Metadata.timeToNextUpdate')
f(7,3996,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(8,3996,1,4,'ObjectSynchronizer::fast_exit(oopDesc*, BasicLock*, Thread*)')
f(5,3997,34,1,'org/apache/kafka/clients/NetworkClient.completeResponses')
f(6,3997,34,1,'org/apache/kafka/clients/ClientResponse.onComplete')
f(7,3997,34,1,'org/apache/kafka/clients/producer/internals/Sender$$Lambda$3157/429953851.onComplete')
f(8,3997,34,1,'org/apache/kafka/clients/producer/internals/Sender.lambda$sendProduceRequest$5')
f(9,3997,34,1,'org/apache/kafka/clients/producer/internals/Sender.handleProduceResponse')
f(10,3997,32,1,'java/lang/Iterable.forEach')
f(11,3997,31,1,'org/apache/kafka/clients/producer/internals/Sender$$Lambda$3160/351292150.accept')
f(12,3997,31,1,'org/apache/kafka/clients/producer/internals/Sender.lambda$handleProduceResponse$2')
f(13,3997,31,1,'java/util/ArrayList.forEach')
f(14,3997,31,1,'org/apache/kafka/clients/producer/internals/Sender$$Lambda$3161/1720623670.accept')
f(15,3997,31,1,'org/apache/kafka/clients/producer/internals/Sender.lambda$null$1',0,1,0)
f(16,3998,1,1,'java/util/HashMap.get')
f(17,3998,1,1,'java/util/HashMap.hash')
f(18,3998,1,1,'org/apache/kafka/common/TopicPartition.hashCode')
f(19,3998,1,1,'java/util/Objects.hashCode')
f(20,3998,1,6,'java/lang/String.hashCode',0,1,0)
f(16,3999,1,1,'java/util/stream/ReferencePipeline.collect')
f(17,3999,1,1,'java/util/stream/AbstractPipeline.evaluate')
f(18,3999,1,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(19,3999,1,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(20,3999,1,6,'java/util/stream/AbstractPipeline.copyInto',0,1,0)
f(21,3999,1,3,'itable stub')
f(16,4000,28,1,'org/apache/kafka/clients/producer/internals/Sender.completeBatch',0,1,0)
f(17,4001,27,1,'org/apache/kafka/clients/producer/internals/Sender.completeBatch',0,2,0)
f(18,4001,1,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.complete')
f(19,4001,1,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.done')
f(20,4001,1,1,'org/apache/logging/slf4j/Log4jLogger.trace')
f(21,4001,1,1,'org/apache/logging/log4j/spi/AbstractLogger.logIfEnabled')
f(22,4001,1,6,'org/apache/logging/log4j/core/Logger.isEnabled',0,1,0)
f(18,4002,18,1,'org/apache/kafka/clients/producer/internals/Sender.maybeRemoveAndDeallocateBatch',2,0,0)
f(19,4002,16,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.deallocate',2,0,0)
f(20,4004,8,1,'org/apache/kafka/clients/producer/internals/BufferPool.deallocate',0,1,0)
f(21,4005,2,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(22,4005,2,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock')
f(23,4005,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquire')
f(24,4005,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireQueued')
f(25,4005,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.parkAndCheckInterrupt')
f(26,4005,2,1,'java/util/concurrent/locks/LockSupport.park')
f(27,4005,2,1,'sun/misc/Unsafe.park')
f(28,4005,2,3,'Unsafe_Park')
f(29,4005,2,4,'Parker::park(bool, long)')
f(30,4005,2,3,'__psynch_cvwait')
f(21,4007,5,1,'java/util/concurrent/locks/ReentrantLock.unlock')
f(22,4007,5,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release')
f(23,4007,5,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.unparkSuccessor')
f(24,4007,5,1,'java/util/concurrent/locks/LockSupport.unpark')
f(25,4007,5,1,'sun/misc/Unsafe.unpark')
f(26,4007,5,3,'Unsafe_Unpark')
f(27,4007,4,4,'Parker::unpark()')
f(28,4007,4,3,'__psynch_cvsignal')
f(27,4011,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(20,4012,6,1,'org/apache/kafka/clients/producer/internals/IncompleteBatches.remove')
f(21,4012,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(22,4012,1,3,'pthread_getspecific')
f(21,4013,5,1,'java/util/HashSet.remove')
f(22,4013,5,1,'java/util/HashMap.remove',0,1,0)
f(23,4013,1,2,'java/util/HashMap.hash',1,0,0)
f(24,4013,1,3,'vtable stub')
f(23,4014,4,6,'java/util/HashMap.removeNode',0,4,0)
f(19,4018,2,1,'org/apache/kafka/clients/producer/internals/Sender.maybeRemoveFromInflightBatches')
f(20,4018,2,1,'java/util/ArrayList.remove')
f(21,4018,2,6,'java/lang/Object.equals',0,2,0)
f(18,4020,8,1,'org/apache/kafka/clients/producer/internals/TransactionManager.handleCompletedBatch',0,1,0)
f(19,4021,4,1,'org/apache/kafka/clients/producer/internals/TransactionManager.maybeUpdateLastAckedSequence')
f(20,4021,4,1,'org/apache/kafka/clients/producer/internals/TransactionManager.lastAckedSequence')
f(21,4021,4,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1500')
f(22,4021,4,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.lastAckedSequence')
f(23,4021,4,6,'java/util/HashMap.get',0,3,0)
f(24,4022,1,1,'java/util/HashMap.getNode')
f(25,4022,1,1,'org/apache/kafka/common/TopicPartition.equals')
f(26,4022,1,1,'java/util/Objects.equals')
f(27,4022,1,6,'java/lang/String.equals',0,1,0)
f(24,4023,2,2,'java/util/HashMap.hash',2,0,0)
f(25,4023,2,3,'vtable stub')
f(19,4025,2,1,'org/apache/kafka/clients/producer/internals/TransactionManager.updateLastAckedOffset')
f(20,4025,2,1,'org/apache/kafka/clients/producer/internals/TransactionManager.lastAckedOffset')
f(21,4025,2,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1600')
f(22,4025,2,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.lastAckedOffset')
f(23,4025,2,1,'java/util/HashMap.get')
f(24,4025,2,1,'java/util/HashMap.getNode')
f(25,4025,2,1,'org/apache/kafka/common/TopicPartition.equals')
f(26,4025,2,1,'java/util/Objects.equals')
f(27,4025,2,6,'java/lang/String.equals',0,2,0)
f(19,4027,1,1,'org/apache/kafka/common/utils/LogContext$LocationAwareKafkaLogger.debug')
f(20,4027,1,1,'org/apache/logging/slf4j/Log4jLogger.isDebugEnabled')
f(21,4027,1,6,'org/apache/logging/log4j/core/Logger.isEnabled',0,1,0)
f(11,4028,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.hasNext',0,1,0)
f(10,4029,2,6,'org/apache/kafka/clients/producer/internals/Sender$SenderMetrics.recordLatency',0,1,0)
f(11,4030,1,1,'java/lang/StringBuilder.append')
f(12,4030,1,1,'java/lang/AbstractStringBuilder.append')
f(13,4030,1,6,'java/lang/String.getChars',0,1,0)
f(14,4030,1,3,'jshort_arraycopy')
f(5,4031,11,1,'org/apache/kafka/clients/NetworkClient.handleCompletedReceives')
f(6,4031,1,1,'java/util/ArrayList.add')
f(7,4031,1,1,'java/util/ArrayList.ensureCapacityInternal')
f(8,4031,1,1,'java/util/ArrayList.ensureExplicitCapacity')
f(9,4031,1,1,'java/util/ArrayList.grow')
f(10,4031,1,1,'java/util/Arrays.copyOf')
f(11,4031,1,6,'java/util/Arrays.copyOf',0,1,0)
f(6,4032,8,1,'org/apache/kafka/clients/NetworkClient.parseResponse')
f(7,4032,8,1,'org/apache/kafka/common/requests/AbstractResponse.parseResponse')
f(8,4032,6,1,'org/apache/kafka/common/requests/AbstractResponse.parseResponse',0,1,0)
f(9,4033,5,1,'org/apache/kafka/common/requests/ProduceResponse.parse')
f(10,4033,5,1,'org/apache/kafka/common/message/ProduceResponseData.<init>')
f(11,4033,5,1,'org/apache/kafka/common/message/ProduceResponseData.read')
f(12,4033,4,1,'org/apache/kafka/common/message/ProduceResponseData$TopicProduceResponse.<init>')
f(13,4033,4,1,'org/apache/kafka/common/message/ProduceResponseData$TopicProduceResponse.read')
f(14,4033,1,1,'org/apache/kafka/common/message/ProduceResponseData$PartitionProduceResponse.<init>')
f(15,4033,1,1,'org/apache/kafka/common/message/ProduceResponseData$PartitionProduceResponse.read')
f(16,4033,1,6,'org/apache/kafka/common/protocol/ByteBufferAccessor.readInt',0,1,0)
f(14,4034,3,1,'org/apache/kafka/common/protocol/Readable.readString')
f(15,4034,3,1,'java/lang/String.<init>')
f(16,4034,3,1,'java/lang/String.<init>')
f(17,4034,3,1,'java/lang/StringCoding.decode',0,1,0)
f(18,4035,1,6,'sun/nio/cs/UTF_8$Decoder.decode',0,1,0)
f(18,4036,1,6,'sun/nio/cs/UTF_8.newDecoder',0,1,0)
f(12,4037,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.add')
f(13,4037,1,6,'org/apache/kafka/common/message/ProduceResponseData$TopicProduceResponse.next',0,1,0)
f(8,4038,1,1,'org/apache/kafka/common/requests/RequestHeader.apiKey')
f(9,4038,1,1,'org/apache/kafka/common/protocol/ApiKeys.forId')
f(10,4038,1,1,'java/util/HashMap.get')
f(11,4038,1,6,'java/util/HashMap.getNode',0,1,0)
f(8,4039,1,1,'org/apache/kafka/common/requests/ResponseHeader.parse')
f(9,4039,1,1,'org/apache/kafka/common/message/ResponseHeaderData.<init>')
f(10,4039,1,1,'org/apache/kafka/common/message/ResponseHeaderData.read')
f(11,4039,1,6,'org/apache/kafka/common/protocol/ByteBufferAccessor.readInt',0,1,0)
f(6,4040,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(7,4040,1,6,'org/apache/kafka/common/metrics/Sensor.recordInternal',0,1,0)
f(6,4041,1,1,'org/apache/kafka/common/utils/LogContext$LocationAwareKafkaLogger.isDebugEnabled')
f(7,4041,1,6,'org/apache/logging/slf4j/Log4jLogger.isDebugEnabled',0,1,0)
f(5,4042,1,1,'org/apache/kafka/clients/NetworkClient.handleDisconnections')
f(6,4042,1,6,'java/util/HashMap$EntrySet.iterator',0,1,0)
f(5,4043,6,1,'org/apache/kafka/clients/NetworkClient.handleTimedOutConnections',0,1,0)
f(6,4044,5,1,'org/apache/kafka/clients/ClusterConnectionStates.nodesWithConnectionSetupTimeout')
f(7,4044,2,1,'java/util/Collection.stream')
f(8,4044,2,1,'java/util/stream/StreamSupport.stream')
f(9,4044,2,1,'java/util/stream/StreamOpFlag.fromCharacteristics')
f(10,4044,2,6,'java/util/HashMap$KeySpliterator.characteristics',0,2,0)
f(7,4046,3,1,'java/util/stream/ReferencePipeline.collect')
f(8,4046,3,1,'java/util/stream/AbstractPipeline.evaluate')
f(9,4046,1,1,'java/util/stream/ReduceOps$3.getOpFlags')
f(10,4046,1,6,'java/util/Collections$UnmodifiableCollection.contains',0,1,0)
f(9,4047,2,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(10,4047,2,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(11,4047,2,1,'java/util/stream/AbstractPipeline.copyInto')
f(12,4047,2,6,'java/util/stream/ReferencePipeline$2$1.begin',0,1,0)
f(13,4048,1,6,'java/util/stream/ReduceOps$3ReducingSink.begin',0,1,0)
f(5,4049,3,1,'org/apache/kafka/clients/NetworkClient.handleTimedOutRequests')
f(6,4049,3,1,'org/apache/kafka/clients/InFlightRequests.nodesWithTimedOutRequests')
f(7,4049,1,6,'java/util/HashMap$HashIterator.hasNext',0,1,0)
f(7,4050,1,6,'java/util/HashMap$Node.getValue',0,1,0)
f(7,4051,1,1,'org/apache/kafka/clients/InFlightRequests.hasExpiredRequest')
f(8,4051,1,6,'java/util/ArrayDeque.iterator',0,1,0)
f(5,4052,114,1,'org/apache/kafka/common/network/Selector.poll')
f(6,4052,5,1,'org/apache/kafka/common/metrics/Sensor.record')
f(7,4052,5,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(8,4052,1,1,'org/apache/kafka/common/metrics/Sensor.checkQuotas')
f(9,4052,1,6,'java/util/LinkedHashMap$LinkedHashIterator.hasNext',0,1,0)
f(8,4053,3,1,'org/apache/kafka/common/metrics/stats/Meter.record')
f(9,4053,1,6,'org/apache/kafka/common/metrics/stats/CumulativeSum.record',0,1,0)
f(9,4054,2,1,'org/apache/kafka/common/metrics/stats/Rate.record')
f(10,4054,2,1,'org/apache/kafka/common/metrics/stats/SampledStat.record')
f(11,4054,1,6,'org/apache/kafka/common/metrics/stats/SampledStat.current',0,1,0)
f(11,4055,1,6,'org/apache/kafka/common/metrics/stats/WindowedCount.update',0,1,0)
f(8,4056,1,1,'org/apache/kafka/common/metrics/stats/SampledStat.record')
f(9,4056,1,1,'org/apache/kafka/common/metrics/stats/SampledStat.current')
f(10,4056,1,6,'java/util/ArrayList.get',0,1,0)
f(6,4057,1,1,'org/apache/kafka/common/network/Selector.clear')
f(7,4057,1,1,'java/util/LinkedHashMap.clear')
f(8,4057,1,6,'java/util/HashMap.clear',0,1,0)
f(6,4058,89,1,'org/apache/kafka/common/network/Selector.pollSelectionKeys',0,1,0)
f(7,4058,1,3,'itable stub')
f(7,4059,1,1,'org/apache/kafka/common/network/KafkaChannel.pollResponseReceivedDuringReauthentication')
f(8,4059,1,6,'org/apache/kafka/common/network/Authenticator.pollResponseReceivedDuringReauthentication',0,1,0)
f(7,4060,1,1,'org/apache/kafka/common/network/Selector$IdleExpiryManager.update')
f(8,4060,1,1,'java/util/HashMap.put')
f(9,4060,1,6,'java/util/HashMap.putVal',0,1,0)
f(10,4060,1,3,'vtable stub')
f(7,4061,4,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.maybeRegisterConnectionMetrics')
f(8,4061,4,1,'org/apache/kafka/common/metrics/Metrics.getSensor')
f(9,4061,4,1,'java/util/concurrent/ConcurrentHashMap.get',0,1,0)
f(10,4062,1,6,'java/lang/String.equals',0,1,0)
f(10,4063,2,6,'java/lang/String.hashCode',0,2,0)
f(7,4065,9,1,'org/apache/kafka/common/network/Selector.attemptRead')
f(8,4065,7,1,'org/apache/kafka/common/network/KafkaChannel.read')
f(9,4065,7,1,'org/apache/kafka/common/network/KafkaChannel.receive')
f(10,4065,7,1,'org/apache/kafka/common/network/NetworkReceive.readFrom')
f(11,4065,7,1,'org/apache/kafka/common/network/PlaintextTransportLayer.read')
f(12,4065,7,1,'sun/nio/ch/SocketChannelImpl.read')
f(13,4065,7,1,'sun/nio/ch/IOUtil.read')
f(14,4065,1,1,'java/nio/HeapByteBuffer.put')
f(15,4065,1,1,'java/nio/DirectByteBuffer.get')
f(16,4065,1,6,'java/nio/ByteBuffer.get',0,1,0)
f(14,4066,6,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(15,4066,6,1,'sun/nio/ch/SocketDispatcher.read')
f(16,4066,6,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(17,4067,1,3,'jni_GetIntField')
f(17,4068,4,3,'read')
f(8,4072,1,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.recordBytesReceived')
f(9,4072,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(10,4072,1,6,'org/apache/kafka/common/metrics/Sensor.recordInternal',0,1,0)
f(8,4073,1,1,'org/apache/kafka/common/network/Selector.addToCompletedReceives')
f(9,4073,1,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.recordCompletedReceive')
f(10,4073,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(11,4073,1,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(12,4073,1,1,'org/apache/kafka/common/metrics/Sensor.checkQuotas')
f(13,4073,1,6,'java/util/LinkedHashMap$LinkedValueIterator.next',0,1,0)
f(7,4074,73,1,'org/apache/kafka/common/network/Selector.attemptWrite')
f(8,4074,73,1,'org/apache/kafka/common/network/Selector.write')
f(9,4074,70,1,'org/apache/kafka/common/network/KafkaChannel.write')
f(10,4074,70,1,'org/apache/kafka/common/network/NetworkSend.writeTo')
f(11,4074,70,1,'org/apache/kafka/common/network/ByteBufferSend.writeTo')
f(12,4074,70,1,'org/apache/kafka/common/network/PlaintextTransportLayer.write')
f(13,4074,70,1,'java/nio/channels/SocketChannel.write')
f(14,4074,70,1,'sun/nio/ch/SocketChannelImpl.write')
f(15,4074,69,1,'sun/nio/ch/IOUtil.write')
f(16,4074,1,6,'java/nio/Buffer.position',0,1,0)
f(16,4075,4,1,'java/nio/DirectByteBuffer.put')
f(17,4075,4,1,'java/nio/DirectByteBuffer.put')
f(18,4075,4,1,'java/nio/Bits.copyFromArray')
f(19,4075,4,1,'sun/misc/Unsafe.copyMemory')
f(20,4076,1,4,'HandleMarkCleaner::~HandleMarkCleaner()')
f(20,4077,2,3,'Unsafe_CopyMemory2')
f(21,4077,2,4,'Copy::conjoint_memory_atomic(void*, void*, unsigned long)')
f(22,4077,2,3,'_platform_memmove$VARIANT$Rosetta')
f(16,4079,63,1,'sun/nio/ch/SocketDispatcher.writev')
f(17,4079,63,1,'sun/nio/ch/FileDispatcherImpl.writev0')
f(18,4079,63,3,'writev')
f(16,4142,1,6,'sun/nio/ch/Util.offerLastTemporaryDirectBuffer',0,1,0)
f(15,4143,1,6,'sun/nio/ch/SocketChannelImpl.ensureWriteOpen',0,1,0)
f(9,4144,3,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.recordBytesSent')
f(10,4144,3,1,'org/apache/kafka/common/metrics/Sensor.record')
f(11,4144,2,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(12,4144,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(13,4144,1,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(14,4144,1,1,'org/apache/kafka/common/metrics/stats/Meter.record')
f(15,4144,1,1,'org/apache/kafka/common/metrics/stats/Rate.record')
f(16,4144,1,1,'org/apache/kafka/common/metrics/stats/SampledStat.record')
f(17,4144,1,1,'org/apache/kafka/common/metrics/stats/SampledStat.current')
f(18,4144,1,6,'java/util/ArrayList.get',0,1,0)
f(19,4144,1,2,'java/util/ArrayList.rangeCheck',1,0,0)
f(12,4145,1,6,'org/apache/kafka/common/metrics/stats/Meter.record',0,1,0)
f(13,4145,1,2,'org/apache/kafka/common/metrics/stats/Rate.record',1,0,0)
f(11,4146,1,1,'org/apache/kafka/common/metrics/Sensor.shouldRecord')
f(12,4146,1,6,'org/apache/kafka/common/metrics/Sensor$RecordingLevel.shouldRecord',0,1,0)
f(6,4147,19,1,'org/apache/kafka/common/network/Selector.select')
f(7,4147,8,1,'sun/nio/ch/SelectorImpl.select')
f(8,4147,8,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(9,4147,8,1,'sun/nio/ch/KQueueSelectorImpl.doSelect')
f(10,4147,8,1,'sun/nio/ch/KQueueArrayWrapper.poll')
f(11,4147,6,1,'sun/nio/ch/KQueueArrayWrapper.kevent0')
f(12,4148,5,3,'kevent')
f(11,4153,2,1,'sun/nio/ch/KQueueArrayWrapper.updateRegistrations')
f(12,4153,2,1,'sun/nio/ch/KQueueArrayWrapper.register0')
f(13,4153,2,3,'kevent')
f(7,4155,11,1,'sun/nio/ch/SelectorImpl.selectNow')
f(8,4155,11,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(9,4155,11,1,'sun/nio/ch/KQueueSelectorImpl.doSelect')
f(10,4155,1,6,'java/nio/channels/spi/AbstractSelector.begin',0,1,0)
f(10,4156,7,1,'sun/nio/ch/KQueueArrayWrapper.poll')
f(11,4156,7,1,'sun/nio/ch/KQueueArrayWrapper.updateRegistrations',0,1,0)
f(12,4157,6,1,'sun/nio/ch/KQueueArrayWrapper.register0')
f(13,4157,6,3,'kevent')
f(10,4163,3,6,'sun/nio/ch/KQueueSelectorImpl.updateSelectedKeys',0,2,0)
f(11,4164,1,3,'monitorexit_nofpu Runtime1 stub')
f(11,4165,1,1,'sun/nio/ch/IOUtil.drain')
f(12,4165,1,3,'read')
f(5,4166,1,6,'org/apache/kafka/common/utils/Utils.min',0,1,0)
f(4,4167,142,1,'org/apache/kafka/clients/producer/internals/Sender.sendProducerData')
f(5,4167,1,6,'java/util/HashMap$HashIterator.hasNext',0,1,0)
f(5,4168,1,6,'java/util/HashMap$HashIterator.remove',0,1,0)
f(6,4168,1,2,'java/util/HashMap.hash',1,0,0)
f(7,4168,1,3,'vtable stub')
f(5,4169,6,1,'org/apache/kafka/clients/NetworkClient.ready')
f(6,4169,6,1,'org/apache/kafka/clients/NetworkClient.isReady')
f(7,4169,3,1,'org/apache/kafka/clients/NetworkClient$DefaultMetadataUpdater.isUpdateDue')
f(8,4169,3,1,'org/apache/kafka/clients/Metadata.timeToNextUpdate')
f(9,4169,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(10,4169,1,3,'pthread_getspecific')
f(9,4170,1,1,'org/apache/kafka/clients/Metadata.timeToAllowUpdate')
f(10,4170,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(11,4170,1,4,'ObjectMonitor::exit(bool, Thread*)')
f(9,4171,1,1,'org/apache/kafka/clients/Metadata.updateRequested')
f(10,4171,1,4,'HandleMarkCleaner::~HandleMarkCleaner()')
f(7,4172,3,1,'org/apache/kafka/clients/NetworkClient.canSendRequest',0,1,0)
f(8,4173,1,1,'org/apache/kafka/clients/ClusterConnectionStates.isReady')
f(9,4173,1,1,'java/util/HashMap.get')
f(10,4173,1,6,'java/util/HashMap.getNode',0,1,0)
f(11,4173,1,3,'vtable stub')
f(8,4174,1,1,'org/apache/kafka/clients/InFlightRequests.canSendMore')
f(9,4174,1,6,'java/util/ArrayDeque.peekFirst',0,1,0)
f(5,4175,55,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.drain')
f(6,4175,55,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.drainBatchesForOneNode',0,2,0)
f(7,4175,22,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(8,4175,22,4,'ObjectMonitor::enter(Thread*)')
f(9,4175,20,4,'ObjectMonitor::EnterI(Thread*)')
f(10,4175,5,4,'os::PlatformEvent::park()')
f(11,4175,5,3,'__psynch_cvwait')
f(10,4180,15,4,'os::PlatformEvent::park(long)')
f(11,4180,1,3,'__gettimeofday')
f(11,4181,14,3,'__psynch_cvwait')
f(9,4195,2,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(7,4197,1,1,'java/util/Collections$UnmodifiableList.get')
f(8,4197,1,6,'java/util/ArrayList.get',0,1,0)
f(9,4197,1,2,'java/util/ArrayList.rangeCheck',1,0,0)
f(7,4198,26,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.close')
f(8,4198,26,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.close')
f(9,4198,2,6,'java/nio/HeapByteBuffer.duplicate',0,1,0)
f(10,4199,1,1,'java/nio/HeapByteBuffer.<init>')
f(11,4199,1,6,'java/nio/ByteBuffer.<init>',0,1,0)
f(9,4200,24,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.writeDefaultBatchHeader')
f(10,4200,24,1,'org/apache/kafka/common/record/DefaultRecordBatch.writeHeader')
f(11,4200,24,1,'org/apache/kafka/common/utils/Crc32C.compute')
f(12,4200,24,1,'org/apache/kafka/common/utils/Checksums.update')
f(13,4200,24,6,'org/apache/kafka/common/utils/PureJavaCrc32C.update',0,24,0)
f(7,4224,2,2,'org/apache/kafka/clients/producer/internals/ProducerBatch.estimatedSizeInBytes',2,0,0)
f(8,4224,2,2,'org/apache/kafka/common/record/MemoryRecordsBuilder.estimatedSizeInBytes',2,0,0)
f(7,4226,1,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.records')
f(8,4226,1,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.build')
f(9,4226,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(10,4226,1,4,'ObjectMonitor::ExitEpilog(Thread*, ObjectWaiter*)')
f(11,4226,1,4,'os::PlatformEvent::unpark()')
f(12,4226,1,3,'__psynch_cvsignal')
f(7,4227,3,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.shouldStopDrainBatchesForPartition',0,1,0)
f(8,4228,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.firstInFlightSequence')
f(9,4228,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.hasInflightBatches')
f(10,4228,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1200')
f(11,4228,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.getOrCreatePartition')
f(12,4228,1,1,'java/util/HashMap.get')
f(13,4228,1,1,'java/util/HashMap.getNode')
f(14,4228,1,1,'org/apache/kafka/common/TopicPartition.equals')
f(15,4228,1,1,'java/util/Objects.equals')
f(16,4228,1,6,'java/lang/String.equals',0,1,0)
f(8,4229,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.hasInflightBatches')
f(9,4229,1,1,'java/util/TreeSet.isEmpty')
f(10,4229,1,6,'java/util/AbstractMap.isEmpty',0,1,0)
f(5,4230,20,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.expiredBatches',0,3,0)
f(6,4231,13,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(7,4231,13,4,'ObjectMonitor::enter(Thread*)')
f(8,4231,12,4,'ObjectMonitor::EnterI(Thread*)')
f(9,4231,1,3,'gettimeofday')
f(9,4232,2,4,'os::PlatformEvent::park()')
f(10,4232,2,3,'__psynch_cvwait')
f(9,4234,9,4,'os::PlatformEvent::park(long)')
f(10,4234,9,3,'__psynch_cvwait')
f(8,4243,1,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(9,4243,1,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(6,4244,2,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(7,4244,1,4,'ObjectMonitor::ExitEpilog(Thread*, ObjectWaiter*)')
f(8,4244,1,4,'os::PlatformEvent::unpark()')
f(9,4244,1,3,'__psynch_cvsignal')
f(7,4245,1,4,'ObjectSynchronizer::fast_exit(oopDesc*, BasicLock*, Thread*)')
f(6,4246,2,3,'itable stub')
f(6,4248,1,6,'java/util/ArrayDeque.getFirst',0,1,0)
f(6,4249,1,1,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.next')
f(7,4249,1,1,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.next')
f(8,4249,1,6,'java/util/HashMap$EntryIterator.next',0,1,0)
f(5,4250,29,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.ready')
f(6,4251,1,4,'Arena::Amalloc_4(unsigned long, AllocFailStrategy::AllocFailEnum)')
f(6,4252,18,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(7,4252,18,4,'ObjectMonitor::enter(Thread*)')
f(8,4252,17,4,'ObjectMonitor::EnterI(Thread*)')
f(9,4252,2,4,'os::PlatformEvent::park()')
f(10,4252,2,3,'__psynch_cvwait')
f(9,4254,15,4,'os::PlatformEvent::park(long)')
f(10,4254,14,3,'__psynch_cvwait')
f(10,4268,1,3,'_pthread_cond_wait')
f(8,4269,1,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(6,4270,1,6,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.next',0,1,0)
f(7,4270,1,2,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.next',1,0,0)
f(8,4270,1,3,'itable stub')
f(6,4271,1,1,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet.iterator')
f(7,4271,1,1,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.<init>')
f(8,4271,1,6,'java/util/HashMap$EntrySet.iterator',0,1,0)
f(6,4272,1,1,'java/util/HashSet.add')
f(7,4272,1,1,'java/util/HashMap.put')
f(8,4272,1,1,'java/util/HashMap.putVal')
f(9,4272,1,6,'java/util/HashMap.afterNodeInsertion',0,1,0)
f(6,4273,3,1,'org/apache/kafka/clients/producer/internals/BufferPool.queued')
f(7,4273,3,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(8,4273,3,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock')
f(9,4273,3,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquire')
f(10,4273,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireQueued')
f(11,4273,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer.shouldParkAfterFailedAcquire',0,1,0)
f(11,4274,1,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.tryAcquire')
f(12,4274,1,6,'java/util/concurrent/locks/ReentrantLock$Sync.nonfairTryAcquire',0,1,0)
f(10,4275,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer.addWaiter',0,1,0)
f(6,4276,3,1,'org/apache/kafka/common/Cluster.leaderFor')
f(7,4276,3,1,'java/util/Collections$UnmodifiableMap.get')
f(8,4276,3,1,'java/util/HashMap.get',0,1,0)
f(9,4276,2,6,'java/util/HashMap.getNode',0,1,0)
f(10,4277,1,1,'org/apache/kafka/common/TopicPartition.equals')
f(11,4277,1,1,'java/util/Objects.equals')
f(12,4277,1,6,'java/lang/String.equals',0,1,0)
f(9,4278,1,2,'java/util/HashMap.hash',1,0,0)
f(10,4278,1,3,'vtable stub')
f(5,4279,13,1,'org/apache/kafka/clients/producer/internals/Sender$SenderMetrics.updateProduceRequestMetrics',0,1,0)
f(6,4279,1,1,'java/lang/StringBuilder.append')
f(7,4279,1,1,'java/lang/AbstractStringBuilder.append')
f(8,4279,1,1,'java/lang/AbstractStringBuilder.ensureCapacityInternal')
f(9,4279,1,6,'java/lang/AbstractStringBuilder.newCapacity',0,1,0)
f(6,4280,1,2,'java/lang/StringBuilder.toString',1,0,0)
f(6,4281,2,6,'org/apache/kafka/clients/producer/internals/Sender$SenderMetrics.maybeRegisterTopicMetrics',0,2,0)
f(7,4281,1,2,'java/lang/StringBuilder.append',1,0,0)
f(7,4282,1,2,'java/lang/StringBuilder.toString',1,0,0)
f(6,4283,1,1,'org/apache/kafka/clients/producer/internals/SenderMetricsRegistry.getSensor')
f(7,4283,1,1,'org/apache/kafka/common/metrics/Metrics.getSensor')
f(8,4283,1,6,'java/util/concurrent/ConcurrentHashMap.get',0,1,0)
f(9,4283,1,3,'vtable stub')
f(6,4284,8,1,'org/apache/kafka/common/metrics/Sensor.record')
f(7,4284,5,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(8,4284,2,6,'java/util/ArrayList$Itr.hasNext',0,2,0)
f(8,4286,2,1,'org/apache/kafka/common/metrics/Sensor.checkQuotas')
f(9,4286,1,6,'java/util/LinkedHashMap$LinkedHashIterator.hasNext',0,1,0)
f(9,4287,1,6,'java/util/LinkedHashMap$LinkedValueIterator.next',0,1,0)
f(8,4288,1,6,'org/apache/kafka/common/metrics/stats/SampledStat.record',0,1,0)
f(7,4289,1,1,'org/apache/kafka/common/metrics/Sensor.shouldRecord')
f(8,4289,1,6,'org/apache/kafka/common/metrics/Sensor$RecordingLevel.shouldRecord',0,1,0)
f(7,4290,2,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,2,0)
f(8,4291,1,4,'os::javaTimeMillis()')
f(9,4291,1,3,'gettimeofday')
f(10,4291,1,3,'__commpage_gettimeofday_internal')
f(11,4291,1,3,'mach_absolute_time')
f(5,4292,1,1,'org/apache/kafka/clients/producer/internals/Sender.addToInflightBatches')
f(6,4292,1,1,'org/apache/kafka/clients/producer/internals/Sender.addToInflightBatches')
f(7,4292,1,1,'java/util/HashMap.get')
f(8,4292,1,6,'java/util/HashMap.getNode',0,1,0)
f(5,4293,1,1,'org/apache/kafka/clients/producer/internals/Sender.getExpiredInflightBatches')
f(6,4293,1,1,'java/util/HashMap$EntrySet.iterator')
f(7,4293,1,1,'java/util/HashMap$EntryIterator.<init>')
f(8,4293,1,6,'java/util/HashMap$HashIterator.<init>',0,1,0)
f(5,4294,14,1,'org/apache/kafka/clients/producer/internals/Sender.sendProduceRequests')
f(6,4294,14,1,'org/apache/kafka/clients/producer/internals/Sender.sendProduceRequest')
f(7,4294,1,1,'java/util/ArrayList.add')
f(8,4294,1,1,'java/util/ArrayList.ensureCapacityInternal')
f(9,4294,1,1,'java/util/ArrayList.ensureExplicitCapacity')
f(10,4294,1,1,'java/util/ArrayList.grow')
f(11,4294,1,1,'java/util/Arrays.copyOf')
f(12,4294,1,6,'java/util/Arrays.copyOf',0,1,0)
f(7,4295,11,1,'org/apache/kafka/clients/NetworkClient.send')
f(8,4295,11,1,'org/apache/kafka/clients/NetworkClient.doSend')
f(9,4295,1,1,'org/apache/kafka/clients/NetworkClient.canSendRequest')
f(10,4295,1,1,'org/apache/kafka/clients/InFlightRequests.canSendMore')
f(11,4295,1,6,'java/util/HashMap.get',0,1,0)
f(9,4296,8,1,'org/apache/kafka/clients/NetworkClient.doSend')
f(10,4296,1,1,'org/apache/kafka/clients/InFlightRequests.add')
f(11,4296,1,6,'java/util/ArrayDeque.addFirst',0,1,0)
f(10,4297,7,1,'org/apache/kafka/common/requests/AbstractRequest.toSend')
f(11,4297,7,1,'org/apache/kafka/common/protocol/SendBuilder.buildRequestSend')
f(12,4297,7,1,'org/apache/kafka/common/protocol/SendBuilder.buildSend')
f(13,4297,1,1,'org/apache/kafka/common/message/ProduceRequestData.addSize')
f(14,4297,1,1,'org/apache/kafka/common/message/ProduceRequestData$TopicProduceData.addSize')
f(15,4297,1,1,'java/lang/String.getBytes')
f(16,4297,1,6,'java/lang/StringCoding.encode',0,1,0)
f(13,4298,3,1,'org/apache/kafka/common/message/ProduceRequestData.write',0,1,0)
f(14,4299,2,1,'org/apache/kafka/common/message/ProduceRequestData$TopicProduceData.write')
f(15,4299,2,1,'org/apache/kafka/common/message/ProduceRequestData$PartitionProduceData.write')
f(16,4299,1,6,'org/apache/kafka/common/protocol/SendBuilder.writeInt',0,1,0)
f(16,4300,1,1,'org/apache/kafka/common/protocol/SendBuilder.writeRecords')
f(17,4300,1,1,'org/apache/kafka/common/record/MemoryRecords.buffer')
f(18,4300,1,1,'java/nio/HeapByteBuffer.duplicate')
f(19,4300,1,1,'java/nio/HeapByteBuffer.<init>')
f(20,4300,1,1,'java/nio/ByteBuffer.<init>')
f(21,4300,1,1,'java/nio/Buffer.<init>')
f(22,4300,1,6,'java/nio/Buffer.position',0,1,0)
f(13,4301,2,1,'org/apache/kafka/common/message/RequestHeaderData.addSize')
f(14,4301,2,1,'java/lang/String.getBytes')
f(15,4301,2,1,'java/lang/StringCoding.encode')
f(16,4301,2,6,'sun/nio/cs/UTF_8$Encoder.encode',0,2,0)
f(13,4303,1,6,'org/apache/kafka/common/protocol/SendBuilder.<init>',0,1,0)
f(9,4304,1,1,'org/apache/kafka/clients/NodeApiVersions.latestUsableVersion')
f(10,4304,1,1,'java/util/EnumMap.containsKey')
f(11,4304,1,6,'java/util/EnumMap.isValidKey',0,1,0)
f(9,4305,1,1,'org/apache/kafka/common/requests/ProduceRequest$Builder.build')
f(10,4305,1,1,'org/apache/kafka/common/requests/ProduceRequest$Builder.build')
f(11,4305,1,1,'org/apache/kafka/common/requests/ProduceRequest$Builder.build')
f(12,4305,1,1,'java/lang/Iterable.forEach')
f(13,4305,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.next')
f(14,4305,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.next',0,1,0)
f(15,4305,1,3,'itable stub')
f(7,4306,1,1,'org/apache/kafka/common/record/AbstractRecords.hasMatchingMagic')
f(8,4306,1,1,'org/apache/kafka/common/record/MemoryRecords$$Lambda$3153/759916243.iterator')
f(9,4306,1,1,'org/apache/kafka/common/record/MemoryRecords.batchIterator')
f(10,4306,1,1,'java/nio/HeapByteBuffer.duplicate')
f(11,4306,1,1,'java/nio/HeapByteBuffer.<init>')
f(12,4306,1,1,'java/nio/ByteBuffer.<init>')
f(13,4306,1,1,'java/nio/Buffer.<init>')
f(14,4306,1,6,'java/nio/Buffer.limit',0,1,0)
f(7,4307,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.add',0,1,0)
f(5,4308,1,6,'org/apache/kafka/common/utils/LogContext$LocationAwareKafkaLogger.trace',0,1,0)
f(2,4309,2,1,'sun/nio/ch/KQueuePort$EventHandlerTask.run')
f(3,4309,1,6,'java/util/concurrent/ArrayBlockingQueue.take',0,1,0)
f(3,4310,1,6,'sun/nio/ch/KQueuePort$EventHandlerTask.poll',0,1,0)
f(1,4311,4,1,'java/lang/ref/Finalizer$FinalizerThread.run')
f(2,4311,4,1,'java/lang/ref/ReferenceQueue.remove')
f(3,4311,4,1,'java/lang/ref/ReferenceQueue.remove')
f(4,4311,4,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(5,4311,2,4,'ObjectMonitor::enter(Thread*)')
f(6,4311,2,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(7,4311,2,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(5,4313,2,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(6,4313,2,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(1,4315,6,1,'java/lang/ref/Reference$ReferenceHandler.run')
f(2,4315,6,1,'java/lang/ref/Reference.tryHandlePending',0,1,0)
f(3,4316,1,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(4,4316,1,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(5,4316,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(3,4317,4,1,'java/lang/ref/ReferenceQueue.enqueue')
f(4,4317,1,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(5,4317,1,4,'ObjectMonitor::enter(Thread*)')
f(6,4317,1,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(4,4318,3,1,'java/lang/Object.notifyAll')
f(5,4318,1,4,'HandleMarkCleaner::~HandleMarkCleaner()')
f(5,4319,1,3,'JVM_MonitorNotifyAll')
f(6,4319,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(5,4320,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(1,4321,1,1,'java/nio/Buffer.<init>')
f(2,4321,1,6,'java/util/ArrayList.iterator',0,1,0)
f(1,4322,1,1,'java/util/HashMap.getNode')
f(2,4322,1,6,'java/util/HashMap.getOrDefault',0,1,0)
f(1,4323,1,1,'java/util/TimerThread.run')
f(2,4323,1,1,'java/util/TimerThread.mainLoop')
f(3,4323,1,1,'java/lang/Object.wait')
f(1,4324,2,1,'net/openhft/chronicle/core/threads/CleaningThread.run')
f(2,4324,2,1,'java/lang/Thread.run')
f(3,4324,2,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(4,4324,2,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(5,4324,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.run')
f(6,4324,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301')
f(7,4324,1,1,'java/util/concurrent/FutureTask.runAndReset')
f(8,4324,1,1,'java/util/concurrent/FutureTask.runAndReset$$$capture')
f(9,4324,1,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(10,4324,1,1,'net/openhft/chronicle/threads/DiskSpaceMonitor.run')
f(11,4324,1,1,'java/util/concurrent/ConcurrentHashMap$ValuesView.iterator')
f(12,4324,1,1,'java/util/concurrent/ConcurrentHashMap$ValueIterator.<init>')
f(13,4324,1,1,'java/util/concurrent/ConcurrentHashMap$BaseIterator.<init>')
f(14,4324,1,6,'java/util/concurrent/ConcurrentHashMap$Traverser.<init>',0,1,0)
f(5,4325,1,1,'java/util/concurrent/ThreadPoolExecutor.getTask')
f(6,4325,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(7,4325,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(8,4325,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos',0,1,0)
f(9,4325,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.isOnSyncQueue',1,0,0)
f(10,4325,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.findNodeFromTail',1,0,0)
f(1,4326,1,1,'oracle/jdbc/driver/T4CTTIrxd.copyRowsAsNeededByOffset')
f(2,4326,1,6,'oracle/jdbc/driver/LobCommonAccessor.setPrefetchedChunkSize',0,1,0)
f(1,4327,1,1,'org/apache/tomcat/websocket/BackgroundProcessManager$WsBackgroundThread.run')
f(2,4327,1,1,'org/apache/tomcat/websocket/BackgroundProcessManager.access$000')
f(3,4327,1,1,'org/apache/tomcat/websocket/BackgroundProcessManager.process')
f(4,4327,1,1,'java/util/HashSet.iterator')
f(5,4327,1,6,'java/util/HashMap$KeySet.iterator',0,1,0)
f(1,4328,1789,3,'thread_start')
f(2,4328,1789,3,'_pthread_start')
f(3,4328,1789,3,'java_start(Thread*)')
f(4,4328,1689,4,'GCTaskThread::run()')
f(5,4328,298,4,'DrainStacksCompactionTask::do_it(GCTaskManager*, unsigned int)')
f(6,4328,298,4,'ParCompactionManager::drain_region_stacks()')
f(7,4328,298,4,'PSParallelCompact::fill_region(ParCompactionManager*, unsigned long)')
f(8,4328,2,4,'MoveAndUpdateClosure::copy_partial_obj()')
f(9,4328,2,3,'_platform_memmove$VARIANT$Rosetta')
f(8,4330,5,4,'MoveAndUpdateClosure::do_addr(HeapWord*, unsigned long)')
f(9,4330,5,3,'_platform_memmove$VARIANT$Rosetta')
f(8,4335,3,4,'PSParallelCompact::skip_live_words(HeapWord*, HeapWord*, unsigned long)')
f(8,4338,286,4,'ParMarkBitMap::iterate(ParMarkBitMapClosure*, unsigned long, unsigned long) const')
f(9,4345,272,4,'MoveAndUpdateClosure::do_addr(HeapWord*, unsigned long)')
f(10,4346,27,4,'InstanceKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(11,4346,1,4,'InstanceKlass::vtable_length() const')
f(11,4347,26,4,'ParallelCompactData::calc_new_pointer(HeapWord*)')
f(12,4347,5,4,'PSParallelCompact::fill_blocks(unsigned long)')
f(12,4352,21,4,'ParMarkBitMap::live_words_in_range(HeapWord*, oopDesc*) const')
f(10,4373,8,4,'ObjArrayKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(11,4373,8,4,'ParallelCompactData::calc_new_pointer(HeapWord*)')
f(12,4373,3,4,'PSParallelCompact::fill_blocks(unsigned long)')
f(12,4376,5,4,'ParMarkBitMap::live_words_in_range(HeapWord*, oopDesc*) const')
f(10,4381,236,3,'_platform_memmove$VARIANT$Rosetta')
f(9,4617,7,3,'_platform_memmove$VARIANT$Rosetta')
f(8,4624,2,3,'_platform_memmove$VARIANT$Rosetta')
f(5,4626,1,4,'GCTaskManager::get_task(unsigned int)')
f(6,4626,1,4,'Monitor::wait(bool, long, bool)')
f(7,4626,1,4,'Monitor::IWait(Thread*, long)')
f(8,4626,1,3,'ParkCommon(ParkEvent*, long)')
f(9,4626,1,4,'os::PlatformEvent::park()')
f(10,4626,1,3,'__psynch_cvwait')
f(5,4627,9,4,'MarkFromRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,4627,1,4,'InstanceKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(6,4628,8,4,'ParCompactionManager::follow_marking_stacks()')
f(7,4628,3,4,'InstanceKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,4629,2,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(9,4630,1,4,'oopDesc::size()')
f(7,4631,1,4,'InstanceMirrorKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,4631,1,4,'InstanceKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(9,4631,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(10,4631,1,4,'oopDesc::size()')
f(7,4632,1,4,'Klass::class_loader() const')
f(7,4633,1,4,'ObjArrayKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,4633,1,4,'void ObjArrayKlass::objarray_follow_contents<unsigned int>(ParCompactionManager*, oopDesc*, int)')
f(9,4633,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(10,4633,1,4,'oopDesc::size()')
f(7,4634,1,4,'void ObjArrayKlass::objarray_follow_contents<unsigned int>(ParCompactionManager*, oopDesc*, int)')
f(8,4634,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(7,4635,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(5,4636,177,4,'OldToYoungRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,4636,168,4,'CardTableExtension::scavenge_contents_parallel(ObjectStartArray*, MutableSpace*, HeapWord*, PSPromotionManager*, unsigned int, unsigned int)')
f(7,4656,1,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(7,4657,1,4,'InstanceKlass::vtable_length() const')
f(7,4658,66,4,'ObjectStartArray::object_start(HeapWord*) const')
f(8,4660,8,4,'ObjectStartArray::offset_addr_for_block(signed char*) const')
f(8,4668,56,4,'oopDesc::size()')
f(7,4724,5,4,'ObjectStartArray::object_starts_in_range(HeapWord*, HeapWord*) const')
f(7,4729,5,4,'ObjectStartArray::offset_addr_for_block(signed char*) const')
f(7,4734,68,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(8,4734,67,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(9,4736,64,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(10,4737,59,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(11,4740,56,3,'_platform_memmove$VARIANT$Rosetta')
f(10,4796,3,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(11,4798,1,4,'void PSPromotionManager::push_depth<unsigned int>(unsigned int*)')
f(10,4799,1,4,'PSOldPromotionLAB::allocate(unsigned long)')
f(9,4800,1,4,'oopDesc::size()')
f(8,4801,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(7,4802,2,4,'oopDesc::size()')
f(6,4804,1,4,'ObjectStartArray::object_starts_in_range(HeapWord*, HeapWord*) const')
f(6,4805,8,4,'oopDesc::size()')
f(5,4813,8,4,'PSParallelCompact::update_and_deadwood_in_dense_prefix(ParCompactionManager*, PSParallelCompact::SpaceId, unsigned long, unsigned long)')
f(6,4813,8,4,'ParMarkBitMap::iterate(ParMarkBitMapClosure*, ParMarkBitMapClosure*, unsigned long, unsigned long, unsigned long) const')
f(7,4814,2,4,'InstanceKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(7,4816,1,4,'ObjArrayKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(7,4817,4,4,'UpdateOnlyClosure::do_addr(HeapWord*, unsigned long)')
f(8,4817,3,4,'InstanceKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(9,4819,1,4,'ParallelCompactData::calc_new_pointer(HeapWord*)')
f(8,4820,1,4,'InstanceRefKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(9,4820,1,4,'InstanceKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(5,4821,8,4,'ScavengeRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,4821,8,4,'ClassLoaderDataGraph::oops_do(OopClosure*, KlassClosure*, bool)')
f(7,4821,4,4,'ClassLoaderData::classes_do(KlassClosure*)')
f(8,4821,4,4,'PSScavengeKlassClosure::do_klass(Klass*)')
f(7,4825,1,4,'ClassLoaderData::oops_do(OopClosure*, KlassClosure*, bool)')
f(8,4825,1,4,'void PSRootsClosure<false>::do_oop_work<oopDesc*>(oopDesc**)')
f(7,4826,3,4,'PSScavengeKlassClosure::do_klass(Klass*)')
f(5,4829,44,4,'StealMarkingTask::do_it(GCTaskManager*, unsigned int)')
f(6,4829,44,4,'ParCompactionManager::follow_marking_stacks()')
f(7,4829,40,4,'InstanceKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,4831,38,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(9,4834,1,4,'ParMarkBitMap::mark_obj(HeapWord*, unsigned long)')
f(9,4835,2,4,'ParallelCompactData::add_obj(HeapWord*, unsigned long)')
f(9,4837,32,4,'oopDesc::size()')
f(7,4869,2,4,'ObjArrayKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,4869,2,4,'void ObjArrayKlass::objarray_follow_contents<unsigned int>(ParCompactionManager*, oopDesc*, int)')
f(9,4869,2,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(10,4869,1,4,'ParMarkBitMap::mark_obj(HeapWord*, unsigned long)')
f(10,4870,1,4,'ParallelCompactData::add_obj(HeapWord*, unsigned long)')
f(7,4871,1,4,'void ObjArrayKlass::objarray_follow_contents<unsigned int>(ParCompactionManager*, oopDesc*, int)')
f(8,4871,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(9,4871,1,4,'oopDesc::size()')
f(7,4872,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(5,4873,58,4,'StealRegionCompactionTask::do_it(GCTaskManager*, unsigned int)')
f(6,4873,39,4,'PSParallelCompact::fill_region(ParCompactionManager*, unsigned long)')
f(7,4873,39,4,'ParMarkBitMap::iterate(ParMarkBitMapClosure*, unsigned long, unsigned long) const')
f(8,4873,39,4,'MoveAndUpdateClosure::do_addr(HeapWord*, unsigned long)')
f(9,4873,1,4,'InstanceKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(10,4873,1,4,'ParallelCompactData::calc_new_pointer(HeapWord*)')
f(11,4873,1,4,'ParMarkBitMap::live_words_in_range(HeapWord*, oopDesc*) const')
f(9,4874,38,3,'_platform_memmove$VARIANT$Rosetta')
f(6,4912,18,4,'ParCompactionManager::drain_region_stacks()')
f(7,4912,18,4,'PSParallelCompact::fill_region(ParCompactionManager*, unsigned long)')
f(8,4912,1,4,'MoveAndUpdateClosure::copy_partial_obj()')
f(9,4912,1,3,'_platform_memmove$VARIANT$Rosetta')
f(8,4913,1,4,'MoveAndUpdateClosure::do_addr(HeapWord*, unsigned long)')
f(8,4914,16,4,'ParMarkBitMap::iterate(ParMarkBitMapClosure*, unsigned long, unsigned long) const')
f(9,4914,16,4,'MoveAndUpdateClosure::do_addr(HeapWord*, unsigned long)')
f(10,4914,4,4,'InstanceKlass::oop_update_pointers(ParCompactionManager*, oopDesc*)')
f(11,4914,4,4,'ParallelCompactData::calc_new_pointer(HeapWord*)')
f(12,4914,1,4,'PSParallelCompact::fill_blocks(unsigned long)')
f(12,4915,3,4,'ParMarkBitMap::live_words_in_range(HeapWord*, oopDesc*) const')
f(10,4918,12,3,'_platform_memmove$VARIANT$Rosetta')
f(6,4930,1,4,'ParallelTaskTerminator::offer_termination(TerminatorTerminator*)')
f(7,4930,1,3,'swtch_pri')
f(5,4931,843,4,'StealTask::do_it(GCTaskManager*, unsigned int)')
f(6,4931,838,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(7,4934,835,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(8,4935,1,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(8,4936,3,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(8,4939,829,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(9,4952,790,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(10,4961,781,3,'_platform_memmove$VARIANT$Rosetta')
f(9,5742,10,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(10,5750,2,4,'void PSPromotionManager::push_depth<unsigned int>(unsigned int*)')
f(9,5752,1,4,'InstanceKlass::vtable_length() const')
f(9,5753,8,4,'ObjArrayKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(9,5761,2,4,'PSOldGen::cas_allocate(unsigned long)')
f(10,5761,2,4,'PSOldGen::cas_allocate_noexpand(unsigned long)')
f(9,5763,1,4,'PSOldPromotionLAB::allocate(unsigned long)')
f(9,5764,1,4,'YieldingFlexibleGangWorker::~YieldingFlexibleGangWorker()')
f(9,5765,1,3,'_os_semaphore_wait.cold.1')
f(9,5766,1,4,'oopDesc::size()')
f(9,5767,1,4,'void PSPromotionManager::push_depth<unsigned int>(unsigned int*)')
f(8,5768,1,4,'oopDesc::size()')
f(6,5769,2,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(7,5770,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(8,5770,1,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(6,5771,3,4,'ParallelTaskTerminator::offer_termination(TerminatorTerminator*)')
f(7,5771,3,3,'swtch_pri')
f(5,5774,224,4,'ThreadRootsMarkingTask::do_it(GCTaskManager*, unsigned int)')
f(6,5774,1,4,'JavaThread::oops_do(OopClosure*, CLDClosure*, CodeBlobClosure*)')
f(7,5774,1,4,'frame::oops_code_blob_do(OopClosure*, CodeBlobClosure*, RegisterMap const*)')
f(8,5774,1,4,'OopMapSet::all_do(frame const*, RegisterMap const*, OopClosure*, void (*)(oopDesc**, oopDesc**), OopClosure*)')
f(9,5774,1,4,'void PSParallelCompact::mark_and_push<oopDesc*>(ParCompactionManager*, oopDesc**)')
f(10,5774,1,4,'ParMarkBitMap::mark_obj(HeapWord*, unsigned long)')
f(6,5775,223,4,'ParCompactionManager::follow_marking_stacks()')
f(7,5776,209,4,'InstanceKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,5783,2,4,'ParMarkBitMap::mark_obj(HeapWord*, unsigned long)')
f(8,5785,2,4,'void PSParallelCompact::mark_and_push<oopDesc*>(ParCompactionManager*, oopDesc**)')
f(9,5786,1,4,'oopDesc::size()')
f(8,5787,198,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(9,5804,22,4,'ParMarkBitMap::mark_obj(HeapWord*, unsigned long)')
f(9,5826,2,4,'ParallelCompactData::add_obj(HeapWord*, unsigned long)')
f(9,5828,157,4,'oopDesc::size()')
f(7,5985,3,4,'InstanceKlass::vtable_length() const')
f(7,5988,2,4,'InstanceMirrorKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,5988,1,4,'InstanceKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(9,5988,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(10,5988,1,4,'oopDesc::size()')
f(8,5989,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(9,5989,1,4,'ParallelCompactData::add_obj(HeapWord*, unsigned long)')
f(7,5990,1,4,'Klass::class_loader() const')
f(7,5991,5,4,'ObjArrayKlass::oop_follow_contents(ParCompactionManager*, oopDesc*)')
f(8,5991,5,4,'void ObjArrayKlass::objarray_follow_contents<unsigned int>(ParCompactionManager*, oopDesc*, int)')
f(9,5991,1,4,'ParallelCompactData::add_obj(HeapWord*, unsigned long)')
f(9,5992,4,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(10,5993,2,4,'ParMarkBitMap::mark_obj(HeapWord*, unsigned long)')
f(10,5995,1,4,'oopDesc::size()')
f(7,5996,1,4,'void ObjArrayKlass::objarray_follow_contents<unsigned int>(ParCompactionManager*, oopDesc*, int)')
f(8,5996,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(9,5996,1,4,'oopDesc::size()')
f(7,5997,1,4,'void PSParallelCompact::mark_and_push<unsigned int>(ParCompactionManager*, unsigned int*)')
f(5,5998,19,4,'ThreadRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,5998,6,4,'JavaThread::oops_do(OopClosure*, CLDClosure*, CodeBlobClosure*)')
f(7,5998,1,4,'MarkingCodeBlobClosure::do_code_blob(CodeBlob*)')
f(8,5998,1,4,'nmethod::test_set_oops_do_mark()')
f(7,5999,1,4,'Method::mask_for(int, InterpreterOopMap*)')
f(7,6000,3,4,'frame::oops_interpreted_do(OopClosure*, CLDClosure*, RegisterMap const*, bool)')
f(8,6000,1,4,'GrowableArray<Metadata*>::append(Metadata* const&)')
f(8,6001,2,4,'Method::mask_for(int, InterpreterOopMap*)')
f(9,6001,2,4,'InstanceKlass::mask_for(methodHandle, int, InterpreterOopMap*)')
f(10,6001,2,4,'OopMapCache::lookup(methodHandle, int, InterpreterOopMap*) const')
f(11,6001,1,4,'Monitor::lock(Thread*)')
f(12,6001,1,4,'Monitor::TryFast()')
f(11,6002,1,4,'OopMapCacheEntry::fill(methodHandle, int)')
f(12,6002,1,4,'OopMapForCacheEntry::compute_map(Thread*)')
f(13,6002,1,4,'GenerateOopMap::compute_map(Thread*)')
f(14,6002,1,4,'GenerateOopMap::do_interpretation()')
f(15,6002,1,4,'GenerateOopMap::interp_all()')
f(16,6002,1,4,'GenerateOopMap::interp_bb(BasicBlock*)')
f(17,6002,1,4,'GenerateOopMap::do_method(int, int, int, int)')
f(18,6002,1,4,'SignatureIterator::iterate_returntype()')
f(7,6003,1,4,'void PSRootsClosure<false>::do_oop_work<oopDesc*>(oopDesc**)')
f(6,6004,13,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(7,6004,13,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(8,6004,13,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(9,6004,11,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(10,6004,11,3,'_platform_memmove$VARIANT$Rosetta')
f(9,6015,1,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(9,6016,1,4,'void PSPromotionManager::push_depth<unsigned int>(unsigned int*)')
f(4,6017,56,4,'JavaThread::run()')
f(5,6017,43,4,'JavaThread::thread_main_inner()')
f(6,6017,11,4,'CompileBroker::compiler_thread_loop()')
f(7,6017,2,4,'CompileBroker::invoke_compiler_on_method(CompileTask*)')
f(8,6017,2,4,'Compiler::compile_method(ciEnv*, ciMethod*, int)')
f(9,6017,2,4,'Compilation::Compilation(AbstractCompiler*, ciEnv*, ciMethod*, int, BufferBlob*)')
f(10,6017,2,4,'Compilation::compile_method()')
f(11,6017,1,4,'Compilation::compile_java_method()')
f(12,6017,1,4,'Compilation::emit_lir()')
f(13,6017,1,4,'LinearScan::do_linear_scan()')
f(14,6017,1,4,'LinearScan::allocate_registers()')
f(15,6017,1,4,'IntervalWalker::walk_to(int)')
f(16,6017,1,4,'IntervalWalker::walk_to(IntervalState, int)')
f(11,6018,1,4,'Compilation::initialize()')
f(12,6018,1,4,'DebugInformationRecorder::DebugInformationRecorder(OopRecorder*)')
f(13,6018,1,4,'CompressedWriteStream::CompressedWriteStream(int)')
f(7,6019,9,4,'CompileQueue::get()')
f(8,6019,9,4,'NMethodSweeper::possibly_sweep()')
f(9,6019,9,4,'NMethodSweeper::sweep_code_cache()')
f(10,6019,9,4,'NMethodSweeper::process_nmethod(nmethod*)')
f(11,6025,1,4,'RelocIterator::next()')
f(11,6026,2,4,'nmethod::cleanup_inline_caches()')
f(12,6026,1,4,'CodeCache::find_blob_unsafe(void*)')
f(13,6026,1,4,'CodeHeap::find_start(void*) const')
f(12,6027,1,4,'RelocIterator::next()')
f(6,6028,32,4,'JavaThread::exit(bool, JavaThread::ExitType)')
f(7,6028,31,4,'JvmtiExport::post_thread_end(JavaThread*)')
f(8,6028,31,3,'cbThreadEnd')
f(9,6028,31,3,'event_callback')
f(10,6028,28,3,'classTrack_processUnloads')
f(11,6031,24,3,'isSameObject')
f(12,6031,9,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(12,6040,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(12,6041,14,3,'jni_IsSameObject')
f(13,6045,6,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(13,6051,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(13,6052,3,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(11,6055,1,3,'jni_IsSameObject')
f(10,6056,3,3,'isSameObject')
f(7,6059,1,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(8,6059,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(9,6059,1,4,'VMThread::execute(VM_Operation*)')
f(10,6059,1,4,'os::PlatformEvent::unpark()')
f(11,6059,1,3,'__psynch_cvsignal')
f(5,6060,13,4,'JvmtiExport::post_thread_start(JavaThread*)')
f(6,6060,13,3,'cbThreadStart')
f(7,6060,13,3,'event_callback')
f(8,6060,12,3,'classTrack_processUnloads')
f(9,6062,8,3,'isSameObject')
f(10,6062,2,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(10,6064,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(10,6065,5,3,'jni_IsSameObject')
f(11,6067,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(11,6068,2,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(9,6070,2,3,'jvmti_GetLoadedClasses')
f(10,6070,2,4,'JvmtiGetLoadedClasses::getLoadedClasses(JvmtiEnv*, int*, _jclass***)')
f(11,6070,2,4,'ClassLoaderDataGraph::loaded_classes_do(KlassClosure*)')
f(12,6070,2,4,'ClassLoaderData::loaded_classes_do(KlassClosure*)')
f(13,6071,1,4,'LoadedClassesClosure::do_klass(Klass*)')
f(8,6072,1,3,'isSameObject')
f(4,6073,43,4,'VMThread::run()')
f(5,6073,43,4,'VMThread::loop()')
f(6,6073,5,4,'SafepointSynchronize::begin()')
f(7,6073,5,4,'SafepointSynchronize::do_cleanup_tasks()')
f(8,6073,1,4,'ObjectSynchronizer::deflate_idle_monitors()')
f(8,6074,4,4,'Threads::nmethods_do(CodeBlobClosure*)')
f(9,6074,3,4,'JavaThread::nmethods_do(CodeBlobClosure*)')
f(10,6074,3,4,'SetHotnessClosure::do_code_blob(CodeBlob*)')
f(9,6077,1,4,'StackFrameStream::is_done()')
f(6,6078,38,4,'VMThread::evaluate_operation(VM_Operation*)')
f(7,6078,38,4,'VM_Operation::evaluate()')
f(8,6078,1,4,'VM_BulkRevokeBias::doit()')
f(9,6078,1,3,'bulk_revoke_or_rebias_at_safepoint(oopDesc*, bool, bool, JavaThread*)')
f(10,6078,1,3,'get_or_compute_monitor_info(JavaThread*)')
f(11,6078,1,4,'vframe::java_sender() const')
f(12,6078,1,4,'compiledVFrame::sender() const')
f(13,6078,1,4,'vframe::sender() const')
f(14,6078,1,4,'vframe::new_vframe(frame const*, RegisterMap const*, JavaThread*)')
f(15,6078,1,4,'Arena::grow(unsigned long, AllocFailStrategy::AllocFailEnum)')
f(16,6078,1,4,'Chunk::operator new(unsigned long, AllocFailStrategy::AllocFailEnum, unsigned long)')
f(17,6078,1,4,'ChunkPool::allocate(unsigned long, AllocFailStrategy::AllocFailEnum)')
f(18,6078,1,4,'os::malloc(unsigned long, MemoryType, NativeCallStack const&)')
f(19,6078,1,3,'szone_malloc_should_clear')
f(20,6078,1,3,'small_malloc_should_clear')
f(21,6078,1,3,'small_malloc_from_free_list')
f(22,6078,1,3,'small_free_list_add_ptr')
f(8,6079,27,4,'VM_ParallelGCFailedAllocation::doit()')
f(9,6079,27,4,'ParallelScavengeHeap::failed_mem_allocate(unsigned long)')
f(10,6079,27,4,'PSScavenge::invoke()')
f(11,6079,23,4,'PSParallelCompact::invoke_no_policy(bool)')
f(12,6079,7,4,'PSParallelCompact::adjust_roots()')
f(13,6079,6,4,'CodeCache::blobs_do(CodeBlobClosure*)')
f(14,6079,5,4,'CodeBlobToOopClosure::do_nmethod(nmethod*)')
f(15,6079,5,4,'nmethod::oops_do(OopClosure*, bool)')
f(16,6079,2,4,'PSParallelCompact::AdjustPointerClosure::do_oop(oopDesc**)')
f(16,6081,1,4,'RelocIterator::next()')
f(16,6082,1,4,'oop_Relocation::oop_addr()')
f(16,6083,1,4,'oop_Relocation::oop_value()')
f(17,6083,1,4,'Assembler::locate_operand(unsigned char*, Assembler::WhichOperand)')
f(14,6084,1,4,'CodeHeap::next_free(HeapBlock*) const')
f(13,6085,1,4,'Threads::oops_do(OopClosure*, CLDClosure*, CodeBlobClosure*)')
f(14,6085,1,4,'JavaThread::oops_do(OopClosure*, CLDClosure*, CodeBlobClosure*)')
f(15,6085,1,4,'frame::oops_interpreted_do(OopClosure*, CLDClosure*, RegisterMap const*, bool)')
f(16,6085,1,4,'ArrayKlass::oops_do(OopClosure*)')
f(12,6086,13,4,'PSParallelCompact::marking_phase(ParCompactionManager*, bool, ParallelOldTracer*)')
f(13,6086,2,4,'CodeCache::do_unloading(BoolObjectClosure*, bool)')
f(14,6086,2,4,'nmethod::do_unloading(BoolObjectClosure*, bool)')
f(15,6086,2,4,'RelocIterator::next()')
f(13,6088,7,4,'Klass::clean_weak_klass_links(BoolObjectClosure*, bool)')
f(14,6089,6,4,'InstanceKlass::clean_weak_instanceklass_links(BoolObjectClosure*)')
f(15,6089,6,4,'InstanceKlass::clean_method_data(BoolObjectClosure*)')
f(13,6095,4,4,'SymbolTable::unlink(int*, int*)')
f(14,6095,4,4,'SymbolTable::buckets_unlink(int, int, BasicHashtable<(MemoryType)9>::BucketUnlinkContext*, unsigned long*)')
f(12,6099,3,4,'PSParallelCompact::post_compact()')
f(13,6099,2,4,'PSParallelCompact::clear_data_covering_space(PSParallelCompact::SpaceId)')
f(14,6099,2,4,'BitMap::clear_range(unsigned long, unsigned long)')
f(13,6101,1,3,'_platform_memset$VARIANT$Rosetta')
f(11,6102,4,4,'PSScavenge::invoke_no_policy()')
f(12,6102,4,4,'ReferenceProcessor::process_discovered_references(BoolObjectClosure*, OopClosure*, VoidClosure*, AbstractRefProcTaskExecutor*, GCTimer*, GCId)')
f(13,6102,1,4,'JNIHandleBlock::weak_oops_do(BoolObjectClosure*, OopClosure*)')
f(14,6102,1,4,'PSIsAliveClosure::do_object_b(oopDesc*)')
f(13,6103,3,4,'ReferenceProcessor::process_discovered_reflist(DiscoveredList*, ReferencePolicy*, bool, BoolObjectClosure*, OopClosure*, VoidClosure*, AbstractRefProcTaskExecutor*)')
f(14,6103,1,4,'ReferenceProcessor::pp2_work(DiscoveredList&, BoolObjectClosure*, OopClosure*)')
f(14,6104,2,4,'ReferenceProcessor::process_phase3(DiscoveredList&, bool, BoolObjectClosure*, OopClosure*, VoidClosure*)')
f(15,6104,1,4,'PSEvacuateFollowersClosure::do_void()')
f(16,6104,1,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(17,6104,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(15,6105,1,4,'void PSKeepAliveClosure::do_oop_work<unsigned int>(unsigned int*)')
f(16,6105,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(17,6105,1,4,'oopDesc::size()')
f(8,6106,10,4,'VM_ParallelGCSystemGC::doit()')
f(9,6106,10,4,'PSScavenge::invoke()')
f(10,6106,9,4,'PSParallelCompact::invoke_no_policy(bool)')
f(11,6106,4,4,'PSParallelCompact::adjust_roots()')
f(12,6106,4,4,'CodeCache::blobs_do(CodeBlobClosure*)')
f(13,6106,4,4,'CodeBlobToOopClosure::do_nmethod(nmethod*)')
f(14,6106,1,4,'RelocIterator::next()')
f(14,6107,3,4,'nmethod::oops_do(OopClosure*, bool)')
f(15,6107,3,4,'PSParallelCompact::AdjustPointerClosure::do_oop(oopDesc**)')
f(11,6110,4,4,'PSParallelCompact::marking_phase(ParCompactionManager*, bool, ParallelOldTracer*)')
f(12,6110,1,4,'CodeCache::do_unloading(BoolObjectClosure*, bool)')
f(13,6110,1,4,'CodeHeap::next_free(HeapBlock*) const')
f(12,6111,1,4,'Klass::clean_weak_klass_links(BoolObjectClosure*, bool)')
f(13,6111,1,4,'InstanceKlass::clean_weak_instanceklass_links(BoolObjectClosure*)')
f(14,6111,1,4,'InstanceKlass::clean_method_data(BoolObjectClosure*)')
f(12,6112,2,4,'SymbolTable::unlink(int*, int*)')
f(13,6112,2,4,'SymbolTable::buckets_unlink(int, int, BasicHashtable<(MemoryType)9>::BucketUnlinkContext*, unsigned long*)')
f(11,6114,1,4,'PSParallelCompact::post_compact()')
f(12,6114,1,4,'PSParallelCompact::clear_data_covering_space(PSParallelCompact::SpaceId)')
f(13,6114,1,4,'BitMap::clear_range(unsigned long, unsigned long)')
f(10,6115,1,4,'PSScavenge::invoke_no_policy()')
f(11,6115,1,4,'PSYoungGen::resize(unsigned long, unsigned long)')
f(12,6115,1,4,'PSYoungGen::resize_generation(unsigned long, unsigned long)')
f(13,6115,1,4,'PSVirtualSpace::shrink_by(unsigned long)')
f(14,6115,1,4,'os::uncommit_memory(char*, unsigned long)')
f(15,6115,1,4,'os::pd_uncommit_memory(char*, unsigned long)')
f(16,6115,1,3,'__mmap')
f(4,6116,1,4,'WatcherThread::run()')
f(5,6116,1,4,'WatcherThread::sleep() const')
f(6,6116,1,4,'Monitor::wait(bool, long, bool)')
f(7,6116,1,4,'Monitor::IWait(Thread*, long)')
f(8,6116,1,4,'os::PlatformEvent::park(long)')
f(9,6116,1,3,'__psynch_cvwait')

search();
</script></body></html>
