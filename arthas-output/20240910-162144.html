<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='utf-8'>
<style>
	body {margin: 0; padding: 10px; background-color: #ffffff}
	h1 {margin: 5px 0 0 0; font-size: 18px; font-weight: normal; text-align: center}
	header {margin: -24px 0 5px 0; line-height: 24px}
	button {font: 12px sans-serif; cursor: pointer}
	p {margin: 5px 0 5px 0}
	a {color: #0366d6}
	#hl {position: absolute; display: none; overflow: hidden; white-space: nowrap; pointer-events: none; background-color: #ffffe0; outline: 1px solid #ffc000; height: 15px}
	#hl span {padding: 0 3px 0 3px}
	#status {overflow: hidden; white-space: nowrap}
	#match {overflow: hidden; white-space: nowrap; display: none; float: right; text-align: right}
	#reset {cursor: pointer}
	#canvas {width: 100%; height: 1184px}
</style>
</head>
<body style='font: 12px Verdana, sans-serif'>
<h1>CPU profile</h1>
<header style='text-align: left'><button id='reverse' title='Reverse'>&#x1f53b;</button>&nbsp;&nbsp;<button id='search' title='Search'>&#x1f50d;</button></header>
<header style='text-align: right'>Produced by <a href='https://github.com/jvm-profiling-tools/async-profiler'>async-profiler</a></header>
<canvas id='canvas'></canvas>
<div id='hl'><span></span></div>
<p id='match'>Matched: <span id='matchval'></span> <span id='reset' title='Clear'>&#x274c;</span></p>
<p id='status'>&nbsp;</p>
<script>
	// Copyright 2020 Andrei Pangin
	// Licensed under the Apache License, Version 2.0.
	'use strict';
	var root, rootLevel, px, pattern;
	var reverse = false;
	const levels = Array(74);
	for (let h = 0; h < levels.length; h++) {
		levels[h] = [];
	}

	const canvas = document.getElementById('canvas');
	const c = canvas.getContext('2d');
	const hl = document.getElementById('hl');
	const status = document.getElementById('status');

	const canvasWidth = canvas.offsetWidth;
	const canvasHeight = canvas.offsetHeight;
	canvas.style.width = canvasWidth + 'px';
	canvas.width = canvasWidth * (devicePixelRatio || 1);
	canvas.height = canvasHeight * (devicePixelRatio || 1);
	if (devicePixelRatio) c.scale(devicePixelRatio, devicePixelRatio);
	c.font = document.body.style.font;

	const palette = [
		[0xb2e1b2, 20, 20, 20],
		[0x50e150, 30, 30, 30],
		[0x50cccc, 30, 30, 30],
		[0xe15a5a, 30, 40, 40],
		[0xc8c83c, 30, 30, 10],
		[0xe17d00, 30, 30,  0],
		[0xcce880, 20, 20, 20],
	];

	function getColor(p) {
		const v = Math.random();
		return '#' + (p[0] + ((p[1] * v) << 16 | (p[2] * v) << 8 | (p[3] * v))).toString(16);
	}

	function f(level, left, width, type, title, inln, c1, int) {
		levels[level].push({left: left, width: width, color: getColor(palette[type]), title: title,
			details: (int ? ', int=' + int : '') + (c1 ? ', c1=' + c1 : '') + (inln ? ', inln=' + inln : '')
		});
	}

	function samples(n) {
		return n === 1 ? '1 sample' : n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' samples';
	}

	function pct(a, b) {
		return a >= b ? '100' : (100 * a / b).toFixed(2);
	}

	function findFrame(frames, x) {
		let left = 0;
		let right = frames.length - 1;

		while (left <= right) {
			const mid = (left + right) >>> 1;
			const f = frames[mid];

			if (f.left > x) {
				right = mid - 1;
			} else if (f.left + f.width <= x) {
				left = mid + 1;
			} else {
				return f;
			}
		}

		if (frames[left] && (frames[left].left - x) * px < 0.5) return frames[left];
		if (frames[right] && (x - (frames[right].left + frames[right].width)) * px < 0.5) return frames[right];

		return null;
	}

	function search(r) {
		if (r === true && (r = prompt('Enter regexp to search:', '')) === null) {
			return;
		}

		pattern = r ? RegExp(r) : undefined;
		const matched = render(root, rootLevel);
		document.getElementById('matchval').textContent = pct(matched, root.width) + '%';
		document.getElementById('match').style.display = r ? 'inherit' : 'none';
	}

	function render(newRoot, newLevel) {
		if (root) {
			c.fillStyle = '#ffffff';
			c.fillRect(0, 0, canvasWidth, canvasHeight);
		}

		root = newRoot || levels[0][0];
		rootLevel = newLevel || 0;
		px = canvasWidth / root.width;

		const x0 = root.left;
		const x1 = x0 + root.width;
		const marked = [];

		function mark(f) {
			return marked[f.left] >= f.width || (marked[f.left] = f.width);
		}

		function totalMarked() {
			let total = 0;
			let left = 0;
			Object.keys(marked).sort(function(a, b) { return a - b; }).forEach(function(x) {
				if (+x >= left) {
					total += marked[x];
					left = +x + marked[x];
				}
			});
			return total;
		}

		function drawFrame(f, y, alpha) {
			if (f.left < x1 && f.left + f.width > x0) {
				c.fillStyle = pattern && f.title.match(pattern) && mark(f) ? '#ee00ee' : f.color;
				c.fillRect((f.left - x0) * px, y, f.width * px, 15);

				if (f.width * px >= 21) {
					const chars = Math.floor(f.width * px / 7);
					const title = f.title.length <= chars ? f.title : f.title.substring(0, chars - 2) + '..';
					c.fillStyle = '#000000';
					c.fillText(title, Math.max(f.left - x0, 0) * px + 3, y + 12, f.width * px - 6);
				}

				if (alpha) {
					c.fillStyle = 'rgba(255, 255, 255, 0.5)';
					c.fillRect((f.left - x0) * px, y, f.width * px, 15);
				}
			}
		}

		for (let h = 0; h < levels.length; h++) {
			const y = reverse ? h * 16 : canvasHeight - (h + 1) * 16;
			const frames = levels[h];
			for (let i = 0; i < frames.length; i++) {
				drawFrame(frames[i], y, h < rootLevel);
			}
		}

		return totalMarked();
	}

	canvas.onmousemove = function() {
		const h = Math.floor((reverse ? event.offsetY : (canvasHeight - event.offsetY)) / 16);
		if (h >= 0 && h < levels.length) {
			const f = findFrame(levels[h], event.offsetX / px + root.left);
			if (f) {
				if (f != root) getSelection().removeAllRanges();
				hl.style.left = (Math.max(f.left - root.left, 0) * px + canvas.offsetLeft) + 'px';
				hl.style.width = (Math.min(f.width, root.width) * px) + 'px';
				hl.style.top = ((reverse ? h * 16 : canvasHeight - (h + 1) * 16) + canvas.offsetTop) + 'px';
				hl.firstChild.textContent = f.title;
				hl.style.display = 'block';
				canvas.title = f.title + '\n(' + samples(f.width) + f.details + ', ' + pct(f.width, levels[0][0].width) + '%)';
				canvas.style.cursor = 'pointer';
				canvas.onclick = function() {
					if (f != root) {
						render(f, h);
						canvas.onmousemove();
					}
				};
				status.textContent = 'Function: ' + canvas.title;
				return;
			}
		}
		canvas.onmouseout();
	}

	canvas.onmouseout = function() {
		hl.style.display = 'none';
		status.textContent = '\xa0';
		canvas.title = '';
		canvas.style.cursor = '';
		canvas.onclick = '';
	}

	canvas.ondblclick = function() {
		getSelection().selectAllChildren(hl);
	}

	document.getElementById('reverse').onclick = function() {
		reverse = !reverse;
		render();
	}

	document.getElementById('search').onclick = function() {
		search(true);
	}

	document.getElementById('reset').onclick = function() {
		search(false);
	}

	window.onkeydown = function() {
		if (event.ctrlKey && event.keyCode === 70) {
			event.preventDefault();
			search(true);
		} else if (event.keyCode === 27) {
			search(false);
		}
	}

f(0,0,4212,3,'all')
f(1,0,4,3,'[deoptimization]')
f(2,0,3,4,'Deoptimization::fetch_unroll_info(JavaThread*)')
f(3,0,3,4,'Deoptimization::fetch_unroll_info_helper(JavaThread*)')
f(4,0,1,4,'CHeapObj<(MemoryType)6>::operator new(unsigned long)')
f(5,0,1,4,'CHeapObj<(MemoryType)6>::operator new(unsigned long, NativeCallStack const&)')
f(6,0,1,4,'os::malloc(unsigned long, MemoryType, NativeCallStack const&)')
f(7,0,1,3,'_platform_memset$VARIANT$Rosetta')
f(4,1,2,4,'Deoptimization::create_vframeArray(JavaThread*, frame, RegisterMap*, GrowableArray<compiledVFrame*>*, bool)')
f(5,1,1,4,'Events::log(Thread*, char const*, ...)')
f(6,1,1,4,'StringEventLog::logv(Thread*, char const*, __va_list_tag*)')
f(7,1,1,3,'jio_vsnprintf')
f(8,1,1,4,'os::vsnprintf(char*, unsigned long, char const*, __va_list_tag*)')
f(9,1,1,3,'vsnprintf')
f(10,1,1,3,'_vsnprintf')
f(11,1,1,3,'__vfprintf')
f(12,1,1,3,'advance_directory.cold.1')
f(5,2,1,4,'vframeArray::allocate(JavaThread*, int, GrowableArray<compiledVFrame*>*, RegisterMap*, frame, frame, frame, bool)')
f(6,2,1,4,'vframeArray::fill_in(JavaThread*, int, GrowableArray<compiledVFrame*>*, RegisterMap const*, bool)')
f(7,2,1,4,'vframeArrayElement::fill_in(compiledVFrame*, bool)')
f(2,3,1,4,'Deoptimization::unpack_frames(JavaThread*, int)')
f(3,3,1,4,'vframeArray::unpack_to_stack(frame&, int, int)')
f(4,3,1,4,'vframeArrayElement::unpack_on_stack(int, int, int, frame*, bool, bool, int)')
f(5,3,1,4,'nmethod::is_compiled_by_c2() const')
f(1,4,11,3,'[unknown_Java]')
f(2,4,1,6,'java/util/concurrent/ThreadPoolExecutor.runWorker',0,1,0)
f(3,4,1,4,'SharedRuntime::ldiv(long, long)')
f(2,5,2,3,'new_type_array Runtime1 stub')
f(2,7,7,3,'slow_subtype_check Runtime1 stub')
f(2,14,1,6,'sun/reflect/GeneratedMethodAccessor139.invoke',0,1,0)
f(1,15,1,3,'call_stub')
f(1,16,1,1,'com/google/common/cache/LocalCache$Segment.getEntry')
f(2,16,1,6,'java/lang/String.hashCode',0,1,0)
f(1,17,3,1,'com/hazelcast/internal/partition/impl/MigrationThread.run')
f(2,17,3,6,'com/hazelcast/internal/partition/impl/MigrationThread.doRun',0,2,0)
f(3,19,1,1,'com/hazelcast/internal/partition/impl/MigrationQueue.poll')
f(4,19,1,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(5,19,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(6,19,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(7,19,1,1,'sun/misc/Unsafe.park')
f(8,19,1,3,'Unsafe_Park')
f(9,19,1,4,'Parker::park(bool, long)')
f(10,19,1,3,'__gettimeofday')
f(1,20,52,1,'com/hazelcast/internal/util/executor/HazelcastManagedThread.run')
f(2,20,46,1,'com/hazelcast/internal/util/executor/HazelcastManagedThread.executeRun')
f(3,20,46,1,'java/lang/Thread.run')
f(4,20,46,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(5,20,46,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(6,20,28,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate$Worker.run')
f(7,20,4,1,'com/hazelcast/internal/partition/impl/PartitionReplicaManager$AntiEntropyTask.run')
f(8,20,4,1,'com/hazelcast/internal/partition/impl/PartitionReplicaManager$AntiEntropyTask.runAntiEntropyTask')
f(9,20,4,1,'com/hazelcast/spi/impl/operationservice/impl/OperationServiceImpl.executeOnPartitions')
f(10,20,4,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.executeOnPartitions')
f(11,20,4,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationQueueImpl.add')
f(12,20,4,1,'java/util/AbstractQueue.add')
f(13,20,4,1,'com/hazelcast/internal/util/concurrent/MPSCQueue.offer')
f(14,20,4,1,'java/util/concurrent/locks/LockSupport.unpark')
f(15,20,4,1,'sun/misc/Unsafe.unpark')
f(16,20,4,3,'Unsafe_Unpark')
f(17,20,4,4,'Parker::unpark()')
f(18,20,4,3,'__psynch_cvsignal')
f(7,24,4,1,'com/hazelcast/jet/impl/JobCoordinationService$$Lambda$1646/1876291408.run')
f(8,24,4,1,'com/hazelcast/jet/impl/JobCoordinationService.scanJobs')
f(9,24,4,1,'com/hazelcast/jet/impl/JobCoordinationService.doScanJobs')
f(10,24,1,1,'com/hazelcast/jet/impl/JobRepository.cleanup')
f(11,24,1,1,'com/hazelcast/jet/impl/JobRepository.cleanupJobResults')
f(12,24,1,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.size')
f(13,24,1,1,'com/hazelcast/map/impl/proxy/MapProxySupport.size')
f(14,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationServiceImpl.invokeOnAllPartitions')
f(15,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/InvokeOnPartitions.invoke')
f(16,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/InvokeOnPartitions.invokeAsync')
f(17,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/InvokeOnPartitions.invokeOnAllPartitions')
f(18,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/InvocationBuilderImpl.invoke')
f(19,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke')
f(20,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke0')
f(21,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvoke')
f(22,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.initInvocationTarget')
f(23,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/TargetInvocation.toTargetMember')
f(24,24,1,1,'com/hazelcast/spi/impl/operationservice/impl/TargetInvocation.toTargetMember')
f(25,24,1,1,'com/hazelcast/internal/cluster/impl/ClusterServiceImpl.getMember')
f(26,24,1,1,'com/hazelcast/internal/cluster/impl/MembershipManager.getMember')
f(27,24,1,1,'com/hazelcast/internal/cluster/impl/MemberMap.getMember')
f(28,24,1,6,'java/util/Collections$SingletonMap.get',0,1,0)
f(29,24,1,2,'java/util/Collections.eq',1,0,0)
f(30,24,1,3,'vtable stub')
f(10,25,2,1,'com/hazelcast/jet/impl/JobRepository.getJobRecords')
f(11,25,2,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.values')
f(12,25,2,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.values')
f(13,25,2,1,'com/hazelcast/map/impl/proxy/MapProxyImpl.executePredicate')
f(14,25,2,1,'com/hazelcast/map/impl/proxy/MapProxySupport.executeQueryInternal')
f(15,25,2,1,'com/hazelcast/map/impl/proxy/MapProxySupport.executeQueryInternal')
f(16,25,2,1,'com/hazelcast/map/impl/query/QueryEngineImpl.execute')
f(17,25,2,1,'com/hazelcast/map/impl/query/QueryEngineImpl.runOnGivenPartitions')
f(18,25,2,1,'com/hazelcast/map/impl/query/QueryEngineImpl.doRunOnQueryThreads')
f(19,25,2,1,'com/hazelcast/map/impl/query/QueryEngineImpl.dispatchOnQueryThreads')
f(20,25,2,1,'com/hazelcast/map/impl/query/QueryEngineImpl.dispatchFullQueryOnQueryThread')
f(21,25,2,1,'com/hazelcast/map/impl/query/QueryEngineImpl.dispatchFullQueryOnAllMembersOnQueryThread')
f(22,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationServiceImpl.invokeOnTarget')
f(23,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke')
f(24,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke0')
f(25,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvoke')
f(26,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvokeLocal')
f(27,25,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.runOrExecute')
f(28,25,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.run')
f(29,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(30,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(31,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.call')
f(32,25,2,1,'com/hazelcast/spi/impl/operationservice/Operation.sendResponse')
f(33,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.sendResponse')
f(34,25,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.complete')
f(35,25,2,1,'com/hazelcast/spi/impl/AbstractInvocationFuture.complete')
f(36,25,2,1,'com/hazelcast/spi/impl/AbstractInvocationFuture.complete0')
f(37,25,2,6,'com/hazelcast/spi/impl/AbstractInvocationFuture.unblockAll',0,1,0)
f(38,26,1,6,'com/hazelcast/spi/impl/AbstractInvocationFuture.unblockOtherNode',0,1,0)
f(10,27,1,1,'com/hazelcast/map/impl/query/QueryResultIterator.next')
f(11,27,1,1,'com/hazelcast/internal/serialization/impl/AbstractSerializationService.toObject')
f(12,27,1,1,'com/hazelcast/internal/serialization/impl/StreamSerializerAdapter.read')
f(13,27,1,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(14,27,1,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(15,27,1,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.readInternal')
f(16,27,1,1,'com/hazelcast/jet/impl/JobRecord.readData')
f(17,27,1,1,'com/hazelcast/internal/serialization/impl/ByteArrayObjectDataInput.readObject')
f(18,27,1,1,'com/hazelcast/internal/serialization/impl/AbstractSerializationService.readObject')
f(19,27,1,1,'com/hazelcast/internal/serialization/impl/StreamSerializerAdapter.read')
f(20,27,1,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(21,27,1,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.read')
f(22,27,1,1,'com/hazelcast/internal/serialization/impl/DataSerializableSerializer.readInternal')
f(23,27,1,1,'com/hazelcast/jet/config/JobConfig.readData')
f(24,27,1,1,'com/hazelcast/internal/serialization/impl/ByteArrayObjectDataInput.readObject')
f(25,27,1,1,'com/hazelcast/internal/serialization/impl/AbstractSerializationService.readObject')
f(26,27,1,1,'com/hazelcast/internal/serialization/impl/StreamSerializerAdapter.read')
f(27,27,1,1,'com/hazelcast/internal/serialization/impl/defaultserializers/JavaDefaultSerializers$JavaSerializer.read')
f(28,27,1,1,'com/hazelcast/internal/serialization/impl/defaultserializers/JavaDefaultSerializers$JavaSerializer.read')
f(29,27,1,1,'com/hazelcast/internal/nio/IOUtil.newObjectInputStream')
f(30,27,1,1,'com/hazelcast/internal/nio/IOUtil$ClassLoaderAwareObjectInputStream.<init>')
f(31,27,1,1,'com/hazelcast/internal/nio/IOUtil$ClassLoaderAwareObjectInputStream.<init>')
f(32,27,1,1,'java/io/ObjectInputStream.<init>')
f(33,27,1,1,'java/io/ObjectInputStream$BlockDataInputStream.<init>')
f(34,27,1,6,'java/io/DataInputStream.<init>',0,1,0)
f(7,28,16,1,'com/hazelcast/spi/impl/executionservice/impl/DelegateAndSkipOnConcurrentExecutionDecorator$DelegateDecorator.run')
f(8,28,1,1,'com/hazelcast/internal/cluster/impl/ClusterHeartbeatManager$$Lambda$1597/1155846026.run')
f(9,28,1,1,'com/hazelcast/internal/cluster/impl/ClusterHeartbeatManager.heartbeat')
f(10,28,1,1,'com/hazelcast/internal/cluster/impl/ClusterHeartbeatManager.heartbeatWhenMaster')
f(11,28,1,6,'com/hazelcast/internal/cluster/impl/ClusterServiceImpl.getMembershipManager',0,1,0)
f(8,29,5,1,'com/hazelcast/internal/eviction/ClearExpiredRecordsTask.run')
f(9,29,4,1,'com/hazelcast/internal/eviction/ClearExpiredRecordsTask.runInternal')
f(10,29,4,1,'com/hazelcast/internal/eviction/ClearExpiredRecordsTask.canProcessContainer')
f(11,29,1,1,'com/hazelcast/map/impl/eviction/MapClearExpiredRecordsTask.hasExpiredKeyToSendBackup')
f(12,29,1,6,'com/hazelcast/map/impl/eviction/MapClearExpiredRecordsTask.hasExpiredKeyToSendBackup',0,1,0)
f(11,30,1,1,'com/hazelcast/map/impl/eviction/MapClearExpiredRecordsTask.hasRunningCleanup')
f(12,30,1,1,'com/hazelcast/map/impl/eviction/MapClearExpiredRecordsTask.hasRunningCleanup')
f(13,30,1,6,'com/hazelcast/map/impl/PartitionContainer.hasRunningCleanup',0,1,0)
f(11,31,2,6,'com/hazelcast/map/impl/eviction/MapClearExpiredRecordsTask.isContainerEmpty',0,1,0)
f(12,32,1,1,'com/hazelcast/map/impl/eviction/MapClearExpiredRecordsTask.isContainerEmpty')
f(13,32,1,6,'com/hazelcast/map/impl/recordstore/DefaultRecordStore.size',0,1,0)
f(9,33,1,6,'java/util/concurrent/atomic/AtomicBoolean.set',0,1,0)
f(8,34,10,1,'com/hazelcast/internal/metrics/impl/MetricsService$$Lambda$1589/1413679210.run')
f(9,34,10,1,'com/hazelcast/internal/metrics/impl/MetricsService.collectMetrics')
f(10,34,9,1,'com/hazelcast/internal/metrics/impl/MetricsService.collectMetrics')
f(11,34,9,1,'com/hazelcast/internal/metrics/impl/MetricsRegistryImpl.collect')
f(12,34,2,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectDynamicMetrics')
f(13,34,2,1,'com/hazelcast/jet/impl/JobExecutionService.provideDynamicMetrics')
f(14,34,2,1,'java/util/concurrent/ConcurrentHashMap.forEach')
f(15,34,2,1,'com/hazelcast/jet/impl/JobExecutionService$$Lambda$1778/1058338580.accept')
f(16,34,2,1,'com/hazelcast/jet/impl/JobExecutionService.lambda$provideDynamicMetrics$20')
f(17,34,2,0,'com/hazelcast/jet/impl/execution/ExecutionContext.provideDynamicMetrics',0,0,1)
f(18,35,1,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.provideDynamicMetrics')
f(19,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle$MetricsContext.collect')
f(20,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.access$200')
f(21,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.extractAndCollectDynamicMetrics')
f(22,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collect')
f(23,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectLong')
f(24,35,1,1,'com/hazelcast/internal/metrics/impl/PublisherMetricsCollector.collectLong')
f(25,35,1,1,'com/hazelcast/internal/metrics/managementcenter/ManagementCenterPublisher.publishLong')
f(26,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.addLong')
f(27,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.writeDescriptor')
f(28,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getDictionaryId')
f(29,35,1,1,'com/hazelcast/internal/metrics/impl/MetricsDictionary.getDictionaryId')
f(30,35,1,1,'java/util/Map.computeIfAbsent')
f(31,35,1,1,'java/util/TreeMap.get')
f(32,35,1,1,'java/util/TreeMap.getEntry')
f(33,35,1,6,'java/util/TreeMap.getEntryUsingComparator',0,1,0)
f(34,35,1,3,'itable stub')
f(12,36,7,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectStaticMetrics',0,1,0)
f(13,37,6,1,'com/hazelcast/internal/metrics/impl/MetricsCollectionCycle.collectLong')
f(14,37,2,1,'com/hazelcast/internal/metrics/impl/MethodProbe$LongMethodProbe.get')
f(15,37,2,1,'java/lang/reflect/Method.invoke')
f(16,37,2,1,'sun/reflect/DelegatingMethodAccessorImpl.invoke')
f(17,37,2,1,'sun/reflect/GeneratedMethodAccessor119.invoke')
f(18,37,2,1,'com/hazelcast/internal/memory/DefaultMemoryStats.getFreePhysical')
f(19,37,2,1,'com/hazelcast/internal/memory/MemoryStatsSupport.freePhysicalMemory')
f(20,37,2,1,'com/hazelcast/internal/util/OperatingSystemMXBeanSupport.readLongAttribute')
f(21,37,2,1,'java/lang/Class.getMethod')
f(22,37,2,1,'java/lang/Class.getMethod0')
f(23,37,2,1,'java/lang/Class.privateGetMethodRecursive')
f(24,37,1,6,'java/lang/Class.privateGetDeclaredMethods',0,1,0)
f(24,38,1,1,'java/lang/Class.searchMethods')
f(25,38,1,1,'java/lang/String.intern')
f(14,39,3,1,'com/hazelcast/internal/metrics/impl/PublisherMetricsCollector.collectLong')
f(15,39,3,1,'com/hazelcast/internal/metrics/managementcenter/ManagementCenterPublisher.publishLong')
f(16,39,3,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.addLong')
f(17,39,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.writeDescriptor')
f(18,39,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getDictionaryId')
f(19,39,1,1,'com/hazelcast/internal/metrics/impl/MetricsDictionary.getDictionaryId')
f(20,39,1,1,'java/util/Map.computeIfAbsent')
f(21,39,1,1,'java/util/TreeMap.get')
f(22,39,1,6,'java/util/TreeMap.getEntry',0,1,0)
f(17,40,2,1,'java/io/DataOutputStream.write')
f(18,40,2,6,'java/util/zip/DeflaterOutputStream.write',0,1,0)
f(19,41,1,1,'java/util/zip/DeflaterOutputStream.deflate')
f(20,41,1,1,'java/util/zip/Deflater.deflate')
f(21,41,1,6,'java/util/zip/Deflater.deflate',0,1,0)
f(14,42,1,6,'com/hazelcast/internal/metrics/metricsets/RuntimeMetricSet$$Lambda$1539/1121686878.get',0,1,0)
f(10,43,1,1,'com/hazelcast/internal/metrics/impl/PublisherMetricsCollector.publishCollectedMetrics')
f(11,43,1,1,'com/hazelcast/internal/metrics/managementcenter/ManagementCenterPublisher.whenComplete')
f(12,43,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getBlobAndReset')
f(13,43,1,1,'com/hazelcast/internal/metrics/impl/MetricsCompressor.getRenderedBlob')
f(14,43,1,1,'java/io/FilterOutputStream.close')
f(15,43,1,1,'java/util/zip/DeflaterOutputStream.close')
f(16,43,1,1,'java/util/zip/DeflaterOutputStream.finish')
f(17,43,1,1,'java/util/zip/DeflaterOutputStream.deflate')
f(18,43,1,1,'java/util/zip/Deflater.deflate')
f(19,43,1,1,'java/util/zip/Deflater.deflate')
f(20,43,1,1,'java/util/zip/Deflater.deflateBytes')
f(21,43,1,3,'Java_java_util_zip_Deflater_deflateBytes')
f(22,43,1,3,'deflate')
f(23,43,1,3,'deflateCopy')
f(24,43,1,3,'inflateCodesUsed')
f(25,43,1,3,'inflateCodesUsed')
f(7,44,4,1,'java/util/concurrent/LinkedBlockingQueue.poll')
f(8,44,4,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(9,44,4,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(10,44,4,1,'sun/misc/Unsafe.park')
f(11,44,4,3,'Unsafe_Park')
f(12,44,4,4,'Parker::park(bool, long)')
f(13,44,4,3,'__psynch_cvwait')
f(6,48,1,6,'com/hazelcast/internal/util/executor/LoggingScheduledExecutor.afterExecute',0,1,0)
f(7,48,1,2,'java/lang/StringBuilder.append',1,0,0)
f(6,49,3,1,'java/util/concurrent/FutureTask.run')
f(7,49,3,1,'java/util/concurrent/FutureTask.run$$$capture')
f(8,49,3,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(9,49,3,1,'com/hazelcast/spi/impl/operationparker/impl/OperationParkerImpl$ExpirationTask.run')
f(10,49,3,1,'com/hazelcast/spi/impl/operationparker/impl/OperationParkerImpl$ExpirationTask.doRun',0,1,0)
f(11,50,2,6,'java/util/concurrent/DelayQueue.poll',0,2,0)
f(6,52,5,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.run')
f(7,52,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201')
f(8,52,1,1,'java/util/concurrent/FutureTask.run')
f(9,52,1,6,'java/util/concurrent/FutureTask.run$$$capture',0,1,0)
f(7,53,2,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301')
f(8,53,2,1,'java/util/concurrent/FutureTask.runAndReset')
f(9,53,1,6,'com/intellij/rt/debugger/agent/CaptureStorage.insertEnter',0,1,0)
f(10,53,1,2,'com/intellij/rt/debugger/agent/CaptureStorage$HardKey.<init>',1,0,0)
f(9,54,1,1,'java/util/concurrent/FutureTask.runAndReset$$$capture')
f(10,54,1,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(11,54,1,1,'com/hazelcast/spi/impl/executionservice/impl/DelegateAndSkipOnConcurrentExecutionDecorator.run')
f(12,54,1,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.execute')
f(13,54,1,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.addNewWorkerIfRequired')
f(14,54,1,1,'java/util/concurrent/ThreadPoolExecutor.execute')
f(15,54,1,1,'java/util/concurrent/SynchronousQueue.offer')
f(16,54,1,6,'java/util/concurrent/SynchronousQueue$TransferStack.transfer',0,1,0)
f(7,55,2,1,'java/util/concurrent/ScheduledThreadPoolExecutor.reExecutePeriodic')
f(8,55,2,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.add')
f(9,55,2,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.add')
f(10,55,2,6,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.offer',0,1,0)
f(11,56,1,6,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.siftUp',0,1,0)
f(6,57,9,1,'java/util/concurrent/ThreadPoolExecutor.getTask',0,1,0)
f(7,58,6,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(8,58,6,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(9,58,1,6,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.finishPoll',0,1,0)
f(9,59,5,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos',0,2,0)
f(10,59,2,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.isOnSyncQueue',2,0,0)
f(11,59,2,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.findNodeFromTail',2,0,0)
f(10,61,3,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(11,61,3,1,'sun/misc/Unsafe.park')
f(12,61,3,3,'Unsafe_Park')
f(13,61,3,4,'Parker::park(bool, long)')
f(14,61,2,3,'__gettimeofday')
f(14,63,1,3,'__psynch_cvwait')
f(7,64,2,1,'java/util/concurrent/SynchronousQueue.poll')
f(8,64,2,1,'java/util/concurrent/SynchronousQueue$TransferStack.transfer')
f(9,64,2,1,'java/util/concurrent/SynchronousQueue$TransferStack.awaitFulfill')
f(10,64,2,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(11,64,2,1,'sun/misc/Unsafe.park')
f(12,64,2,3,'Unsafe_Park')
f(13,64,2,4,'Parker::park(bool, long)')
f(14,64,2,3,'__psynch_cvwait')
f(2,66,6,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.executeRun')
f(3,66,3,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationQueueImpl.take')
f(4,66,3,1,'com/hazelcast/internal/util/concurrent/MPSCQueue.take')
f(5,66,3,6,'com/hazelcast/internal/util/concurrent/MPSCQueue.takeAll',0,2,0)
f(6,66,1,2,'java/lang/Thread.isInterrupted',1,0,0)
f(6,67,2,2,'java/util/concurrent/locks/LockSupport.park',1,0,0)
f(7,68,1,1,'sun/misc/Unsafe.park')
f(8,68,1,3,'Unsafe_Park')
f(9,68,1,4,'Parker::park(bool, long)')
f(10,68,1,3,'__psynch_cvwait')
f(3,69,3,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.process')
f(4,69,3,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.process')
f(5,69,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationThread.process')
f(6,69,2,6,'com/hazelcast/internal/partition/impl/PartitionPrimaryReplicaAntiEntropyTask.run',0,1,0)
f(7,70,1,1,'com/hazelcast/internal/partition/impl/AbstractPartitionPrimaryReplicaAntiEntropyTask.retainAndGetNamespaces')
f(8,70,1,6,'com/hazelcast/ringbuffer/impl/RingbufferService.getAllServiceNamespaces',0,1,0)
f(5,71,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(6,71,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(7,71,1,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.call')
f(8,71,1,1,'com/hazelcast/map/impl/operation/MapOperation.call')
f(9,71,1,1,'com/hazelcast/map/impl/operation/MapOperation.run')
f(10,71,1,1,'com/hazelcast/map/impl/operation/GetOperation.runInternal')
f(11,71,1,1,'com/hazelcast/map/impl/recordstore/RecordStore.get')
f(12,71,1,1,'com/hazelcast/map/impl/recordstore/DefaultRecordStore.get')
f(13,71,1,1,'com/hazelcast/map/impl/recordstore/DefaultRecordStore.getRecordOrNull')
f(14,71,1,1,'com/hazelcast/map/impl/recordstore/StorageImpl.get')
f(15,71,1,1,'com/hazelcast/map/impl/recordstore/StorageImpl.get')
f(16,71,1,1,'com/hazelcast/internal/util/ConcurrentReferenceHashMap.get')
f(17,71,1,1,'com/hazelcast/internal/util/ConcurrentReferenceHashMap$Segment.get')
f(18,71,1,1,'com/hazelcast/internal/util/ConcurrentReferenceHashMap$Segment.keyEq')
f(19,71,1,1,'com/hazelcast/internal/serialization/impl/HeapData.equals')
f(20,71,1,6,'com/hazelcast/internal/serialization/impl/HeapData.getType',0,1,0)
f(21,71,1,2,'com/hazelcast/internal/serialization/impl/HeapData.totalSize',1,0,0)
f(1,72,4,1,'com/hazelcast/spi/impl/operationexecutor/slowoperationdetector/SlowOperationDetector$DetectorThread.run',0,0,1)
f(2,73,3,6,'com/hazelcast/spi/impl/operationexecutor/slowoperationdetector/SlowOperationDetector$DetectorThread.sleepInterval',0,2,0)
f(3,75,1,1,'java/util/concurrent/TimeUnit.sleep')
f(4,75,1,1,'java/lang/Thread.sleep')
f(5,75,1,1,'java/lang/Thread.sleep')
f(6,75,1,3,'JVM_Sleep')
f(7,75,1,4,'os::sleep(Thread*, long, bool)')
f(8,75,1,4,'os::PlatformEvent::park(long)')
f(9,75,1,3,'__psynch_cvwait')
f(1,76,1,1,'com/intellij/rt/debugger/agent/CaptureStorage.insertExit')
f(2,76,1,1,'java/lang/ThreadLocal.get')
f(3,76,1,1,'java/lang/ThreadLocal$ThreadLocalMap.access$000')
f(4,76,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer.transferForSignal',0,1,0)
f(1,77,2,1,'io/tapdata/wsclient/modules/imclient/impls/MonitorThread.run')
f(2,77,2,1,'io/tapdata/wsclient/modules/imclient/impls/websocket/WebsocketPushChannel.ping')
f(3,77,2,1,'io/tapdata/wsclient/modules/imclient/impls/websocket/WebsocketPushChannel.send')
f(4,77,1,1,'io/netty/buffer/Unpooled.directBuffer')
f(5,77,1,1,'io/netty/buffer/AbstractByteBufAllocator.directBuffer')
f(6,77,1,1,'io/netty/buffer/AbstractByteBufAllocator.directBuffer')
f(7,77,1,1,'io/netty/buffer/UnpooledByteBufAllocator.newDirectBuffer')
f(8,77,1,1,'io/netty/buffer/AbstractByteBufAllocator.toLeakAwareBuffer')
f(9,77,1,1,'io/netty/util/ResourceLeakDetector.track')
f(10,77,1,1,'io/netty/util/ResourceLeakDetector.track0')
f(11,77,1,6,'java/util/concurrent/ThreadLocalRandom.nextInt',0,1,0)
f(4,78,1,1,'io/netty/channel/AbstractChannel.writeAndFlush')
f(5,78,1,1,'io/netty/channel/DefaultChannelPipeline.writeAndFlush')
f(6,78,1,1,'io/netty/channel/AbstractChannelHandlerContext.writeAndFlush')
f(7,78,1,1,'io/netty/channel/AbstractChannelHandlerContext.writeAndFlush')
f(8,78,1,1,'io/netty/channel/AbstractChannelHandlerContext.write')
f(9,78,1,1,'io/netty/channel/AbstractChannelHandlerContext$WriteAndFlushTask.access$1600')
f(10,78,1,1,'io/netty/channel/AbstractChannelHandlerContext$WriteAndFlushTask.newInstance')
f(11,78,1,6,'io/netty/util/Recycler.get',0,1,0)
f(1,79,3884,1,'java/lang/Thread.run')
f(2,79,2,1,'com/alibaba/arthas/deps/io/netty/util/concurrent/FastThreadLocalRunnable.run')
f(3,79,2,1,'com/alibaba/arthas/deps/io/netty/util/internal/ThreadExecutorMap$2.run')
f(4,79,2,1,'com/alibaba/arthas/deps/io/netty/util/concurrent/SingleThreadEventExecutor$4.run')
f(5,79,2,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.run')
f(6,79,2,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.processSelectedKeys')
f(7,79,2,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.processSelectedKeysOptimized')
f(8,79,2,1,'com/alibaba/arthas/deps/io/netty/channel/nio/NioEventLoop.processSelectedKey')
f(9,79,2,1,'com/alibaba/arthas/deps/io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe.read')
f(10,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/DefaultChannelPipeline.fireChannelRead')
f(11,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(12,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(13,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/DefaultChannelPipeline$HeadContext.channelRead')
f(14,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.fireChannelRead')
f(15,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(16,79,1,1,'com/alibaba/arthas/deps/io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(17,79,1,1,'io/termd/core/telnet/netty/TelnetChannelHandler.channelRead')
f(18,79,1,1,'io/termd/core/telnet/TelnetConnection.receive')
f(19,79,1,1,'io/termd/core/telnet/TelnetConnection.flushDataIfNecessary')
f(20,79,1,1,'io/termd/core/telnet/TelnetConnection.flushData')
f(21,79,1,1,'io/termd/core/telnet/TelnetTtyConnection.onData')
f(22,79,1,1,'io/termd/core/io/BinaryDecoder.write')
f(23,79,1,1,'io/termd/core/io/BinaryDecoder.write')
f(24,79,1,1,'io/termd/core/tty/ReadBuffer.accept')
f(25,79,1,1,'io/termd/core/tty/ReadBuffer.accept')
f(26,79,1,1,'io/termd/core/tty/TtyEventDecoder.accept')
f(27,79,1,1,'io/termd/core/tty/TtyEventDecoder.accept')
f(28,79,1,1,'io/termd/core/readline/Readline$Interaction$2.accept')
f(29,79,1,1,'io/termd/core/readline/Readline$Interaction$2.accept')
f(30,79,1,1,'io/termd/core/readline/Readline.access$500')
f(31,79,1,1,'io/termd/core/readline/Readline.deliver')
f(32,79,1,1,'io/termd/core/readline/Readline$Interaction.access$200')
f(33,79,1,1,'io/termd/core/readline/Readline$Interaction.handle')
f(34,79,1,1,'com/sun/proxy/$Proxy137.apply')
f(35,79,1,1,'com/taobao/arthas/core/shell/term/impl/FunctionInvocationHandler.invoke')
f(36,79,1,1,'java/lang/reflect/Method.invoke')
f(37,79,1,1,'sun/reflect/DelegatingMethodAccessorImpl.invoke')
f(38,79,1,1,'sun/reflect/NativeMethodAccessorImpl.invoke')
f(39,79,1,1,'sun/reflect/NativeMethodAccessorImpl.invoke0')
f(40,79,1,1,'io/termd/core/readline/functions/HistorySearchBackward.apply')
f(41,79,1,1,'io/termd/core/readline/Readline$Interaction.refresh')
f(42,79,1,1,'io/termd/core/readline/Readline$Interaction.refresh')
f(43,79,1,1,'io/termd/core/tty/TtyOutputMode.accept')
f(44,79,1,1,'io/termd/core/tty/TtyOutputMode.accept')
f(45,79,1,1,'io/termd/core/tty/TtyOutputMode.sendChunk')
f(46,79,1,1,'io/termd/core/io/BinaryEncoder.accept')
f(47,79,1,1,'io/termd/core/io/BinaryEncoder.accept')
f(48,79,1,1,'io/termd/core/telnet/TelnetTtyConnection$2.accept')
f(49,79,1,1,'io/termd/core/telnet/TelnetTtyConnection$2.accept')
f(50,79,1,1,'io/termd/core/telnet/TelnetConnection.write')
f(51,79,1,1,'io/termd/core/telnet/netty/NettyTelnetConnection.send')
f(52,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/Unpooled.buffer')
f(53,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/AbstractByteBufAllocator.heapBuffer')
f(54,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/AbstractByteBufAllocator.heapBuffer')
f(55,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/UnpooledByteBufAllocator.newHeapBuffer')
f(56,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/UnpooledByteBufAllocator$InstrumentedUnpooledUnsafeHeapByteBuf.<init>')
f(57,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/UnpooledUnsafeHeapByteBuf.<init>')
f(58,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/UnpooledHeapByteBuf.<init>')
f(59,79,1,1,'com/alibaba/arthas/deps/io/netty/buffer/AbstractReferenceCountedByteBuf.<init>')
f(60,79,1,1,'com/alibaba/arthas/deps/io/netty/util/internal/ReferenceCountUpdater.setInitialValue')
f(61,79,1,1,'com/alibaba/arthas/deps/io/netty/util/internal/PlatformDependent.safeConstructPutInt')
f(62,79,1,1,'com/alibaba/arthas/deps/io/netty/util/internal/PlatformDependent0.safeConstructPutInt')
f(63,79,1,1,'sun/misc/Unsafe.putInt')
f(10,80,1,1,'com/alibaba/arthas/deps/io/netty/channel/DefaultMaxMessagesRecvByteBufAllocator$MaxMessageHandle.allocate')
f(11,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/AbstractByteBufAllocator.ioBuffer')
f(12,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/AbstractByteBufAllocator.directBuffer')
f(13,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/AbstractByteBufAllocator.directBuffer')
f(14,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/PooledByteBufAllocator.newDirectBuffer')
f(15,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/PoolArena.allocate')
f(16,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/PoolArena$DirectArena.newByteBuf')
f(17,80,1,1,'com/alibaba/arthas/deps/io/netty/buffer/PooledUnsafeDirectByteBuf.newInstance')
f(18,80,1,1,'com/alibaba/arthas/deps/io/netty/util/internal/ObjectPool$RecyclerObjectPool.get')
f(19,80,1,1,'com/alibaba/arthas/deps/io/netty/util/Recycler.get')
f(20,80,1,4,'SharedRuntime::resolve_opt_virtual_call_C(JavaThread*)')
f(21,80,1,4,'SharedRuntime::resolve_helper(JavaThread*, bool, bool, Thread*)')
f(22,80,1,4,'SharedRuntime::resolve_sub_helper(JavaThread*, bool, bool, Thread*)')
f(23,80,1,4,'SharedRuntime::find_callee_info(JavaThread*, Bytecodes::Code&, CallInfo&, Thread*)')
f(24,80,1,4,'SharedRuntime::find_callee_info_helper(JavaThread*, vframeStream&, Bytecodes::Code&, CallInfo&, Thread*)')
f(25,80,1,4,'LinkResolver::resolve_invoke(CallInfo&, Handle, constantPoolHandle, int, Bytecodes::Code, Thread*)')
f(26,80,1,4,'LinkResolver::resolve_invokevirtual(CallInfo&, Handle, constantPoolHandle, int, Thread*)')
f(27,80,1,4,'LinkResolver::resolve_virtual_call(CallInfo&, Handle, KlassHandle, KlassHandle, Symbol*, Symbol*, KlassHandle, bool, bool, Thread*)')
f(28,80,1,4,'LinkResolver::runtime_resolve_virtual_method(CallInfo&, methodHandle, KlassHandle, Handle, KlassHandle, bool, Thread*)')
f(29,80,1,4,'CallInfo::set_virtual(KlassHandle, KlassHandle, methodHandle, methodHandle, int, Thread*)')
f(30,80,1,4,'CallInfo::set_common(KlassHandle, KlassHandle, methodHandle, methodHandle, CallInfo::CallKind, int, Thread*)')
f(31,80,1,4,'CompilationPolicy::must_be_compiled(methodHandle, int)')
f(32,80,1,4,'CompilationPolicy::can_be_compiled(methodHandle, int)')
f(33,80,1,4,'AbstractInterpreter::can_be_compiled(methodHandle)')
f(34,80,1,4,'AbstractInterpreter::method_kind(methodHandle)')
f(2,81,169,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$CooperativeWorker.run')
f(3,81,164,1,'com/hazelcast/internal/util/concurrent/BackoffIdleStrategy.idle')
f(4,81,1,6,'com/hazelcast/internal/util/concurrent/BackoffIdleStrategy.parkTime',0,1,0)
f(4,82,163,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(5,82,163,1,'sun/misc/Unsafe.park')
f(6,85,160,3,'Unsafe_Park')
f(7,85,1,4,'JavaThreadParkedState::JavaThreadParkedState(JavaThread*, bool)')
f(8,85,1,4,'JavaThreadStatusChanger::JavaThreadStatusChanger(JavaThread*, java_lang_Thread::ThreadStatus)')
f(9,85,1,4,'java_lang_Thread::get_thread_status(oopDesc*)')
f(7,86,1,4,'JfrBackend::is_event_enabled(TraceEventId)')
f(7,87,157,4,'Parker::park(bool, long)')
f(8,88,1,4,'Monitor::IUnlock(bool)')
f(8,89,1,3,'__commpage_gettimeofday')
f(8,90,2,3,'__gettimeofday')
f(8,92,149,3,'__psynch_cvwait')
f(8,241,1,3,'_pthread_cond_wait')
f(9,241,1,3,'pthread_testcancel')
f(8,242,1,3,'cerror_nocancel')
f(8,243,1,3,'gettimeofday')
f(9,243,1,3,'__commpage_gettimeofday_internal')
f(10,243,1,3,'mach_absolute_time')
f(7,244,1,4,'YieldingFlexibleGangWorker::~YieldingFlexibleGangWorker()')
f(3,245,1,6,'com/hazelcast/internal/util/counters/SwCounter$UnsafeSwCounter.inc',0,1,0)
f(3,246,1,1,'com/hazelcast/logging/AbstractLogger.isFinestEnabled')
f(4,246,1,1,'com/hazelcast/logging/impl/LoggingServiceImpl$DefaultLogger.isLoggable')
f(5,246,1,1,'com/hazelcast/logging/Log4j2Factory$Log4j2Logger.isLoggable')
f(6,246,1,1,'org/apache/logging/log4j/spi/AbstractLogger.isEnabled')
f(7,246,1,1,'org/apache/logging/log4j/core/Logger.isEnabled')
f(8,246,1,1,'org/apache/logging/log4j/core/Logger$PrivateConfig.filter')
f(9,246,1,6,'org/apache/logging/log4j/core/filter/AbstractFilterable.getFilter',0,1,0)
f(3,247,3,1,'java/util/concurrent/CopyOnWriteArrayList.forEach',0,1,0)
f(4,248,2,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$CooperativeWorker$$Lambda$1607/**********.accept')
f(5,248,2,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$CooperativeWorker.runTasklet')
f(6,248,1,6,'com/hazelcast/jet/impl/execution/StoreSnapshotTasklet.call',0,1,0)
f(6,249,1,6,'com/hazelcast/jet/impl/util/ProgressTracker.mergeWith',0,1,0)
f(2,250,1,1,'com/hazelcast/sql/impl/state/QueryStateRegistryUpdater$Worker.run')
f(3,250,1,1,'com/hazelcast/sql/impl/state/QueryStateRegistryUpdater$Worker.checkClientState')
f(4,250,1,1,'com/hazelcast/sql/impl/NodeServiceProviderImpl.getClientIds')
f(5,250,1,1,'com/hazelcast/client/impl/ClientServiceProxy.getConnectedClients')
f(6,250,1,1,'com/hazelcast/client/impl/ClientEngineImpl.getClients')
f(7,250,1,1,'java/util/concurrent/ConcurrentHashMap$CollectionView.size')
f(8,250,1,1,'java/util/concurrent/ConcurrentHashMap.size')
f(9,250,1,6,'java/util/concurrent/ConcurrentHashMap.sumCount',0,1,0)
f(2,251,8,1,'com/mongodb/internal/connection/DefaultServerMonitor$ServerMonitorRunnable.run')
f(3,251,8,1,'com/mongodb/internal/connection/DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription')
f(4,251,7,1,'com/mongodb/internal/connection/CommandHelper.executeCommand')
f(5,251,7,1,'com/mongodb/internal/connection/CommandHelper.sendAndReceive')
f(6,251,1,1,'com/mongodb/internal/connection/CommandHelper.getCommandMessage')
f(7,251,1,1,'com/mongodb/internal/connection/CommandMessage.<init>')
f(8,251,1,1,'com/mongodb/internal/connection/CommandMessage.<init>')
f(9,251,1,1,'com/mongodb/internal/connection/CommandMessage.getOpCode')
f(10,251,1,1,'com/mongodb/internal/connection/CommandMessage.isServerVersionAtLeastThreeDotSix')
f(11,251,1,6,'com/mongodb/connection/ServerVersion.compareTo',0,1,0)
f(6,252,5,1,'com/mongodb/internal/connection/InternalStreamConnection.sendAndReceive')
f(7,252,3,1,'com/mongodb/internal/connection/CommandMessage.encode')
f(8,252,3,1,'com/mongodb/internal/connection/RequestMessage.encode')
f(9,252,2,1,'com/mongodb/internal/connection/CommandMessage.encodeMessageBodyWithMetadata')
f(10,252,2,1,'com/mongodb/internal/connection/RequestMessage.addDocument')
f(11,252,2,1,'com/mongodb/internal/connection/RequestMessage.addDocument')
f(12,252,2,1,'org/bson/codecs/BsonDocumentCodec.encode')
f(13,252,2,1,'org/bson/codecs/BsonDocumentCodec.encode')
f(14,252,2,1,'com/mongodb/internal/connection/ElementExtendingBsonWriter.writeEndDocument')
f(15,252,2,1,'com/mongodb/internal/connection/BsonWriterHelper.writeElements')
f(16,252,2,6,'org/bson/codecs/BsonDocumentCodec.encode',0,1,0)
f(17,253,1,1,'org/bson/codecs/BsonDocumentCodec.encode')
f(18,253,1,1,'org/bson/codecs/BsonDocumentCodec.writeValue')
f(19,253,1,1,'org/bson/codecs/EncoderContext.encodeWithChildContext')
f(20,253,1,1,'org/bson/codecs/configuration/LazyCodec.encode')
f(21,253,1,1,'org/bson/codecs/BsonDocumentCodec.encode')
f(22,253,1,1,'org/bson/codecs/BsonDocumentCodec.encode')
f(23,253,1,1,'org/bson/codecs/BsonDocumentCodec.writeValue')
f(24,253,1,1,'org/bson/codecs/EncoderContext.encodeWithChildContext')
f(25,253,1,1,'org/bson/codecs/BsonInt64Codec.encode')
f(26,253,1,1,'org/bson/codecs/BsonInt64Codec.encode')
f(27,253,1,6,'org/bson/AbstractBsonWriter.writeInt64',0,1,0)
f(9,254,1,1,'com/mongodb/internal/connection/RequestMessage.writeMessagePrologue')
f(10,254,1,1,'org/bson/io/OutputBuffer.writeInt32')
f(11,254,1,1,'org/bson/io/OutputBuffer.write')
f(12,254,1,1,'com/mongodb/connection/ByteBufferBsonOutput.writeByte')
f(13,254,1,1,'com/mongodb/connection/ByteBufferBsonOutput.getCurrentByteBuffer')
f(14,254,1,1,'com/mongodb/connection/ByteBufferBsonOutput.getByteBufferAtIndex')
f(15,254,1,1,'com/mongodb/internal/connection/InternalStreamConnection.getBuffer')
f(16,254,1,1,'com/mongodb/internal/connection/SocketStream.getBuffer')
f(17,254,1,1,'com/mongodb/internal/connection/PowerOfTwoBufferPool.getBuffer')
f(18,254,1,1,'com/mongodb/internal/connection/PowerOfTwoBufferPool.log2')
f(19,254,1,6,'java/lang/Integer.numberOfLeadingZeros',0,1,0)
f(7,255,1,1,'com/mongodb/internal/connection/InternalStreamConnection.receiveCommandMessageResponse')
f(8,255,1,1,'com/mongodb/internal/connection/InternalStreamConnection.updateSessionContext')
f(9,255,1,6,'com/mongodb/internal/connection/ProtocolHelper.getClusterTime',0,1,0)
f(10,255,1,2,'com/mongodb/internal/connection/ProtocolHelper.createBsonReader',1,0,0)
f(7,256,1,1,'com/mongodb/internal/connection/InternalStreamConnection.sendCommandMessage')
f(8,256,1,1,'com/mongodb/internal/connection/InternalStreamConnection.sendMessage')
f(9,256,1,1,'com/mongodb/internal/connection/SocketChannelStream.write')
f(10,256,1,1,'java/nio/channels/SocketChannel.write')
f(11,256,1,1,'sun/nio/ch/SocketChannelImpl.write')
f(12,256,1,1,'sun/nio/ch/IOUtil.write')
f(13,256,1,1,'sun/nio/ch/SocketDispatcher.writev')
f(14,256,1,1,'sun/nio/ch/FileDispatcherImpl.writev0')
f(15,256,1,3,'writev')
f(6,257,1,1,'org/bson/codecs/BsonDocumentCodec.<init>')
f(7,257,1,1,'org/bson/codecs/BsonDocumentCodec.<init>')
f(8,257,1,1,'org/bson/codecs/BsonTypeCodecMap.<init>')
f(9,257,1,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(10,257,1,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(11,257,1,1,'org/bson/codecs/configuration/CodecCache.getOrThrow')
f(12,257,1,6,'java/util/concurrent/ConcurrentHashMap.get',0,1,0)
f(13,257,1,2,'java/lang/Object.hashCode',1,0,0)
f(14,257,1,3,'JVM_IHashCode')
f(4,258,1,1,'com/mongodb/internal/connection/DescriptionHelper.createServerDescription')
f(5,258,1,1,'com/mongodb/internal/connection/DescriptionHelper.listToSet')
f(6,258,1,6,'org/bson/BsonArray.iterator',0,1,0)
f(2,259,2,1,'io/netty/util/concurrent/FastThreadLocalRunnable.run')
f(3,259,2,1,'io/netty/util/concurrent/SingleThreadEventExecutor$5.run')
f(4,259,2,1,'io/netty/channel/nio/NioEventLoop.run')
f(5,259,1,1,'io/netty/channel/nio/NioEventLoop.processSelectedKeys')
f(6,259,1,1,'io/netty/channel/nio/NioEventLoop.processSelectedKeysOptimized')
f(7,259,1,1,'io/netty/channel/nio/NioEventLoop.processSelectedKey')
f(8,259,1,1,'io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe.read')
f(9,259,1,1,'io/netty/channel/DefaultChannelPipeline.fireChannelRead')
f(10,259,1,1,'io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(11,259,1,1,'io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(12,259,1,1,'io/netty/channel/DefaultChannelPipeline$HeadContext.channelRead')
f(13,259,1,1,'io/netty/channel/AbstractChannelHandlerContext.fireChannelRead')
f(14,259,1,1,'io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(15,259,1,1,'io/netty/channel/AbstractChannelHandlerContext.invokeChannelRead')
f(16,259,1,1,'io/netty/handler/codec/ByteToMessageDecoder.channelRead')
f(17,259,1,1,'io/netty/handler/codec/ByteToMessageDecoder.callDecode')
f(18,259,1,1,'io/netty/handler/codec/ByteToMessageDecoder.decodeRemovalReentryProtection')
f(19,259,1,1,'io/netty/handler/codec/http/websocketx/WebSocket08FrameDecoder.decode')
f(20,259,1,1,'io/netty/buffer/AbstractByteBuf.readByte')
f(21,259,1,6,'io/netty/buffer/AbstractByteBuf.checkReadableBytes0',0,1,0)
f(5,260,1,6,'io/netty/channel/nio/NioEventLoop.select',0,1,0)
f(2,261,3316,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(3,261,3316,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(4,261,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx$$Lambda$2381/1045511461.run')
f(5,261,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx.lambda$start$1')
f(6,261,1,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx.checkJoin')
f(7,261,1,6,'com/google/common/collect/Queues.drain',0,1,0)
f(4,262,3254,1,'java/util/concurrent/FutureTask.run')
f(5,262,3254,1,'java/util/concurrent/FutureTask.run$$$capture')
f(6,262,3,1,'io/tapdata/observable/logging/appender/AppenderFactory$$Lambda$2243/1943097246.call')
f(7,262,3,1,'io/tapdata/observable/logging/appender/AppenderFactory.lambda$new$3')
f(8,262,3,1,'io/tapdata/observable/logging/appender/AppenderFactory.readMessageFromCacheQueue')
f(9,262,2,1,'java/util/concurrent/Semaphore.tryAcquire')
f(10,262,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.tryAcquireSharedNanos')
f(11,262,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.doAcquireSharedNanos')
f(12,262,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer.cancelAcquire',0,1,0)
f(12,263,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(13,263,1,1,'sun/misc/Unsafe.park')
f(14,263,1,3,'Unsafe_Park')
f(15,263,1,4,'Parker::park(bool, long)')
f(16,263,1,3,'__psynch_cvwait')
f(9,264,1,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.readDocument')
f(10,264,1,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.readingDocument')
f(11,264,1,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.readingDocument0')
f(12,264,1,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.inACycle')
f(13,264,1,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.inACycle2')
f(14,264,1,1,'net/openhft/chronicle/wire/AbstractWire.readDataHeader')
f(15,264,1,1,'net/openhft/chronicle/wire/AbstractWire.alignForRead')
f(16,264,1,1,'net/openhft/chronicle/bytes/StreamingDataInput.readPositionForHeader')
f(17,264,1,1,'net/openhft/chronicle/bytes/MappedBytes.readSkip')
f(18,264,1,6,'net/openhft/chronicle/bytes/MappedBytes.readSkip',0,1,0)
f(6,265,6,1,'io/tapdata/observable/logging/appender/AppenderFactory$$Lambda$2244/1935465477.call')
f(7,265,6,1,'io/tapdata/observable/logging/appender/AppenderFactory.lambda$new$4')
f(8,265,6,1,'io/tapdata/observable/logging/appender/AppenderFactory.readMessageFromCacheQueue')
f(9,265,4,1,'java/util/concurrent/Semaphore.tryAcquire')
f(10,265,4,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.tryAcquireSharedNanos')
f(11,265,4,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.doAcquireSharedNanos')
f(12,265,4,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(13,265,4,1,'sun/misc/Unsafe.park')
f(14,265,4,3,'Unsafe_Park')
f(15,265,4,4,'Parker::park(bool, long)')
f(16,265,1,3,'__gettimeofday')
f(16,266,3,3,'__psynch_cvwait')
f(9,269,2,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.readDocument')
f(10,269,2,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.readingDocument')
f(11,269,2,1,'net/openhft/chronicle/queue/impl/single/StoreTailer.readingDocument0')
f(12,269,2,6,'net/openhft/chronicle/queue/impl/single/StoreTailer.setAddress',0,1,0)
f(13,270,1,1,'net/openhft/chronicle/bytes/MappedBytes.addressForRead')
f(14,270,1,1,'net/openhft/chronicle/bytes/internal/NativeBytesStore.addressForRead')
f(15,270,1,6,'net/openhft/chronicle/bytes/MappedBytesStore.start',0,1,0)
f(6,271,3245,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(7,271,71,1,'com/hazelcast/jet/impl/execution/TaskletExecutionService$BlockingWorker.run')
f(8,271,11,1,'com/hazelcast/internal/util/concurrent/BackoffIdleStrategy.idle')
f(9,271,11,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(10,271,11,1,'sun/misc/Unsafe.park')
f(11,271,11,3,'Unsafe_Park')
f(12,271,11,4,'Parker::park(bool, long)')
f(13,271,1,3,'__gettimeofday')
f(13,272,10,3,'__psynch_cvwait')
f(8,282,60,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.call',0,1,0)
f(9,283,59,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.stateMachineStep',0,1,0)
f(10,284,51,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.complete')
f(11,284,51,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.complete')
f(12,284,1,1,'com/google/common/collect/Queues.drain')
f(13,284,1,1,'java/util/concurrent/LinkedBlockingQueue.drainTo')
f(14,284,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(15,284,1,6,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock',0,1,0)
f(12,285,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/HazelcastBaseNode.offer')
f(13,285,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/HazelcastBaseNode.tryEmit')
f(14,285,1,1,'com/hazelcast/jet/core/AbstractProcessor.tryEmit')
f(15,285,1,1,'com/hazelcast/jet/impl/execution/OutboxImpl.offer')
f(16,285,1,1,'com/hazelcast/jet/impl/execution/OutboxImpl.offerInternal')
f(17,285,1,1,'com/hazelcast/jet/impl/execution/OutboxImpl.doOffer')
f(18,285,1,1,'com/hazelcast/jet/impl/execution/ConveyorCollector.offer')
f(19,285,1,1,'com/hazelcast/jet/impl/execution/ConveyorCollector.offerToConveyor')
f(20,285,1,1,'com/hazelcast/internal/util/concurrent/ConcurrentConveyor.offer')
f(21,285,1,1,'com/hazelcast/internal/util/concurrent/ConcurrentConveyor.offer')
f(22,285,1,6,'com/hazelcast/internal/util/concurrent/ConcurrentConveyor.checkDrainerGone',0,1,0)
f(12,286,48,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.batchTransformToTapValue')
f(13,286,48,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode.transformToTapValue')
f(14,286,48,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.tapRecordToTapValue')
f(15,286,48,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.toTapValue')
f(16,286,47,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.transformToTapValueMap')
f(17,286,47,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.transformToTapValueMap')
f(18,286,3,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.getOrInitTransformToTapValueFieldWrapper')
f(19,286,3,1,'com/google/common/cache/LocalCache$LocalManualCache.get')
f(20,286,3,1,'com/google/common/cache/LocalCache.get')
f(21,286,2,1,'com/google/common/cache/LocalCache$Segment.get')
f(22,286,1,6,'com/google/common/cache/LocalCache$Segment.getEntry',0,1,0)
f(22,287,1,1,'com/google/common/cache/LocalCache$Segment.postReadCleanup')
f(23,287,1,1,'com/google/common/cache/LocalCache$Segment.cleanUp')
f(24,287,1,1,'com/google/common/cache/LocalCache$Segment.runLockedCleanup')
f(25,287,1,1,'com/google/common/cache/LocalCache$Segment.expireEntries')
f(26,287,1,1,'com/google/common/cache/LocalCache$Segment.drainRecencyQueue')
f(27,287,1,6,'java/util/concurrent/ConcurrentLinkedQueue.poll',0,1,0)
f(21,288,1,1,'com/google/common/cache/LocalCache.hash')
f(22,288,1,6,'com/google/common/cache/LocalCache.rehash',0,1,0)
f(18,289,44,1,'io/tapdata/entity/codec/filter/impl/AllLayerMapIterator.iterate',0,1,0)
f(19,290,37,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced$$Lambda$3113/658150783.filter')
f(20,290,37,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.lambda$transformToTapValueMap$0',0,2,0)
f(21,292,8,1,'io/tapdata/connector/oracle/OracleConnector$$Lambda$2000/445001180.toTapValue')
f(22,292,8,1,'io/tapdata/connector/oracle/OracleConnector.lambda$registerCapabilities$9')
f(23,292,8,1,'oracle/sql/TIMESTAMP.toLocalDateTime')
f(24,292,8,1,'oracle/sql/TIMESTAMP.timestampValue')
f(25,292,8,1,'oracle/sql/TIMESTAMP.toTimestamp')
f(26,292,8,1,'oracle/sql/TIMESTAMP.toTimestamp')
f(27,292,5,1,'java/util/Calendar.getInstance')
f(28,292,2,1,'java/util/Calendar.createCalendar')
f(29,292,2,1,'sun/util/locale/provider/CalendarProviderImpl.getInstance')
f(30,292,2,1,'java/util/Calendar$Builder.build')
f(31,292,2,1,'java/util/Calendar.setTimeInMillis')
f(32,292,2,1,'java/util/GregorianCalendar.computeFields')
f(33,292,1,6,'java/util/Calendar.setFieldsComputed',0,1,0)
f(33,293,1,1,'java/util/GregorianCalendar.computeFields')
f(34,293,1,1,'sun/util/calendar/ZoneInfo.getOffsets')
f(35,293,1,6,'sun/util/calendar/ZoneInfo.getOffsets',0,1,0)
f(28,294,3,1,'java/util/TimeZone.getDefault')
f(29,294,3,1,'sun/util/calendar/ZoneInfo.clone')
f(30,294,3,1,'java/util/TimeZone.clone')
f(31,294,3,1,'java/lang/Object.clone')
f(32,294,1,4,'JNIHandleBlock::allocate_handle(oopDesc*)')
f(32,295,2,3,'JVM_Clone')
f(33,296,1,3,'pthread_getspecific')
f(27,297,3,1,'java/util/Calendar.getTime')
f(28,297,3,1,'java/util/Calendar.getTimeInMillis')
f(29,297,3,1,'java/util/Calendar.updateTime')
f(30,297,3,1,'java/util/GregorianCalendar.computeTime',0,1,0)
f(31,297,1,4,'SharedRuntime::lrem(long, long)')
f(31,298,1,1,'java/util/GregorianCalendar.computeFields')
f(32,298,1,1,'sun/util/calendar/ZoneInfo.getOffsets')
f(33,298,1,1,'sun/util/calendar/ZoneInfo.getOffsets')
f(34,298,1,6,'sun/util/calendar/ZoneInfo.getTransitionIndex',0,1,0)
f(31,299,1,1,'sun/util/calendar/ZoneInfo.getOffsetsByWall')
f(32,299,1,1,'sun/util/calendar/ZoneInfo.getOffsets')
f(33,299,1,6,'sun/util/calendar/ZoneInfo.getTransitionIndex',0,1,0)
f(21,300,24,1,'io/tapdata/entity/codec/filter/entity/TransformToTapValueFieldWrapper.getField')
f(22,300,3,6,'java/util/ArrayList.get',0,3,0)
f(22,303,21,6,'java/util/ArrayList.size',0,21,0)
f(21,324,3,1,'java/util/HashSet.add',0,1,0)
f(22,325,2,6,'java/util/HashMap.put',0,1,0)
f(23,325,1,2,'java/util/HashMap.hash',1,0,0)
f(23,326,1,6,'java/util/HashMap.putVal',0,1,0)
f(19,327,3,6,'java/util/HashMap$Node.getValue',0,3,0)
f(19,330,1,6,'java/util/HashMap$Node.setValue',0,1,0)
f(19,331,1,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(20,331,1,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(21,331,1,6,'java/util/LinkedHashMap$LinkedHashIterator.nextNode',0,1,0)
f(19,332,1,6,'java/util/LinkedHashMap$LinkedHashIterator.hasNext',0,1,0)
f(16,333,1,1,'io/tapdata/schema/TapTableMap.get')
f(17,333,1,6,'com/tapdata/entity/task/config/TaskRetryConfig.getMaxRetryTime',0,1,0)
f(18,333,1,4,'SharedRuntime::ldiv(long, long)')
f(12,334,1,1,'java/util/ArrayList.<init>')
f(13,334,1,6,'java/util/AbstractCollection.toArray',0,1,0)
f(10,335,1,6,'com/hazelcast/jet/impl/execution/ProcessorTasklet.processInbox',0,1,0)
f(10,336,6,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.stateMachineStep')
f(11,336,6,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.stateMachineStep')
f(12,336,6,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.processInbox')
f(13,336,1,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.fillInbox')
f(14,336,1,1,'com/hazelcast/jet/impl/execution/ConcurrentInboundEdgeStream$RoundRobinDrain.drainTo')
f(15,336,1,1,'com/hazelcast/jet/impl/execution/ConcurrentInboundEdgeStream$RoundRobinDrain.drainQueue')
f(16,336,1,1,'com/hazelcast/internal/util/concurrent/OneToOneConcurrentArrayQueue.drain')
f(17,336,1,6,'com/hazelcast/jet/impl/execution/ConcurrentInboundEdgeStream$RoundRobinDrain$ItemDetector.test',0,1,0)
f(13,337,5,1,'com/hazelcast/jet/impl/util/Util.doWithClassLoader',0,1,0)
f(14,338,4,1,'com/hazelcast/jet/function/RunnableEx.run')
f(15,338,4,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet$$Lambda$3081/154163761.runEx')
f(16,338,4,1,'com/hazelcast/jet/impl/execution/ProcessorTasklet.lambda$processInbox$2f647568$2')
f(17,338,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.process',0,1,0)
f(18,339,1,1,'com/tapdata/tm/commons/dag/Element.disabledNode')
f(19,339,1,1,'java/util/HashMap.containsKey')
f(20,339,1,6,'java/util/HashMap.getNode',0,1,0)
f(18,340,2,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.enqueue')
f(19,340,2,1,'java/util/concurrent/LinkedBlockingQueue.offer')
f(20,340,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(21,340,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(22,340,1,1,'sun/misc/Unsafe.park')
f(23,340,1,3,'Unsafe_Park')
f(24,340,1,4,'Parker::park(bool, long)')
f(25,340,1,3,'__psynch_cvwait')
f(20,341,1,1,'java/util/concurrent/locks/ReentrantLock.lockInterruptibly')
f(21,341,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireInterruptibly')
f(22,341,1,1,'java/lang/Thread.interrupted')
f(23,341,1,1,'java/lang/Thread.isInterrupted')
f(24,341,1,3,'JVM_IsInterrupted')
f(25,341,1,4,'os::is_interrupted(Thread*, bool)')
f(7,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkBaseNode$$Lambda$2992/945890163.run')
f(8,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.startSourceRunner')
f(9,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.doSnapshotWithControl')
f(10,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/controller/SnapshotOrderController.runWithControl')
f(11,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$2994/441002518.run')
f(12,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2')
f(13,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.doSnapshot')
f(14,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.doSnapshotInvoke')
f(15,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/HazelcastBaseNode.executeDataFuncAspect')
f(16,342,931,1,'io/tapdata/aspect/utils/AspectUtils.executeDataFuncAspect')
f(17,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3014/998296398.accept')
f(18,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12')
f(19,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invoke')
f(20,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethod')
f(21,342,931,1,'io/tapdata/pdk/core/utils/RetryUtils.autoRetry')
f(22,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3028/980981694.run')
f(23,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$invokePDKMethod$11')
f(24,342,931,1,'io/tapdata/pdk/core/api/Node.applyClassLoaderContext')
f(25,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3029/333708744.run')
f(26,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$null$10')
f(27,342,931,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethodPrivate')
f(28,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3026/2084335851.run')
f(29,342,931,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$null$11')
f(30,342,931,1,'io/tapdata/connector/oracle/OracleConnector$$Lambda$1977/1066631167.batchRead')
f(31,342,931,1,'io/tapdata/common/CommonDbConnector.batchReadWithoutOffset')
f(32,342,931,1,'io/tapdata/common/CommonDbConnector.batchReadWithoutHashSplit')
f(33,342,931,1,'io/tapdata/common/JdbcContext.query',0,0,1)
f(34,342,1,4,'SharedRuntime::lrem(long, long)')
f(34,343,930,1,'io/tapdata/common/CommonDbConnector$$Lambda$3032/1315683681.accept')
f(35,343,930,1,'io/tapdata/common/CommonDbConnector.lambda$batchReadWithoutHashSplit$30',0,1,0)
f(36,343,100,1,'com/zaxxer/hikari/pool/HikariProxyResultSet.next')
f(37,343,100,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.next')
f(38,343,100,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.absoluteInternal')
f(39,343,100,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.fetchNextRows')
f(40,343,100,1,'oracle/jdbc/driver/InsensitiveScrollableResultSet.fetchMoreRows')
f(41,343,100,1,'oracle/jdbc/driver/OracleStatement.fetchMoreRows')
f(42,343,1,1,'oracle/jdbc/driver/OracleStatement.cleanTempLobsBeforeFetchMoreRows')
f(43,343,1,6,'java/util/ArrayList.isEmpty',0,1,0)
f(42,344,99,1,'oracle/jdbc/driver/T4CStatement.fetch')
f(43,344,99,1,'oracle/jdbc/driver/T4CStatement.doOall8')
f(44,344,99,1,'oracle/jdbc/driver/T4C8Oall.doOALL')
f(45,344,99,1,'oracle/jdbc/driver/T4CTTIfun.doRPC')
f(46,344,99,1,'oracle/jdbc/driver/T4CTTIfun.receive',0,1,0)
f(47,345,1,1,'oracle/jdbc/driver/T4C8Oall.readBVC')
f(48,345,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB2')
f(49,345,1,6,'oracle/jdbc/driver/T4CMAREngineNIO.buffer2Value',0,1,0)
f(47,346,97,1,'oracle/jdbc/driver/T4C8Oall.readRXD')
f(48,346,97,1,'oracle/jdbc/driver/T4CTTIrxd.unmarshal')
f(49,346,97,1,'oracle/jdbc/driver/T4CTTIrxd.unmarshal',0,2,0)
f(50,346,91,1,'oracle/jdbc/driver/T4CClobAccessor.unmarshalOneRow',0,1,0)
f(51,347,90,1,'oracle/jdbc/driver/T4CClobAccessor.unmarshalBytes',0,1,0)
f(52,348,5,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalCLR',0,1,0)
f(53,348,1,4,'SharedRuntime::ldiv(long, long)')
f(53,349,3,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalBuffer')
f(54,349,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalNBytes')
f(55,349,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.getNBytes')
f(56,349,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(57,349,3,1,'oracle/net/ns/NIONSDataChannel.readDataFromSocketChannel')
f(58,349,3,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(59,349,3,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(60,349,3,1,'oracle/net/ns/NIOPacket.readPacketFromSocketChannel')
f(61,349,1,1,'oracle/net/ns/NIOPacket.readHeader')
f(62,349,1,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(63,349,1,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(64,349,1,1,'sun/nio/ch/SocketChannelImpl.read')
f(65,349,1,1,'sun/nio/ch/IOUtil.read')
f(66,349,1,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(67,349,1,1,'sun/nio/ch/SocketDispatcher.read')
f(68,349,1,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(69,349,1,3,'read')
f(61,350,2,1,'oracle/net/ns/NIOPacket.readPayload')
f(62,350,2,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(63,350,2,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(64,350,2,1,'sun/nio/ch/SocketChannelImpl.read')
f(65,350,1,1,'sun/nio/ch/IOUtil.read')
f(66,350,1,1,'java/nio/HeapByteBuffer.put')
f(67,350,1,1,'java/nio/DirectByteBuffer.get')
f(68,350,1,1,'java/nio/Bits.copyToArray')
f(69,350,1,1,'sun/misc/Unsafe.copyMemory')
f(70,350,1,3,'acs_CopyRight')
f(65,351,1,6,'sun/nio/ch/SocketChannelImpl.ensureReadOpen',0,1,0)
f(53,352,1,6,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB1',0,1,0)
f(52,353,84,1,'oracle/jdbc/driver/T4CClobAccessor.unmarshalPrefetchData')
f(53,353,76,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalCLR')
f(54,353,72,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalBuffer',0,1,0)
f(55,354,71,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalNBytes',0,1,0)
f(56,355,70,1,'oracle/jdbc/driver/T4CMAREngineNIO.getNBytes')
f(57,355,9,6,'java/nio/HeapByteBuffer.get',0,9,0)
f(58,357,1,3,'jbyte_arraycopy')
f(58,358,6,3,'jbyte_disjoint_arraycopy')
f(57,364,61,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(58,364,61,1,'oracle/net/ns/NIONSDataChannel.readDataFromSocketChannel')
f(59,364,61,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(60,364,61,1,'oracle/net/ns/NIOPacket.readFromSocketChannel',0,1,0)
f(61,365,60,1,'oracle/net/ns/NIOPacket.readPacketFromSocketChannel')
f(62,365,3,1,'oracle/net/ns/NIOPacket.readHeader')
f(63,365,1,1,'oracle/net/ns/NIOHeader.readHeaderBuffer')
f(64,365,1,1,'oracle/net/ns/NIOHeader.readNSHeader')
f(65,365,1,1,'java/nio/HeapByteBuffer.getShort')
f(66,365,1,1,'java/nio/Bits.getShort')
f(67,365,1,1,'java/nio/Bits.getShortB')
f(68,365,1,6,'java/nio/HeapByteBuffer._get',0,1,0)
f(63,366,2,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(64,366,2,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(65,366,2,1,'sun/nio/ch/SocketChannelImpl.read')
f(66,366,2,1,'sun/nio/ch/IOUtil.read')
f(67,366,2,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(68,366,2,1,'sun/nio/ch/SocketDispatcher.read')
f(69,366,2,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(70,366,2,3,'read')
f(62,368,57,1,'oracle/net/ns/NIOPacket.readPayload')
f(63,368,57,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(64,368,57,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(65,368,4,1,'oracle/net/nt/TimeoutSocketChannel.cancelTimeout')
f(66,368,4,1,'oracle/net/nt/TimeoutInterruptHandler.cancelInterrupt')
f(67,368,3,1,'java/util/Timer.purge',0,1,0)
f(68,368,1,4,'ObjectMonitor::enter(Thread*)')
f(68,369,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(69,369,1,4,'ObjectSynchronizer::fast_exit(oopDesc*, BasicLock*, Thread*)')
f(70,369,1,4,'ObjectSynchronizer::inflate(Thread*, oopDesc*)')
f(68,370,1,3,'monitorenter_nofpu Runtime1 stub')
f(67,371,1,1,'java/util/concurrent/ConcurrentHashMap.remove')
f(68,371,1,6,'java/util/concurrent/ConcurrentHashMap.replaceNode',0,1,0)
f(65,372,3,1,'oracle/net/nt/TimeoutSocketChannel.scheduleInterrupt')
f(66,372,3,1,'oracle/net/nt/TimeoutInterruptHandler.scheduleInterrupt',0,1,0)
f(67,372,2,2,'java/util/Timer.schedule',1,0,0)
f(68,372,1,6,'java/util/Timer.sched',0,1,0)
f(68,373,1,4,'os::javaTimeMillis()')
f(69,373,1,3,'advance_directory.cold.1')
f(67,374,1,1,'java/util/concurrent/ConcurrentHashMap.put')
f(68,374,1,6,'java/util/concurrent/ConcurrentHashMap.putVal',0,1,0)
f(65,375,50,1,'sun/nio/ch/SocketChannelImpl.read')
f(66,375,1,1,'java/nio/channels/spi/AbstractInterruptibleChannel.begin')
f(67,375,1,1,'java/lang/Thread.isInterrupted')
f(68,375,1,1,'java/lang/Thread.isInterrupted')
f(69,375,1,3,'JVM_IsInterrupted')
f(70,375,1,4,'Thread::is_interrupted(Thread*, bool)')
f(71,375,1,4,'os::is_interrupted(Thread*, bool)')
f(66,376,48,1,'sun/nio/ch/IOUtil.read')
f(67,376,2,1,'java/nio/HeapByteBuffer.put')
f(68,376,2,1,'java/nio/DirectByteBuffer.get')
f(69,376,2,1,'java/nio/Bits.copyToArray')
f(70,376,2,1,'sun/misc/Unsafe.copyMemory')
f(71,376,2,3,'Unsafe_CopyMemory2')
f(72,376,1,4,'Copy::conjoint_memory_atomic(void*, void*, unsigned long)')
f(73,376,1,3,'_platform_memmove$VARIANT$Rosetta')
f(72,377,1,4,'HandleMarkCleaner::~HandleMarkCleaner()')
f(67,378,45,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(68,378,45,1,'sun/nio/ch/SocketDispatcher.read')
f(69,378,45,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(70,378,45,3,'read')
f(67,423,1,1,'sun/nio/ch/Util.getTemporaryDirectBuffer')
f(68,423,1,6,'java/lang/ThreadLocal.get',0,1,0)
f(66,424,1,6,'sun/nio/ch/SocketChannelImpl.ensureReadOpen',0,1,0)
f(54,425,4,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB1')
f(55,425,1,6,'java/nio/HeapByteBuffer.get',0,1,0)
f(55,426,3,6,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall',0,3,0)
f(56,428,1,2,'oracle/jdbc/driver/DatabaseError.createSqlException',1,0,0)
f(57,428,1,2,'oracle/jdbc/driver/DatabaseError.createSqlException',1,0,0)
f(53,429,1,1,'oracle/jdbc/driver/T4CMAREngine.unmarshalSB8')
f(54,429,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.buffer2Value')
f(55,429,1,6,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall',0,1,0)
f(53,430,2,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB1')
f(54,430,2,6,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall',0,2,0)
f(53,432,2,6,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB2',0,1,0)
f(54,433,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.buffer2Value')
f(55,433,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(56,433,1,1,'oracle/net/ns/NIONSDataChannel.readDataFromSocketChannel')
f(57,433,1,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(58,433,1,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(59,433,1,1,'oracle/net/ns/NIOPacket.readPacketFromSocketChannel')
f(60,433,1,1,'oracle/net/ns/NIOPacket.readPayload')
f(61,433,1,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(62,433,1,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(63,433,1,1,'sun/nio/ch/SocketChannelImpl.read')
f(64,433,1,1,'sun/nio/ch/IOUtil.read')
f(65,433,1,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(66,433,1,1,'sun/nio/ch/SocketDispatcher.read')
f(67,433,1,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(68,433,1,3,'read')
f(53,434,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalUB4')
f(54,434,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalSB4')
f(55,434,3,1,'oracle/jdbc/driver/T4CMAREngineNIO.buffer2Value')
f(56,434,1,6,'java/nio/HeapByteBuffer.get',0,1,0)
f(56,435,1,1,'java/nio/HeapByteBuffer.getShort')
f(57,435,1,1,'java/nio/Bits.getShort')
f(58,435,1,1,'java/nio/Bits.getShortB')
f(59,435,1,6,'java/nio/HeapByteBuffer._get',0,1,0)
f(56,436,1,6,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall',0,1,0)
f(50,437,2,1,'oracle/jdbc/driver/T4CTTIrxd.copyRowsAsNeeded')
f(51,437,2,6,'oracle/jdbc/driver/T4CVarcharAccessor.copyRow',0,2,0)
f(50,439,2,1,'oracle/jdbc/driver/T4CVarcharAccessor.unmarshalOneRow')
f(51,439,2,1,'oracle/jdbc/driver/T4CMarshaller$BasicMarshaller.unmarshalOneRow')
f(52,439,1,1,'oracle/jdbc/driver/T4CMarshaller$BasicMarshaller.unmarshalBytes')
f(53,439,1,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalCLR')
f(54,439,1,1,'oracle/jdbc/driver/DynamicByteArray.unmarshalBuffer')
f(55,439,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.unmarshalNBytes')
f(56,439,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.getNBytes')
f(57,439,1,1,'oracle/jdbc/driver/T4CMAREngineNIO.prepareForUnmarshall')
f(58,439,1,1,'oracle/net/ns/NIONSDataChannel.readDataFromSocketChannel')
f(59,439,1,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(60,439,1,1,'oracle/net/ns/NIOPacket.readFromSocketChannel')
f(61,439,1,1,'oracle/net/ns/NIOPacket.readPacketFromSocketChannel')
f(62,439,1,1,'oracle/net/ns/NIOPacket.readPayload')
f(63,439,1,1,'oracle/net/ns/NSProtocolNIO.doSocketRead')
f(64,439,1,1,'oracle/net/nt/TimeoutSocketChannel.read')
f(65,439,1,1,'sun/nio/ch/SocketChannelImpl.read')
f(66,439,1,1,'sun/nio/ch/IOUtil.read')
f(67,439,1,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(68,439,1,1,'sun/nio/ch/SocketDispatcher.read')
f(69,439,1,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(70,439,1,3,'read')
f(52,440,1,6,'oracle/jdbc/driver/T4CVarcharAccessor.unmarshalColumnMetadata',0,1,0)
f(50,441,2,3,'vtable stub')
f(36,443,1,2,'io/tapdata/base/ConnectorBase.insertRecordEvent',1,0,0)
f(37,443,1,2,'io/tapdata/entity/simplify/TapSimplify.insertRecordEvent',1,0,0)
f(38,443,1,2,'io/tapdata/entity/event/dml/TapInsertRecordEvent.init',1,0,0)
f(39,443,1,4,'os::javaTimeMillis()')
f(40,443,1,3,'gettimeofday')
f(41,443,1,3,'__commpage_gettimeofday_internal')
f(42,443,1,3,'mach_absolute_time')
f(36,444,367,1,'io/tapdata/connector/oracle/OracleConnector.processDataMap')
f(37,444,1,6,'java/util/LinkedHashMap$LinkedEntrySet.iterator',0,1,0)
f(37,445,366,1,'oracle/sql/CLOB.stringValue')
f(38,445,366,1,'oracle/jdbc/driver/OracleClob.stringValue',0,158,0)
f(39,602,96,1,'java/io/Reader.read',0,1,0)
f(40,603,95,1,'oracle/jdbc/driver/OracleClobReader.read')
f(41,603,95,1,'oracle/jdbc/driver/OracleClobReader.needChars')
f(42,603,3,1,'oracle/jdbc/driver/OracleClob.getChars')
f(43,603,1,1,'oracle/jdbc/driver/OracleClob.getDBAccess')
f(44,603,1,1,'oracle/jdbc/driver/PhysicalConnection.isClosed')
f(45,603,1,6,'oracle/jdbc/internal/Monitor.acquireCloseableLock',0,1,0)
f(46,603,1,3,'itable stub')
f(43,604,2,1,'oracle/jdbc/driver/T4CConnection.getChars')
f(44,604,2,6,'oracle/jdbc/driver/T4CConnection.copyPrefetchedClobChars',0,2,0)
f(45,604,2,3,'jshort_disjoint_arraycopy')
f(42,606,91,1,'oracle/jdbc/driver/PhysicalConnection.getCharBuffer')
f(43,606,91,1,'oracle/jdbc/driver/BufferCache.get',0,1,0)
f(44,607,90,1,'java/lang/reflect/Array.newInstance')
f(45,607,90,1,'java/lang/reflect/Array.newArray')
f(46,607,90,3,'JVM_NewArray')
f(47,607,89,4,'TypeArrayKlass::allocate_common(int, bool, Thread*)')
f(48,607,88,4,'CollectedHeap::array_allocate(KlassHandle, int, int, Thread*)')
f(48,695,1,4,'CollectedHeap::common_mem_allocate_noinit(KlassHandle, unsigned long, Thread*)')
f(47,696,1,4,'java_lang_Class::is_primitive(oopDesc*)')
f(42,697,1,1,'oracle/jdbc/internal/Monitor.acquireCloseableLock')
f(43,697,1,6,'oracle/jdbc/internal/Monitor.acquireLock',0,1,0)
f(44,697,1,3,'itable stub')
f(39,698,103,6,'java/io/StringWriter.<init>',0,103,0)
f(39,801,1,1,'java/io/StringWriter.write')
f(40,801,1,1,'java/lang/StringBuffer.append')
f(41,801,1,6,'java/lang/AbstractStringBuilder.append',0,1,0)
f(42,801,1,2,'java/lang/AbstractStringBuilder.ensureCapacityInternal',1,0,0)
f(43,801,1,3,'jshort_disjoint_arraycopy')
f(39,802,4,1,'java/lang/StringBuffer.substring')
f(40,802,4,1,'java/lang/StringBuffer.substring')
f(41,802,4,1,'java/lang/AbstractStringBuilder.substring')
f(42,802,4,1,'java/lang/String.<init>')
f(43,802,4,6,'java/util/Arrays.copyOfRange',0,4,0)
f(44,802,4,2,'java/lang/StringBuilder.append',4,0,0)
f(45,805,1,3,'jshort_disjoint_arraycopy')
f(39,806,1,1,'oracle/jdbc/driver/OracleClob.getBufferSize')
f(40,806,1,6,'oracle/sql/DatumWithConnection.getPhysicalConnection',0,1,0)
f(39,807,3,1,'oracle/jdbc/driver/OracleClob.getCharacterStream')
f(40,807,3,1,'oracle/jdbc/driver/OracleClob.getDBAccess')
f(41,807,2,1,'oracle/jdbc/driver/PhysicalConnection.isClosed')
f(42,807,2,6,'oracle/jdbc/internal/Monitor.acquireCloseableLock',0,1,0)
f(43,807,1,3,'itable stub')
f(43,808,1,1,'oracle/jdbc/internal/Monitor.acquireLock')
f(44,808,1,6,'oracle/jdbc/OracleConnectionWrapper.getMonitorLock',0,1,0)
f(41,809,1,6,'oracle/sql/DatumWithConnection.getPhysicalConnection',0,1,0)
f(39,810,1,2,'oracle/sql/DatumWithConnection.getConnectionDuringExceptionHandling',1,0,0)
f(40,810,1,2,'oracle/sql/DatumWithConnection.getConnectionDuringExceptionHandling',1,0,0)
f(36,811,3,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode$$Lambda$3030/236230606.accept')
f(37,811,3,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastSourcePdkDataNode.lambda$null$7')
f(38,811,2,1,'io/tapdata/aspect/utils/AspectUtils.accept')
f(39,811,2,1,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError')
f(40,811,2,1,'io/tapdata/aspect/utils/AspectUtils$$Lambda$3089/399383540.run')
f(41,811,2,1,'io/tapdata/aspect/utils/AspectUtils.lambda$accept$0')
f(42,811,2,1,'io/tapdata/aspect/BatchReadFuncAspect$$Lambda$3019/1957290875.accept')
f(43,811,2,1,'io/tapdata/aspect/BatchReadFuncAspect.lambda$readCompleteConsumer$1')
f(44,811,2,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3018/2122329294.accept')
f(45,811,2,1,'io/tapdata/observable/metric/ObservableAspectTask.lambda$handleBatchReadFunc$11')
f(46,811,1,1,'io/tapdata/observable/metric/handler/ObservableAspectTaskUtil.batchReadComplete')
f(47,811,1,1,'java/util/concurrent/CompletableFuture.thenRun')
f(48,811,1,1,'java/util/concurrent/CompletableFuture.uniRunStage')
f(49,811,1,1,'java/util/concurrent/CompletableFuture.uniRun')
f(50,811,1,1,'io/tapdata/observable/metric/handler/ObservableAspectTaskUtil$$Lambda$3094/260712272.run')
f(51,811,1,1,'io/tapdata/observable/metric/handler/ObservableAspectTaskUtil.lambda$batchReadComplete$5')
f(52,811,1,1,'java/util/HashMap.get')
f(53,811,1,6,'java/util/HashMap.getNode',0,1,0)
f(46,812,1,1,'io/tapdata/observable/metric/util/SyncGetMemorySizeHandler.getEventTypeRecorderSyncTapEvent')
f(47,812,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countTapEvent')
f(48,812,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countTapEvent')
f(49,812,1,6,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError',0,1,0)
f(50,812,1,3,'itable stub')
f(38,813,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.sampleMemoryToTapEvent')
f(39,813,1,1,'io/tapdata/observable/metric/handler/RandomSampleEventHandler.sampleMemoryTapEvent')
f(40,813,1,1,'io/tapdata/observable/metric/handler/RandomSampleEventHandler.sizeOfTapEvent')
f(41,813,1,1,'io/tapdata/observable/metric/handler/RandomSampleEventHandler.sizeOfDataMap')
f(42,813,1,1,'org/apache/lucene/util/RamUsageEstimator.sizeOfMap')
f(43,813,1,1,'org/apache/lucene/util/RamUsageEstimator.sizeOfMap')
f(44,813,1,6,'org/apache/lucene/util/RamUsageEstimator.sizeOfObject',0,1,0)
f(45,813,1,2,'org/apache/lucene/util/RamUsageEstimator.shallowSizeOf',1,0,0)
f(36,814,459,1,'io/tapdata/kit/DbKit.getRowFromResultSet')
f(37,814,456,1,'com/zaxxer/hikari/pool/HikariProxyResultSet.getObject')
f(38,814,456,1,'oracle/jdbc/driver/GeneratedScrollableResultSet.getObject')
f(39,814,451,1,'oracle/jdbc/driver/GeneratedStatement.getObject',0,2,0)
f(40,816,1,6,'oracle/jdbc/driver/OracleStatement.physicalRowIndex',0,1,0)
f(40,817,156,1,'oracle/jdbc/driver/T4CClobAccessor.getObject',0,2,0)
f(41,819,154,1,'oracle/jdbc/driver/ClobAccessor.getObject')
f(42,819,154,1,'oracle/jdbc/driver/ClobAccessor.getCLOB')
f(43,819,153,1,'oracle/jdbc/driver/ClobAccessor.getCLOB_')
f(44,819,147,1,'oracle/jdbc/driver/ClobAccessor.getPrefetchedCharData')
f(45,819,147,1,'oracle/jdbc/driver/DynamicByteArray.getChars',0,6,0)
f(46,823,2,3,'jshort_disjoint_arraycopy')
f(46,825,141,1,'oracle/jdbc/driver/DynamicByteArray.getString')
f(47,825,141,1,'oracle/jdbc/driver/DynamicByteArray.getStringFromAL16UTF16')
f(48,825,8,1,'java/lang/String.<init>')
f(49,825,8,6,'java/util/Arrays.copyOfRange',0,8,0)
f(50,825,8,2,'java/lang/StringBuilder.append',8,0,0)
f(51,827,6,3,'jshort_disjoint_arraycopy')
f(48,833,133,1,'oracle/jdbc/driver/DynamicByteArray.getCharsFromAL16UTF16',0,16,0)
f(49,849,116,6,'oracle/jdbc/driver/DynamicByteArray.next',0,116,0)
f(49,965,1,6,'oracle/jdbc/driver/DynamicByteArray.setGlobals',0,1,0)
f(50,965,1,4,'SharedRuntime::lrem(long, long)')
f(44,966,5,1,'oracle/sql/CLOB.<init>')
f(45,966,5,1,'oracle/sql/CLOB.<init>')
f(46,966,5,1,'oracle/jdbc/driver/OracleClob.<init>')
f(47,966,3,1,'oracle/jdbc/driver/OracleClob.isTemporary',0,1,0)
f(48,967,2,1,'oracle/jdbc/driver/OracleClob.getDBAccess')
f(49,967,2,1,'oracle/jdbc/driver/PhysicalConnection.isClosed')
f(50,967,1,1,'oracle/jdbc/internal/Monitor$CloseableLock.close')
f(51,967,1,6,'java/util/concurrent/locks/ReentrantLock.unlock',0,1,0)
f(50,968,1,1,'oracle/jdbc/internal/Monitor.acquireCloseableLock')
f(51,968,1,1,'oracle/jdbc/internal/Monitor.acquireLock')
f(52,968,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(53,968,1,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock')
f(54,968,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquire')
f(55,968,1,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.tryAcquire')
f(56,968,1,6,'java/util/concurrent/locks/ReentrantLock$Sync.nonfairTryAcquire',0,1,0)
f(47,969,1,1,'oracle/jdbc/driver/T4CConnection.createClobDBAccess')
f(48,969,1,1,'oracle/jdbc/internal/Monitor$CloseableLock.close')
f(49,969,1,6,'java/util/concurrent/locks/ReentrantLock.unlock',0,1,0)
f(47,970,1,1,'oracle/jdbc/driver/T4CConnection.incrementTempLobReferenceCount')
f(48,970,1,1,'oracle/jdbc/internal/Monitor.acquireCloseableLock')
f(49,970,1,1,'oracle/jdbc/internal/Monitor.acquireLock')
f(50,970,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(51,970,1,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock')
f(52,970,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquire')
f(53,970,1,1,'java/util/concurrent/locks/ReentrantLock$NonfairSync.tryAcquire')
f(54,970,1,6,'java/util/concurrent/locks/ReentrantLock$Sync.nonfairTryAcquire',0,1,0)
f(44,971,1,1,'oracle/sql/CLOB.isTemporary')
f(45,971,1,1,'oracle/jdbc/driver/OracleClob.isTemporary')
f(46,971,1,6,'oracle/jdbc/driver/OracleClob.getDBAccess',0,1,0)
f(43,972,1,1,'oracle/jdbc/driver/ClobAccessor.normalizeFormOfUse')
f(44,972,1,6,'oracle/sql/CLOB.getFormOfUseFromLocator',0,1,0)
f(40,973,292,1,'oracle/jdbc/driver/T4CVarcharAccessor.getObject',0,1,0)
f(41,974,291,1,'oracle/jdbc/driver/CharCommonAccessor.getObject')
f(42,974,291,1,'oracle/jdbc/driver/CharCommonAccessor.getString')
f(43,974,291,1,'oracle/jdbc/driver/DynamicByteArray.getString')
f(44,974,291,6,'oracle/jdbc/driver/DynamicByteArray.getStringFromAL32UTF8',0,262,0)
f(45,1236,28,1,'java/lang/String.<init>',0,1,0)
f(46,1237,27,6,'java/util/Arrays.copyOfRange',0,27,0)
f(47,1237,27,2,'java/lang/StringBuilder.append',27,0,0)
f(48,1257,7,3,'jshort_disjoint_arraycopy')
f(45,1264,1,6,'oracle/jdbc/driver/DynamicByteArray.next',0,1,0)
f(39,1265,5,1,'oracle/jdbc/internal/Monitor.acquireCloseableLock',0,1,0)
f(40,1265,1,3,'itable stub')
f(40,1266,4,1,'oracle/jdbc/internal/Monitor.acquireLock',0,1,0)
f(41,1266,1,3,'itable stub')
f(41,1267,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(42,1267,1,6,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock',0,1,0)
f(41,1268,2,6,'oracle/jdbc/OracleConnectionWrapper.getMonitorLock',0,2,0)
f(37,1270,2,6,'java/util/ArrayList$Itr.hasNext',0,2,0)
f(37,1272,1,1,'java/util/HashMap.put')
f(38,1272,1,6,'java/util/HashMap.putVal',0,1,0)
f(7,1273,9,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2831/548323019.run')
f(8,1273,9,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.queueConsume')
f(9,1273,9,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.drainAndRun')
f(10,1273,9,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2835/1970175363.run')
f(11,1273,9,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.processTargetEvents',0,2,0)
f(12,1275,7,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.fromTapValue')
f(13,1275,7,1,'io/tapdata/entity/codec/filter/TapCodecsFilterManagerSchemaEnforced.transformFromTapValueMap',0,1,0)
f(14,1276,1,1,'java/util/HashMap$KeyIterator.next')
f(15,1276,1,6,'java/util/HashMap$HashIterator.nextNode',0,1,0)
f(14,1277,4,1,'java/util/HashMap.containsKey')
f(15,1277,4,1,'java/util/HashMap.getNode')
f(16,1277,4,6,'java/lang/String.equals',0,4,0)
f(14,1281,1,1,'java/util/HashMap.put')
f(15,1281,1,1,'java/util/HashMap.putVal')
f(16,1281,1,6,'java/util/LinkedHashMap.afterNodeAccess',0,1,0)
f(7,1282,2233,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2832/863808055.run')
f(8,1282,2233,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.processQueueConsume')
f(9,1282,2233,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.drainAndRun')
f(10,1282,3,1,'com/google/common/collect/Queues.drain')
f(11,1282,3,1,'java/util/concurrent/LinkedBlockingQueue.drainTo',0,1,0)
f(12,1283,1,1,'java/util/ArrayList.add')
f(13,1283,1,1,'java/util/ArrayList.ensureCapacityInternal')
f(14,1283,1,1,'java/util/ArrayList.ensureExplicitCapacity')
f(15,1283,1,1,'java/util/ArrayList.grow')
f(16,1283,1,1,'java/util/Arrays.copyOf')
f(17,1283,1,6,'java/util/Arrays.copyOf',0,1,0)
f(18,1283,1,3,'oop_disjoint_arraycopy')
f(12,1284,1,1,'java/util/concurrent/LinkedBlockingQueue.signalNotFull')
f(13,1284,1,1,'java/util/concurrent/locks/ReentrantLock.unlock')
f(14,1284,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release')
f(15,1284,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.unparkSuccessor')
f(16,1284,1,1,'java/util/concurrent/locks/LockSupport.unpark')
f(17,1284,1,1,'sun/misc/Unsafe.unpark')
f(18,1284,1,3,'Unsafe_Unpark')
f(19,1284,1,4,'Parker::unpark()')
f(20,1284,1,3,'__psynch_cvsignal')
f(10,1285,2230,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2839/399876831.run')
f(11,1285,2230,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13')
f(12,1285,2230,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.dispatchTapdataEvents')
f(13,1285,2230,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$3082/909784505.accept')
f(14,1285,2230,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.lambda$null$12',0,1,0)
f(15,1286,2229,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.initialProcessEvents',0,1,0)
f(16,1287,2228,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEvents')
f(17,1287,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.flushOffsetByTapdataEventForNoConcurrent')
f(18,1287,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.flushSyncProgressMap')
f(19,1287,1,1,'java/util/concurrent/ConcurrentHashMap.computeIfAbsent')
f(20,1287,1,6,'java/lang/String.equals',0,1,0)
f(17,1288,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEvent')
f(18,1288,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEvent')
f(19,1288,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleTapdataEventDML')
f(20,1288,1,6,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.handleExactlyOnceWriteCacheIfNeed',0,1,0)
f(17,1289,2226,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.processTapEvents')
f(18,1289,2226,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.processEvents')
f(19,1289,1,6,'java/util/ArrayList.add',0,1,0)
f(20,1289,1,2,'java/util/ArrayList.ensureCapacityInternal',1,0,0)
f(21,1289,1,2,'java/util/ArrayList.ensureExplicitCapacity',1,0,0)
f(19,1290,2224,1,'java/util/HashMap.forEach')
f(20,1290,2224,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3116/476499187.accept')
f(21,1290,2224,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$processEvents$26')
f(22,1290,2224,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.writeRecord',0,1,0)
f(23,1291,2221,1,'io/tapdata/flow/engine/V2/node/hazelcast/HazelcastBaseNode.executeDataFuncAspect')
f(24,1291,2221,1,'io/tapdata/aspect/utils/AspectUtils.executeDataFuncAspect')
f(25,1291,2220,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3121/448406304.accept')
f(26,1291,2220,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$writeRecord$59')
f(27,1291,2220,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invoke')
f(28,1291,2220,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethod')
f(29,1291,1,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.release')
f(30,1291,1,1,'io/tapdata/pdk/core/entity/params/PDKMethodInvoker.cancelRetry')
f(31,1291,1,1,'java/lang/Object.notifyAll')
f(29,1292,2219,1,'io/tapdata/pdk/core/utils/RetryUtils.autoRetry')
f(30,1292,2219,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3028/980981694.run',0,1,0)
f(31,1293,2218,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$invokePDKMethod$11')
f(32,1293,2218,1,'io/tapdata/pdk/core/api/Node.applyClassLoaderContext')
f(33,1293,2218,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor$$Lambda$3029/333708744.run')
f(34,1293,2218,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.lambda$null$10')
f(35,1293,2218,1,'io/tapdata/pdk/core/monitor/PDKInvocationMonitor.invokePDKMethodPrivate')
f(36,1293,2218,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3134/1430190202.run')
f(37,1293,2218,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$null$58')
f(38,1293,2,1,'io/tapdata/aspect/utils/AspectUtils.executeAspect')
f(39,1293,2,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl.executeAspect')
f(40,1293,2,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl.getInterceptResult')
f(41,1293,2,1,'io/tapdata/aspect/task/impl/TaskSessionClassHolder$$Lambda$1782/1767304024.intercept')
f(42,1293,2,1,'io/tapdata/aspect/task/impl/TaskSessionClassHolder.interceptNodeAspect')
f(43,1293,2,1,'io/tapdata/aspect/task/AbstractAspectTask.onInterceptAspect')
f(44,1293,2,6,'io/tapdata/entity/simplify/pretty/TypeHandlers.handle',0,1,0)
f(45,1294,1,6,'java/util/ArrayList$Itr.hasNext',0,1,0)
f(38,1295,2216,1,'io/tapdata/flow/engine/V2/policy/PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl')
f(39,1295,2216,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3138/1548007300.apply')
f(40,1295,2216,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$null$57')
f(41,1295,2216,1,'io/tapdata/connector/kafka/KafkaConnector$$Lambda$2065/145413703.writeRecord')
f(42,1295,2216,1,'io/tapdata/connector/kafka/KafkaConnector.writeRecord')
f(43,1295,2216,1,'io/tapdata/connector/kafka/KafkaService.produce',0,6,0)
f(44,1301,8,1,'io/tapdata/connector/kafka/KafkaService.getKafkaMessageKey')
f(45,1301,1,6,'io/tapdata/entity/schema/TapTable.primaryKeys',0,1,0)
f(45,1302,2,1,'io/tapdata/pdk/core/api/impl/JsonParserImpl.toJsonBytes')
f(46,1302,2,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(47,1302,2,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(48,1302,2,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(49,1302,2,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(50,1302,2,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(51,1302,2,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(52,1302,2,1,'com/alibaba/fastjson/serializer/JSONSerializer.write')
f(53,1302,2,1,'com/alibaba/fastjson/serializer/JSONSerializer.getObjectWriter')
f(54,1302,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(55,1302,2,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(56,1302,2,6,'com/alibaba/fastjson/serializer/SerializeConfig.get',0,1,0)
f(57,1302,1,2,'com/alibaba/fastjson/JSON.getMixInAnnotations',1,0,0)
f(57,1303,1,1,'com/alibaba/fastjson/util/IdentityHashMap.get')
f(58,1303,1,1,'java/lang/System.identityHashCode')
f(59,1303,1,3,'JVM_IHashCode')
f(60,1303,1,4,'ObjectSynchronizer::FastHashCode(Thread*, oopDesc*)')
f(61,1303,1,3,'ReadStableMark(oopDesc*)')
f(45,1304,1,1,'java/util/Collection.stream')
f(46,1304,1,6,'java/util/ArrayList.spliterator',0,1,0)
f(45,1305,4,1,'java/util/stream/ReferencePipeline.collect')
f(46,1305,4,1,'java/util/stream/AbstractPipeline.evaluate')
f(47,1305,4,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(48,1305,4,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(49,1305,4,6,'java/util/stream/AbstractPipeline.copyInto',0,2,0)
f(50,1305,2,3,'itable stub')
f(50,1307,1,1,'java/util/ArrayList$ArrayListSpliterator.forEachRemaining')
f(51,1307,1,1,'java/util/stream/ReferencePipeline$3$1.accept')
f(52,1307,1,1,'java/util/stream/ReduceOps$3ReducingSink.accept')
f(53,1307,1,1,'java/util/stream/Collectors$$Lambda$26/2121995675.accept')
f(54,1307,1,1,'java/util/StringJoiner.add')
f(55,1307,1,1,'java/lang/StringBuilder.append')
f(56,1307,1,1,'java/lang/AbstractStringBuilder.append')
f(57,1307,1,1,'java/lang/StringBuilder.append')
f(58,1307,1,1,'java/lang/StringBuilder.append')
f(59,1307,1,6,'java/lang/AbstractStringBuilder.append',0,1,0)
f(60,1307,1,2,'java/lang/AbstractStringBuilder.ensureCapacityInternal',1,0,0)
f(50,1308,1,1,'java/util/stream/Sink$ChainedReference.begin')
f(51,1308,1,1,'java/util/stream/ReduceOps$3ReducingSink.begin')
f(52,1308,1,1,'java/util/stream/Collectors$$Lambda$25/1033348658.get')
f(53,1308,1,1,'java/util/stream/Collectors.lambda$joining$6')
f(54,1308,1,1,'java/util/StringJoiner.<init>')
f(55,1308,1,1,'java/lang/StringBuilder.append')
f(56,1308,1,6,'java/lang/AbstractStringBuilder.append',0,1,0)
f(57,1308,1,2,'java/lang/AbstractStringBuilder.ensureCapacityInternal',1,0,0)
f(44,1309,2,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3135/1197100591.accept')
f(45,1309,2,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$null$53')
f(46,1309,2,1,'io/tapdata/aspect/utils/AspectUtils.accept')
f(47,1309,2,1,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError')
f(48,1309,2,1,'io/tapdata/aspect/utils/AspectUtils$$Lambda$3044/1800367162.run')
f(49,1309,2,1,'io/tapdata/aspect/utils/AspectUtils.lambda$accept$1')
f(50,1309,2,1,'io/tapdata/aspect/WriteRecordFuncAspect$$Lambda$3132/704833469.accept')
f(51,1309,2,1,'io/tapdata/aspect/WriteRecordFuncAspect.lambda$consumer$0')
f(52,1309,2,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3131/1979614046.accept')
f(53,1309,2,1,'io/tapdata/observable/metric/ObservableAspectTask.lambda$handleWriteRecordFunc$29')
f(54,1309,2,1,'io/tapdata/observable/metric/util/TapCompletableFutureEx.runAsync')
f(55,1309,2,1,'java/util/concurrent/CompletableFuture.runAsync')
f(56,1309,2,1,'java/util/concurrent/CompletableFuture.asyncRunStage')
f(57,1309,2,1,'java/util/concurrent/CompletableFuture$AsyncRun.<init>')
f(58,1309,2,1,'com/intellij/rt/debugger/agent/CaptureStorage.capture')
f(59,1309,1,1,'java/lang/ThreadLocal.get')
f(60,1309,1,1,'java/lang/ThreadLocal$ThreadLocalMap.access$000')
f(61,1309,1,6,'java/lang/ThreadLocal$ThreadLocalMap.getEntry',0,1,0)
f(59,1310,1,1,'java/lang/Throwable.<init>')
f(60,1310,1,1,'java/lang/Throwable.fillInStackTrace')
f(61,1310,1,1,'java/lang/Throwable.fillInStackTrace')
f(62,1310,1,3,'Java_java_lang_Throwable_fillInStackTrace')
f(63,1310,1,4,'java_lang_Throwable::fill_in_stack_trace(Handle, methodHandle)')
f(44,1311,1158,1,'io/tapdata/pdk/core/api/impl/JsonParserImpl.toJsonBytes')
f(45,1311,1158,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(46,1311,1158,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(47,1311,1158,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(48,1311,1158,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(49,1311,1158,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(50,1311,1158,1,'com/alibaba/fastjson/JSON.toJSONBytes')
f(51,1311,881,1,'com/alibaba/fastjson/serializer/JSONSerializer.write')
f(52,1311,881,1,'com/alibaba/fastjson/serializer/MapSerializer.write')
f(53,1311,881,1,'com/alibaba/fastjson/serializer/MapSerializer.write',0,6,0)
f(54,1314,3,1,'com/alibaba/fastjson/serializer/DateCodec.write',0,1,0)
f(55,1315,2,6,'com/alibaba/fastjson/serializer/SerializeWriter.writeLong',0,1,0)
f(56,1316,1,6,'com/alibaba/fastjson/util/IOUtils.stringSize',0,1,0)
f(54,1317,1,1,'com/alibaba/fastjson/serializer/JSONSerializer.getObjectWriter')
f(55,1317,1,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(56,1317,1,1,'com/alibaba/fastjson/serializer/SerializeConfig.getObjectWriter')
f(57,1317,1,1,'com/alibaba/fastjson/serializer/SerializeConfig.get')
f(58,1317,1,6,'com/alibaba/fastjson/util/IdentityHashMap.get',0,1,0)
f(54,1318,16,1,'com/alibaba/fastjson/serializer/SerializeWriter.writeFieldName')
f(55,1318,16,1,'com/alibaba/fastjson/serializer/SerializeWriter.writeStringWithDoubleQuote',0,5,0)
f(56,1323,11,6,'java/lang/String.getChars',0,11,0)
f(57,1323,11,3,'jshort_disjoint_arraycopy')
f(54,1334,854,1,'com/alibaba/fastjson/serializer/StringCodec.write')
f(55,1334,854,1,'com/alibaba/fastjson/serializer/StringCodec.write')
f(56,1334,854,1,'com/alibaba/fastjson/serializer/SerializeWriter.writeString')
f(57,1334,854,6,'com/alibaba/fastjson/serializer/SerializeWriter.writeStringWithDoubleQuote',0,820,0)
f(58,2154,34,6,'java/lang/String.getChars',0,34,0)
f(59,2154,34,3,'jshort_disjoint_arraycopy')
f(54,2188,3,3,'itable stub')
f(54,2191,1,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(55,2191,1,1,'java/util/LinkedHashMap$LinkedEntryIterator.next')
f(56,2191,1,6,'java/util/LinkedHashMap$LinkedHashIterator.nextNode',0,1,0)
f(51,2192,277,1,'com/alibaba/fastjson/serializer/SerializeWriter.toBytes')
f(52,2192,277,1,'com/alibaba/fastjson/serializer/SerializeWriter.encodeToUTF8Bytes',0,45,0)
f(53,2219,232,6,'com/alibaba/fastjson/util/IOUtils.encodeUTF8',0,232,0)
f(53,2451,18,2,'java/lang/ThreadLocal.set',18,0,0)
f(54,2451,18,2,'java/lang/ThreadLocal$ThreadLocalMap.access$100',18,0,0)
f(55,2467,2,3,'jbyte_disjoint_arraycopy')
f(44,2469,3,1,'java/lang/String.getBytes')
f(45,2469,3,1,'java/lang/StringCoding.encode')
f(46,2469,3,1,'java/lang/StringCoding.encode')
f(47,2469,3,1,'java/lang/StringCoding$StringEncoder.encode',0,1,0)
f(48,2470,2,6,'sun/nio/cs/UTF_8$Encoder.encode',0,2,0)
f(44,2472,2,1,'java/util/concurrent/CountDownLatch.await')
f(45,2472,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.tryAcquireSharedNanos')
f(46,2472,2,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.doAcquireSharedNanos')
f(47,2472,2,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(48,2472,2,1,'sun/misc/Unsafe.park')
f(49,2472,2,3,'Unsafe_Park')
f(50,2472,2,4,'Parker::park(bool, long)')
f(51,2472,2,3,'__psynch_cvwait')
f(44,2474,1037,1,'org/apache/kafka/clients/producer/KafkaProducer.send')
f(45,2474,1037,1,'org/apache/kafka/clients/producer/KafkaProducer.doSend',0,1,0)
f(46,2474,3,1,'org/apache/kafka/clients/producer/KafkaProducer.partition')
f(47,2474,3,1,'org/apache/kafka/clients/producer/internals/DefaultPartitioner.partition',0,1,0)
f(48,2475,2,1,'org/apache/kafka/clients/producer/internals/DefaultPartitioner.partition')
f(49,2475,2,6,'org/apache/kafka/common/utils/Utils.murmur2',0,2,0)
f(46,2477,2,1,'org/apache/kafka/clients/producer/KafkaProducer.waitOnMetadata')
f(47,2477,2,1,'org/apache/kafka/common/Cluster.partitionCountForTopic')
f(48,2477,2,6,'java/util/Collections$UnmodifiableMap.get',0,2,0)
f(49,2477,2,3,'itable stub')
f(46,2479,1,1,'org/apache/kafka/clients/producer/internals/DefaultPartitioner.onNewBatch')
f(47,2479,1,1,'org/apache/kafka/clients/producer/internals/StickyPartitionCache.nextPartition')
f(48,2479,1,1,'org/apache/kafka/common/Cluster.partitionsForTopic')
f(49,2479,1,1,'java/util/Collections$UnmodifiableMap.getOrDefault')
f(50,2479,1,1,'java/util/HashMap.getOrDefault')
f(51,2479,1,1,'java/util/HashMap.getNode')
f(52,2479,1,6,'java/lang/String.equals',0,1,0)
f(46,2480,1017,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.append',0,2,0)
f(47,2480,3,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(48,2480,3,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(49,2481,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(49,2482,1,3,'update_heuristics(oopDesc*, bool)')
f(47,2483,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(48,2483,1,4,'JfrBackend::is_event_enabled(TraceEventId)')
f(47,2484,1,3,'monitorenter_nofpu Runtime1 stub')
f(47,2485,28,1,'org/apache/kafka/clients/producer/internals/BufferPool.allocate')
f(48,2485,1,1,'java/util/concurrent/locks/ReentrantLock.lock')
f(49,2485,1,6,'java/util/concurrent/locks/ReentrantLock$NonfairSync.lock',0,1,0)
f(50,2485,1,2,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquire',1,0,0)
f(51,2485,1,2,'java/util/concurrent/locks/ReentrantLock$NonfairSync.tryAcquire',1,0,0)
f(48,2486,27,6,'org/apache/kafka/clients/producer/internals/BufferPool.safeAllocateByteBuffer',0,27,0)
f(49,2486,27,2,'org/apache/kafka/clients/producer/internals/BufferPool.allocateByteBuffer',27,0,0)
f(50,2486,27,2,'java/nio/ByteBuffer.allocate',27,0,0)
f(51,2486,27,2,'java/nio/HeapByteBuffer.<init>',27,0,0)
f(47,2513,2,2,'org/apache/kafka/clients/producer/internals/IncompleteBatches.add',1,0,0)
f(48,2514,1,1,'java/util/HashSet.add')
f(49,2514,1,1,'java/util/HashMap.put')
f(50,2514,1,6,'java/util/HashMap.putVal',0,1,0)
f(47,2515,761,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.tryAppend',0,1,0)
f(48,2516,760,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.append')
f(49,2516,759,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.append')
f(50,2516,759,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.appendWithOffset')
f(51,2516,759,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.appendWithOffset')
f(52,2516,759,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.appendDefaultRecord')
f(53,2516,759,1,'org/apache/kafka/common/record/DefaultRecord.writeTo',0,3,0)
f(54,2517,1,1,'java/io/DataOutputStream.write')
f(55,2517,1,6,'java/io/BufferedOutputStream.write',0,1,0)
f(56,2517,1,2,'java/io/BufferedOutputStream.flushBuffer',1,0,0)
f(54,2518,2,2,'org/apache/kafka/common/utils/ByteUtils.sizeOfVarint',2,0,0)
f(55,2518,2,2,'org/apache/kafka/common/utils/ByteUtils.sizeOfUnsignedVarint',2,0,0)
f(54,2520,1,1,'org/apache/kafka/common/utils/ByteUtils.writeVarint')
f(55,2520,1,1,'org/apache/kafka/common/utils/ByteUtils.writeUnsignedVarint')
f(56,2520,1,1,'java/io/DataOutputStream.writeByte')
f(57,2520,1,6,'java/io/BufferedOutputStream.write',0,1,0)
f(58,2520,1,2,'java/io/BufferedOutputStream.flushBuffer',1,0,0)
f(54,2521,754,1,'org/apache/kafka/common/utils/Utils.writeTo')
f(55,2521,754,1,'java/io/DataOutputStream.write')
f(56,2521,754,1,'java/io/BufferedOutputStream.write')
f(57,2521,2,1,'java/io/BufferedOutputStream.flushBuffer')
f(58,2521,2,1,'java/util/zip/GZIPOutputStream.write')
f(59,2521,2,1,'java/util/zip/DeflaterOutputStream.write')
f(60,2521,2,1,'java/util/zip/DeflaterOutputStream.deflate')
f(61,2521,2,1,'java/util/zip/Deflater.deflate')
f(62,2521,2,1,'java/util/zip/Deflater.deflate')
f(63,2521,2,1,'java/util/zip/Deflater.deflateBytes')
f(64,2521,2,3,'Java_java_util_zip_Deflater_deflateBytes')
f(65,2521,2,3,'jni_GetObjectField')
f(66,2522,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(57,2523,752,1,'java/util/zip/GZIPOutputStream.write')
f(58,2523,7,6,'java/util/zip/CRC32.update',0,7,0)
f(59,2523,7,3,'updateBytesCRC32')
f(58,2530,745,1,'java/util/zip/DeflaterOutputStream.write')
f(59,2530,745,1,'java/util/zip/DeflaterOutputStream.deflate')
f(60,2530,745,1,'java/util/zip/Deflater.deflate')
f(61,2530,745,1,'java/util/zip/Deflater.deflate',0,1,0)
f(62,2531,744,1,'java/util/zip/Deflater.deflateBytes')
f(63,2531,744,3,'Java_java_util_zip_Deflater_deflateBytes')
f(64,2531,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(64,2532,743,3,'deflate')
f(65,2532,743,3,'deflateCopy')
f(66,3082,1,3,'_platform_bzero$VARIANT$Rosetta')
f(66,3083,12,3,'crc32_combine')
f(67,3083,11,3,'deflateCopy')
f(68,3083,11,3,'_platform_memmove$VARIANT$Rosetta')
f(67,3094,1,3,'gzclose_w')
f(66,3095,180,3,'deflateCopy')
f(49,3275,1,1,'org/apache/kafka/common/utils/Utils.wrapNullable')
f(50,3275,1,1,'java/nio/ByteBuffer.wrap')
f(51,3275,1,1,'java/nio/ByteBuffer.wrap')
f(52,3275,1,1,'java/nio/HeapByteBuffer.<init>')
f(53,3275,1,6,'java/nio/ByteBuffer.<init>',0,1,0)
f(47,3276,2,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.getOrCreateDeque')
f(48,3276,2,6,'org/apache/kafka/common/utils/CopyOnWriteMap.get',0,1,0)
f(49,3277,1,1,'java/util/Collections$UnmodifiableMap.get')
f(50,3277,1,6,'java/util/HashMap.get',0,1,0)
f(47,3278,56,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.recordsBuilder')
f(48,3278,56,1,'org/apache/kafka/common/record/MemoryRecords.builder')
f(49,3278,56,1,'org/apache/kafka/common/record/MemoryRecords.builder')
f(50,3278,56,1,'org/apache/kafka/common/record/MemoryRecords.builder',0,1,0)
f(51,3279,55,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.<init>')
f(52,3279,55,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.<init>')
f(53,3279,55,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.<init>',0,1,0)
f(54,3280,54,1,'org/apache/kafka/common/record/CompressionType$2.wrapForOutput',0,5,0)
f(55,3285,49,1,'java/util/zip/GZIPOutputStream.<init>')
f(56,3285,49,1,'java/util/zip/GZIPOutputStream.<init>')
f(57,3285,41,1,'java/util/zip/Deflater.<init>',0,1,0)
f(58,3285,4,4,'SharedRuntime::register_finalizer(JavaThread*, oopDesc*)')
f(59,3286,3,4,'InstanceKlass::register_finalizer(instanceOopDesc*, Thread*)')
f(60,3286,1,4,'GrowableArray<Metadata*>::append(Metadata* const&)')
f(60,3287,1,4,'GrowableArray<Metadata*>::remove_at(int)')
f(60,3288,1,4,'JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*)')
f(58,3289,37,1,'java/util/zip/Deflater.init')
f(59,3289,37,3,'Java_java_util_zip_Deflater_init')
f(60,3289,4,3,'_platform_memset$VARIANT$Rosetta')
f(60,3293,32,3,'deflateInit2_')
f(61,3293,31,3,'_platform_memset$VARIANT$Rosetta')
f(61,3324,1,3,'zError')
f(62,3324,1,3,'szone_malloc_should_clear')
f(63,3324,1,3,'small_malloc_should_clear')
f(64,3324,1,3,'small_malloc_from_free_list')
f(65,3324,1,3,'small_free_list_remove_ptr_no_clear')
f(60,3325,1,3,'deflateReset')
f(61,3325,1,3,'deflateResetKeep')
f(62,3325,1,3,'inflateCodesUsed')
f(57,3326,8,6,'java/util/zip/DeflaterOutputStream.<init>',0,8,0)
f(47,3334,162,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.tryAppend')
f(48,3334,159,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.closeForRecordAppends')
f(49,3334,159,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.closeForRecordAppends')
f(50,3334,159,1,'java/io/FilterOutputStream.close')
f(51,3334,3,1,'java/io/DataOutputStream.flush')
f(52,3334,3,1,'java/io/BufferedOutputStream.flush',0,1,0)
f(53,3335,1,1,'java/io/BufferedOutputStream.flushBuffer')
f(54,3335,1,1,'java/util/zip/GZIPOutputStream.write')
f(55,3335,1,6,'java/util/zip/CRC32.update',0,1,0)
f(56,3335,1,3,'updateBytesCRC32')
f(53,3336,1,6,'java/util/zip/DeflaterOutputStream.flush',0,1,0)
f(54,3336,1,3,'vtable stub')
f(51,3337,156,1,'java/io/FilterOutputStream.close')
f(52,3337,156,1,'java/util/zip/DeflaterOutputStream.close')
f(53,3337,2,1,'java/util/zip/Deflater.end')
f(54,3337,2,1,'java/util/zip/Deflater.end')
f(55,3337,2,3,'Java_java_util_zip_Deflater_end')
f(56,3337,2,3,'deflateEnd')
f(57,3337,2,3,'free_medium')
f(58,3338,1,3,'madvise')
f(53,3339,154,1,'java/util/zip/GZIPOutputStream.finish')
f(54,3339,153,1,'java/util/zip/Deflater.deflate')
f(55,3339,153,1,'java/util/zip/Deflater.deflate',0,1,0)
f(56,3340,152,1,'java/util/zip/Deflater.deflateBytes')
f(57,3341,151,3,'Java_java_util_zip_Deflater_deflateBytes')
f(58,3341,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(58,3342,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(58,3343,148,3,'deflate')
f(59,3343,148,3,'deflateCopy')
f(60,3343,1,3,'deflateCopy')
f(60,3344,1,3,'deflateTune')
f(61,3344,1,3,'_platform_memmove$VARIANT$Rosetta')
f(60,3345,146,3,'inflateCodesUsed')
f(61,3345,146,3,'inflateCodesUsed')
f(62,3450,41,3,'inflateCodesUsed')
f(58,3491,1,3,'jni_GetPrimitiveArrayCritical')
f(54,3492,1,1,'org/apache/kafka/common/utils/ByteBufferOutputStream.write')
f(55,3492,1,6,'java/nio/HeapByteBuffer.put',0,1,0)
f(56,3492,1,3,'jbyte_disjoint_arraycopy')
f(48,3493,3,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.tryAppend')
f(49,3493,3,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.hasRoomFor')
f(50,3493,2,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.hasRoomFor')
f(51,3493,1,1,'org/apache/kafka/common/record/DefaultRecord.sizeInBytes')
f(52,3493,1,1,'org/apache/kafka/common/record/DefaultRecord.sizeOfBodyInBytes')
f(53,3493,1,1,'org/apache/kafka/common/record/DefaultRecord.sizeOfBodyInBytes')
f(54,3493,1,1,'org/apache/kafka/common/record/DefaultRecord.sizeOf')
f(55,3493,1,6,'org/apache/kafka/common/utils/Utils.utf8Length',0,1,0)
f(51,3494,1,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.isFull')
f(52,3494,1,6,'org/apache/kafka/common/record/MemoryRecordsBuilder.estimatedBytesWritten',0,1,0)
f(50,3495,1,1,'org/apache/kafka/common/utils/Utils.wrapNullable')
f(51,3495,1,1,'java/nio/ByteBuffer.wrap')
f(52,3495,1,1,'java/nio/ByteBuffer.wrap')
f(53,3495,1,1,'java/nio/HeapByteBuffer.<init>')
f(54,3495,1,1,'java/nio/ByteBuffer.<init>')
f(55,3495,1,6,'java/nio/Buffer.<init>',0,1,0)
f(47,3496,1,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,1,0)
f(48,3496,1,4,'os::javaTimeMillis()')
f(49,3496,1,3,'gettimeofday')
f(50,3496,1,3,'__commpage_gettimeofday_internal')
f(51,3496,1,3,'mach_absolute_time')
f(46,3497,9,1,'org/apache/kafka/clients/producer/internals/Sender.wakeup')
f(47,3497,9,1,'org/apache/kafka/clients/NetworkClient.wakeup')
f(48,3497,9,1,'org/apache/kafka/common/network/Selector.wakeup')
f(49,3497,9,1,'sun/nio/ch/KQueueSelectorImpl.wakeup')
f(50,3497,9,1,'sun/nio/ch/KQueueArrayWrapper.interrupt')
f(51,3497,9,1,'sun/nio/ch/KQueueArrayWrapper.interrupt')
f(52,3497,9,3,'write')
f(46,3506,2,1,'org/apache/kafka/common/header/internals/RecordHeaders.toArray')
f(47,3506,2,1,'java/util/ArrayList.toArray')
f(48,3506,2,1,'java/util/Arrays.copyOf')
f(49,3506,1,1,'java/lang/Class.getComponentType')
f(50,3506,1,3,'JVM_GetComponentType')
f(51,3506,1,4,'java_lang_Class::as_Klass(oopDesc*)')
f(49,3507,1,1,'java/lang/reflect/Array.newInstance')
f(50,3507,1,1,'java/lang/reflect/Array.newArray')
f(51,3507,1,3,'JVM_NewArray')
f(52,3507,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(46,3508,1,2,'org/apache/kafka/common/record/AbstractRecords.estimateSizeInBytesUpperBound',1,0,0)
f(47,3508,1,2,'org/apache/kafka/common/utils/Utils.wrapNullable',1,0,0)
f(48,3508,1,2,'java/nio/ByteBuffer.wrap',1,0,0)
f(49,3508,1,2,'java/nio/ByteBuffer.wrap',1,0,0)
f(50,3508,1,2,'java/nio/HeapByteBuffer.<init>',1,0,0)
f(46,3509,2,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,2,0)
f(47,3509,2,4,'os::javaTimeMillis()')
f(48,3509,2,3,'gettimeofday')
f(49,3509,2,3,'__commpage_gettimeofday_internal')
f(50,3509,2,3,'mach_absolute_time')
f(25,3511,1,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl.executeAspect')
f(26,3511,1,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl.executeAspectObservers')
f(27,3511,1,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl.executeObservers')
f(28,3511,1,1,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError')
f(29,3511,1,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl$$Lambda$331/1649706918.run')
f(30,3511,1,1,'io/tapdata/pdk/core/aspect/AspectManagerImpl.lambda$executeObservers$1')
f(31,3511,1,1,'io/tapdata/aspect/task/impl/TaskSessionClassHolder$$Lambda$1781/1050768030.observe')
f(32,3511,1,1,'io/tapdata/aspect/task/impl/TaskSessionClassHolder.observeNodeAspect')
f(33,3511,1,1,'io/tapdata/observable/metric/ObservableAspectTask.onObserveAspect')
f(34,3511,1,1,'io/tapdata/entity/simplify/pretty/ClassHandlers.handle')
f(35,3511,1,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$2340/828301568.apply')
f(36,3511,1,1,'io/tapdata/observable/metric/ObservableAspectTask.handleWriteRecordFunc')
f(37,3511,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countTapEvent')
f(38,3511,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.countTapEvent')
f(39,3511,1,1,'io/tapdata/pdk/core/utils/CommonUtils.ignoreAnyError')
f(40,3511,1,1,'io/tapdata/observable/metric/handler/HandlerUtil$$Lambda$3090/685936922.run')
f(41,3511,1,1,'io/tapdata/observable/metric/handler/HandlerUtil.lambda$countTapEvent$1')
f(42,3511,1,1,'io/tapdata/observable/metric/handler/HandlerUtil$EventTypeRecorder.calculateMaxReplicateLag')
f(43,3511,1,1,'java/util/Collection.stream')
f(44,3511,1,1,'java/util/stream/StreamSupport.stream')
f(45,3511,1,6,'java/util/stream/StreamOpFlag.fromCharacteristics',0,1,0)
f(46,3511,1,3,'itable stub')
f(23,3512,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastPdkBaseNode.createPdkMethodInvoker')
f(24,3512,1,1,'io/tapdata/flow/engine/V2/task/retry/task/TaskRetryService.getMethodRetryDurationMinutes')
f(25,3512,1,6,'io/tapdata/flow/engine/V2/task/retry/task/TaskRetryService.getMethodRetryDurationMs',0,1,0)
f(26,3512,1,2,'java/lang/Math.min',1,0,0)
f(23,3513,1,1,'java/util/ArrayList.forEach')
f(24,3513,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode$$Lambda$3117/1246754272.accept')
f(25,3513,1,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkDataNode.lambda$writeRecord$50')
f(26,3513,1,6,'java/util/ArrayList.add',0,1,0)
f(19,3514,1,1,'java/util/stream/ReferencePipeline.findFirst')
f(20,3514,1,1,'java/util/stream/AbstractPipeline.evaluate')
f(21,3514,1,1,'java/util/stream/FindOps$FindOp.evaluateSequential')
f(22,3514,1,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(23,3514,1,1,'java/util/stream/AbstractPipeline.copyInto')
f(24,3514,1,1,'java/util/stream/AbstractPipeline.copyIntoWithCancel')
f(25,3514,1,1,'java/util/stream/ReferencePipeline.forEachWithCancel')
f(26,3514,1,6,'java/util/stream/Sink$ChainedReference.cancellationRequested',0,1,0)
f(7,3515,1,1,'io/tapdata/flow/engine/V2/schedule/TapdataTaskScheduler$$Lambda$1713/1924480807.run')
f(8,3515,1,1,'io/tapdata/flow/engine/V2/schedule/TapdataTaskScheduler.lambda$init$2')
f(9,3515,1,6,'java/util/concurrent/LinkedBlockingQueue.poll',0,1,0)
f(4,3516,58,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.run')
f(5,3516,13,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201')
f(6,3516,13,1,'java/util/concurrent/FutureTask.run')
f(7,3516,13,1,'java/util/concurrent/FutureTask.run$$$capture')
f(8,3516,13,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(9,3516,1,1,'com/taobao/arthas/core/shell/system/impl/ProcessImpl$CommandProcessTask.run')
f(10,3516,1,1,'com/taobao/arthas/core/shell/command/impl/AnnotatedCommandImpl$ProcessHandler.handle')
f(11,3516,1,1,'com/taobao/arthas/core/shell/command/impl/AnnotatedCommandImpl$ProcessHandler.handle')
f(12,3516,1,1,'com/taobao/arthas/core/shell/command/impl/AnnotatedCommandImpl.access$100')
f(13,3516,1,1,'com/taobao/arthas/core/shell/command/impl/AnnotatedCommandImpl.process')
f(14,3516,1,1,'com/taobao/arthas/core/command/monitor200/ProfilerCommand.process')
f(15,3516,1,1,'com/taobao/arthas/core/shell/system/impl/ProcessImpl$CommandProcessImpl.appendResult')
f(16,3516,1,1,'com/taobao/arthas/core/shell/system/impl/ProcessImpl.access$2100')
f(17,3516,1,1,'com/taobao/arthas/core/shell/system/impl/ProcessImpl.appendResult')
f(18,3516,1,1,'com/taobao/arthas/core/distribution/impl/TermResultDistributorImpl.appendResult')
f(19,3516,1,1,'com/taobao/arthas/core/command/view/ProfilerView.draw')
f(20,3516,1,1,'com/taobao/arthas/core/command/view/ProfilerView.draw')
f(21,3516,1,1,'com/taobao/arthas/core/command/view/ProfilerView.drawExecuteResult')
f(22,3516,1,0,'com/taobao/arthas/core/shell/system/impl/ProcessImpl$CommandProcessImpl.write',0,0,1)
f(23,3516,1,4,'InterpreterRuntime::monitorenter(JavaThread*, BasicObjectLock*)')
f(24,3516,1,4,'ObjectSynchronizer::fast_enter(Handle, BasicLock*, bool, Thread*)')
f(25,3516,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(26,3516,1,4,'VMThread::execute(VM_Operation*)')
f(27,3516,1,4,'Monitor::wait(bool, long, bool)')
f(28,3516,1,4,'Monitor::IWait(Thread*, long)')
f(29,3516,1,3,'ParkCommon(ParkEvent*, long)')
f(30,3516,1,4,'os::PlatformEvent::park()')
f(31,3516,1,3,'__psynch_cvwait')
f(9,3517,12,1,'org/springframework/scheduling/concurrent/ReschedulingRunnable.run')
f(10,3517,1,1,'org/springframework/scheduling/concurrent/ReschedulingRunnable.schedule')
f(11,3517,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor.schedule')
f(12,3517,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.<init>')
f(13,3517,1,1,'java/util/concurrent/FutureTask.<init>')
f(14,3517,1,1,'com/intellij/rt/debugger/agent/CaptureStorage.capture')
f(15,3517,1,1,'com/intellij/rt/debugger/agent/CaptureStorage.createCapturedStack')
f(16,3517,1,1,'com/intellij/rt/debugger/agent/CaptureStorage.getStackTrace')
f(17,3517,1,6,'com/intellij/rt/debugger/agent/CaptureStorage$ExceptionCapturedStack.getStackTrace',0,1,0)
f(10,3518,11,1,'org/springframework/scheduling/support/DelegatingErrorHandlingRunnable.run')
f(11,3518,11,1,'io/tapdata/task/TapdataTaskScheduler$$Lambda$390/303946916.run')
f(12,3518,11,1,'io/tapdata/task/TapdataTaskScheduler.lambda$start$0')
f(13,3518,11,1,'io/tapdata/task/TapdataTaskScheduler.taskListen')
f(14,3518,11,1,'com/tapdata/mongo/HttpClientMongoOperator.findAndModify')
f(15,3518,11,1,'com/tapdata/mongo/HttpClientMongoOperator.findAndModifyByCollection')
f(16,3518,11,1,'com/tapdata/mongo/HttpClientMongoOperator.findAndModifTask')
f(17,3518,11,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(18,3518,6,1,'com/tapdata/mongo/HttpClientMongoOperator.handleParams')
f(19,3518,6,1,'org/bson/Document.toJson')
f(20,3518,6,1,'org/bson/Document.toJson')
f(21,3518,2,1,'org/bson/Document.toJson')
f(22,3518,1,1,'java/io/StringWriter.toString')
f(23,3518,1,1,'java/lang/StringBuffer.toString')
f(24,3518,1,6,'java/util/Arrays.copyOfRange',0,1,0)
f(22,3519,1,1,'org/bson/codecs/DocumentCodec.encode')
f(23,3519,1,1,'org/bson/codecs/DocumentCodec.encode')
f(24,3519,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(25,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(26,3519,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(27,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(28,3519,1,1,'org/bson/codecs/DocumentCodec.writeIterable')
f(29,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(30,3519,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(31,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(32,3519,1,1,'org/bson/codecs/DocumentCodec.writeIterable')
f(33,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(34,3519,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(35,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(36,3519,1,1,'org/bson/codecs/DocumentCodec.writeMap')
f(37,3519,1,1,'org/bson/codecs/DocumentCodec.writeValue')
f(38,3519,1,1,'org/bson/codecs/EncoderContext.encodeWithChildContext')
f(39,3519,1,6,'org/bson/codecs/BooleanCodec.encode',0,1,0)
f(21,3520,4,1,'org/bson/codecs/DocumentCodec.<init>')
f(22,3520,4,1,'org/bson/codecs/DocumentCodec.<init>')
f(23,3520,4,1,'org/bson/codecs/DocumentCodec.<init>')
f(24,3520,4,1,'org/bson/codecs/DocumentCodec.<init>')
f(25,3520,4,1,'org/bson/codecs/BsonTypeCodecMap.<init>')
f(26,3520,4,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get',0,1,0)
f(27,3521,3,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(28,3521,3,1,'org/bson/codecs/configuration/CodecCache.getOrThrow')
f(29,3521,2,1,'java/lang/String.format')
f(30,3521,1,1,'java/util/Formatter.<init>')
f(31,3521,1,1,'java/util/Formatter.<init>')
f(32,3521,1,1,'java/util/Formatter.getZero')
f(33,3521,1,1,'java/text/DecimalFormatSymbols.getInstance')
f(34,3521,1,1,'sun/util/locale/provider/DecimalFormatSymbolsProviderImpl.getInstance')
f(35,3521,1,1,'java/text/DecimalFormatSymbols.<init>')
f(36,3521,1,6,'java/text/DecimalFormatSymbols.initialize',0,1,0)
f(30,3522,1,1,'java/util/Formatter.format')
f(31,3522,1,1,'java/util/Formatter.format')
f(32,3522,1,1,'java/util/Formatter$FormatSpecifier.print')
f(33,3522,1,1,'java/util/Formatter$FormatSpecifier.printString')
f(34,3522,1,1,'java/util/Formatter$FormatSpecifier.print')
f(35,3522,1,6,'java/util/Formatter$FormatSpecifier.justify',0,1,0)
f(29,3523,1,1,'org/bson/codecs/configuration/CodecConfigurationException.<init>')
f(30,3523,1,1,'java/lang/RuntimeException.<init>')
f(31,3523,1,1,'java/lang/Exception.<init>')
f(32,3523,1,1,'java/lang/Throwable.<init>')
f(33,3523,1,1,'java/lang/Throwable.fillInStackTrace')
f(34,3523,1,1,'java/lang/Throwable.fillInStackTrace')
f(35,3523,1,3,'Java_java_lang_Throwable_fillInStackTrace')
f(36,3523,1,3,'JVM_FillInStackTrace')
f(37,3523,1,4,'java_lang_Throwable::fill_in_stack_trace(Handle, methodHandle)')
f(38,3523,1,4,'frame::entry_frame_is_first() const')
f(18,3524,5,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(19,3524,5,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(20,3524,5,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(21,3524,5,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$339/424832797.tryFunc')
f(22,3524,5,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getBatch$10')
f(23,3524,2,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.getURI')
f(24,3524,1,1,'org/springframework/web/util/UriComponentsBuilder.build')
f(25,3524,1,1,'org/springframework/web/util/HierarchicalUriComponents.<init>')
f(26,3524,1,1,'org/springframework/web/util/HierarchicalUriComponents.verify')
f(27,3524,1,1,'java/util/Collections$UnmodifiableCollection$1.hasNext')
f(28,3524,1,6,'java/util/LinkedList$ListItr.hasNext',0,1,0)
f(24,3525,1,1,'org/springframework/web/util/UriComponentsBuilder.fromUriString')
f(25,3525,1,1,'org/springframework/web/util/UriComponentsBuilder.path')
f(26,3525,1,1,'org/springframework/web/util/UriComponentsBuilder$CompositePathComponentBuilder.addPath')
f(27,3525,1,1,'org/springframework/util/StringUtils.hasText')
f(28,3525,1,6,'org/springframework/util/StringUtils.containsText',0,1,0)
f(23,3526,1,1,'com/tapdata/mongo/RestTemplateOperator.getListBody')
f(24,3526,1,1,'com/tapdata/constant/JSONUtil.json2List')
f(25,3526,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(26,3526,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(27,3526,1,1,'com/fasterxml/jackson/databind/ObjectMapper._initForReading')
f(28,3526,1,1,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser.nextToken')
f(29,3526,1,6,'com/fasterxml/jackson/core/json/JsonReadContext.createChildArrayContext',0,1,0)
f(23,3527,2,1,'org/springframework/web/client/RestTemplate.exchange')
f(24,3527,2,1,'org/springframework/web/client/RestTemplate.execute')
f(25,3527,2,1,'org/springframework/web/client/RestTemplate.doExecute')
f(26,3527,2,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(27,3527,2,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(28,3527,2,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(29,3527,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.read')
f(30,3527,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.readJavaType')
f(31,3527,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(32,3527,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(33,3527,1,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(34,3527,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(35,3527,1,1,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser.nextToken')
f(36,3527,1,1,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser._parseName')
f(37,3527,1,6,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser.parseMediumName',0,1,0)
f(29,3528,1,1,'org/springframework/web/client/MessageBodyClientHttpResponseWrapper.hasEmptyMessageBody')
f(30,3528,1,1,'java/io/PushbackInputStream.read')
f(31,3528,1,1,'java/io/FilterInputStream.read')
f(32,3528,1,1,'org/apache/http/client/entity/LazyDecompressingInputStream.read')
f(33,3528,1,1,'org/apache/http/client/entity/LazyDecompressingInputStream.initWrapper')
f(34,3528,1,1,'org/apache/http/client/entity/GZIPInputStreamFactory.create')
f(35,3528,1,1,'java/util/zip/GZIPInputStream.<init>')
f(36,3528,1,1,'java/util/zip/GZIPInputStream.<init>')
f(37,3528,1,1,'java/util/zip/GZIPInputStream.readHeader')
f(38,3528,1,1,'java/util/zip/GZIPInputStream.skipBytes')
f(39,3528,1,6,'java/util/zip/CheckedInputStream.read',0,1,0)
f(5,3529,45,1,'java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301')
f(6,3529,45,1,'java/util/concurrent/FutureTask.runAndReset')
f(7,3529,45,1,'java/util/concurrent/FutureTask.runAndReset$$$capture')
f(8,3529,45,1,'java/util/concurrent/Executors$RunnableAdapter.call')
f(9,3529,1,1,'com/hazelcast/internal/metrics/metricsets/GarbageCollectionMetricSet$GcStats.run')
f(10,3529,1,1,'java/lang/management/ManagementFactory.getGarbageCollectorMXBeans')
f(11,3529,1,6,'sun/management/ManagementFactoryHelper.getGarbageCollectorMXBeans',0,1,0)
f(9,3530,1,6,'com/hazelcast/internal/networking/nio/NioNetworking$PublishAllTask.run',0,1,0)
f(9,3531,2,1,'io/tapdata/common/sample/CollectorFactory$$Lambda$1790/133321775.run')
f(10,3531,2,1,'io/tapdata/common/sample/CollectorFactory.lambda$start$1')
f(11,3531,1,1,'io/tapdata/common/sample/CollectorFactory.wrapSampleRequest')
f(12,3531,1,1,'io/tapdata/common/sample/SampleCollector.calculateInPeriod')
f(13,3531,1,1,'io/tapdata/observable/metric/handler/DataNodeSampleHandler$$Lambda$2730/123984596.value')
f(14,3531,1,1,'io/tapdata/observable/metric/handler/DataNodeSampleHandler.lambda$doInit$0')
f(15,3531,1,6,'java/math/BigInteger.longValue',0,1,0)
f(11,3532,1,1,'io/tapdata/observable/metric/TaskSampleReporter.execute')
f(12,3532,1,1,'com/tapdata/mongo/HttpClientMongoOperator.insertOne')
f(13,3532,1,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(14,3532,1,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(15,3532,1,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(16,3532,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$1764/485421119.tryFunc')
f(17,3532,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$3')
f(18,3532,1,1,'org/springframework/web/client/RestTemplate.postForEntity')
f(19,3532,1,1,'org/springframework/web/client/RestTemplate.execute')
f(20,3532,1,1,'org/springframework/web/client/RestTemplate.doExecute')
f(21,3532,1,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(22,3532,1,1,'org/springframework/web/client/RestTemplate$AcceptHeaderRequestCallback.doWithRequest')
f(23,3532,1,1,'org/springframework/http/HttpHeaders.setAccept')
f(24,3532,1,1,'org/springframework/http/MediaType.toString')
f(25,3532,1,6,'org/springframework/util/MimeTypeUtils.toString',0,1,0)
f(9,3533,2,1,'io/tapdata/flow/engine/V2/monitor/impl/JetJobStatusMonitor$$Lambda$2773/1619464370.run')
f(10,3533,2,1,'io/tapdata/flow/engine/V2/monitor/impl/JetJobStatusMonitor.lambda$start$0')
f(11,3533,2,1,'com/hazelcast/jet/impl/AbstractJobProxy.getStatus')
f(12,3533,2,1,'com/hazelcast/jet/impl/JobProxy.getStatus0')
f(13,3533,2,1,'com/hazelcast/jet/impl/JobProxy.invokeOp')
f(14,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/InvocationBuilderImpl.invoke')
f(15,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke')
f(16,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.invoke0')
f(17,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvoke')
f(18,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/Invocation.doInvokeLocal')
f(19,3533,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.runOrExecute')
f(20,3533,2,1,'com/hazelcast/spi/impl/operationexecutor/impl/OperationExecutorImpl.run')
f(21,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(22,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.run')
f(23,3533,2,1,'com/hazelcast/spi/impl/operationservice/impl/OperationRunnerImpl.call')
f(24,3533,2,1,'com/hazelcast/spi/impl/operationservice/Operation.call')
f(25,3533,2,1,'com/hazelcast/jet/impl/operation/AsyncOperation.run')
f(26,3533,2,1,'com/hazelcast/jet/impl/operation/GetJobStatusOperation.doRun')
f(27,3533,2,1,'com/hazelcast/jet/impl/JobCoordinationService.getJobStatus')
f(28,3533,2,1,'com/hazelcast/jet/impl/JobCoordinationService.callWithJob')
f(29,3533,1,1,'com/hazelcast/jet/Util.idToString')
f(30,3533,1,1,'java/lang/Long.toHexString')
f(31,3533,1,1,'java/lang/Long.toUnsignedString0')
f(32,3533,1,6,'java/lang/Long.formatUnsignedLong',0,1,0)
f(29,3534,1,1,'com/hazelcast/jet/impl/JobCoordinationService.submitToCoordinatorThread')
f(30,3534,1,1,'com/hazelcast/spi/impl/executionservice/impl/ExecutionServiceImpl.submit')
f(31,3534,1,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.submit')
f(32,3534,1,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.execute')
f(33,3534,1,1,'com/hazelcast/internal/util/executor/CachedExecutorServiceDelegate.addNewWorkerIfRequired')
f(34,3534,1,6,'java/util/concurrent/locks/ReentrantLock.unlock',0,1,0)
f(9,3535,6,1,'io/tapdata/flow/engine/V2/monitor/impl/TaskPingTimeMonitor$$Lambda$2390/1359141143.run')
f(10,3535,6,1,'io/tapdata/flow/engine/V2/monitor/impl/TaskPingTimeMonitor.lambda$start$0')
f(11,3535,6,1,'io/tapdata/flow/engine/V2/monitor/impl/TaskPingTimeMonitor.taskPingTimeUseHttp')
f(12,3535,6,1,'com/tapdata/mongo/HttpClientMongoOperator.update')
f(13,3535,4,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(14,3535,3,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(15,3535,2,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$352/1794607688.tryFunc')
f(16,3535,2,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$8')
f(17,3535,2,1,'org/springframework/web/client/RestTemplate.postForObject')
f(18,3535,2,1,'org/springframework/web/client/RestTemplate.execute')
f(19,3535,2,1,'org/springframework/web/client/RestTemplate.doExecute')
f(20,3535,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(21,3535,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(22,3535,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(23,3535,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(24,3535,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(25,3535,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(26,3535,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(27,3535,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(28,3535,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(29,3535,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(30,3535,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(31,3535,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(32,3535,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(33,3535,2,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(34,3535,2,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(35,3535,2,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(36,3535,2,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(37,3535,1,6,'org/apache/http/impl/execchain/MainClientExec.needAuthentication',0,1,0)
f(37,3536,1,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(38,3536,1,1,'org/apache/http/protocol/HttpRequestExecutor.doReceiveResponse')
f(39,3536,1,1,'org/apache/http/impl/conn/CPoolProxy.receiveResponseHeader')
f(40,3536,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.receiveResponseHeader')
f(41,3536,1,1,'org/apache/http/impl/io/AbstractMessageParser.parse')
f(42,3536,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(43,3536,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(44,3536,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.readLine')
f(45,3536,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.fillBuffer')
f(46,3536,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.streamRead')
f(47,3536,1,1,'java/net/SocketInputStream.read')
f(48,3536,1,1,'java/net/SocketInputStream.read')
f(49,3536,1,1,'java/net/SocketInputStream.socketRead')
f(50,3536,1,1,'java/net/SocketInputStream.socketRead0')
f(51,3536,1,3,'Java_java_net_SocketInputStream_socketRead0')
f(52,3536,1,3,'select$DARWIN_EXTSN')
f(15,3537,1,1,'java/util/Optional.map')
f(16,3537,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$303/490475818.apply')
f(17,3537,1,1,'io/tapdata/Schedule/ConnectorManager$$Lambda$191/**********.get')
f(18,3537,1,1,'io/tapdata/Schedule/ConnectorManager.lambda$new$0')
f(19,3537,1,1,'io/tapdata/pdk/core/utils/CommonUtils.getPropertyLong')
f(20,3537,1,1,'io/tapdata/pdk/core/utils/CommonUtils.getProperty')
f(21,3537,1,1,'io/tapdata/pdk/core/utils/CommonUtils.getenv')
f(22,3537,1,1,'java/lang/System.getenv')
f(23,3537,1,1,'java/lang/ProcessEnvironment.getenv')
f(24,3537,1,1,'java/util/Collections$UnmodifiableMap.get')
f(25,3537,1,1,'java/lang/ProcessEnvironment$StringEnvironment.get')
f(26,3537,1,1,'java/lang/ProcessEnvironment$StringEnvironment.get')
f(27,3537,1,1,'java/util/HashMap.get')
f(28,3537,1,1,'java/util/HashMap.hash')
f(29,3537,1,6,'java/lang/ProcessEnvironment$ExternalData.hashCode',0,1,0)
f(14,3538,1,1,'java/lang/invoke/LambdaForm$MH/1968552224.linkToTargetMethod')
f(15,3538,1,6,'java/lang/invoke/LambdaForm$DMH/**********.invokeStatic_L5_L',0,1,0)
f(13,3539,2,1,'org/bson/Document.toJson')
f(14,3539,2,1,'org/bson/Document.toJson')
f(15,3539,2,1,'org/bson/codecs/DocumentCodec.<init>')
f(16,3539,2,1,'org/bson/codecs/DocumentCodec.<init>')
f(17,3539,2,1,'org/bson/codecs/DocumentCodec.<init>')
f(18,3539,2,1,'org/bson/codecs/DocumentCodec.<init>')
f(19,3539,2,1,'org/bson/codecs/BsonTypeCodecMap.<init>')
f(20,3539,2,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(21,3539,2,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(22,3539,2,1,'org/bson/codecs/configuration/CodecCache.getOrThrow')
f(23,3539,2,1,'org/bson/codecs/configuration/CodecConfigurationException.<init>')
f(24,3539,2,1,'java/lang/RuntimeException.<init>')
f(25,3539,2,1,'java/lang/Exception.<init>')
f(26,3539,2,1,'java/lang/Throwable.<init>')
f(27,3539,2,6,'java/lang/Throwable.fillInStackTrace',0,1,0)
f(28,3540,1,1,'java/lang/Throwable.fillInStackTrace')
f(29,3540,1,3,'Java_java_lang_Throwable_fillInStackTrace')
f(30,3540,1,3,'JVM_FillInStackTrace')
f(31,3540,1,4,'java_lang_Throwable::fill_in_stack_trace(Handle, methodHandle)')
f(32,3540,1,4,'java_lang_Throwable::fill_in_stack_trace(Handle, methodHandle, Thread*)')
f(33,3540,1,4,'frame::sender(RegisterMap*) const')
f(34,3540,1,4,'frame::sender_for_compiled_frame(RegisterMap*) const')
f(35,3540,1,4,'nmethod::is_zombie() const')
f(9,3541,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode$$Lambda$2836/**********.run')
f(10,3541,4,1,'io/tapdata/flow/engine/V2/node/hazelcast/data/pdk/HazelcastTargetPdkBaseNode.saveToSnapshot')
f(11,3541,1,1,'com/tapdata/mongo/HttpClientMongoOperator.insertOne')
f(12,3541,1,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(13,3541,1,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(14,3541,1,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(15,3541,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$1764/485421119.tryFunc')
f(16,3541,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$3')
f(17,3541,1,1,'org/springframework/web/client/RestTemplate.postForEntity')
f(18,3541,1,1,'org/springframework/web/client/RestTemplate.execute')
f(19,3541,1,1,'org/springframework/web/client/RestTemplate.doExecute')
f(20,3541,1,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(21,3541,1,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(22,3541,1,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(23,3541,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.read')
f(24,3541,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.readJavaType')
f(25,3541,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(26,3541,1,1,'com/fasterxml/jackson/core/JsonFactory.createParser')
f(27,3541,1,1,'com/fasterxml/jackson/core/JsonFactory._createParser')
f(28,3541,1,1,'com/fasterxml/jackson/core/json/ByteSourceJsonBootstrapper.constructParser')
f(29,3541,1,1,'com/fasterxml/jackson/core/json/ByteSourceJsonBootstrapper.detectEncoding')
f(30,3541,1,1,'com/fasterxml/jackson/core/json/ByteSourceJsonBootstrapper.ensureLoaded')
f(31,3541,1,1,'java/io/PushbackInputStream.read')
f(32,3541,1,1,'java/io/FilterInputStream.read')
f(33,3541,1,1,'org/apache/http/client/entity/LazyDecompressingInputStream.read')
f(34,3541,1,1,'java/util/zip/GZIPInputStream.read')
f(35,3541,1,1,'java/util/zip/InflaterInputStream.read')
f(36,3541,1,1,'java/util/zip/Inflater.inflate')
f(37,3541,1,1,'java/util/zip/Inflater.inflateBytes')
f(38,3541,1,3,'Java_java_util_zip_Inflater_inflateBytes')
f(39,3541,1,3,'jni_SetIntField')
f(11,3542,3,1,'io/tapdata/flow/engine/V2/util/PdkUtil.encodeOffset')
f(12,3542,3,1,'org/apache/commons/net/util/Base64.encodeBase64String')
f(13,3542,1,1,'org/apache/commons/net/util/Base64.encodeBase64')
f(14,3542,1,1,'org/apache/commons/net/util/Base64.encodeBase64')
f(15,3542,1,1,'org/apache/commons/net/util/Base64.encodeBase64')
f(16,3542,1,1,'org/apache/commons/net/util/Base64.encode')
f(17,3542,1,6,'org/apache/commons/net/util/Base64.encode',0,1,0)
f(13,3543,2,1,'org/apache/commons/net/util/Base64.newStringUtf8')
f(14,3543,2,1,'java/lang/String.<init>')
f(15,3543,2,1,'java/lang/String.<init>')
f(16,3543,2,6,'java/lang/StringCoding.decode',0,1,0)
f(17,3544,1,1,'java/lang/StringCoding.lookupCharset')
f(18,3544,1,1,'java/nio/charset/Charset.isSupported')
f(19,3544,1,1,'java/nio/charset/Charset.lookup')
f(20,3544,1,1,'java/nio/charset/Charset.lookup2')
f(21,3544,1,6,'java/lang/String.equals',0,1,0)
f(9,3545,2,1,'io/tapdata/milestone/MilestoneAspectTask$$Lambda$2312/1622925019.run')
f(10,3545,2,1,'io/tapdata/milestone/MilestoneAspectTask.storeMilestone')
f(11,3545,2,1,'com/tapdata/mongo/HttpClientMongoOperator.update')
f(12,3545,2,1,'com/tapdata/mongo/RestTemplateOperator.post')
f(13,3545,2,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(14,3545,2,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$352/1794607688.tryFunc')
f(15,3545,2,1,'com/tapdata/mongo/RestTemplateOperator.lambda$post$8')
f(16,3545,2,1,'org/springframework/web/client/RestTemplate.postForObject')
f(17,3545,2,1,'org/springframework/web/client/RestTemplate.execute')
f(18,3545,2,1,'org/springframework/web/client/RestTemplate.doExecute')
f(19,3545,2,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(20,3545,2,1,'org/springframework/http/converter/AbstractGenericHttpMessageConverter.write')
f(21,3545,2,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.writeInternal')
f(22,3545,2,1,'com/fasterxml/jackson/databind/ObjectWriter.writeValue')
f(23,3545,2,1,'com/fasterxml/jackson/databind/ObjectWriter$Prefetch.serialize')
f(24,3545,2,1,'com/fasterxml/jackson/databind/ser/DefaultSerializerProvider.serializeValue')
f(25,3545,2,1,'com/fasterxml/jackson/databind/ser/DefaultSerializerProvider._serialize')
f(26,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(27,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(28,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(29,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(30,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(31,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(32,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(33,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(34,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(35,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(36,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(37,3545,2,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(38,3545,2,1,'com/fasterxml/jackson/databind/ser/BeanSerializer.serialize')
f(39,3545,2,1,'com/fasterxml/jackson/databind/ser/std/BeanSerializerBase.serializeFields')
f(40,3545,2,1,'com/fasterxml/jackson/databind/ser/BeanPropertyWriter.serializeAsField')
f(41,3545,2,1,'java/lang/reflect/Method.invoke')
f(42,3545,2,1,'sun/reflect/DelegatingMethodAccessorImpl.invoke')
f(43,3545,1,6,'sun/reflect/GeneratedMethodAccessor498.invoke',0,1,0)
f(43,3546,1,6,'sun/reflect/GeneratedMethodAccessor501.invoke',0,1,0)
f(9,3547,7,1,'io/tapdata/observable/logging/ObsLoggerFactory$$Lambda$1892/107607915.run')
f(10,3547,7,1,'io/tapdata/observable/logging/ObsLoggerFactory.renewTaskLogSetting')
f(11,3547,7,1,'com/tapdata/mongo/HttpClientMongoOperator.findOne')
f(12,3547,1,1,'com/tapdata/mongo/HttpClientMongoOperator.handleParams')
f(13,3547,1,1,'org/bson/Document.toJson')
f(14,3547,1,1,'org/bson/Document.toJson')
f(15,3547,1,1,'org/bson/codecs/DocumentCodec.<init>')
f(16,3547,1,1,'org/bson/codecs/DocumentCodec.<init>')
f(17,3547,1,1,'org/bson/codecs/DocumentCodec.<init>')
f(18,3547,1,1,'org/bson/codecs/DocumentCodec.<init>')
f(19,3547,1,1,'org/bson/codecs/BsonTypeCodecMap.<init>')
f(20,3547,1,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(21,3547,1,1,'org/bson/codecs/configuration/ProvidersCodecRegistry.get')
f(22,3547,1,1,'org/bson/codecs/configuration/CodecCache.getOrThrow')
f(23,3547,1,1,'java/lang/String.format')
f(24,3547,1,1,'java/util/Formatter.format')
f(25,3547,1,1,'java/util/Formatter.format')
f(26,3547,1,1,'java/util/Formatter.parse')
f(27,3547,1,1,'java/util/regex/Matcher.find')
f(28,3547,1,1,'java/util/regex/Matcher.search')
f(29,3547,1,1,'java/util/regex/Pattern$Start.match')
f(30,3547,1,6,'java/util/regex/Pattern$BmpCharProperty.match',0,1,0)
f(12,3548,6,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(13,3548,6,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(14,3548,6,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(15,3548,6,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$316/**********.tryFunc')
f(16,3548,6,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getOne$11')
f(17,3548,4,1,'com/tapdata/mongo/RestTemplateOperator.getBody')
f(18,3548,4,1,'com/tapdata/constant/JSONUtil.json2POJO')
f(19,3548,4,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(20,3548,4,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(21,3548,4,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(22,3548,4,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(23,3548,4,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(24,3548,4,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.vanillaDeserialize')
f(25,3548,4,1,'com/fasterxml/jackson/databind/deser/impl/MethodProperty.deserializeAndSet')
f(26,3548,1,1,'com/fasterxml/jackson/databind/deser/std/MapDeserializer.deserialize')
f(27,3548,1,1,'com/fasterxml/jackson/databind/deser/std/MapDeserializer.deserialize')
f(28,3548,1,1,'com/fasterxml/jackson/databind/deser/std/MapDeserializer._readAndBindStringKeyMap')
f(29,3548,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(30,3548,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(31,3548,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapObject')
f(32,3548,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(33,3548,1,1,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser.getText')
f(34,3548,1,1,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser._finishString')
f(35,3548,1,6,'com/fasterxml/jackson/core/util/TextBuffer.resetWithCopy',0,1,0)
f(26,3549,3,1,'com/tapdata/tm/commons/base/convert/DagDeserialize.deserialize')
f(27,3549,3,1,'com/tapdata/tm/commons/base/convert/DagDeserialize.deserialize')
f(28,3549,3,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(29,3549,3,1,'com/fasterxml/jackson/databind/ObjectMapper._readValue')
f(30,3549,3,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(31,3549,3,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(32,3549,3,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.vanillaDeserialize')
f(33,3549,3,1,'com/fasterxml/jackson/databind/deser/impl/MethodProperty.deserializeAndSet')
f(34,3549,3,1,'com/tapdata/tm/base/convert/NodeDeserialize.deserialize')
f(35,3549,3,1,'com/tapdata/tm/base/convert/NodeDeserialize.deserialize')
f(36,3549,3,1,'com/tapdata/tm/commons/util/JsonUtil.toJson')
f(37,3549,3,1,'com/google/gson/Gson.toJson')
f(38,3549,3,1,'com/google/gson/Gson.toJson')
f(39,3549,3,1,'com/google/gson/Gson.toJson')
f(40,3549,3,1,'com/google/gson/Gson.toJson')
f(41,3549,1,1,'com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.write')
f(42,3549,1,1,'com/google/gson/internal/bind/MapTypeAdapterFactory$Adapter.write')
f(43,3549,1,1,'com/google/gson/internal/bind/TypeAdapterRuntimeTypeWrapper.write')
f(44,3549,1,1,'com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.write')
f(45,3549,1,6,'com/google/gson/internal/bind/CollectionTypeAdapterFactory$Adapter.write',0,1,0)
f(41,3550,2,1,'com/google/gson/reflect/TypeToken.get')
f(42,3550,2,1,'com/google/gson/reflect/TypeToken.<init>')
f(43,3550,1,6,'com/google/gson/internal/$Gson$Types.canonicalize',0,1,0)
f(43,3551,1,1,'java/lang/Object.hashCode')
f(17,3552,2,1,'org/springframework/web/client/RestTemplate.exchange')
f(18,3552,2,1,'org/springframework/web/client/RestTemplate.execute')
f(19,3552,2,1,'org/springframework/web/client/RestTemplate.doExecute')
f(20,3552,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(21,3552,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(22,3552,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(23,3552,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(24,3552,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(25,3552,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(26,3552,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(27,3552,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(28,3552,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(29,3552,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(30,3552,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(31,3552,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(32,3552,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(33,3552,2,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(34,3552,2,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(35,3552,2,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(36,3552,2,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(37,3552,2,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager$1.get')
f(38,3552,2,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager.leaseConnection')
f(39,3552,2,1,'org/apache/http/pool/AbstractConnPool$2.get')
f(40,3552,2,1,'org/apache/http/pool/AbstractConnPool$2.get')
f(41,3552,2,1,'org/apache/http/pool/AbstractConnPool.access$200')
f(42,3552,2,1,'org/apache/http/pool/AbstractConnPool.getPoolEntryBlocking')
f(43,3552,1,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager$InternalConnectionFactory.create')
f(44,3552,1,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager$InternalConnectionFactory.create')
f(45,3552,1,1,'org/apache/http/impl/conn/ManagedHttpClientConnectionFactory.create')
f(46,3552,1,1,'org/apache/http/impl/conn/ManagedHttpClientConnectionFactory.create')
f(47,3552,1,1,'org/apache/http/impl/conn/LoggingManagedHttpClientConnection.<init>')
f(48,3552,1,1,'org/apache/http/impl/conn/DefaultManagedHttpClientConnection.<init>')
f(49,3552,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.<init>')
f(50,3552,1,1,'org/apache/http/impl/BHttpConnectionBase.<init>')
f(51,3552,1,0,'org/apache/http/impl/io/SessionInputBufferImpl.<init>',0,0,1)
f(52,3552,1,4,'InterpreterRuntime::newarray(JavaThread*, BasicType, int)')
f(53,3552,1,4,'TypeArrayKlass::allocate_common(int, bool, Thread*)')
f(54,3552,1,4,'CollectedHeap::array_allocate(KlassHandle, int, int, Thread*)')
f(43,3553,1,1,'org/apache/http/pool/AbstractConnPool.getPool')
f(44,3553,1,1,'java/util/HashMap.get')
f(45,3553,1,1,'java/util/HashMap.hash')
f(46,3553,1,1,'org/apache/http/conn/routing/HttpRoute.hashCode')
f(47,3553,1,1,'org/apache/http/util/LangUtils.hashCode')
f(48,3553,1,6,'org/apache/http/HttpHost.hashCode',0,1,0)
f(9,3554,1,1,'io/tapdata/websocket/ManagementWebsocketHandler$$Lambda$1716/*********.run')
f(10,3554,1,1,'io/tapdata/websocket/ManagementWebsocketHandler.lambda$init$2')
f(11,3554,1,1,'io/tapdata/websocket/ManagementWebsocketHandler.sendMessage')
f(12,3554,1,1,'io/tapdata/websocket/ManagementWebsocketHandler$SessionOption.sendMessage')
f(13,3554,1,1,'org/springframework/web/socket/adapter/AbstractWebSocketSession.sendMessage')
f(14,3554,1,1,'org/springframework/web/socket/adapter/standard/StandardWebSocketSession.sendTextMessage')
f(15,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointBasic.sendText')
f(16,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendPartialString')
f(17,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendMessageBlock')
f(18,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.sendMessageBlock')
f(19,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase.writeMessagePart')
f(20,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplBase$OutputBufferSendHandler.write')
f(21,3554,1,1,'org/apache/tomcat/websocket/WsRemoteEndpointImplClient.doWrite')
f(22,3554,1,1,'org/apache/tomcat/websocket/AsyncChannelWrapperNonSecure.write')
f(23,3554,1,1,'sun/nio/ch/AsynchronousSocketChannelImpl.write')
f(24,3554,1,6,'sun/nio/ch/AsynchronousSocketChannelImpl.write',0,1,0)
f(9,3555,19,1,'org/springframework/scheduling/support/DelegatingErrorHandlingRunnable.run')
f(10,3555,19,1,'org/springframework/scheduling/support/ScheduledMethodRunnable.run')
f(11,3555,19,1,'java/lang/reflect/Method.invoke')
f(12,3555,19,1,'sun/reflect/DelegatingMethodAccessorImpl.invoke')
f(13,3555,1,1,'sun/reflect/GeneratedMethodAccessor140.invoke')
f(14,3555,1,1,'io/tapdata/Schedule/TransformerManager.workerHeartBeat')
f(15,3555,1,1,'com/tapdata/constant/SystemUtil.getProcessCpuLoad')
f(16,3555,1,1,'com/sun/jmx/mbeanserver/JmxMBeanServer.getAttributes')
f(17,3555,1,1,'com/sun/jmx/interceptor/DefaultMBeanServerInterceptor.getAttributes')
f(18,3555,1,1,'javax/management/StandardMBean.getAttributes')
f(19,3555,1,1,'com/sun/jmx/mbeanserver/MBeanSupport.getAttributes')
f(20,3555,1,1,'com/sun/jmx/mbeanserver/MBeanSupport.getAttribute')
f(21,3555,1,1,'com/sun/jmx/mbeanserver/PerInterface.getAttribute')
f(22,3555,1,1,'com/sun/jmx/mbeanserver/MBeanIntrospector.invokeM')
f(23,3555,1,1,'com/sun/jmx/mbeanserver/MXBeanIntrospector.invokeM2')
f(24,3555,1,1,'com/sun/jmx/mbeanserver/MXBeanIntrospector.invokeM2')
f(25,3555,1,1,'com/sun/jmx/mbeanserver/ConvertingMethod.invokeWithOpenReturn')
f(26,3555,1,1,'com/sun/jmx/mbeanserver/ConvertingMethod.invokeWithOpenReturn')
f(27,3555,1,1,'sun/reflect/misc/MethodUtil.invoke')
f(28,3555,1,6,'java/lang/reflect/Method.invoke',0,1,0)
f(13,3556,5,1,'sun/reflect/GeneratedMethodAccessor141.invoke')
f(14,3556,5,1,'io/tapdata/Schedule/ConnectorManager.workerHeartBeat')
f(15,3556,5,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(16,3556,5,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(17,3556,1,1,'com/tapdata/mongo/HttpClientMongoOperator.handleParams')
f(18,3556,1,1,'org/springframework/data/mongodb/core/query/Query.getQueryObject')
f(19,3556,1,1,'org/bson/Document.putAll')
f(20,3556,1,1,'java/util/HashMap.putAll')
f(21,3556,1,6,'java/util/HashMap.putMapEntries',0,1,0)
f(22,3556,1,3,'itable stub')
f(17,3557,4,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(18,3557,4,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(19,3557,4,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(20,3557,4,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$339/424832797.tryFunc')
f(21,3557,4,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getBatch$10')
f(22,3557,4,1,'org/springframework/web/client/RestTemplate.exchange')
f(23,3557,4,1,'org/springframework/web/client/RestTemplate.execute')
f(24,3557,4,1,'org/springframework/web/client/RestTemplate.doExecute')
f(25,3557,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(26,3557,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(27,3557,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(28,3557,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(29,3557,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(30,3557,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(31,3557,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(32,3557,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(33,3557,1,1,'org/springframework/http/HttpHeaders.addAll')
f(34,3557,1,1,'java/util/Map.computeIfAbsent')
f(35,3557,1,1,'org/springframework/util/LinkedCaseInsensitiveMap.get')
f(36,3557,1,1,'java/util/HashMap.get')
f(37,3557,1,6,'java/util/HashMap.getNode',0,1,0)
f(33,3558,1,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(34,3558,1,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(35,3558,1,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(36,3558,1,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(37,3558,1,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(38,3558,1,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(39,3558,1,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(40,3558,1,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(41,3558,1,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(42,3558,1,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(43,3558,1,1,'org/apache/http/protocol/HttpRequestExecutor.doReceiveResponse')
f(44,3558,1,1,'org/apache/http/impl/conn/CPoolProxy.receiveResponseHeader')
f(45,3558,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.receiveResponseHeader')
f(46,3558,1,1,'org/apache/http/impl/io/AbstractMessageParser.parse')
f(47,3558,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(48,3558,1,1,'org/apache/http/impl/conn/DefaultHttpResponseParser.parseHead')
f(49,3558,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.readLine')
f(50,3558,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.fillBuffer')
f(51,3558,1,1,'org/apache/http/impl/io/SessionInputBufferImpl.streamRead')
f(52,3558,1,1,'java/net/SocketInputStream.read')
f(53,3558,1,1,'java/net/SocketInputStream.read')
f(54,3558,1,1,'java/net/SocketInputStream.socketRead')
f(55,3558,1,1,'java/net/SocketInputStream.socketRead0')
f(56,3558,1,3,'Java_java_net_SocketInputStream_socketRead0')
f(57,3558,1,3,'NET_Timeout0')
f(25,3559,2,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(26,3559,2,1,'org/springframework/web/client/RestTemplate$ResponseEntityResponseExtractor.extractData')
f(27,3559,2,1,'org/springframework/web/client/HttpMessageConverterExtractor.extractData')
f(28,3559,2,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.read')
f(29,3559,2,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.readJavaType')
f(30,3559,2,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(31,3559,2,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(32,3559,2,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(33,3559,2,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(34,3559,2,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserializeFromObject')
f(35,3559,2,1,'com/fasterxml/jackson/databind/deser/impl/MethodProperty.deserializeAndSet')
f(36,3559,1,1,'com/fasterxml/jackson/databind/deser/std/StringDeserializer.deserialize')
f(37,3559,1,1,'com/fasterxml/jackson/databind/deser/std/StringDeserializer.deserialize')
f(38,3559,1,6,'com/fasterxml/jackson/core/json/UTF8StreamJsonParser.getText',0,1,0)
f(36,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(37,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(38,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapObject')
f(39,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(40,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapArray')
f(41,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(42,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapObject')
f(43,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(44,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.mapObject')
f(45,3560,1,1,'com/fasterxml/jackson/databind/deser/std/UntypedObjectDeserializer$Vanilla.deserialize')
f(46,3560,1,1,'com/fasterxml/jackson/core/base/ParserBase.getNumberValue')
f(47,3560,1,1,'com/fasterxml/jackson/core/base/ParserBase._parseNumericValue')
f(48,3560,1,1,'com/fasterxml/jackson/core/base/ParserBase._parseSlowFloat')
f(49,3560,1,1,'com/fasterxml/jackson/core/util/TextBuffer.contentsAsDouble')
f(50,3560,1,1,'com/fasterxml/jackson/core/io/NumberInput.parseDouble')
f(51,3560,1,1,'java/lang/Double.parseDouble')
f(52,3560,1,1,'sun/misc/FloatingDecimal.parseDouble')
f(53,3560,1,1,'sun/misc/FloatingDecimal$ASCIIToBinaryBuffer.doubleValue')
f(54,3560,1,6,'sun/misc/FDBigInteger.<init>',0,1,0)
f(13,3561,1,1,'sun/reflect/GeneratedMethodAccessor631.invoke')
f(14,3561,1,1,'io/tapdata/Schedule/ConnectorManager.loadSettings')
f(15,3561,1,1,'io/tapdata/common/SettingService.loadSettings')
f(16,3561,1,1,'com/tapdata/mongo/HttpClientMongoOperator.find')
f(17,3561,1,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(18,3561,1,1,'com/tapdata/mongo/RestTemplateOperator.getBatch')
f(19,3561,1,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(20,3561,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$339/424832797.tryFunc')
f(21,3561,1,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getBatch$10')
f(22,3561,1,1,'org/springframework/web/client/RestTemplate.exchange')
f(23,3561,1,1,'org/springframework/web/client/RestTemplate.execute')
f(24,3561,1,1,'org/springframework/web/client/RestTemplate.doExecute')
f(25,3561,1,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(26,3561,1,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(27,3561,1,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(28,3561,1,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(29,3561,1,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(30,3561,1,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(31,3561,1,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(32,3561,1,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(33,3561,1,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(34,3561,1,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(35,3561,1,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(36,3561,1,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(37,3561,1,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(38,3561,1,1,'org/apache/http/impl/client/InternalHttpClient.doExecute')
f(39,3561,1,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(40,3561,1,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(41,3561,1,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(42,3561,1,1,'org/apache/http/impl/conn/PoolingHttpClientConnectionManager$1.get')
f(43,3561,1,1,'org/apache/http/impl/conn/CPoolProxy.setSocketTimeout')
f(44,3561,1,1,'org/apache/http/impl/conn/LoggingManagedHttpClientConnection.setSocketTimeout')
f(45,3561,1,1,'org/apache/http/impl/BHttpConnectionBase.setSocketTimeout')
f(46,3561,1,1,'java/net/Socket.setSoTimeout')
f(47,3561,1,6,'java/net/AbstractPlainSocketImpl.setOption',0,1,0)
f(13,3562,12,1,'sun/reflect/GeneratedMethodAccessor72.invoke')
f(14,3562,12,1,'io/tapdata/Schedule/ConnectorManager.refreshToken')
f(15,3562,12,1,'com/tapdata/mongo/HttpClientMongoOperator.findOne')
f(16,3562,12,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(17,3562,12,1,'com/tapdata/mongo/RestTemplateOperator.getOne')
f(18,3562,12,1,'com/tapdata/mongo/RestTemplateOperator.retryWrap')
f(19,3562,9,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$316/**********.tryFunc')
f(20,3562,9,1,'com/tapdata/mongo/RestTemplateOperator.lambda$getOne$11')
f(21,3562,1,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.getURI')
f(22,3562,1,1,'org/springframework/web/util/HierarchicalUriComponents.toUri')
f(23,3562,1,1,'org/springframework/web/util/UriComponents.toString')
f(24,3562,1,1,'org/springframework/web/util/HierarchicalUriComponents.toUriString')
f(25,3562,1,1,'org/springframework/web/util/HierarchicalUriComponents.getQuery')
f(26,3562,1,1,'org/springframework/util/CollectionUtils$MultiValueMapAdapter.isEmpty')
f(27,3562,1,1,'java/util/Collections$UnmodifiableMap.isEmpty')
f(28,3562,1,6,'java/util/HashMap.isEmpty',0,1,0)
f(21,3563,5,1,'com/tapdata/mongo/RestTemplateOperator.getBody')
f(22,3563,1,1,'com/tapdata/constant/JSONUtil.json2POJO')
f(23,3563,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(24,3563,1,1,'com/fasterxml/jackson/databind/ObjectMapper.readValue')
f(25,3563,1,1,'com/fasterxml/jackson/databind/ObjectMapper._readMapAndClose')
f(26,3563,1,1,'com/fasterxml/jackson/databind/deser/DefaultDeserializationContext.readRootValue')
f(27,3563,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.deserialize')
f(28,3563,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializer.vanillaDeserialize')
f(29,3563,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializerBase.handleUnknownVanilla')
f(30,3563,1,1,'com/fasterxml/jackson/databind/deser/BeanDeserializerBase.handleUnknownProperty')
f(31,3563,1,1,'com/fasterxml/jackson/databind/deser/std/StdDeserializer.handleUnknownProperty')
f(32,3563,1,1,'com/fasterxml/jackson/databind/DeserializationContext.handleUnknownProperty')
f(33,3563,1,1,'com/fasterxml/jackson/core/base/ParserMinimalBase.skipChildren')
f(34,3563,1,1,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser.nextToken')
f(35,3563,1,6,'com/fasterxml/jackson/core/json/ReaderBasedJsonParser._parseName',0,1,0)
f(22,3564,4,1,'com/tapdata/constant/JSONUtil.obj2Json')
f(23,3564,4,1,'com/fasterxml/jackson/databind/ObjectMapper.writeValueAsString')
f(24,3564,4,1,'com/fasterxml/jackson/databind/ObjectMapper._writeValueAndClose')
f(25,3564,4,1,'com/fasterxml/jackson/databind/ser/DefaultSerializerProvider.serializeValue')
f(26,3564,4,1,'com/fasterxml/jackson/databind/ser/DefaultSerializerProvider._serialize')
f(27,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(28,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(29,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(30,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(31,3564,4,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(32,3564,4,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(33,3564,4,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serializeContents')
f(34,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(35,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(36,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(37,3564,4,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(38,3564,2,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(39,3564,2,1,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serialize')
f(40,3564,2,6,'com/fasterxml/jackson/databind/ser/impl/IndexedListSerializer.serializeContents',0,1,0)
f(41,3564,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(42,3564,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serialize')
f(43,3564,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeWithoutTypeInfo')
f(44,3564,1,1,'com/fasterxml/jackson/databind/ser/std/MapSerializer.serializeFields')
f(45,3564,1,1,'com/fasterxml/jackson/databind/ser/std/StdKeySerializers$Dynamic.serialize')
f(46,3564,1,1,'com/fasterxml/jackson/databind/ser/std/StdKeySerializers$StringKeySerializer.serialize')
f(47,3564,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator.writeFieldName')
f(48,3564,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator._writeFieldName')
f(49,3564,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator._writeString')
f(50,3564,1,6,'java/lang/String.getChars',0,1,0)
f(41,3565,1,3,'vtable stub')
f(38,3566,2,6,'com/fasterxml/jackson/databind/ser/std/StringSerializer.serialize',0,1,0)
f(39,3567,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator.writeString')
f(40,3567,1,1,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator._writeString')
f(41,3567,1,6,'com/fasterxml/jackson/core/json/WriterBasedJsonGenerator._writeString2',0,1,0)
f(21,3568,3,1,'org/springframework/web/client/RestTemplate.exchange')
f(22,3568,3,1,'org/springframework/web/client/RestTemplate.execute')
f(23,3568,3,1,'org/springframework/web/client/RestTemplate.doExecute')
f(24,3568,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(25,3568,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(26,3568,2,1,'org/springframework/http/client/InterceptingClientHttpRequest.executeInternal')
f(27,3568,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(28,3568,2,1,'com/tapdata/interceptor/LoggingInterceptor.intercept')
f(29,3568,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(30,3568,2,1,'com/tapdata/tm/sdk/interceptor/VersionHeaderInterceptor.intercept')
f(31,3568,2,1,'org/springframework/http/client/InterceptingClientHttpRequest$InterceptingRequestExecution.execute')
f(32,3568,2,1,'org/springframework/http/client/AbstractClientHttpRequest.execute')
f(33,3568,2,1,'org/springframework/http/client/AbstractBufferingClientHttpRequest.executeInternal')
f(34,3568,2,1,'org/springframework/http/client/HttpComponentsClientHttpRequest.executeInternal')
f(35,3568,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(36,3568,2,1,'org/apache/http/impl/client/CloseableHttpClient.execute')
f(37,3568,2,6,'org/apache/http/impl/client/InternalHttpClient.doExecute',0,1,0)
f(38,3569,1,1,'org/apache/http/impl/execchain/RedirectExec.execute')
f(39,3569,1,1,'org/apache/http/impl/execchain/ProtocolExec.execute')
f(40,3569,1,1,'org/apache/http/impl/execchain/MainClientExec.execute')
f(41,3569,1,1,'org/apache/http/protocol/HttpRequestExecutor.execute')
f(42,3569,1,1,'org/apache/http/protocol/HttpRequestExecutor.doReceiveResponse')
f(43,3569,1,1,'org/apache/http/impl/conn/CPoolProxy.receiveResponseEntity')
f(44,3569,1,1,'org/apache/http/impl/DefaultBHttpClientConnection.receiveResponseEntity')
f(45,3569,1,1,'org/apache/http/impl/BHttpConnectionBase.prepareInput')
f(46,3569,1,1,'org/apache/http/impl/entity/LaxContentLengthStrategy.determineLength')
f(47,3569,1,1,'org/apache/http/message/BufferedHeader.getElements')
f(48,3569,1,1,'org/apache/http/message/BasicHeaderValueParser.parseElements')
f(49,3569,1,1,'org/apache/http/message/BasicHeaderValueParser.parseHeaderElement')
f(50,3569,1,6,'org/apache/http/message/BasicHeaderValueParser.parseNameValuePair',0,1,0)
f(24,3570,1,1,'org/springframework/web/client/RestTemplate$HttpEntityRequestCallback.doWithRequest')
f(25,3570,1,1,'org/springframework/web/client/RestTemplate$AcceptHeaderRequestCallback.doWithRequest')
f(26,3570,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.canRead')
f(27,3570,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.canRead')
f(28,3570,1,1,'org/springframework/http/converter/json/AbstractJackson2HttpMessageConverter.getJavaType')
f(29,3570,1,1,'com/fasterxml/jackson/databind/type/TypeFactory.constructType')
f(30,3570,1,1,'com/fasterxml/jackson/databind/type/TypeFactory._fromAny')
f(31,3570,1,1,'com/fasterxml/jackson/databind/type/TypeFactory._fromClass')
f(32,3570,1,1,'com/fasterxml/jackson/databind/util/LRUMap.get')
f(33,3570,1,6,'java/util/concurrent/ConcurrentHashMap.get',0,1,0)
f(19,3571,2,1,'com/tapdata/mongo/RestTemplateOperator$RetryInfo.<init>')
f(20,3571,2,1,'java/util/UUID.randomUUID')
f(21,3571,2,1,'java/security/SecureRandom.nextBytes')
f(22,3571,2,1,'sun/security/provider/NativePRNG.engineNextBytes')
f(23,3571,2,1,'sun/security/provider/NativePRNG$RandomIO.access$400')
f(24,3571,2,1,'sun/security/provider/NativePRNG$RandomIO.implNextBytes')
f(25,3571,2,6,'sun/security/provider/SecureRandom.engineNextBytes',0,1,0)
f(26,3572,1,1,'java/security/MessageDigest.update')
f(27,3572,1,6,'java/security/MessageDigest$Delegate.engineUpdate',0,1,0)
f(19,3573,1,1,'java/util/Optional.map')
f(20,3573,1,1,'com/tapdata/mongo/RestTemplateOperator$$Lambda$303/490475818.apply')
f(21,3573,1,6,'io/tapdata/Schedule/ConnectorManager$$Lambda$191/**********.get',0,1,0)
f(4,3574,3,1,'java/util/concurrent/ThreadPoolExecutor.getTask',0,1,0)
f(5,3575,2,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(6,3575,2,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(7,3575,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.await')
f(8,3575,1,1,'java/util/concurrent/locks/LockSupport.park')
f(9,3575,1,1,'sun/misc/Unsafe.park')
f(10,3575,1,3,'Unsafe_Park')
f(11,3575,1,4,'Parker::park(bool, long)')
f(12,3575,1,3,'__psynch_cvwait')
f(7,3576,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject.awaitNanos')
f(8,3576,1,1,'java/util/concurrent/locks/LockSupport.parkNanos')
f(9,3576,1,1,'sun/misc/Unsafe.park')
f(10,3576,1,3,'Unsafe_Park')
f(11,3576,1,4,'Parker::park(bool, long)')
f(12,3576,1,3,'__psynch_cvwait')
f(2,3577,386,1,'org/apache/kafka/clients/producer/internals/Sender.run',0,1,0)
f(3,3578,385,1,'org/apache/kafka/clients/producer/internals/Sender.runOnce',0,1,0)
f(4,3579,240,1,'org/apache/kafka/clients/NetworkClient.poll',0,2,0)
f(5,3581,1,6,'org/apache/kafka/clients/NetworkClient$DefaultMetadataUpdater.maybeUpdate',0,1,0)
f(5,3582,25,1,'org/apache/kafka/clients/NetworkClient.completeResponses')
f(6,3582,1,6,'java/util/ArrayList$Itr.next',0,1,0)
f(6,3583,24,1,'org/apache/kafka/clients/ClientResponse.onComplete')
f(7,3583,24,1,'org/apache/kafka/clients/producer/internals/Sender$$Lambda$3157/429953851.onComplete')
f(8,3583,24,1,'org/apache/kafka/clients/producer/internals/Sender.lambda$sendProduceRequest$5')
f(9,3583,24,1,'org/apache/kafka/clients/producer/internals/Sender.handleProduceResponse')
f(10,3583,23,1,'java/lang/Iterable.forEach',0,1,0)
f(11,3584,21,1,'org/apache/kafka/clients/producer/internals/Sender$$Lambda$3160/351292150.accept')
f(12,3584,21,1,'org/apache/kafka/clients/producer/internals/Sender.lambda$handleProduceResponse$2')
f(13,3584,21,1,'java/util/ArrayList.forEach')
f(14,3584,21,1,'org/apache/kafka/clients/producer/internals/Sender$$Lambda$3161/1720623670.accept')
f(15,3584,21,1,'org/apache/kafka/clients/producer/internals/Sender.lambda$null$1',0,1,0)
f(16,3584,1,1,'java/util/Collection.stream')
f(17,3584,1,6,'java/util/ArrayList.spliterator',0,1,0)
f(16,3585,1,1,'java/util/HashMap.get')
f(17,3585,1,1,'java/util/HashMap.getNode')
f(18,3585,1,6,'org/apache/kafka/common/TopicPartition.equals',0,1,0)
f(19,3585,1,2,'java/util/Objects.equals',1,0,0)
f(16,3586,1,1,'java/util/stream/ReferencePipeline.collect')
f(17,3586,1,1,'java/util/stream/AbstractPipeline.evaluate')
f(18,3586,1,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(19,3586,1,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(20,3586,1,1,'java/util/stream/AbstractPipeline.copyInto')
f(21,3586,1,6,'java/util/Spliterator.getExactSizeIfKnown',0,1,0)
f(16,3587,1,2,'java/util/stream/ReferencePipeline.map',1,0,0)
f(17,3587,1,2,'java/util/stream/ReferencePipeline$3.<init>',1,0,0)
f(16,3588,16,1,'org/apache/kafka/clients/producer/internals/Sender.completeBatch',0,1,0)
f(17,3588,1,2,'org/apache/kafka/clients/producer/internals/RecordAccumulator.unmutePartition',1,0,0)
f(17,3589,15,1,'org/apache/kafka/clients/producer/internals/Sender.completeBatch')
f(18,3589,5,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.complete')
f(19,3589,5,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.done',0,1,0)
f(20,3589,1,6,'java/lang/Long.valueOf',0,1,0)
f(20,3590,1,2,'java/lang/StringBuilder.append',1,0,0)
f(20,3591,3,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.completeFutureAndFireCallbacks')
f(21,3591,1,6,'java/util/ArrayList.get',0,1,0)
f(21,3592,2,1,'org/apache/kafka/clients/producer/KafkaProducer$InterceptorCallback.onCompletion')
f(22,3592,1,1,'io/tapdata/connector/kafka/KafkaService$$Lambda$3140/1766144059.onCompletion')
f(23,3592,1,1,'io/tapdata/connector/kafka/KafkaService.lambda$produce$5')
f(24,3592,1,1,'java/util/concurrent/CountDownLatch.countDown')
f(25,3592,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.releaseShared')
f(26,3592,1,6,'java/util/concurrent/CountDownLatch$Sync.tryReleaseShared',0,1,0)
f(22,3593,1,6,'org/apache/kafka/clients/producer/internals/ProducerInterceptors.onAcknowledgement',0,1,0)
f(18,3594,2,1,'org/apache/kafka/clients/producer/internals/Sender.maybeRemoveAndDeallocateBatch')
f(19,3594,2,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.deallocate')
f(20,3594,1,1,'org/apache/kafka/clients/producer/internals/BufferPool.deallocate')
f(21,3594,1,1,'java/util/concurrent/locks/ReentrantLock.unlock')
f(22,3594,1,6,'java/util/concurrent/locks/AbstractQueuedSynchronizer.release',0,1,0)
f(20,3595,1,1,'org/apache/kafka/clients/producer/internals/IncompleteBatches.remove')
f(21,3595,1,1,'java/util/HashSet.remove')
f(22,3595,1,1,'java/util/HashMap.remove')
f(23,3595,1,6,'java/util/HashMap.removeNode',0,1,0)
f(18,3596,8,1,'org/apache/kafka/clients/producer/internals/TransactionManager.handleCompletedBatch')
f(19,3596,3,1,'org/apache/kafka/clients/producer/internals/TransactionManager.maybeUpdateLastAckedSequence')
f(20,3596,3,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1300')
f(21,3596,3,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.getPartition')
f(22,3596,3,1,'java/util/HashMap.get',0,1,0)
f(23,3596,2,1,'java/util/HashMap.getNode')
f(24,3596,2,1,'org/apache/kafka/common/TopicPartition.equals')
f(25,3596,2,1,'java/util/Objects.equals')
f(26,3596,2,6,'java/lang/String.equals',0,2,0)
f(23,3598,1,2,'java/util/HashMap.hash',1,0,0)
f(24,3598,1,3,'vtable stub')
f(19,3599,4,1,'org/apache/kafka/clients/producer/internals/TransactionManager.removeInFlightBatch')
f(20,3599,3,1,'java/util/TreeSet.remove')
f(21,3599,3,1,'java/util/TreeMap.remove')
f(22,3599,1,1,'java/util/TreeMap.deleteEntry')
f(23,3599,1,6,'java/util/TreeMap.fixAfterDeletion',0,1,0)
f(22,3600,2,1,'java/util/TreeMap.getEntry')
f(23,3600,2,1,'java/util/TreeMap.getEntryUsingComparator')
f(24,3600,2,6,'java/util/Comparator$$Lambda$2816/1089857848.compare',0,1,0)
f(25,3601,1,1,'java/util/Comparator.lambda$comparingInt$7b0bb60$1')
f(26,3601,1,6,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionEntry$$Lambda$3156/1653008149.applyAsInt',0,1,0)
f(20,3602,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.hasInflightBatches')
f(21,3602,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1200')
f(22,3602,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.getOrCreatePartition')
f(23,3602,1,6,'java/util/HashMap.get',0,1,0)
f(24,3602,1,2,'java/util/HashMap.hash',1,0,0)
f(25,3602,1,3,'vtable stub')
f(19,3603,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.updateLastAckedOffset')
f(20,3603,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1300')
f(21,3603,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.getPartition')
f(22,3603,1,1,'java/util/HashMap.get')
f(23,3603,1,1,'java/util/HashMap.hash')
f(24,3603,1,6,'org/apache/kafka/common/TopicPartition.hashCode',0,1,0)
f(16,3604,1,6,'org/apache/kafka/common/protocol/Errors.forCode',0,1,0)
f(11,3605,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.iterator')
f(12,3605,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.listIterator')
f(13,3605,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.<init>')
f(14,3605,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$HeadElement.next',0,1,0)
f(10,3606,1,1,'org/apache/kafka/clients/producer/internals/Sender$SenderMetrics.recordLatency')
f(11,3606,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(12,3606,1,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(13,3606,1,1,'org/apache/kafka/common/metrics/Sensor$StatAndConfig.config')
f(14,3606,1,6,'org/apache/kafka/common/metrics/Sensor$$Lambda$2814/607772895.get',0,1,0)
f(5,3607,13,1,'org/apache/kafka/clients/NetworkClient.handleCompletedReceives',0,1,0)
f(6,3608,11,1,'org/apache/kafka/clients/NetworkClient.parseResponse')
f(7,3608,11,1,'org/apache/kafka/common/requests/AbstractResponse.parseResponse')
f(8,3608,11,1,'org/apache/kafka/common/requests/AbstractResponse.parseResponse')
f(9,3608,11,1,'org/apache/kafka/common/requests/ProduceResponse.parse')
f(10,3608,11,1,'org/apache/kafka/common/message/ProduceResponseData.<init>')
f(11,3608,11,1,'org/apache/kafka/common/message/ProduceResponseData.read',0,1,0)
f(12,3609,6,1,'org/apache/kafka/common/message/ProduceResponseData$TopicProduceResponse.<init>')
f(13,3609,6,1,'org/apache/kafka/common/message/ProduceResponseData$TopicProduceResponse.read')
f(14,3609,2,1,'org/apache/kafka/common/message/ProduceResponseData$PartitionProduceResponse.<init>')
f(15,3609,2,1,'org/apache/kafka/common/message/ProduceResponseData$PartitionProduceResponse.read')
f(16,3609,2,1,'org/apache/kafka/common/protocol/ByteBufferAccessor.readLong')
f(17,3609,2,1,'java/nio/HeapByteBuffer.getLong')
f(18,3609,2,1,'java/nio/Bits.getLong')
f(19,3609,2,1,'java/nio/Bits.getLongB')
f(20,3609,2,6,'java/nio/HeapByteBuffer._get',0,2,0)
f(14,3611,1,6,'org/apache/kafka/common/protocol/ByteBufferAccessor.readInt',0,1,0)
f(14,3612,3,1,'org/apache/kafka/common/protocol/Readable.readString')
f(15,3612,3,1,'java/lang/String.<init>')
f(16,3612,3,1,'java/lang/String.<init>')
f(17,3612,3,1,'java/lang/StringCoding.decode')
f(18,3612,3,1,'sun/nio/cs/UTF_8.newDecoder',0,1,0)
f(19,3613,2,1,'sun/nio/cs/UTF_8$Decoder.<init>')
f(20,3613,2,1,'sun/nio/cs/UTF_8$Decoder.<init>')
f(21,3613,2,1,'java/nio/charset/CharsetDecoder.<init>')
f(22,3613,2,1,'java/nio/charset/CharsetDecoder.<init>')
f(23,3613,1,1,'java/nio/charset/Charset.atBugLevel')
f(24,3613,1,6,'java/lang/String.equals',0,1,0)
f(23,3614,1,6,'java/nio/charset/CharsetDecoder.replaceWith',0,1,0)
f(24,3614,1,3,'vtable stub')
f(12,3615,4,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.add',0,2,0)
f(13,3616,1,3,'itable stub')
f(13,3617,2,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.addToListTail')
f(14,3617,1,6,'org/apache/kafka/common/message/ProduceResponseData$TopicProduceResponse.setPrev',0,1,0)
f(14,3618,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$HeadElement.setNext',0,1,0)
f(6,3619,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(7,3619,1,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(8,3619,1,6,'java/util/ArrayList$Itr.hasNext',0,1,0)
f(9,3619,1,2,'java/util/ArrayList.access$000',1,0,0)
f(5,3620,2,6,'org/apache/kafka/clients/NetworkClient.handleCompletedSends',0,2,0)
f(5,3622,7,1,'org/apache/kafka/clients/NetworkClient.handleTimedOutConnections')
f(6,3622,7,1,'org/apache/kafka/clients/ClusterConnectionStates.nodesWithConnectionSetupTimeout')
f(7,3622,1,6,'java/util/Collection.stream',0,1,0)
f(8,3622,1,3,'itable stub')
f(7,3623,1,1,'java/util/stream/Collectors.toList')
f(8,3623,1,1,'java/util/stream/Collectors$CollectorImpl.<init>')
f(9,3623,1,6,'java/util/stream/Collectors$CollectorImpl.<init>',0,1,0)
f(7,3624,5,1,'java/util/stream/ReferencePipeline.collect')
f(8,3624,5,1,'java/util/stream/AbstractPipeline.evaluate')
f(9,3624,1,1,'java/util/stream/ReduceOps$3.getOpFlags')
f(10,3624,1,6,'java/util/Collections$UnmodifiableCollection.contains',0,1,0)
f(9,3625,4,1,'java/util/stream/ReduceOps$ReduceOp.evaluateSequential')
f(10,3625,4,1,'java/util/stream/AbstractPipeline.wrapAndCopyInto')
f(11,3625,4,1,'java/util/stream/AbstractPipeline.copyInto')
f(12,3625,4,6,'java/util/stream/ReferencePipeline$2$1.begin',0,2,0)
f(13,3626,1,3,'itable stub')
f(13,3627,2,6,'java/util/stream/ReduceOps$3ReducingSink.begin',0,1,0)
f(14,3628,1,6,'java/util/stream/Collectors$$Lambda$78/944752500.get',0,1,0)
f(5,3629,2,1,'org/apache/kafka/clients/NetworkClient.handleTimedOutRequests')
f(6,3629,2,1,'org/apache/kafka/clients/InFlightRequests.nodesWithTimedOutRequests')
f(7,3629,2,6,'org/apache/kafka/clients/InFlightRequests.hasExpiredRequest',0,1,0)
f(8,3630,1,6,'java/util/ArrayDeque.iterator',0,1,0)
f(5,3631,187,1,'org/apache/kafka/common/network/Selector.poll',0,2,0)
f(6,3633,8,1,'org/apache/kafka/common/metrics/Sensor.record')
f(7,3633,7,1,'org/apache/kafka/common/metrics/Sensor.recordInternal',0,1,0)
f(8,3633,1,3,'itable stub')
f(8,3634,1,6,'java/util/ArrayList$Itr.hasNext',0,1,0)
f(9,3634,1,2,'java/util/ArrayList.access$000',1,0,0)
f(8,3635,2,1,'org/apache/kafka/common/metrics/Sensor$StatAndConfig.config')
f(9,3635,2,6,'org/apache/kafka/common/metrics/Sensor$$Lambda$2817/187731712.get',0,2,0)
f(8,3637,2,1,'org/apache/kafka/common/metrics/Sensor.checkQuotas')
f(9,3637,2,6,'java/util/LinkedHashMap$LinkedHashIterator.hasNext',0,2,0)
f(8,3639,1,1,'org/apache/kafka/common/metrics/stats/Meter.record')
f(9,3639,1,1,'org/apache/kafka/common/metrics/stats/Rate.record')
f(10,3639,1,1,'org/apache/kafka/common/metrics/stats/SampledStat.record')
f(11,3639,1,6,'org/apache/kafka/common/metrics/stats/SampledStat.current',0,1,0)
f(7,3640,1,1,'org/apache/kafka/common/metrics/Sensor.shouldRecord')
f(8,3640,1,6,'org/apache/kafka/common/metrics/Sensor$RecordingLevel.shouldRecord',0,1,0)
f(6,3641,1,1,'org/apache/kafka/common/network/Selector.clear')
f(7,3641,1,6,'java/util/ArrayList$Itr.hasNext',0,1,0)
f(6,3642,99,1,'org/apache/kafka/common/network/Selector.pollSelectionKeys',0,1,0)
f(7,3642,1,1,'org/apache/kafka/common/network/KafkaChannel.isConnected')
f(8,3642,1,1,'org/apache/kafka/common/network/PlaintextTransportLayer.isConnected')
f(9,3642,1,6,'sun/nio/ch/SocketChannelImpl.isConnected',0,1,0)
f(7,3643,1,1,'org/apache/kafka/common/network/Selector$IdleExpiryManager.update')
f(8,3643,1,1,'java/util/HashMap.put')
f(9,3643,1,6,'java/util/HashMap.putVal',0,1,0)
f(7,3644,4,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.maybeRegisterConnectionMetrics',0,1,0)
f(8,3645,3,1,'org/apache/kafka/common/metrics/Metrics.getSensor')
f(9,3645,3,6,'java/util/concurrent/ConcurrentHashMap.get',0,3,0)
f(10,3647,1,3,'vtable stub')
f(7,3648,17,1,'org/apache/kafka/common/network/Selector.attemptRead')
f(8,3648,10,1,'org/apache/kafka/common/network/KafkaChannel.read')
f(9,3648,10,1,'org/apache/kafka/common/network/KafkaChannel.receive')
f(10,3648,10,1,'org/apache/kafka/common/network/NetworkReceive.readFrom')
f(11,3648,10,1,'org/apache/kafka/common/network/PlaintextTransportLayer.read')
f(12,3648,10,1,'sun/nio/ch/SocketChannelImpl.read')
f(13,3648,9,1,'sun/nio/ch/IOUtil.read')
f(14,3648,8,1,'sun/nio/ch/IOUtil.readIntoNativeBuffer')
f(15,3648,8,1,'sun/nio/ch/SocketDispatcher.read')
f(16,3648,8,1,'sun/nio/ch/FileDispatcherImpl.read0')
f(17,3648,8,3,'read')
f(14,3656,1,1,'sun/nio/ch/Util.getTemporaryDirectBuffer')
f(15,3656,1,6,'sun/nio/ch/Util$BufferCache.get',0,1,0)
f(13,3657,1,1,'sun/nio/ch/NativeThread.current')
f(14,3657,1,3,'pthread_self')
f(8,3658,3,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.recordBytesReceived')
f(9,3658,3,1,'org/apache/kafka/common/metrics/Sensor.record')
f(10,3658,3,1,'org/apache/kafka/common/metrics/Sensor.recordInternal',0,1,0)
f(11,3659,1,1,'org/apache/kafka/common/metrics/Sensor.record')
f(12,3659,1,1,'org/apache/kafka/common/metrics/Sensor.recordInternal')
f(13,3659,1,6,'org/apache/kafka/common/metrics/stats/Meter.record',0,1,0)
f(14,3659,1,2,'org/apache/kafka/common/metrics/stats/Rate.record',1,0,0)
f(11,3660,1,6,'org/apache/kafka/common/metrics/stats/Meter.record',0,1,0)
f(8,3661,4,1,'org/apache/kafka/common/network/Selector.addToCompletedReceives')
f(9,3661,1,1,'org/apache/kafka/common/network/Selector$SelectorMetrics.recordCompletedReceive')
f(10,3661,1,1,'org/apache/kafka/common/metrics/Metrics.getSensor')
f(11,3661,1,1,'java/util/concurrent/ConcurrentHashMap.get')
f(12,3661,1,6,'java/lang/String.hashCode',0,1,0)
f(9,3662,3,1,'org/apache/kafka/common/network/Selector.hasCompletedReceive')
f(10,3662,3,6,'java/util/HashMap.containsKey',0,2,0)
f(11,3664,1,6,'java/util/HashMap.getNode',0,1,0)
f(7,3665,74,1,'org/apache/kafka/common/network/Selector.attemptWrite',0,1,0)
f(8,3666,73,1,'org/apache/kafka/common/network/Selector.write')
f(9,3666,73,1,'org/apache/kafka/common/network/KafkaChannel.write')
f(10,3666,73,1,'org/apache/kafka/common/network/NetworkSend.writeTo')
f(11,3666,73,1,'org/apache/kafka/common/network/ByteBufferSend.writeTo')
f(12,3666,73,1,'org/apache/kafka/common/network/PlaintextTransportLayer.write')
f(13,3666,73,1,'java/nio/channels/SocketChannel.write')
f(14,3666,73,1,'sun/nio/ch/SocketChannelImpl.write',0,2,0)
f(15,3667,71,1,'sun/nio/ch/IOUtil.write')
f(16,3667,3,1,'java/nio/DirectByteBuffer.put')
f(17,3667,1,6,'java/nio/Buffer.position',0,1,0)
f(17,3668,2,1,'java/nio/DirectByteBuffer.put')
f(18,3668,2,1,'java/nio/Bits.copyFromArray')
f(19,3668,2,1,'sun/misc/Unsafe.copyMemory')
f(20,3668,2,3,'Unsafe_CopyMemory2')
f(21,3668,1,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(21,3669,1,4,'YieldingFlexibleGangWorker::~YieldingFlexibleGangWorker()')
f(16,3670,68,1,'sun/nio/ch/SocketDispatcher.writev')
f(17,3670,68,1,'sun/nio/ch/FileDispatcherImpl.writev0')
f(18,3670,68,3,'writev')
f(15,3738,1,2,'sun/nio/ch/SocketChannelImpl.writerCleanup',1,0,0)
f(7,3739,1,2,'org/apache/kafka/common/network/Selector.maybeRecordTimePerConnection',1,0,0)
f(7,3740,1,1,'sun/nio/ch/Util$3.iterator')
f(8,3740,1,1,'java/util/HashSet.iterator')
f(9,3740,1,6,'java/util/HashMap$KeySet.iterator',0,1,0)
f(6,3741,73,1,'org/apache/kafka/common/network/Selector.select',0,1,0)
f(7,3742,55,1,'sun/nio/ch/SelectorImpl.select',0,1,0)
f(8,3743,54,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(9,3743,54,1,'sun/nio/ch/KQueueSelectorImpl.doSelect',0,1,0)
f(10,3744,2,6,'java/nio/channels/spi/AbstractSelector.begin',0,1,0)
f(11,3744,1,1,'java/lang/Thread.isInterrupted')
f(12,3744,1,1,'java/lang/Thread.isInterrupted')
f(13,3744,1,3,'JVM_IsInterrupted')
f(14,3744,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(11,3745,1,2,'java/nio/channels/spi/AbstractInterruptibleChannel.blockedOn',1,0,0)
f(12,3745,1,2,'java/lang/System$2.blockedOn',1,0,0)
f(13,3745,1,2,'java/lang/Thread.blockedOn',1,0,0)
f(10,3746,39,1,'sun/nio/ch/KQueueArrayWrapper.poll')
f(11,3746,34,1,'sun/nio/ch/KQueueArrayWrapper.kevent0')
f(12,3746,1,3,'Java_sun_nio_ch_KQueueArrayWrapper_kevent0')
f(12,3747,33,3,'kevent')
f(11,3780,5,1,'sun/nio/ch/KQueueArrayWrapper.updateRegistrations')
f(12,3780,5,1,'sun/nio/ch/KQueueArrayWrapper.register0')
f(13,3780,5,3,'kevent')
f(10,3785,11,6,'sun/nio/ch/KQueueSelectorImpl.updateSelectedKeys',0,6,0)
f(11,3791,2,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(12,3791,2,4,'ObjectMonitor::enter(Thread*)')
f(13,3791,1,4,'ObjectMonitor::EnterI(Thread*)')
f(14,3791,1,4,'os::PlatformEvent::park(long)')
f(15,3791,1,3,'__psynch_cvwait')
f(13,3792,1,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(11,3793,2,2,'sun/nio/ch/IOUtil.drain',1,0,0)
f(12,3793,1,3,'Java_sun_nio_ch_IOUtil_drain')
f(12,3794,1,3,'read')
f(11,3795,1,6,'sun/nio/ch/SocketChannelImpl.translateAndSetReadyOps',0,1,0)
f(10,3796,1,6,'sun/nio/ch/SelectorImpl.processDeregisterQueue',0,1,0)
f(7,3797,17,1,'sun/nio/ch/SelectorImpl.selectNow')
f(8,3797,17,1,'sun/nio/ch/SelectorImpl.lockAndDoSelect')
f(9,3797,17,1,'sun/nio/ch/KQueueSelectorImpl.doSelect')
f(10,3797,15,1,'sun/nio/ch/KQueueArrayWrapper.poll')
f(11,3797,3,1,'sun/nio/ch/KQueueArrayWrapper.kevent0')
f(12,3798,2,3,'kevent')
f(11,3800,12,1,'sun/nio/ch/KQueueArrayWrapper.updateRegistrations')
f(12,3800,12,1,'sun/nio/ch/KQueueArrayWrapper.register0')
f(13,3800,12,3,'kevent')
f(10,3812,2,1,'sun/nio/ch/KQueueSelectorImpl.updateSelectedKeys')
f(11,3812,2,6,'java/util/HashSet.add',0,1,0)
f(12,3813,1,1,'java/util/HashMap.put')
f(13,3813,1,1,'java/util/HashMap.putVal')
f(14,3813,1,6,'java/util/HashMap.newNode',0,1,0)
f(6,3814,2,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,2,0)
f(7,3814,2,4,'os::javaTimeMillis()')
f(8,3814,2,3,'gettimeofday')
f(9,3814,2,3,'__commpage_gettimeofday_internal')
f(10,3814,2,3,'mach_absolute_time')
f(6,3816,2,6,'org/apache/kafka/common/utils/SystemTime.nanoseconds',0,2,0)
f(7,3817,1,4,'os::javaTimeNanos()')
f(8,3817,1,3,'mach_absolute_time')
f(5,3818,1,6,'org/apache/kafka/common/utils/Utils.min',0,1,0)
f(4,3819,2,6,'org/apache/kafka/clients/producer/internals/Sender.maybeSendAndPollTransactionalRequest',0,2,0)
f(5,3819,2,2,'org/apache/kafka/clients/producer/internals/RecordAccumulator.hasIncomplete',2,0,0)
f(6,3819,2,2,'org/apache/kafka/clients/producer/internals/IncompleteBatches.isEmpty',2,0,0)
f(4,3821,141,1,'org/apache/kafka/clients/producer/internals/Sender.sendProducerData')
f(5,3821,1,1,'java/util/ArrayList.addAll')
f(6,3821,1,1,'java/util/ArrayList.toArray')
f(7,3821,1,1,'java/util/Arrays.copyOf')
f(8,3821,1,6,'java/util/Arrays.copyOf',0,1,0)
f(5,3822,1,6,'java/util/ArrayList.iterator',0,1,0)
f(5,3823,1,1,'java/util/HashMap$HashIterator.remove')
f(6,3823,1,1,'java/util/HashMap.hash')
f(7,3823,1,6,'org/apache/kafka/common/Node.hashCode',0,1,0)
f(5,3824,1,1,'java/util/HashSet.iterator')
f(6,3824,1,6,'java/util/HashMap$KeySet.iterator',0,1,0)
f(5,3825,6,1,'org/apache/kafka/clients/NetworkClient.ready')
f(6,3825,6,1,'org/apache/kafka/clients/NetworkClient.isReady')
f(7,3825,3,1,'org/apache/kafka/clients/NetworkClient$DefaultMetadataUpdater.isUpdateDue',0,1,0)
f(8,3826,2,6,'org/apache/kafka/clients/Metadata.timeToNextUpdate',0,2,0)
f(7,3828,3,1,'org/apache/kafka/clients/NetworkClient.canSendRequest')
f(8,3828,2,1,'org/apache/kafka/clients/InFlightRequests.canSendMore')
f(9,3828,1,6,'java/util/ArrayDeque.isEmpty',0,1,0)
f(9,3829,1,6,'java/util/HashMap.get',0,1,0)
f(8,3830,1,1,'org/apache/kafka/common/network/Selector.isChannelReady')
f(9,3830,1,1,'java/util/HashMap.get')
f(10,3830,1,1,'java/util/HashMap.getNode')
f(11,3830,1,6,'java/lang/String.equals',0,1,0)
f(5,3831,72,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.drain')
f(6,3831,72,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.drainBatchesForOneNode',0,1,0)
f(7,3831,1,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(8,3831,1,4,'ObjectMonitor::enter(Thread*)')
f(9,3831,1,4,'ObjectMonitor::EnterI(Thread*)')
f(10,3831,1,4,'os::PlatformEvent::park(long)')
f(11,3831,1,3,'__psynch_cvwait')
f(7,3832,1,1,'java/util/ArrayList.add')
f(8,3832,1,1,'java/util/ArrayList.ensureCapacityInternal')
f(9,3832,1,1,'java/util/ArrayList.ensureExplicitCapacity')
f(10,3832,1,1,'java/util/ArrayList.grow')
f(11,3832,1,1,'java/util/Arrays.copyOf')
f(12,3832,1,6,'java/util/Arrays.copyOf',0,1,0)
f(7,3833,61,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.close')
f(8,3833,1,6,'org/apache/kafka/common/record/CompressionRatioEstimator.updateEstimation',0,1,0)
f(8,3834,60,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.close')
f(9,3834,24,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.closeForRecordAppends')
f(10,3834,24,1,'java/io/FilterOutputStream.close')
f(11,3834,2,1,'java/io/DataOutputStream.flush')
f(12,3834,2,1,'java/io/BufferedOutputStream.flush')
f(13,3834,2,1,'java/io/BufferedOutputStream.flushBuffer')
f(14,3834,2,1,'java/util/zip/GZIPOutputStream.write')
f(15,3834,1,6,'java/util/zip/CRC32.update',0,1,0)
f(16,3834,1,3,'updateBytesCRC32')
f(15,3835,1,1,'java/util/zip/DeflaterOutputStream.write')
f(16,3835,1,1,'java/util/zip/DeflaterOutputStream.deflate')
f(17,3835,1,1,'java/util/zip/Deflater.deflate')
f(18,3835,1,1,'java/util/zip/Deflater.deflate')
f(19,3835,1,1,'java/util/zip/Deflater.deflateBytes')
f(20,3835,1,3,'Java_java_util_zip_Deflater_deflateBytes')
f(21,3835,1,3,'deflate')
f(22,3835,1,3,'deflateCopy')
f(23,3835,1,3,'crc32_combine')
f(24,3835,1,3,'_platform_memmove$VARIANT$Rosetta')
f(11,3836,22,1,'java/io/FilterOutputStream.close')
f(12,3836,22,1,'java/util/zip/DeflaterOutputStream.close')
f(13,3836,22,1,'java/util/zip/GZIPOutputStream.finish')
f(14,3836,20,1,'java/util/zip/Deflater.deflate')
f(15,3836,20,1,'java/util/zip/Deflater.deflate')
f(16,3836,20,1,'java/util/zip/Deflater.deflateBytes')
f(17,3836,20,3,'Java_java_util_zip_Deflater_deflateBytes')
f(18,3836,1,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(18,3837,19,3,'deflate')
f(19,3837,19,3,'deflateCopy')
f(20,3838,1,3,'deflateTune')
f(21,3838,1,3,'_platform_memmove$VARIANT$Rosetta')
f(20,3839,17,3,'inflateCodesUsed')
f(21,3839,17,3,'inflateCodesUsed')
f(22,3854,2,3,'inflateCodesUsed')
f(14,3856,2,1,'org/apache/kafka/common/utils/ByteBufferOutputStream.write')
f(15,3856,2,6,'java/nio/HeapByteBuffer.put',0,2,0)
f(16,3856,2,3,'jbyte_disjoint_arraycopy')
f(9,3858,36,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.writeDefaultBatchHeader')
f(10,3858,36,1,'org/apache/kafka/common/record/DefaultRecordBatch.writeHeader')
f(11,3858,2,1,'java/nio/HeapByteBuffer.putInt')
f(12,3858,2,1,'java/nio/Bits.putInt')
f(13,3858,2,1,'java/nio/Bits.putIntB')
f(14,3858,2,6,'java/nio/HeapByteBuffer._put',0,2,0)
f(11,3860,34,1,'org/apache/kafka/common/utils/Crc32C.compute')
f(12,3860,34,1,'org/apache/kafka/common/utils/Checksums.update')
f(13,3860,34,6,'org/apache/kafka/common/utils/PureJavaCrc32C.update',0,34,0)
f(7,3894,1,1,'org/apache/kafka/clients/producer/internals/ProducerBatch.estimatedSizeInBytes')
f(8,3894,1,1,'org/apache/kafka/common/record/MemoryRecordsBuilder.estimatedSizeInBytes')
f(9,3894,1,6,'org/apache/kafka/common/record/MemoryRecordsBuilder.estimatedBytesWritten',0,1,0)
f(7,3895,1,2,'org/apache/kafka/clients/producer/internals/ProducerBatch.setProducerState',1,0,0)
f(7,3896,2,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.getDeque')
f(8,3896,2,6,'org/apache/kafka/common/utils/CopyOnWriteMap.get',0,1,0)
f(9,3897,1,1,'java/util/Collections$UnmodifiableMap.get')
f(10,3897,1,1,'java/util/HashMap.get')
f(11,3897,1,1,'java/util/HashMap.getNode')
f(12,3897,1,1,'org/apache/kafka/common/TopicPartition.equals')
f(13,3897,1,1,'java/util/Objects.equals')
f(14,3897,1,6,'java/lang/String.equals',0,1,0)
f(7,3898,2,6,'org/apache/kafka/clients/producer/internals/RecordAccumulator.shouldStopDrainBatchesForPartition',0,1,0)
f(8,3898,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.firstInFlightSequence')
f(9,3898,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.hasInflightBatches')
f(10,3898,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1200')
f(11,3898,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.getOrCreatePartition')
f(12,3898,1,1,'java/util/HashMap.get')
f(13,3898,1,1,'java/util/HashMap.getNode')
f(14,3898,1,1,'org/apache/kafka/common/TopicPartition.equals')
f(15,3898,1,1,'java/util/Objects.equals')
f(16,3898,1,6,'java/lang/String.equals',0,1,0)
f(8,3899,1,2,'org/apache/kafka/clients/producer/internals/TransactionManager.hasStaleProducerIdAndEpoch',1,0,0)
f(9,3899,1,2,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1200',1,0,0)
f(7,3900,2,1,'org/apache/kafka/clients/producer/internals/TransactionManager.addInFlightBatch')
f(8,3900,2,1,'java/util/TreeSet.add')
f(9,3900,2,6,'java/util/TreeMap.put',0,1,0)
f(10,3901,1,6,'java/util/TreeMap.fixAfterInsertion',0,1,0)
f(7,3902,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.incrementSequenceNumber')
f(8,3902,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.access$1300')
f(9,3902,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager$TopicPartitionBookkeeper.getPartition')
f(10,3902,1,6,'java/util/HashMap.get',0,1,0)
f(5,3903,3,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.expiredBatches')
f(6,3903,1,6,'java/util/ArrayDeque.isEmpty',0,1,0)
f(6,3904,1,1,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.next')
f(7,3904,1,1,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1.next')
f(8,3904,1,6,'java/util/HashMap$EntryIterator.next',0,1,0)
f(6,3905,1,6,'org/apache/kafka/clients/producer/internals/RecordAccumulator.maybeUpdateNextBatchExpiryTime',0,1,0)
f(5,3906,24,1,'org/apache/kafka/clients/producer/internals/RecordAccumulator.ready',0,2,0)
f(6,3907,1,4,'Arena::Amalloc_4(unsigned long, AllocFailStrategy::AllocFailEnum)')
f(6,3908,6,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(7,3908,6,4,'ObjectMonitor::enter(Thread*)')
f(8,3908,6,4,'ObjectMonitor::EnterI(Thread*)')
f(9,3908,1,4,'YieldingFlexibleGangWorker::~YieldingFlexibleGangWorker()')
f(9,3909,5,4,'os::PlatformEvent::park(long)')
f(10,3909,5,3,'__psynch_cvwait')
f(6,3914,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(7,3914,1,4,'ExceptionMark::ExceptionMark(Thread*&)')
f(6,3915,1,6,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry.getKey',0,1,0)
f(6,3916,1,6,'java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet.iterator',0,1,0)
f(6,3917,2,1,'java/util/HashSet.add')
f(7,3917,2,6,'java/util/HashMap.put',0,1,0)
f(8,3917,1,2,'java/util/HashMap.hash',1,0,0)
f(9,3917,1,3,'vtable stub')
f(8,3918,1,1,'java/util/HashMap.putVal')
f(9,3918,1,6,'java/util/HashMap.newNode',0,1,0)
f(6,3919,1,1,'java/util/HashSet.contains')
f(7,3919,1,6,'java/util/HashMap.containsKey',0,1,0)
f(8,3919,1,2,'java/util/HashMap.hash',1,0,0)
f(9,3919,1,3,'vtable stub')
f(6,3920,1,3,'monitorenter_nofpu Runtime1 stub')
f(6,3921,1,6,'org/apache/kafka/clients/producer/internals/BufferPool.queued',0,1,0)
f(7,3921,1,2,'java/util/concurrent/locks/ReentrantLock.lock',1,0,0)
f(6,3922,5,1,'org/apache/kafka/common/Cluster.leaderFor')
f(7,3922,5,1,'java/util/Collections$UnmodifiableMap.get')
f(8,3922,5,6,'java/util/HashMap.get',0,3,0)
f(9,3923,1,1,'java/util/HashMap.getNode')
f(10,3923,1,1,'org/apache/kafka/common/TopicPartition.equals')
f(11,3923,1,1,'java/util/Objects.equals')
f(12,3923,1,6,'java/lang/String.equals',0,1,0)
f(9,3924,3,2,'java/util/HashMap.hash',2,0,0)
f(10,3925,1,6,'org/apache/kafka/common/TopicPartition.hashCode',0,1,0)
f(10,3926,1,3,'vtable stub')
f(6,3927,3,1,'org/apache/kafka/common/utils/CopyOnWriteMap.entrySet',0,1,0)
f(7,3927,1,3,'itable stub')
f(7,3928,2,6,'java/util/Collections$UnmodifiableMap.entrySet',0,2,0)
f(5,3930,14,1,'org/apache/kafka/clients/producer/internals/Sender$SenderMetrics.updateProduceRequestMetrics',0,2,0)
f(6,3930,1,2,'java/lang/StringBuilder.append',1,0,0)
f(6,3931,1,1,'org/apache/kafka/clients/producer/internals/SenderMetricsRegistry.getSensor')
f(7,3931,1,1,'org/apache/kafka/common/metrics/Metrics.getSensor')
f(8,3931,1,1,'java/util/concurrent/ConcurrentHashMap.get')
f(9,3931,1,6,'java/lang/String.equals',0,1,0)
f(6,3932,11,1,'org/apache/kafka/common/metrics/Sensor.record',1,0,0)
f(7,3932,9,1,'org/apache/kafka/common/metrics/Sensor.recordInternal',0,1,0)
f(8,3932,1,2,'org/apache/kafka/common/metrics/Sensor$StatAndConfig.config',1,0,0)
f(9,3932,1,3,'itable stub')
f(8,3933,3,1,'org/apache/kafka/common/metrics/Sensor.checkQuotas')
f(9,3933,2,1,'java/util/LinkedHashMap$LinkedValueIterator.next')
f(10,3933,2,6,'java/util/LinkedHashMap$LinkedHashIterator.nextNode',0,2,0)
f(9,3935,1,6,'java/util/LinkedHashMap$LinkedValues.iterator',0,1,0)
f(8,3936,5,1,'org/apache/kafka/common/metrics/stats/SampledStat.record',0,2,0)
f(9,3937,3,6,'org/apache/kafka/common/metrics/stats/SampledStat.current',0,3,0)
f(10,3939,1,2,'org/apache/kafka/common/metrics/stats/SampledStat.newSample',1,0,0)
f(9,3940,1,3,'vtable stub')
f(7,3941,1,2,'org/apache/kafka/common/metrics/Sensor.shouldRecord',1,0,0)
f(7,3942,1,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,1,0)
f(8,3942,1,4,'YieldingFlexibleGangWorker::~YieldingFlexibleGangWorker()')
f(6,3943,1,6,'org/apache/kafka/common/utils/SystemTime.milliseconds',0,1,0)
f(7,3943,1,4,'os::javaTimeMillis()')
f(8,3943,1,3,'gettimeofday')
f(9,3943,1,3,'__commpage_gettimeofday_internal')
f(5,3944,17,1,'org/apache/kafka/clients/producer/internals/Sender.sendProduceRequests',0,2,0)
f(6,3945,1,3,'itable stub')
f(6,3946,15,1,'org/apache/kafka/clients/producer/internals/Sender.sendProduceRequest')
f(7,3946,1,1,'java/util/HashMap.<init>')
f(8,3946,1,6,'java/util/HashMap.<init>',0,1,0)
f(7,3947,6,1,'org/apache/kafka/clients/NetworkClient.send')
f(8,3947,6,1,'org/apache/kafka/clients/NetworkClient.doSend')
f(9,3947,5,1,'org/apache/kafka/clients/NetworkClient.doSend')
f(10,3947,1,1,'org/apache/kafka/clients/InFlightRequests.add')
f(11,3947,1,1,'java/util/HashMap.get')
f(12,3947,1,1,'java/util/HashMap.hash')
f(13,3947,1,6,'java/lang/String.hashCode',0,1,0)
f(10,3948,4,1,'org/apache/kafka/common/requests/AbstractRequest.toSend')
f(11,3948,4,1,'org/apache/kafka/common/protocol/SendBuilder.buildRequestSend')
f(12,3948,4,1,'org/apache/kafka/common/protocol/SendBuilder.buildSend')
f(13,3948,2,1,'org/apache/kafka/common/message/ProduceRequestData.addSize')
f(14,3948,1,1,'org/apache/kafka/common/message/ProduceRequestData$TopicProduceData.addSize')
f(15,3948,1,6,'org/apache/kafka/common/message/ProduceRequestData$PartitionProduceData.addSize',0,1,0)
f(14,3949,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.next')
f(15,3949,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.next',0,1,0)
f(16,3949,1,3,'itable stub')
f(13,3950,2,6,'org/apache/kafka/common/message/RequestHeaderData.addSize',0,1,0)
f(14,3950,2,2,'java/lang/String.getBytes',1,0,0)
f(15,3951,1,1,'java/lang/StringCoding.encode')
f(16,3951,1,1,'sun/nio/cs/UTF_8.newEncoder')
f(17,3951,1,1,'sun/nio/cs/UTF_8$Encoder.<init>')
f(18,3951,1,1,'sun/nio/cs/UTF_8$Encoder.<init>')
f(19,3951,1,1,'java/nio/charset/CharsetEncoder.<init>')
f(20,3951,1,1,'java/nio/charset/CharsetEncoder.<init>')
f(21,3951,1,6,'java/nio/charset/CharsetEncoder.replaceWith',0,1,0)
f(9,3952,1,1,'org/apache/kafka/common/requests/ProduceRequest$Builder.build')
f(10,3952,1,1,'org/apache/kafka/common/requests/ProduceRequest$Builder.build')
f(11,3952,1,1,'org/apache/kafka/common/requests/ProduceRequest$Builder.build')
f(12,3952,1,1,'java/lang/Iterable.forEach')
f(13,3952,1,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.next')
f(14,3952,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$ImplicitLinkedHashCollectionIterator.next',0,1,0)
f(7,3953,2,1,'org/apache/kafka/common/record/AbstractRecords.hasMatchingMagic')
f(8,3953,2,1,'org/apache/kafka/common/utils/AbstractIterator.hasNext')
f(9,3953,2,1,'org/apache/kafka/common/utils/AbstractIterator.maybeComputeNext')
f(10,3953,2,1,'org/apache/kafka/common/record/RecordBatchIterator.makeNext')
f(11,3953,2,1,'org/apache/kafka/common/record/RecordBatchIterator.makeNext')
f(12,3953,2,1,'org/apache/kafka/common/record/ByteBufferLogInputStream.nextBatch')
f(13,3953,2,6,'org/apache/kafka/common/record/ByteBufferLogInputStream.nextBatch',0,1,0)
f(14,3954,1,1,'org/apache/kafka/common/record/ByteBufferLogInputStream.nextBatchSize')
f(15,3954,1,1,'java/nio/HeapByteBuffer.getInt')
f(16,3954,1,1,'java/nio/Bits.getInt')
f(17,3954,1,1,'java/nio/Bits.getIntB')
f(18,3954,1,6,'java/nio/HeapByteBuffer._get',0,1,0)
f(7,3955,6,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.add',0,1,0)
f(8,3956,1,6,'org/apache/kafka/common/message/ProduceRequestData$TopicProduceData.next',0,1,0)
f(8,3957,1,6,'org/apache/kafka/common/message/ProduceRequestData$TopicProduceData.prev',0,1,0)
f(8,3958,3,1,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection.addToListTail',0,1,0)
f(9,3958,1,3,'itable stub')
f(9,3959,1,6,'org/apache/kafka/common/message/ProduceRequestData$TopicProduceData.setPrev',0,1,0)
f(9,3960,1,6,'org/apache/kafka/common/utils/ImplicitLinkedHashCollection$HeadElement.setNext',0,1,0)
f(5,3961,1,1,'org/apache/kafka/common/utils/LogContext$LocationAwareKafkaLogger.trace')
f(6,3961,1,1,'org/apache/logging/slf4j/Log4jLogger.isTraceEnabled')
f(7,3961,1,6,'org/apache/logging/log4j/core/Logger.isEnabled',0,1,0)
f(4,3962,1,1,'org/apache/kafka/clients/producer/internals/TransactionManager.maybeResolveSequences')
f(5,3962,1,1,'java/util/HashMap$KeySet.iterator')
f(6,3962,1,1,'java/util/HashMap$KeyIterator.<init>')
f(7,3962,1,6,'java/util/HashMap$HashIterator.<init>',0,1,0)
f(1,3963,8,1,'java/lang/ref/Finalizer$FinalizerThread.run')
f(2,3963,8,1,'java/lang/ref/ReferenceQueue.remove')
f(3,3963,8,1,'java/lang/ref/ReferenceQueue.remove')
f(4,3964,4,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(5,3964,4,4,'ObjectMonitor::enter(Thread*)')
f(6,3964,1,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(6,3965,3,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(7,3967,1,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(4,3968,2,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(5,3968,1,4,'ExceptionMark::ExceptionMark(Thread*&)')
f(5,3969,1,4,'ObjectSynchronizer::fast_exit(oopDesc*, BasicLock*, Thread*)')
f(6,3969,1,4,'ObjectSynchronizer::inflate(Thread*, oopDesc*)')
f(4,3970,1,1,'java/lang/Object.wait')
f(5,3970,1,3,'JVM_MonitorWait')
f(6,3970,1,4,'ObjectSynchronizer::wait(Handle, long, Thread*)')
f(7,3970,1,4,'ObjectMonitor::wait(long, bool, Thread*)')
f(8,3970,1,4,'os::PlatformEvent::park()')
f(9,3970,1,3,'__psynch_cvwait')
f(1,3971,9,1,'java/lang/ref/Reference$ReferenceHandler.run')
f(2,3971,9,1,'java/lang/ref/Reference.tryHandlePending')
f(3,3971,9,1,'java/lang/ref/ReferenceQueue.enqueue')
f(4,3972,3,4,'Runtime1::monitorenter(JavaThread*, oopDesc*, BasicObjectLock*)')
f(5,3972,1,4,'JavaThreadBlockedOnMonitorEnterState::JavaThreadBlockedOnMonitorEnterState(JavaThread*, ObjectMonitor*)')
f(5,3973,2,4,'ObjectMonitor::enter(Thread*)')
f(6,3973,1,4,'ObjectMonitor::NotRunnable(Thread*, Thread*)')
f(6,3974,1,4,'ObjectMonitor::TrySpin_VaryDuration(Thread*)')
f(4,3975,1,4,'Runtime1::monitorexit(JavaThread*, BasicObjectLock*)')
f(5,3975,1,4,'ObjectSynchronizer::fast_exit(oopDesc*, BasicLock*, Thread*)')
f(4,3976,4,1,'java/lang/Object.notifyAll')
f(5,3976,2,4,'HandleMarkCleaner::~HandleMarkCleaner()')
f(5,3978,2,3,'JVM_MonitorNotifyAll')
f(6,3978,2,4,'ObjectSynchronizer::notifyall(Handle, Thread*)')
f(7,3978,1,4,'BiasedLocking::revoke_and_rebias(Handle, bool, Thread*)')
f(8,3978,1,3,'update_heuristics(oopDesc*, bool)')
f(7,3979,1,4,'ObjectSynchronizer::inflate(Thread*, oopDesc*)')
f(1,3980,1,1,'java/nio/ByteBuffer.<init>')
f(2,3980,1,6,'org/apache/logging/log4j/core/filter/AbstractFilterable.getFilter',0,1,0)
f(1,3981,1,1,'java/nio/HeapByteBuffer.slice')
f(2,3981,1,1,'java/nio/HeapByteBuffer.<init>')
f(3,3981,1,1,'java/nio/ByteBuffer.<init>')
f(4,3981,1,1,'java/nio/Buffer.<init>')
f(5,3981,1,6,'java/nio/Buffer.position',0,1,0)
f(1,3982,1,1,'java/util/TimerThread.run')
f(2,3982,1,1,'java/util/TimerThread.mainLoop')
f(3,3982,1,1,'java/util/TaskQueue.rescheduleMin')
f(4,3982,1,6,'java/util/TaskQueue.fixDown',0,1,0)
f(1,3983,4,1,'java/util/concurrent/ForkJoinWorkerThread.run')
f(2,3983,4,1,'java/util/concurrent/ForkJoinPool.runWorker')
f(3,3983,2,1,'java/util/concurrent/ForkJoinPool$WorkQueue.runTask')
f(4,3983,2,1,'java/util/concurrent/ForkJoinTask.doExec')
f(5,3983,2,1,'java/util/concurrent/ForkJoinTask.doExec$$$capture')
f(6,3983,2,1,'java/util/concurrent/CompletableFuture$AsyncRun.exec')
f(7,3983,2,1,'java/util/concurrent/CompletableFuture$AsyncRun.run')
f(8,3983,2,1,'java/util/concurrent/CompletableFuture$AsyncRun.run$$$capture')
f(9,3983,2,1,'io/tapdata/observable/metric/ObservableAspectTask$$Lambda$3163/1962361081.run')
f(10,3983,2,1,'io/tapdata/observable/metric/ObservableAspectTask.lambda$null$28')
f(11,3983,1,1,'io/tapdata/observable/metric/PipelineDelayImpl.refreshDelay')
f(12,3983,1,6,'java/util/concurrent/ConcurrentHashMap.putIfAbsent',0,1,0)
f(11,3984,1,6,'java/util/ArrayList.isEmpty',0,1,0)
f(3,3985,2,1,'java/util/concurrent/ForkJoinPool.awaitWork')
f(4,3985,2,1,'sun/misc/Unsafe.park')
f(5,3985,2,3,'Unsafe_Park')
f(6,3985,2,4,'Parker::park(bool, long)')
f(7,3985,2,3,'__psynch_cvwait')
f(1,3987,1,1,'net/openhft/chronicle/core/threads/CleaningThread.run')
f(2,3987,1,1,'java/lang/Thread.run')
f(3,3987,1,1,'java/util/concurrent/ThreadPoolExecutor$Worker.run')
f(4,3987,1,1,'java/util/concurrent/ThreadPoolExecutor.runWorker')
f(5,3987,1,1,'java/util/concurrent/ThreadPoolExecutor.getTask')
f(6,3987,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(7,3987,1,1,'java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue.take')
f(8,3987,1,1,'java/util/concurrent/locks/ReentrantLock.lockInterruptibly')
f(9,3987,1,1,'java/util/concurrent/locks/AbstractQueuedSynchronizer.acquireInterruptibly')
f(10,3987,1,1,'java/lang/Thread.interrupted')
f(11,3987,1,1,'java/lang/Thread.isInterrupted')
f(12,3987,1,3,'JVM_IsInterrupted')
f(13,3987,1,4,'Thread::is_interrupted(Thread*, bool)')
f(14,3987,1,4,'os::is_interrupted(Thread*, bool)')
f(1,3988,1,1,'oracle/jdbc/driver/T4CTTIrxd.copyRowsAsNeeded')
f(2,3988,1,6,'oracle/jdbc/driver/Accessor.setNull',0,1,0)
f(3,3988,1,2,'oracle/jdbc/driver/DatabaseError.createSqlException',1,0,0)
f(4,3988,1,2,'oracle/jdbc/driver/DatabaseError.createSqlException',1,0,0)
f(1,3989,223,3,'thread_start')
f(2,3989,223,3,'_pthread_start')
f(3,3989,223,3,'java_start(Thread*)')
f(4,3989,147,4,'GCTaskThread::run()')
f(5,3989,3,4,'GCTaskManager::get_task(unsigned int)')
f(6,3989,2,4,'Monitor::lock_without_safepoint_check()')
f(7,3989,2,4,'Monitor::ILock(Thread*)')
f(8,3989,1,4,'Monitor::TryFast()')
f(8,3990,1,3,'ParkCommon(ParkEvent*, long)')
f(9,3990,1,4,'os::PlatformEvent::park()')
f(10,3990,1,3,'__psynch_cvwait')
f(6,3991,1,4,'Monitor::wait(bool, long, bool)')
f(7,3991,1,4,'Monitor::IWait(Thread*, long)')
f(8,3991,1,3,'ParkCommon(ParkEvent*, long)')
f(9,3991,1,4,'os::PlatformEvent::park()')
f(10,3991,1,3,'__psynch_cvwait')
f(5,3992,3,4,'GCTaskManager::note_completion(unsigned int)')
f(6,3992,3,4,'Monitor::lock_without_safepoint_check()')
f(7,3992,3,4,'Monitor::ILock(Thread*)')
f(8,3992,1,4,'Monitor::TryFast()')
f(8,3993,2,3,'ParkCommon(ParkEvent*, long)')
f(9,3993,2,4,'os::PlatformEvent::park()')
f(10,3993,2,3,'__psynch_cvwait')
f(5,3995,29,4,'OldToYoungRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,3995,29,4,'CardTableExtension::scavenge_contents_parallel(ObjectStartArray*, MutableSpace*, HeapWord*, PSPromotionManager*, unsigned int, unsigned int)')
f(7,4000,1,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(7,4001,2,4,'ObjArrayKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(7,4003,5,4,'ObjectStartArray::object_start(HeapWord*) const')
f(8,4005,1,4,'ObjectStartArray::offset_addr_for_block(signed char*) const')
f(8,4006,2,4,'oopDesc::size()')
f(7,4008,2,4,'ObjectStartArray::object_starts_in_range(HeapWord*, HeapWord*) const')
f(7,4010,2,4,'ObjectStartArray::offset_addr_for_block(signed char*) const')
f(7,4012,11,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(8,4012,10,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(9,4013,2,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(9,4015,6,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(10,4016,3,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(11,4016,3,3,'_platform_memmove$VARIANT$Rosetta')
f(10,4019,1,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(10,4020,1,4,'void PSPromotionManager::push_depth<unsigned int>(unsigned int*)')
f(9,4021,1,4,'oopDesc::size()')
f(8,4022,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(7,4023,1,4,'oopDesc::size()')
f(5,4024,7,4,'ScavengeRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,4024,7,4,'ClassLoaderDataGraph::oops_do(OopClosure*, KlassClosure*, bool)')
f(7,4024,6,4,'ClassLoaderData::classes_do(KlassClosure*)')
f(8,4025,5,4,'PSScavengeKlassClosure::do_klass(Klass*)')
f(7,4030,1,4,'ClassLoaderData::oops_do(OopClosure*, KlassClosure*, bool)')
f(8,4030,1,4,'ClassLoaderData::ChunkedHandleList::oops_do(OopClosure*)')
f(9,4030,1,4,'void PSRootsClosure<false>::do_oop_work<oopDesc*>(oopDesc**)')
f(5,4031,85,4,'StealTask::do_it(GCTaskManager*, unsigned int)')
f(6,4031,28,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(7,4031,27,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(8,4032,26,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(9,4032,26,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(10,4032,26,3,'_platform_memmove$VARIANT$Rosetta')
f(7,4058,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(6,4059,33,4,'ParallelTaskTerminator::offer_termination(TerminatorTerminator*)')
f(7,4075,17,3,'swtch_pri')
f(6,4092,24,3,'SpinPause')
f(5,4116,20,4,'ThreadRootsTask::do_it(GCTaskManager*, unsigned int)')
f(6,4116,9,4,'JavaThread::oops_do(OopClosure*, CLDClosure*, CodeBlobClosure*)')
f(7,4116,1,4,'CodeBlobToOopClosure::do_nmethod(nmethod*)')
f(8,4116,1,4,'nmethod::oops_do(OopClosure*, bool)')
f(9,4116,1,4,'RelocIterator::initialize(nmethod*, unsigned char*, unsigned char*)')
f(7,4117,1,4,'StackFrameStream::next()')
f(8,4117,1,4,'frame::sender(RegisterMap*) const')
f(9,4117,1,4,'frame::sender_for_compiled_frame(RegisterMap*) const')
f(10,4117,1,4,'OopMapSet::update_register_map(frame const*, RegisterMap*)')
f(11,4117,1,4,'OopMapStream::OopMapStream(OopMap*, int)')
f(12,4117,1,3,'pthread_getspecific')
f(7,4118,1,4,'frame::oops_code_blob_do(OopClosure*, CodeBlobClosure*, RegisterMap const*)')
f(8,4118,1,4,'OopMapSet::all_do(frame const*, RegisterMap const*, OopClosure*, void (*)(oopDesc**, oopDesc**), OopClosure*)')
f(9,4118,1,4,'void PSRootsClosure<false>::do_oop_work<oopDesc*>(oopDesc**)')
f(10,4118,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(11,4118,1,4,'oopDesc::size()')
f(7,4119,6,4,'frame::oops_interpreted_do(OopClosure*, CLDClosure*, RegisterMap const*, bool)')
f(8,4121,1,4,'ConstantPool::impl_signature_ref_at(int, bool)')
f(9,4121,1,4,'ConstantPool::impl_name_and_type_ref_index_at(int, bool)')
f(8,4122,1,4,'InterpreterOopMap::iterate_oop(OffsetClosure*) const')
f(8,4123,2,4,'Method::mask_for(int, InterpreterOopMap*)')
f(9,4124,1,4,'InstanceKlass::mask_for(methodHandle, int, InterpreterOopMap*)')
f(10,4124,1,4,'OopMapCache::lookup(methodHandle, int, InterpreterOopMap*) const')
f(11,4124,1,4,'OopMapCacheEntry::fill(methodHandle, int)')
f(12,4124,1,4,'OopMapForCacheEntry::compute_map(Thread*)')
f(13,4124,1,4,'GenerateOopMap::interp_bb(BasicBlock*)')
f(14,4124,1,4,'BytecodeStream::next()')
f(15,4124,1,4,'BaseBytecodeStream::bcp() const')
f(6,4125,11,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(7,4125,11,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(8,4126,1,4,'InstanceKlass::oop_push_contents(PSPromotionManager*, oopDesc*)')
f(8,4127,9,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(9,4129,6,4,'Copy::pd_disjoint_words(HeapWord*, HeapWord*, unsigned long)')
f(10,4129,6,3,'_platform_memmove$VARIANT$Rosetta')
f(9,4135,1,4,'oopDesc::size()')
f(4,4136,67,4,'JavaThread::run()')
f(5,4136,63,4,'JavaThread::thread_main_inner()')
f(6,4136,12,4,'CompileBroker::compiler_thread_loop()')
f(7,4136,4,4,'CompileBroker::invoke_compiler_on_method(CompileTask*)')
f(8,4136,4,4,'Compiler::compile_method(ciEnv*, ciMethod*, int)')
f(9,4136,4,4,'Compilation::Compilation(AbstractCompiler*, ciEnv*, ciMethod*, int, BufferBlob*)')
f(10,4136,4,4,'Compilation::compile_method()')
f(11,4136,3,4,'Compilation::compile_java_method()')
f(12,4136,3,4,'Compilation::build_hir()')
f(13,4136,3,4,'IR::IR(Compilation*, ciMethod*, int)')
f(14,4136,3,4,'IRScope::IRScope(Compilation*, IRScope*, int, ciMethod*, int, bool)')
f(15,4136,3,4,'GraphBuilder::GraphBuilder(Compilation*, IRScope*)')
f(16,4136,1,4,'BlockListBuilder::BlockListBuilder(Compilation*, IRScope*, int)')
f(17,4136,1,4,'BlockListBuilder::set_leaders()')
f(18,4136,1,4,'ciMethod::bci_block_start()')
f(19,4136,1,4,'MethodLiveness::compute_liveness()')
f(20,4136,1,4,'MethodLiveness::init_basic_blocks()')
f(16,4137,1,4,'GraphBuilder::iterate_all_blocks(bool)')
f(17,4137,1,4,'GraphBuilder::iterate_bytecodes_for_block(int)')
f(18,4137,1,4,'GraphBuilder::invoke(Bytecodes::Code)')
f(19,4137,1,4,'GraphBuilder::try_inline(ciMethod*, bool, Bytecodes::Code, Instruction*)')
f(20,4137,1,4,'GraphBuilder::try_inline_full(ciMethod*, bool, Bytecodes::Code, Instruction*)')
f(21,4137,1,4,'GraphBuilder::iterate_all_blocks(bool)')
f(22,4137,1,4,'GraphBuilder::iterate_bytecodes_for_block(int)')
f(23,4137,1,4,'GraphBuilder::invoke(Bytecodes::Code)')
f(24,4137,1,4,'GraphBuilder::try_inline(ciMethod*, bool, Bytecodes::Code, Instruction*)')
f(25,4137,1,4,'GraphBuilder::try_inline_full(ciMethod*, bool, Bytecodes::Code, Instruction*)')
f(26,4137,1,4,'GraphBuilder::iterate_all_blocks(bool)')
f(27,4137,1,4,'GraphBuilder::iterate_bytecodes_for_block(int)')
f(28,4137,1,4,'GraphBuilder::invoke(Bytecodes::Code)')
f(29,4137,1,4,'GraphBuilder::try_inline(ciMethod*, bool, Bytecodes::Code, Instruction*)')
f(30,4137,1,4,'GraphBuilder::try_inline_full(ciMethod*, bool, Bytecodes::Code, Instruction*)')
f(31,4137,1,4,'GraphBuilder::iterate_all_blocks(bool)')
f(32,4137,1,4,'GraphBuilder::iterate_bytecodes_for_block(int)')
f(33,4137,1,4,'GraphBuilder::invoke(Bytecodes::Code)')
f(34,4137,1,4,'ciBytecodeStream::get_method(bool&, ciSignature**)')
f(35,4137,1,4,'ciEnv::get_method_by_index(constantPoolHandle, int, Bytecodes::Code, ciInstanceKlass*)')
f(36,4137,1,4,'ciEnv::get_method_by_index_impl(constantPoolHandle, int, Bytecodes::Code, ciInstanceKlass*)')
f(37,4137,1,4,'ciEnv::lookup_method(InstanceKlass*, InstanceKlass*, Symbol*, Symbol*, Bytecodes::Code)')
f(38,4137,1,4,'LinkResolver::resolve_special_call_or_null(KlassHandle, Symbol*, Symbol*, KlassHandle, bool)')
f(39,4137,1,4,'LinkResolver::resolve_special_call(CallInfo&, Handle, KlassHandle, Symbol*, Symbol*, KlassHandle, bool, Thread*)')
f(40,4137,1,4,'LinkResolver::runtime_resolve_special_method(CallInfo&, methodHandle, KlassHandle, KlassHandle, Handle, bool, Thread*)')
f(41,4137,1,4,'CallInfo::set_static(KlassHandle, methodHandle, Thread*)')
f(42,4137,1,4,'CallInfo::set_common(KlassHandle, KlassHandle, methodHandle, methodHandle, CallInfo::CallKind, int, Thread*)')
f(43,4137,1,4,'CompilationPolicy::must_be_compiled(methodHandle, int)')
f(44,4137,1,4,'CompilationPolicy::can_be_compiled(methodHandle, int)')
f(45,4137,1,4,'AbstractInterpreter::can_be_compiled(methodHandle)')
f(46,4137,1,4,'AbstractInterpreter::method_kind(methodHandle)')
f(47,4137,1,4,'Method::is_accessor() const')
f(48,4137,1,4,'Bytecodes::java_code_at(Method const*, unsigned char*)')
f(16,4138,1,4,'PhiSimplifier::PhiSimplifier(BlockBegin*)')
f(17,4138,1,4,'boolArray::initialize(int, bool)')
f(11,4139,1,4,'Compilation::install_code(int)')
f(12,4139,1,4,'ciEnv::register_method(ciMethod*, int, CodeOffsets*, int, CodeBuffer*, int, OopMapSet*, ExceptionHandlerTable*, ImplicitExceptionTable*, AbstractCompiler*, int, bool, bool, RTMState)')
f(13,4139,1,4,'nmethod::new_nmethod(methodHandle, int, int, CodeOffsets*, int, DebugInformationRecorder*, Dependencies*, CodeBuffer*, int, OopMapSet*, ExceptionHandlerTable*, ImplicitExceptionTable*, AbstractCompiler*, int)')
f(14,4139,1,4,'nmethod::nmethod(Method*, int, int, int, CodeOffsets*, int, DebugInformationRecorder*, Dependencies*, CodeBuffer*, int, OopMapSet*, ExceptionHandlerTable*, ImplicitExceptionTable*, AbstractCompiler*, int)')
f(15,4139,1,4,'DetectScavengeRoot::do_oop(oopDesc**)')
f(7,4140,8,4,'CompileQueue::get()')
f(8,4140,8,4,'NMethodSweeper::possibly_sweep()')
f(9,4140,8,4,'NMethodSweeper::sweep_code_cache()')
f(10,4140,8,4,'NMethodSweeper::process_nmethod(nmethod*)')
f(11,4147,1,4,'nmethod::cleanup_inline_caches()')
f(12,4147,1,4,'CodeCache::find_blob_unsafe(void*)')
f(13,4147,1,4,'CodeHeap::find_start(void*) const')
f(6,4148,50,4,'JavaThread::exit(bool, JavaThread::ExitType)')
f(7,4148,2,4,'JNIHandleBlock::release_block(JNIHandleBlock*, Thread*)')
f(8,4149,1,4,'Monitor::lock_without_safepoint_check()')
f(9,4149,1,4,'Monitor::ILock(Thread*)')
f(10,4149,1,3,'ParkCommon(ParkEvent*, long)')
f(11,4149,1,4,'os::PlatformEvent::park()')
f(12,4149,1,3,'__psynch_cvwait')
f(7,4150,48,4,'JvmtiExport::post_thread_end(JavaThread*)')
f(8,4150,48,3,'cbThreadEnd')
f(9,4150,48,3,'event_callback')
f(10,4150,36,3,'classTrack_processUnloads')
f(11,4153,27,3,'isSameObject')
f(12,4155,5,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(12,4160,20,3,'jni_IsSameObject')
f(13,4168,4,4,'JavaThread::thread_from_jni_environment(JNIEnv_*)')
f(13,4172,3,4,'ThreadStateTransition::trans_and_fence(JavaThreadState, JavaThreadState)')
f(13,4175,5,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(11,4180,2,3,'jni_IsSameObject')
f(11,4182,4,3,'jvmti_GetLoadedClasses')
f(12,4182,4,4,'JvmtiGetLoadedClasses::getLoadedClasses(JvmtiEnv*, int*, _jclass***)')
f(13,4182,4,4,'ClassLoaderDataGraph::loaded_classes_do(KlassClosure*)')
f(14,4182,4,4,'ClassLoaderData::loaded_classes_do(KlassClosure*)')
f(15,4184,2,4,'LoadedClassesClosure::do_klass(Klass*)')
f(10,4186,2,3,'commonRef_compact')
f(11,4186,2,3,'isSameObject')
f(12,4186,2,3,'jni_IsSameObject')
f(10,4188,10,3,'isSameObject')
f(6,4198,1,4,'JvmtiAgentThread::call_start_function()')
f(7,4198,1,3,'commandLoop')
f(8,4198,1,3,'outStream_sendCommand')
f(9,4198,1,3,'transport_sendPacket')
f(10,4198,1,3,'socketTransport_writePacket')
f(11,4198,1,3,'send_fully')
f(12,4198,1,3,'__sendto')
f(5,4199,4,4,'JvmtiExport::post_thread_start(JavaThread*)')
f(6,4199,4,3,'cbThreadStart')
f(7,4199,4,3,'event_callback')
f(8,4199,3,3,'classTrack_processUnloads')
f(9,4200,2,3,'isSameObject')
f(10,4201,1,3,'jni_IsSameObject')
f(11,4201,1,4,'ThreadStateTransition::trans_from_native(JavaThreadState)')
f(8,4202,1,3,'isSameObject')
f(4,4203,8,4,'VMThread::run()')
f(5,4203,8,4,'VMThread::loop()')
f(6,4203,3,4,'SafepointSynchronize::begin()')
f(7,4203,3,4,'SafepointSynchronize::do_cleanup_tasks()')
f(8,4203,3,4,'Threads::nmethods_do(CodeBlobClosure*)')
f(9,4203,3,4,'JavaThread::nmethods_do(CodeBlobClosure*)')
f(10,4203,2,4,'SetHotnessClosure::do_code_blob(CodeBlob*)')
f(10,4205,1,4,'StackFrameStream::next()')
f(11,4205,1,4,'frame::sender(RegisterMap*) const')
f(12,4205,1,4,'frame::sender_for_compiled_frame(RegisterMap*) const')
f(13,4205,1,4,'frame::update_map_with_saved_link(RegisterMap*, long**)')
f(6,4206,5,4,'VMThread::evaluate_operation(VM_Operation*)')
f(7,4206,5,4,'VM_Operation::evaluate()')
f(8,4206,4,4,'VM_ParallelGCFailedAllocation::doit()')
f(9,4206,4,4,'ParallelScavengeHeap::failed_mem_allocate(unsigned long)')
f(10,4206,4,4,'PSScavenge::invoke()')
f(11,4206,4,4,'PSScavenge::invoke_no_policy()')
f(12,4206,2,4,'ReferenceProcessor::process_discovered_references(BoolObjectClosure*, OopClosure*, VoidClosure*, AbstractRefProcTaskExecutor*, GCTimer*, GCId)')
f(13,4206,1,4,'JvmtiTagMap::weak_oops_do(BoolObjectClosure*, OopClosure*)')
f(14,4206,1,4,'JvmtiTagMap::do_weak_oops(BoolObjectClosure*, OopClosure*)')
f(13,4207,1,4,'ReferenceProcessor::process_discovered_reflist(DiscoveredList*, ReferencePolicy*, bool, BoolObjectClosure*, OopClosure*, VoidClosure*, AbstractRefProcTaskExecutor*)')
f(14,4207,1,4,'ReferenceProcessor::process_phase3(DiscoveredList&, bool, BoolObjectClosure*, OopClosure*, VoidClosure*)')
f(15,4207,1,4,'PSEvacuateFollowersClosure::do_void()')
f(16,4207,1,4,'PSPromotionManager::drain_stacks_depth(bool)')
f(17,4207,1,4,'PSPromotionManager::process_popped_location_depth(StarTask)')
f(18,4207,1,4,'oopDesc* PSPromotionManager::copy_to_survivor_space<false>(oopDesc*)')
f(12,4208,1,4,'StringTable::unlink_or_oops_do(BoolObjectClosure*, OopClosure*, int*, int*)')
f(13,4208,1,4,'StringTable::buckets_unlink_or_oops_do(BoolObjectClosure*, OopClosure*, int, int, BasicHashtable<(MemoryType)9>::BucketUnlinkContext*)')
f(12,4209,1,4,'ThreadLocalAllocBuffer::resize_all_tlabs()')
f(13,4209,1,4,'ThreadLocalAllocBuffer::resize()')
f(8,4210,1,4,'VM_ParallelGCSystemGC::doit()')
f(9,4210,1,4,'PSScavenge::invoke()')
f(10,4210,1,4,'PSScavenge::invoke_no_policy()')
f(11,4210,1,4,'StringTable::unlink_or_oops_do(BoolObjectClosure*, OopClosure*, int*, int*)')
f(12,4210,1,4,'void PSRootsClosure<false>::do_oop_work<oopDesc*>(oopDesc**)')
f(4,4211,1,4,'WatcherThread::run()')
f(5,4211,1,4,'WatcherThread::sleep() const')
f(6,4211,1,4,'Monitor::wait(bool, long, bool)')
f(7,4211,1,4,'Monitor::IWait(Thread*, long)')
f(8,4211,1,4,'os::PlatformEvent::park(long)')
f(9,4211,1,3,'__psynch_cvwait')

search();
</script></body></html>
