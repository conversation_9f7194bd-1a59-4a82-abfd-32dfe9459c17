/**
 * <AUTHOR>
 * @date 2021/5/27 下午9:44
 * @description
 */

const fs = require('fs');
const path = require('path');

const { ManagementClient } = require('authing-js-sdk');
const userPoolId = '5ed5cfc063690a8107edf079';
const secret = '1ed743a71915b8ff25fe37bc3aac6ab9';

// 如果文件较大建议分批次读入
// 请将用户信息与本文件保存在同一个目录，文件内容为用户数据的数组 JSON，一个元素为一个用户的信息对象
let users = fs.readFileSync(path.resolve('users.json'), { encoding: 'utf8' });
users = JSON.parse(users);

async function main() {
    const managementClient = new ManagementClient({
        userPoolId,
        secret,
    });

    for (let i = 0; i < users.length; i++) {
        let yourUser = users[i];
        try {
            // 在此完成字段对齐
            await managementClient.users.create(
                {
                    nickname: yourUser.nickname,
                    password: yourUser.password,
                    email: yourUser.mail,
                    emailVerified: yourUser.emailVerified,
                    phone: yourUser.phone,
                    loginsCount: yourUser.loginsCount,
                    // 存储原始数据，以备使用
                    oauth: JSON.stringify(yourUser),
                },
                {
                    /**
                     * 开启这个开关，password 字段会直接写入 Authing 数据库，Authing 不会再次加密此字段
                     * 如果你的密码不是明文存储，你应该保持开启，并编写密码函数计算
                     */
                    keepPassword: true,
                },
            );
        } catch (err) {
            console.log(err);
            // 将导入失败的用户写入文件
            fs.writeFileSync(
                path.resolve('users_failed.json'),
                JSON.stringify(yourUser) + '\n',
                {
                    flag: 'a',
                },
            );
        }
    }
}

main();
