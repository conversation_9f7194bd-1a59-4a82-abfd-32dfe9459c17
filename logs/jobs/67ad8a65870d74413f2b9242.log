[TRACE] 2025-02-13 14:00:44.982 - [任务 7] - Task initialization... 
[TRACE] 2025-02-13 14:00:45.203 - [任务 7] - Start task milestones: 67ad8a65870d74413f2b9242(任务 7) 
[INFO ] 2025-02-13 14:00:46.618 - [任务 7] - Loading table structure completed 
[TRACE] 2025-02-13 14:00:46.646 - [任务 7] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-02-13 14:00:46.646 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-13 14:00:46.852 - [任务 7] - Task started 
[TRACE] 2025-02-13 14:00:46.982 - [任务 7][pg_hdtest - SP9_lab] - Node pg_hdtest - SP9_lab[bed9d12c-bbd0-4f68-9ff2-02ee1e67b248] start preload schema,table counts: 239 
[TRACE] 2025-02-13 14:00:46.982 - [任务 7][sybase_190 -LAB_DB_9] - Node sybase_190 -LAB_DB_9[d08e6019-4c23-419b-91c6-0580ccd3f230] start preload schema,table counts: 239 
[TRACE] 2025-02-13 14:00:46.982 - [任务 7][pg_hdtest - SP9_lab] - Node pg_hdtest - SP9_lab[bed9d12c-bbd0-4f68-9ff2-02ee1e67b248] preload schema finished, cost 1 ms 
[TRACE] 2025-02-13 14:00:46.982 - [任务 7][sybase_190 -LAB_DB_9] - Node sybase_190 -LAB_DB_9[d08e6019-4c23-419b-91c6-0580ccd3f230] preload schema finished, cost 1 ms 
[INFO ] 2025-02-13 14:00:47.304 - [任务 7][sybase_190 -LAB_DB_9] - Source connector(sybase_190 -LAB_DB_9) initialization completed 
[TRACE] 2025-02-13 14:00:47.304 - [任务 7][sybase_190 -LAB_DB_9] - Source node "sybase_190 -LAB_DB_9" read batch size: 100 
[TRACE] 2025-02-13 14:00:47.304 - [任务 7][sybase_190 -LAB_DB_9] - Source node "sybase_190 -LAB_DB_9" event queue capacity: 200 
[TRACE] 2025-02-13 14:00:47.304 - [任务 7][sybase_190 -LAB_DB_9] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-13 14:00:47.492 - [任务 7][sybase_190 -LAB_DB_9] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-13 14:00:47.492 - [任务 7][pg_hdtest - SP9_lab] - Sink connector(pg_hdtest - SP9_lab) initialization completed 
[TRACE] 2025-02-13 14:00:47.492 - [任务 7][pg_hdtest - SP9_lab] - Node(pg_hdtest - SP9_lab) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-13 14:00:47.492 - [任务 7][pg_hdtest - SP9_lab] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-13 14:00:47.493 - [任务 7][pg_hdtest - SP9_lab] - Apply table structure to target database 
[INFO ] 2025-02-13 14:00:47.932 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from 239 tables 
[TRACE] 2025-02-13 14:00:47.932 - [任务 7][sybase_190 -LAB_DB_9] - Initial sync started 
[INFO ] 2025-02-13 14:00:47.932 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: message_board 
[TRACE] 2025-02-13 14:00:47.933 - [任务 7][sybase_190 -LAB_DB_9] - Table message_board is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.009 - [任务 7][sybase_190 -LAB_DB_9] - Table message_board has been completed batch read 
[INFO ] 2025-02-13 14:00:48.009 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_request_copy_hist 
[TRACE] 2025-02-13 14:00:48.009 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_request_copy_hist is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.120 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_request_copy_hist has been completed batch read 
[INFO ] 2025-02-13 14:00:48.120 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: keyword_list 
[TRACE] 2025-02-13 14:00:48.120 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_list is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.208 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_list has been completed batch read 
[INFO ] 2025-02-13 14:00:48.208 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: QC 
[TRACE] 2025-02-13 14:00:48.208 - [任务 7][sybase_190 -LAB_DB_9] - Table QC is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.298 - [任务 7][sybase_190 -LAB_DB_9] - Table QC has been completed batch read 
[INFO ] 2025-02-13 14:00:48.299 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pat_amend_log 
[TRACE] 2025-02-13 14:00:48.299 - [任务 7][sybase_190 -LAB_DB_9] - Table pat_amend_log is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.406 - [任务 7][sybase_190 -LAB_DB_9] - Table pat_amend_log has been completed batch read 
[INFO ] 2025-02-13 14:00:48.407 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: user_contact_list 
[TRACE] 2025-02-13 14:00:48.407 - [任务 7][sybase_190 -LAB_DB_9] - Table user_contact_list is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.467 - [任务 7][sybase_190 -LAB_DB_9] - Table user_contact_list has been completed batch read 
[INFO ] 2025-02-13 14:00:48.467 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ecsearch_login_log 
[TRACE] 2025-02-13 14:00:48.467 - [任务 7][sybase_190 -LAB_DB_9] - Table ecsearch_login_log is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.540 - [任务 7][sybase_190 -LAB_DB_9] - Table ecsearch_login_log has been completed batch read 
[INFO ] 2025-02-13 14:00:48.540 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_organism_master 
[TRACE] 2025-02-13 14:00:48.540 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_organism_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.596 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_organism_master has been completed batch read 
[INFO ] 2025-02-13 14:00:48.596 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_standard_list 
[TRACE] 2025-02-13 14:00:48.596 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.659 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list has been completed batch read 
[INFO ] 2025-02-13 14:00:48.659 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: testact_def 
[TRACE] 2025-02-13 14:00:48.659 - [任务 7][sybase_190 -LAB_DB_9] - Table testact_def is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.725 - [任务 7][sybase_190 -LAB_DB_9] - Table testact_def has been completed batch read 
[INFO ] 2025-02-13 14:00:48.725 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_service 
[TRACE] 2025-02-13 14:00:48.725 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_service is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.794 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_service has been completed batch read 
[INFO ] 2025-02-13 14:00:48.795 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: request_format_upd 
[TRACE] 2025-02-13 14:00:48.795 - [任务 7][sybase_190 -LAB_DB_9] - Table request_format_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:48.900 - [任务 7][sybase_190 -LAB_DB_9] - Table request_format_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:48.900 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_registrable_upd 
[TRACE] 2025-02-13 14:00:48.900 - [任务 7][sybase_190 -LAB_DB_9] - Table test_registrable_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.010 - [任务 7][sybase_190 -LAB_DB_9] - Table test_registrable_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:49.010 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mb_parasite 
[TRACE] 2025-02-13 14:00:49.010 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_parasite is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.172 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_parasite has been completed batch read 
[INFO ] 2025-02-13 14:00:49.172 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_test 
[TRACE] 2025-02-13 14:00:49.172 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.291 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test has been completed batch read 
[INFO ] 2025-02-13 14:00:49.291 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: chk_table 
[TRACE] 2025-02-13 14:00:49.291 - [任务 7][sybase_190 -LAB_DB_9] - Table chk_table is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.366 - [任务 7][sybase_190 -LAB_DB_9] - Table chk_table has been completed batch read 
[INFO ] 2025-02-13 14:00:49.366 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: event_reminder_schedule 
[TRACE] 2025-02-13 14:00:49.366 - [任务 7][sybase_190 -LAB_DB_9] - Table event_reminder_schedule is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.449 - [任务 7][sybase_190 -LAB_DB_9] - Table event_reminder_schedule has been completed batch read 
[INFO ] 2025-02-13 14:00:49.451 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: authen_token_hist 
[TRACE] 2025-02-13 14:00:49.451 - [任务 7][sybase_190 -LAB_DB_9] - Table authen_token_hist is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.645 - [任务 7][sybase_190 -LAB_DB_9] - Table authen_token_hist has been completed batch read 
[INFO ] 2025-02-13 14:00:49.645 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: comment_upd 
[TRACE] 2025-02-13 14:00:49.645 - [任务 7][sybase_190 -LAB_DB_9] - Table comment_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:49.982 - [任务 7][sybase_190 -LAB_DB_9] - Table comment_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:49.982 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: als_config 
[TRACE] 2025-02-13 14:00:49.982 - [任务 7][sybase_190 -LAB_DB_9] - Table als_config is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.284 - [任务 7][sybase_190 -LAB_DB_9] - Table als_config has been completed batch read 
[INFO ] 2025-02-13 14:00:50.284 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_ha_chargetable 
[TRACE] 2025-02-13 14:00:50.284 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_ha_chargetable is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.361 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_ha_chargetable has been completed batch read 
[INFO ] 2025-02-13 14:00:50.361 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_console 
[TRACE] 2025-02-13 14:00:50.361 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_console is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.433 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_console has been completed batch read 
[INFO ] 2025-02-13 14:00:50.433 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: operation_audit 
[TRACE] 2025-02-13 14:00:50.433 - [任务 7][sybase_190 -LAB_DB_9] - Table operation_audit is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.516 - [任务 7][sybase_190 -LAB_DB_9] - Table operation_audit has been completed batch read 
[INFO ] 2025-02-13 14:00:50.519 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: dict_counter 
[TRACE] 2025-02-13 14:00:50.519 - [任务 7][sybase_190 -LAB_DB_9] - Table dict_counter is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.572 - [任务 7][sybase_190 -LAB_DB_9] - Table dict_counter has been completed batch read 
[INFO ] 2025-02-13 14:00:50.572 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_map_upd 
[TRACE] 2025-02-13 14:00:50.572 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.634 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:50.634 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisg_tasklist_arch 
[TRACE] 2025-02-13 14:00:50.634 - [任务 7][sybase_190 -LAB_DB_9] - Table lisg_tasklist_arch is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.691 - [任务 7][sybase_190 -LAB_DB_9] - Table lisg_tasklist_arch has been completed batch read 
[INFO ] 2025-02-13 14:00:50.691 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisrep_trace 
[TRACE] 2025-02-13 14:00:50.691 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_trace is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.765 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_trace has been completed batch read 
[INFO ] 2025-02-13 14:00:50.765 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_enum_result 
[TRACE] 2025-02-13 14:00:50.765 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_enum_result is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.856 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_enum_result has been completed batch read 
[INFO ] 2025-02-13 14:00:50.856 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_user_application 
[TRACE] 2025-02-13 14:00:50.856 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_user_application is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.925 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_user_application has been completed batch read 
[INFO ] 2025-02-13 14:00:50.925 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lms_group 
[TRACE] 2025-02-13 14:00:50.925 - [任务 7][sybase_190 -LAB_DB_9] - Table lms_group is going to be initial synced 
[INFO ] 2025-02-13 14:00:50.984 - [任务 7][sybase_190 -LAB_DB_9] - Table lms_group has been completed batch read 
[INFO ] 2025-02-13 14:00:50.984 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_capability_upd 
[TRACE] 2025-02-13 14:00:51.069 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_capability_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.069 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_capability_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:51.069 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_option_relation 
[TRACE] 2025-02-13 14:00:51.069 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_relation is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.143 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_relation has been completed batch read 
[INFO ] 2025-02-13 14:00:51.143 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_result_rule 
[TRACE] 2025-02-13 14:00:51.143 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_result_rule is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.200 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_result_rule has been completed batch read 
[INFO ] 2025-02-13 14:00:51.200 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mb_organism_upd 
[TRACE] 2025-02-13 14:00:51.200 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_organism_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.277 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_organism_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:51.278 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: als_sysout_log_filter 
[TRACE] 2025-02-13 14:00:51.278 - [任务 7][sybase_190 -LAB_DB_9] - Table als_sysout_log_filter is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.332 - [任务 7][sybase_190 -LAB_DB_9] - Table als_sysout_log_filter has been completed batch read 
[INFO ] 2025-02-13 14:00:51.332 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: usid_serial 
[TRACE] 2025-02-13 14:00:51.332 - [任务 7][sybase_190 -LAB_DB_9] - Table usid_serial is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.391 - [任务 7][sybase_190 -LAB_DB_9] - Table usid_serial has been completed batch read 
[INFO ] 2025-02-13 14:00:51.391 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_standard_list_alias_OLD 
[TRACE] 2025-02-13 14:00:51.391 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_alias_OLD is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.444 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_alias_OLD has been completed batch read 
[INFO ] 2025-02-13 14:00:51.444 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_ap_g_request 
[TRACE] 2025-02-13 14:00:51.444 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_ap_g_request is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.511 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_ap_g_request has been completed batch read 
[INFO ] 2025-02-13 14:00:51.511 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_ap_transient 
[TRACE] 2025-02-13 14:00:51.511 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_ap_transient is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.577 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_ap_transient has been completed batch read 
[INFO ] 2025-02-13 14:00:51.577 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: dim_body_system 
[TRACE] 2025-02-13 14:00:51.628 - [任务 7][sybase_190 -LAB_DB_9] - Table dim_body_system is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.628 - [任务 7][sybase_190 -LAB_DB_9] - Table dim_body_system has been completed batch read 
[INFO ] 2025-02-13 14:00:51.628 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_specimen_master 
[TRACE] 2025-02-13 14:00:51.628 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_specimen_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.678 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_specimen_master has been completed batch read 
[INFO ] 2025-02-13 14:00:51.678 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_test_master_list 
[TRACE] 2025-02-13 14:00:51.678 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_master_list is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.732 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_master_list has been completed batch read 
[INFO ] 2025-02-13 14:00:51.732 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pbcatfmt 
[TRACE] 2025-02-13 14:00:51.732 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatfmt is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.780 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatfmt has been completed batch read 
[INFO ] 2025-02-13 14:00:51.781 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_option 
[TRACE] 2025-02-13 14:00:51.781 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.850 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option has been completed batch read 
[INFO ] 2025-02-13 14:00:51.850 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: request_format 
[TRACE] 2025-02-13 14:00:51.850 - [任务 7][sybase_190 -LAB_DB_9] - Table request_format is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.906 - [任务 7][sybase_190 -LAB_DB_9] - Table request_format has been completed batch read 
[INFO ] 2025-02-13 14:00:51.906 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_operation_list_master 
[TRACE] 2025-02-13 14:00:51.906 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_operation_list_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:51.972 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_operation_list_master has been completed batch read 
[INFO ] 2025-02-13 14:00:51.973 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_option_def 
[TRACE] 2025-02-13 14:00:51.973 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_def is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.040 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_def has been completed batch read 
[INFO ] 2025-02-13 14:00:52.040 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_tmp_dft_link 
[TRACE] 2025-02-13 14:00:52.040 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_tmp_dft_link is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.127 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_tmp_dft_link has been completed batch read 
[INFO ] 2025-02-13 14:00:52.127 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_send_out 
[TRACE] 2025-02-13 14:00:52.127 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_send_out is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.195 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_send_out has been completed batch read 
[INFO ] 2025-02-13 14:00:52.195 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: top_menu 
[TRACE] 2025-02-13 14:00:52.195 - [任务 7][sybase_190 -LAB_DB_9] - Table top_menu is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.267 - [任务 7][sybase_190 -LAB_DB_9] - Table top_menu has been completed batch read 
[INFO ] 2025-02-13 14:00:52.267 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_standard_list_OLD 
[TRACE] 2025-02-13 14:00:52.267 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_OLD is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.341 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_OLD has been completed batch read 
[INFO ] 2025-02-13 14:00:52.341 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_pmi_ex 
[TRACE] 2025-02-13 14:00:52.341 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_pmi_ex is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.413 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_pmi_ex has been completed batch read 
[INFO ] 2025-02-13 14:00:52.413 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_constant 
[TRACE] 2025-02-13 14:00:52.413 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_constant is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.463 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_constant has been completed batch read 
[INFO ] 2025-02-13 14:00:52.464 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_map_master_upd 
[TRACE] 2025-02-13 14:00:52.464 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_master_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.520 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_master_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:52.520 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_info 
[TRACE] 2025-02-13 14:00:52.520 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_info is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.573 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_info has been completed batch read 
[INFO ] 2025-02-13 14:00:52.573 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pbcatvld 
[TRACE] 2025-02-13 14:00:52.573 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatvld is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.629 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatvld has been completed batch read 
[INFO ] 2025-02-13 14:00:52.629 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisrep_seed 
[TRACE] 2025-02-13 14:00:52.698 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_seed is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.698 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_seed has been completed batch read 
[INFO ] 2025-02-13 14:00:52.698 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: retain_master 
[TRACE] 2025-02-13 14:00:52.698 - [任务 7][sybase_190 -LAB_DB_9] - Table retain_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.784 - [任务 7][sybase_190 -LAB_DB_9] - Table retain_master has been completed batch read 
[INFO ] 2025-02-13 14:00:52.785 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: message_code 
[TRACE] 2025-02-13 14:00:52.785 - [任务 7][sybase_190 -LAB_DB_9] - Table message_code is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.903 - [任务 7][sybase_190 -LAB_DB_9] - Table message_code has been completed batch read 
[INFO ] 2025-02-13 14:00:52.903 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: labuser_password_audit 
[TRACE] 2025-02-13 14:00:52.903 - [任务 7][sybase_190 -LAB_DB_9] - Table labuser_password_audit is going to be initial synced 
[INFO ] 2025-02-13 14:00:52.966 - [任务 7][sybase_190 -LAB_DB_9] - Table labuser_password_audit has been completed batch read 
[INFO ] 2025-02-13 14:00:52.967 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: als_log_filter 
[TRACE] 2025-02-13 14:00:52.967 - [任务 7][sybase_190 -LAB_DB_9] - Table als_log_filter is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.018 - [任务 7][sybase_190 -LAB_DB_9] - Table als_log_filter has been completed batch read 
[INFO ] 2025-02-13 14:00:53.018 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: report_style 
[TRACE] 2025-02-13 14:00:53.018 - [任务 7][sybase_190 -LAB_DB_9] - Table report_style is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.118 - [任务 7][sybase_190 -LAB_DB_9] - Table report_style has been completed batch read 
[INFO ] 2025-02-13 14:00:53.119 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_action 
[TRACE] 2025-02-13 14:00:53.119 - [任务 7][sybase_190 -LAB_DB_9] - Table test_action is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.188 - [任务 7][sybase_190 -LAB_DB_9] - Table test_action has been completed batch read 
[INFO ] 2025-02-13 14:00:53.188 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ccc_big5 
[TRACE] 2025-02-13 14:00:53.188 - [任务 7][sybase_190 -LAB_DB_9] - Table ccc_big5 is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.261 - [任务 7][sybase_190 -LAB_DB_9] - Table ccc_big5 has been completed batch read 
[INFO ] 2025-02-13 14:00:53.261 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: admin_audit 
[TRACE] 2025-02-13 14:00:53.261 - [任务 7][sybase_190 -LAB_DB_9] - Table admin_audit is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.340 - [任务 7][sybase_190 -LAB_DB_9] - Table admin_audit has been completed batch read 
[INFO ] 2025-02-13 14:00:53.340 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: queue_config 
[TRACE] 2025-02-13 14:00:53.341 - [任务 7][sybase_190 -LAB_DB_9] - Table queue_config is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.399 - [任务 7][sybase_190 -LAB_DB_9] - Table queue_config has been completed batch read 
[INFO ] 2025-02-13 14:00:53.399 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_capability 
[TRACE] 2025-02-13 14:00:53.399 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_capability is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.454 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_capability has been completed batch read 
[INFO ] 2025-02-13 14:00:53.458 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_site 
[TRACE] 2025-02-13 14:00:53.458 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_site is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.529 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_site has been completed batch read 
[INFO ] 2025-02-13 14:00:53.529 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: usid_mapping 
[TRACE] 2025-02-13 14:00:53.607 - [任务 7][sybase_190 -LAB_DB_9] - Table usid_mapping is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.607 - [任务 7][sybase_190 -LAB_DB_9] - Table usid_mapping has been completed batch read 
[INFO ] 2025-02-13 14:00:53.607 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_organism_upd 
[TRACE] 2025-02-13 14:00:53.607 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_organism_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.672 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_organism_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:53.675 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_backup4_disableADA 
[TRACE] 2025-02-13 14:00:53.675 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_backup4_disableADA is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.753 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_backup4_disableADA has been completed batch read 
[INFO ] 2025-02-13 14:00:53.753 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_standard_list_subset 
[TRACE] 2025-02-13 14:00:53.753 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_subset is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.823 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_subset has been completed batch read 
[INFO ] 2025-02-13 14:00:53.823 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_bb_request_code 
[TRACE] 2025-02-13 14:00:53.823 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_bb_request_code is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.895 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_bb_request_code has been completed batch read 
[INFO ] 2025-02-13 14:00:53.895 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: print_entity 
[TRACE] 2025-02-13 14:00:53.895 - [任务 7][sybase_190 -LAB_DB_9] - Table print_entity is going to be initial synced 
[INFO ] 2025-02-13 14:00:53.973 - [任务 7][sybase_190 -LAB_DB_9] - Table print_entity has been completed batch read 
[INFO ] 2025-02-13 14:00:53.973 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: email_ctr 
[TRACE] 2025-02-13 14:00:53.973 - [任务 7][sybase_190 -LAB_DB_9] - Table email_ctr is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.055 - [任务 7][sybase_190 -LAB_DB_9] - Table email_ctr has been completed batch read 
[INFO ] 2025-02-13 14:00:54.055 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_reference_upd 
[TRACE] 2025-02-13 14:00:54.055 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.174 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:54.174 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: labuser 
[TRACE] 2025-02-13 14:00:54.174 - [任务 7][sybase_190 -LAB_DB_9] - Table labuser is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.237 - [任务 7][sybase_190 -LAB_DB_9] - Table labuser has been completed batch read 
[INFO ] 2025-02-13 14:00:54.237 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: workbench 
[TRACE] 2025-02-13 14:00:54.237 - [任务 7][sybase_190 -LAB_DB_9] - Table workbench is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.316 - [任务 7][sybase_190 -LAB_DB_9] - Table workbench has been completed batch read 
[INFO ] 2025-02-13 14:00:54.316 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: QC_type_upd 
[TRACE] 2025-02-13 14:00:54.317 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_type_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.385 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_type_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:54.385 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: user_function 
[TRACE] 2025-02-13 14:00:54.385 - [任务 7][sybase_190 -LAB_DB_9] - Table user_function is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.446 - [任务 7][sybase_190 -LAB_DB_9] - Table user_function has been completed batch read 
[INFO ] 2025-02-13 14:00:54.446 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_test_master_EXT 
[TRACE] 2025-02-13 14:00:54.489 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_master_EXT is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.489 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_master_EXT has been completed batch read 
[INFO ] 2025-02-13 14:00:54.489 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: QC_Sample_Map 
[TRACE] 2025-02-13 14:00:54.489 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_Sample_Map is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.546 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_Sample_Map has been completed batch read 
[INFO ] 2025-02-13 14:00:54.546 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test 
[TRACE] 2025-02-13 14:00:54.546 - [任务 7][sybase_190 -LAB_DB_9] - Table test is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.614 - [任务 7][sybase_190 -LAB_DB_9] - Table test has been completed batch read 
[INFO ] 2025-02-13 14:00:54.617 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_usid_profile_relation 
[TRACE] 2025-02-13 14:00:54.617 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_usid_profile_relation is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.706 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_usid_profile_relation has been completed batch read 
[INFO ] 2025-02-13 14:00:54.706 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rsltflow 
[TRACE] 2025-02-13 14:00:54.706 - [任务 7][sybase_190 -LAB_DB_9] - Table rsltflow is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.787 - [任务 7][sybase_190 -LAB_DB_9] - Table rsltflow has been completed batch read 
[INFO ] 2025-02-13 14:00:54.787 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: cras_location_upd 
[TRACE] 2025-02-13 14:00:54.787 - [任务 7][sybase_190 -LAB_DB_9] - Table cras_location_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.840 - [任务 7][sybase_190 -LAB_DB_9] - Table cras_location_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:54.840 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_map_master 
[TRACE] 2025-02-13 14:00:54.840 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.917 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_master has been completed batch read 
[INFO ] 2025-02-13 14:00:54.917 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rs_lastcommit 
[TRACE] 2025-02-13 14:00:54.917 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_lastcommit is going to be initial synced 
[INFO ] 2025-02-13 14:00:54.970 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_lastcommit has been completed batch read 
[INFO ] 2025-02-13 14:00:54.970 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser 
[TRACE] 2025-02-13 14:00:54.970 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.051 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser has been completed batch read 
[INFO ] 2025-02-13 14:00:55.051 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: epr_patient_ex 
[TRACE] 2025-02-13 14:00:55.051 - [任务 7][sybase_190 -LAB_DB_9] - Table epr_patient_ex is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.129 - [任务 7][sybase_190 -LAB_DB_9] - Table epr_patient_ex has been completed batch read 
[INFO ] 2025-02-13 14:00:55.130 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: function_group 
[TRACE] 2025-02-13 14:00:55.130 - [任务 7][sybase_190 -LAB_DB_9] - Table function_group is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.200 - [任务 7][sybase_190 -LAB_DB_9] - Table function_group has been completed batch read 
[INFO ] 2025-02-13 14:00:55.201 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: laboratory 
[TRACE] 2025-02-13 14:00:55.201 - [任务 7][sybase_190 -LAB_DB_9] - Table laboratory is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.258 - [任务 7][sybase_190 -LAB_DB_9] - Table laboratory has been completed batch read 
[INFO ] 2025-02-13 14:00:55.258 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_request_detail 
[TRACE] 2025-02-13 14:00:55.258 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_request_detail is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.338 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_request_detail has been completed batch read 
[INFO ] 2025-02-13 14:00:55.338 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: object_attribute 
[TRACE] 2025-02-13 14:00:55.338 - [任务 7][sybase_190 -LAB_DB_9] - Table object_attribute is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.390 - [任务 7][sybase_190 -LAB_DB_9] - Table object_attribute has been completed batch read 
[INFO ] 2025-02-13 14:00:55.390 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_tag_setup_upd 
[TRACE] 2025-02-13 14:00:55.390 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_setup_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.460 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_setup_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:55.460 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: office_upd 
[TRACE] 2025-02-13 14:00:55.460 - [任务 7][sybase_190 -LAB_DB_9] - Table office_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.516 - [任务 7][sybase_190 -LAB_DB_9] - Table office_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:55.516 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lrr_delphic_test 
[TRACE] 2025-02-13 14:00:55.516 - [任务 7][sybase_190 -LAB_DB_9] - Table lrr_delphic_test is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.606 - [任务 7][sybase_190 -LAB_DB_9] - Table lrr_delphic_test has been completed batch read 
[INFO ] 2025-02-13 14:00:55.606 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: report_template_profile 
[TRACE] 2025-02-13 14:00:55.606 - [任务 7][sybase_190 -LAB_DB_9] - Table report_template_profile is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.671 - [任务 7][sybase_190 -LAB_DB_9] - Table report_template_profile has been completed batch read 
[INFO ] 2025-02-13 14:00:55.671 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lab_option_master 
[TRACE] 2025-02-13 14:00:55.671 - [任务 7][sybase_190 -LAB_DB_9] - Table lab_option_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.730 - [任务 7][sybase_190 -LAB_DB_9] - Table lab_option_master has been completed batch read 
[INFO ] 2025-02-13 14:00:55.730 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: poct_location_upd 
[TRACE] 2025-02-13 14:00:55.730 - [任务 7][sybase_190 -LAB_DB_9] - Table poct_location_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.786 - [任务 7][sybase_190 -LAB_DB_9] - Table poct_location_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:55.786 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lrr_audit 
[TRACE] 2025-02-13 14:00:55.786 - [任务 7][sybase_190 -LAB_DB_9] - Table lrr_audit is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.861 - [任务 7][sybase_190 -LAB_DB_9] - Table lrr_audit has been completed batch read 
[INFO ] 2025-02-13 14:00:55.864 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: object_class_map 
[TRACE] 2025-02-13 14:00:55.864 - [任务 7][sybase_190 -LAB_DB_9] - Table object_class_map is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.916 - [任务 7][sybase_190 -LAB_DB_9] - Table object_class_map has been completed batch read 
[INFO ] 2025-02-13 14:00:55.916 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_patient 
[TRACE] 2025-02-13 14:00:55.916 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_patient is going to be initial synced 
[INFO ] 2025-02-13 14:00:55.973 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_patient has been completed batch read 
[INFO ] 2025-02-13 14:00:55.973 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rs_dbversion 
[TRACE] 2025-02-13 14:00:55.973 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_dbversion is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.075 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_dbversion has been completed batch read 
[INFO ] 2025-02-13 14:00:56.075 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_dict 
[TRACE] 2025-02-13 14:00:56.075 - [任务 7][sybase_190 -LAB_DB_9] - Table test_dict is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.135 - [任务 7][sybase_190 -LAB_DB_9] - Table test_dict has been completed batch read 
[INFO ] 2025-02-13 14:00:56.135 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_gcrs_request_order 
[TRACE] 2025-02-13 14:00:56.135 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_gcrs_request_order is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.205 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_gcrs_request_order has been completed batch read 
[INFO ] 2025-02-13 14:00:56.205 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: trans_corp_organism 
[TRACE] 2025-02-13 14:00:56.205 - [任务 7][sybase_190 -LAB_DB_9] - Table trans_corp_organism is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.280 - [任务 7][sybase_190 -LAB_DB_9] - Table trans_corp_organism has been completed batch read 
[INFO ] 2025-02-13 14:00:56.281 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: message 
[TRACE] 2025-02-13 14:00:56.281 - [任务 7][sybase_190 -LAB_DB_9] - Table message is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.359 - [任务 7][sybase_190 -LAB_DB_9] - Table message has been completed batch read 
[INFO ] 2025-02-13 14:00:56.359 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: keyword_group_upd 
[TRACE] 2025-02-13 14:00:56.359 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_group_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.424 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_group_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:56.424 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ward_hospital_access 
[TRACE] 2025-02-13 14:00:56.424 - [任务 7][sybase_190 -LAB_DB_9] - Table ward_hospital_access is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.506 - [任务 7][sybase_190 -LAB_DB_9] - Table ward_hospital_access has been completed batch read 
[INFO ] 2025-02-13 14:00:56.506 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: report_map 
[TRACE] 2025-02-13 14:00:56.506 - [任务 7][sybase_190 -LAB_DB_9] - Table report_map is going to be initial synced 
[TRACE] 2025-02-13 14:00:56.563 - [任务 7][pg_hdtest - SP9_lab] - Table: rs_lastcommit already exists Index: TapIndex indexFields: [TapIndexField name origin fieldAsc true indexType null; TapIndexField name conn_id fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2025-02-13 14:00:56.563 - [任务 7][sybase_190 -LAB_DB_9] - Table report_map has been completed batch read 
[INFO ] 2025-02-13 14:00:56.563 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_request 
[TRACE] 2025-02-13 14:00:56.563 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_request is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.620 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_request has been completed batch read 
[INFO ] 2025-02-13 14:00:56.620 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: user_function_group 
[TRACE] 2025-02-13 14:00:56.620 - [任务 7][sybase_190 -LAB_DB_9] - Table user_function_group is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.686 - [任务 7][sybase_190 -LAB_DB_9] - Table user_function_group has been completed batch read 
[INFO ] 2025-02-13 14:00:56.686 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_lab_server 
[TRACE] 2025-02-13 14:00:56.686 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_lab_server is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.767 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_lab_server has been completed batch read 
[INFO ] 2025-02-13 14:00:56.767 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_registrable 
[TRACE] 2025-02-13 14:00:56.767 - [任务 7][sybase_190 -LAB_DB_9] - Table test_registrable is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.828 - [任务 7][sybase_190 -LAB_DB_9] - Table test_registrable has been completed batch read 
[INFO ] 2025-02-13 14:00:56.828 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: comment 
[TRACE] 2025-02-13 14:00:56.828 - [任务 7][sybase_190 -LAB_DB_9] - Table comment is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.890 - [任务 7][sybase_190 -LAB_DB_9] - Table comment has been completed batch read 
[INFO ] 2025-02-13 14:00:56.890 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: print_profile 
[TRACE] 2025-02-13 14:00:56.956 - [任务 7][sybase_190 -LAB_DB_9] - Table print_profile is going to be initial synced 
[INFO ] 2025-02-13 14:00:56.957 - [任务 7][sybase_190 -LAB_DB_9] - Table print_profile has been completed batch read 
[INFO ] 2025-02-13 14:00:56.957 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: queue_redirect_hist 
[TRACE] 2025-02-13 14:00:56.957 - [任务 7][sybase_190 -LAB_DB_9] - Table queue_redirect_hist is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.020 - [任务 7][sybase_190 -LAB_DB_9] - Table queue_redirect_hist has been completed batch read 
[INFO ] 2025-02-13 14:00:57.020 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_map_master_OLD 
[TRACE] 2025-02-13 14:00:57.020 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_master_OLD is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.125 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map_master_OLD has been completed batch read 
[INFO ] 2025-02-13 14:00:57.125 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_audit 
[TRACE] 2025-02-13 14:00:57.125 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_audit is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.189 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_audit has been completed batch read 
[INFO ] 2025-02-13 14:00:57.189 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_test_category 
[TRACE] 2025-02-13 14:00:57.189 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_category is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.261 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_category has been completed batch read 
[INFO ] 2025-02-13 14:00:57.261 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_type_upd 
[TRACE] 2025-02-13 14:00:57.261 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_type_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.394 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_type_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:57.394 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_model 
[TRACE] 2025-02-13 14:00:57.394 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_model is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.511 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_model has been completed batch read 
[INFO ] 2025-02-13 14:00:57.511 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: email_message 
[TRACE] 2025-02-13 14:00:57.511 - [任务 7][sybase_190 -LAB_DB_9] - Table email_message is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.597 - [任务 7][sybase_190 -LAB_DB_9] - Table email_message has been completed batch read 
[INFO ] 2025-02-13 14:00:57.598 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_reference_supp_upd 
[TRACE] 2025-02-13 14:00:57.598 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference_supp_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.701 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference_supp_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:57.701 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_specimen 
[TRACE] 2025-02-13 14:00:57.701 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_specimen is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.801 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_specimen has been completed batch read 
[INFO ] 2025-02-13 14:00:57.801 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rs_mat_status 
[TRACE] 2025-02-13 14:00:57.802 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_mat_status is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.868 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_mat_status has been completed batch read 
[INFO ] 2025-02-13 14:00:57.868 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rrs_channel 
[TRACE] 2025-02-13 14:00:57.868 - [任务 7][sybase_190 -LAB_DB_9] - Table rrs_channel is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.929 - [任务 7][sybase_190 -LAB_DB_9] - Table rrs_channel has been completed batch read 
[INFO ] 2025-02-13 14:00:57.929 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_user_option 
[TRACE] 2025-02-13 14:00:57.929 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_user_option is going to be initial synced 
[INFO ] 2025-02-13 14:00:57.989 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_user_option has been completed batch read 
[INFO ] 2025-02-13 14:00:57.990 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: email_address_book 
[TRACE] 2025-02-13 14:00:57.990 - [任务 7][sybase_190 -LAB_DB_9] - Table email_address_book is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.051 - [任务 7][sybase_190 -LAB_DB_9] - Table email_address_book has been completed batch read 
[INFO ] 2025-02-13 14:00:58.051 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: epr_dict_update 
[TRACE] 2025-02-13 14:00:58.051 - [任务 7][sybase_190 -LAB_DB_9] - Table epr_dict_update is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.153 - [任务 7][sybase_190 -LAB_DB_9] - Table epr_dict_update has been completed batch read 
[INFO ] 2025-02-13 14:00:58.156 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: public_holiday 
[TRACE] 2025-02-13 14:00:58.156 - [任务 7][sybase_190 -LAB_DB_9] - Table public_holiday is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.232 - [任务 7][sybase_190 -LAB_DB_9] - Table public_holiday has been completed batch read 
[INFO ] 2025-02-13 14:00:58.232 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pid_lab_checked 
[TRACE] 2025-02-13 14:00:58.232 - [任务 7][sybase_190 -LAB_DB_9] - Table pid_lab_checked is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.316 - [任务 7][sybase_190 -LAB_DB_9] - Table pid_lab_checked has been completed batch read 
[INFO ] 2025-02-13 14:00:58.316 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lab_function 
[TRACE] 2025-02-13 14:00:58.316 - [任务 7][sybase_190 -LAB_DB_9] - Table lab_function is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.433 - [任务 7][sybase_190 -LAB_DB_9] - Table lab_function has been completed batch read 
[INFO ] 2025-02-13 14:00:58.433 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_option_control 
[TRACE] 2025-02-13 14:00:58.433 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_control is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.532 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_control has been completed batch read 
[INFO ] 2025-02-13 14:00:58.532 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: access_control 
[TRACE] 2025-02-13 14:00:58.533 - [任务 7][sybase_190 -LAB_DB_9] - Table access_control is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.622 - [任务 7][sybase_190 -LAB_DB_9] - Table access_control has been completed batch read 
[INFO ] 2025-02-13 14:00:58.623 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisrep_append_ident_list 
[TRACE] 2025-02-13 14:00:58.623 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_append_ident_list is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.699 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_append_ident_list has been completed batch read 
[INFO ] 2025-02-13 14:00:58.700 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_usid_relation_master 
[TRACE] 2025-02-13 14:00:58.700 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_usid_relation_master is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.776 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_usid_relation_master has been completed batch read 
[INFO ] 2025-02-13 14:00:58.776 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: print_work 
[TRACE] 2025-02-13 14:00:58.776 - [任务 7][sybase_190 -LAB_DB_9] - Table print_work is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.854 - [任务 7][sybase_190 -LAB_DB_9] - Table print_work has been completed batch read 
[INFO ] 2025-02-13 14:00:58.854 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: transaction_log 
[TRACE] 2025-02-13 14:00:58.854 - [任务 7][sybase_190 -LAB_DB_9] - Table transaction_log is going to be initial synced 
[INFO ] 2025-02-13 14:00:58.929 - [任务 7][sybase_190 -LAB_DB_9] - Table transaction_log has been completed batch read 
[INFO ] 2025-02-13 14:00:58.930 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: usage_arch 
[TRACE] 2025-02-13 14:00:58.930 - [任务 7][sybase_190 -LAB_DB_9] - Table usage_arch is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.002 - [任务 7][sybase_190 -LAB_DB_9] - Table usage_arch has been completed batch read 
[INFO ] 2025-02-13 14:00:59.002 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_antibiotics 
[TRACE] 2025-02-13 14:00:59.002 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_antibiotics is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.101 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_antibiotics has been completed batch read 
[INFO ] 2025-02-13 14:00:59.101 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: spell_cust_word 
[TRACE] 2025-02-13 14:00:59.101 - [任务 7][sybase_190 -LAB_DB_9] - Table spell_cust_word is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.202 - [任务 7][sybase_190 -LAB_DB_9] - Table spell_cust_word has been completed batch read 
[INFO ] 2025-02-13 14:00:59.202 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_mb_testinfo 
[TRACE] 2025-02-13 14:00:59.202 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_mb_testinfo is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.290 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_mb_testinfo has been completed batch read 
[INFO ] 2025-02-13 14:00:59.290 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: trans_corp_antibiotics 
[TRACE] 2025-02-13 14:00:59.385 - [任务 7][sybase_190 -LAB_DB_9] - Table trans_corp_antibiotics is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.385 - [任务 7][sybase_190 -LAB_DB_9] - Table trans_corp_antibiotics has been completed batch read 
[INFO ] 2025-02-13 14:00:59.385 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_mb_request 
[TRACE] 2025-02-13 14:00:59.385 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_mb_request is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.496 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_mb_request has been completed batch read 
[INFO ] 2025-02-13 14:00:59.496 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_entity_group 
[TRACE] 2025-02-13 14:00:59.496 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_entity_group is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.596 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_entity_group has been completed batch read 
[INFO ] 2025-02-13 14:00:59.596 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_organism 
[TRACE] 2025-02-13 14:00:59.596 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_organism is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.665 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_organism has been completed batch read 
[INFO ] 2025-02-13 14:00:59.666 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mb_parasite_upd 
[TRACE] 2025-02-13 14:00:59.666 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_parasite_upd is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.785 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_parasite_upd has been completed batch read 
[INFO ] 2025-02-13 14:00:59.785 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: usage_index 
[TRACE] 2025-02-13 14:00:59.785 - [任务 7][sybase_190 -LAB_DB_9] - Table usage_index is going to be initial synced 
[INFO ] 2025-02-13 14:00:59.907 - [任务 7][sybase_190 -LAB_DB_9] - Table usage_index has been completed batch read 
[INFO ] 2025-02-13 14:00:59.907 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: global_ctr 
[TRACE] 2025-02-13 14:00:59.907 - [任务 7][sybase_190 -LAB_DB_9] - Table global_ctr is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.025 - [任务 7][sybase_190 -LAB_DB_9] - Table global_ctr has been completed batch read 
[INFO ] 2025-02-13 14:01:00.027 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: email_folder 
[TRACE] 2025-02-13 14:01:00.027 - [任务 7][sybase_190 -LAB_DB_9] - Table email_folder is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.230 - [任务 7][sybase_190 -LAB_DB_9] - Table email_folder has been completed batch read 
[INFO ] 2025-02-13 14:01:00.230 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_enum_result_master 
[TRACE] 2025-02-13 14:01:00.230 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_enum_result_master is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.370 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_enum_result_master has been completed batch read 
[INFO ] 2025-02-13 14:01:00.370 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pprofile_style 
[TRACE] 2025-02-13 14:01:00.370 - [任务 7][sybase_190 -LAB_DB_9] - Table pprofile_style is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.467 - [任务 7][sybase_190 -LAB_DB_9] - Table pprofile_style has been completed batch read 
[INFO ] 2025-02-13 14:01:00.468 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: keyword_group 
[TRACE] 2025-02-13 14:01:00.468 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_group is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.558 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_group has been completed batch read 
[INFO ] 2025-02-13 14:01:00.562 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_tag_grouping 
[TRACE] 2025-02-13 14:01:00.562 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_grouping is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.627 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_grouping has been completed batch read 
[INFO ] 2025-02-13 14:01:00.627 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: dim_loinc 
[TRACE] 2025-02-13 14:01:00.627 - [任务 7][sybase_190 -LAB_DB_9] - Table dim_loinc is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.719 - [任务 7][sybase_190 -LAB_DB_9] - Table dim_loinc has been completed batch read 
[INFO ] 2025-02-13 14:01:00.719 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_tag_grouping_upd 
[TRACE] 2025-02-13 14:01:00.719 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_grouping_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:00.887 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_grouping_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:00.887 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mb_organism 
[TRACE] 2025-02-13 14:01:00.887 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_organism is going to be initial synced 
[INFO ] 2025-02-13 14:01:01.037 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_organism has been completed batch read 
[INFO ] 2025-02-13 14:01:01.037 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_reference 
[TRACE] 2025-02-13 14:01:01.038 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference is going to be initial synced 
[INFO ] 2025-02-13 14:01:01.151 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference has been completed batch read 
[INFO ] 2025-02-13 14:01:01.151 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pprofile_style_upd 
[TRACE] 2025-02-13 14:01:01.151 - [任务 7][sybase_190 -LAB_DB_9] - Table pprofile_style_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:01.250 - [任务 7][sybase_190 -LAB_DB_9] - Table pprofile_style_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:01.250 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mis_table 
[TRACE] 2025-02-13 14:01:01.250 - [任务 7][sybase_190 -LAB_DB_9] - Table mis_table is going to be initial synced 
[INFO ] 2025-02-13 14:01:01.344 - [任务 7][sybase_190 -LAB_DB_9] - Table mis_table has been completed batch read 
[INFO ] 2025-02-13 14:01:01.344 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: phlc_lab_order_map 
[TRACE] 2025-02-13 14:01:01.344 - [任务 7][sybase_190 -LAB_DB_9] - Table phlc_lab_order_map is going to be initial synced 
[INFO ] 2025-02-13 14:01:01.490 - [任务 7][sybase_190 -LAB_DB_9] - Table phlc_lab_order_map has been completed batch read 
[INFO ] 2025-02-13 14:01:01.490 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: cras_location 
[TRACE] 2025-02-13 14:01:01.490 - [任务 7][sybase_190 -LAB_DB_9] - Table cras_location is going to be initial synced 
[INFO ] 2025-02-13 14:01:01.601 - [任务 7][sybase_190 -LAB_DB_9] - Table cras_location has been completed batch read 
[INFO ] 2025-02-13 14:01:01.601 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_test_master 
[TRACE] 2025-02-13 14:01:01.601 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_master is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.413 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_test_master has been completed batch read 
[INFO ] 2025-02-13 14:01:02.414 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_option_upd 
[TRACE] 2025-02-13 14:01:02.414 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.488 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_option_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:02.489 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: print_profile_upd 
[TRACE] 2025-02-13 14:01:02.489 - [任务 7][sybase_190 -LAB_DB_9] - Table print_profile_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.565 - [任务 7][sybase_190 -LAB_DB_9] - Table print_profile_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:02.566 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_antibiotics_master 
[TRACE] 2025-02-13 14:01:02.566 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_antibiotics_master is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.642 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_antibiotics_master has been completed batch read 
[INFO ] 2025-02-13 14:01:02.642 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mb_antibiotics_upd 
[TRACE] 2025-02-13 14:01:02.642 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_antibiotics_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.720 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_antibiotics_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:02.720 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_loinc 
[TRACE] 2025-02-13 14:01:02.720 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_loinc is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.787 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_loinc has been completed batch read 
[INFO ] 2025-02-13 14:01:02.787 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rrs_channel_upd 
[TRACE] 2025-02-13 14:01:02.787 - [任务 7][sybase_190 -LAB_DB_9] - Table rrs_channel_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.867 - [任务 7][sybase_190 -LAB_DB_9] - Table rrs_channel_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:02.868 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_testrslt_audit 
[TRACE] 2025-02-13 14:01:02.868 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_testrslt_audit is going to be initial synced 
[INFO ] 2025-02-13 14:01:02.933 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_testrslt_audit has been completed batch read 
[INFO ] 2025-02-13 14:01:02.934 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pbcatcol 
[TRACE] 2025-02-13 14:01:02.934 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatcol is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.077 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatcol has been completed batch read 
[INFO ] 2025-02-13 14:01:03.077 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: reqlist_log 
[TRACE] 2025-02-13 14:01:03.077 - [任务 7][sybase_190 -LAB_DB_9] - Table reqlist_log is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.191 - [任务 7][sybase_190 -LAB_DB_9] - Table reqlist_log has been completed batch read 
[INFO ] 2025-02-13 14:01:03.191 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: z_ccc_big5_unicode 
[TRACE] 2025-02-13 14:01:03.191 - [任务 7][sybase_190 -LAB_DB_9] - Table z_ccc_big5_unicode is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.280 - [任务 7][sybase_190 -LAB_DB_9] - Table z_ccc_big5_unicode has been completed batch read 
[INFO ] 2025-02-13 14:01:03.280 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_map 
[TRACE] 2025-02-13 14:01:03.280 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.362 - [任务 7][sybase_190 -LAB_DB_9] - Table test_map has been completed batch read 
[INFO ] 2025-02-13 14:01:03.362 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_valid 
[TRACE] 2025-02-13 14:01:03.362 - [任务 7][sybase_190 -LAB_DB_9] - Table test_valid is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.434 - [任务 7][sybase_190 -LAB_DB_9] - Table test_valid has been completed batch read 
[INFO ] 2025-02-13 14:01:03.434 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: file_audit 
[TRACE] 2025-02-13 14:01:03.434 - [任务 7][sybase_190 -LAB_DB_9] - Table file_audit is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.502 - [任务 7][sybase_190 -LAB_DB_9] - Table file_audit has been completed batch read 
[INFO ] 2025-02-13 14:01:03.502 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_mdro_rule 
[TRACE] 2025-02-13 14:01:03.502 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_mdro_rule is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.574 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_mdro_rule has been completed batch read 
[INFO ] 2025-02-13 14:01:03.574 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: office 
[TRACE] 2025-02-13 14:01:03.574 - [任务 7][sybase_190 -LAB_DB_9] - Table office is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.669 - [任务 7][sybase_190 -LAB_DB_9] - Table office has been completed batch read 
[INFO ] 2025-02-13 14:01:03.669 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: cpi_transaction 
[TRACE] 2025-02-13 14:01:03.669 - [任务 7][sybase_190 -LAB_DB_9] - Table cpi_transaction is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.759 - [任务 7][sybase_190 -LAB_DB_9] - Table cpi_transaction has been completed batch read 
[INFO ] 2025-02-13 14:01:03.759 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: print_def 
[TRACE] 2025-02-13 14:01:03.759 - [任务 7][sybase_190 -LAB_DB_9] - Table print_def is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.837 - [任务 7][sybase_190 -LAB_DB_9] - Table print_def has been completed batch read 
[INFO ] 2025-02-13 14:01:03.837 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_check 
[TRACE] 2025-02-13 14:01:03.837 - [任务 7][sybase_190 -LAB_DB_9] - Table test_check is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.900 - [任务 7][sybase_190 -LAB_DB_9] - Table test_check has been completed batch read 
[INFO ] 2025-02-13 14:01:03.900 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: poct_location 
[TRACE] 2025-02-13 14:01:03.900 - [任务 7][sybase_190 -LAB_DB_9] - Table poct_location is going to be initial synced 
[INFO ] 2025-02-13 14:01:03.974 - [任务 7][sybase_190 -LAB_DB_9] - Table poct_location has been completed batch read 
[INFO ] 2025-02-13 14:01:03.974 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_check_upd 
[TRACE] 2025-02-13 14:01:03.974 - [任务 7][sybase_190 -LAB_DB_9] - Table test_check_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.061 - [任务 7][sybase_190 -LAB_DB_9] - Table test_check_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:04.062 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_operation_list_OLD 
[TRACE] 2025-02-13 14:01:04.062 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_operation_list_OLD is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.153 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_operation_list_OLD has been completed batch read 
[INFO ] 2025-02-13 14:01:04.153 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: default_print_queue 
[TRACE] 2025-02-13 14:01:04.153 - [任务 7][sybase_190 -LAB_DB_9] - Table default_print_queue is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.241 - [任务 7][sybase_190 -LAB_DB_9] - Table default_print_queue has been completed batch read 
[INFO ] 2025-02-13 14:01:04.241 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_upd 
[TRACE] 2025-02-13 14:01:04.241 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.314 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:04.314 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_tag_setup 
[TRACE] 2025-02-13 14:01:04.314 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_setup is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.384 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_tag_setup has been completed batch read 
[INFO ] 2025-02-13 14:01:04.384 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: corp_antibiotics_upd 
[TRACE] 2025-02-13 14:01:04.384 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_antibiotics_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.466 - [任务 7][sybase_190 -LAB_DB_9] - Table corp_antibiotics_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:04.466 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: report_style_upd 
[TRACE] 2025-02-13 14:01:04.466 - [任务 7][sybase_190 -LAB_DB_9] - Table report_style_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.538 - [任务 7][sybase_190 -LAB_DB_9] - Table report_style_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:04.538 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pid_check 
[TRACE] 2025-02-13 14:01:04.538 - [任务 7][sybase_190 -LAB_DB_9] - Table pid_check is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.616 - [任务 7][sybase_190 -LAB_DB_9] - Table pid_check has been completed batch read 
[INFO ] 2025-02-13 14:01:04.616 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pbcatedt 
[TRACE] 2025-02-13 14:01:04.616 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatedt is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.689 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcatedt has been completed batch read 
[INFO ] 2025-02-13 14:01:04.689 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: email_user_group 
[TRACE] 2025-02-13 14:01:04.689 - [任务 7][sybase_190 -LAB_DB_9] - Table email_user_group is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.787 - [任务 7][sybase_190 -LAB_DB_9] - Table email_user_group has been completed batch read 
[INFO ] 2025-02-13 14:01:04.787 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient_upd 
[TRACE] 2025-02-13 14:01:04.787 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.853 - [任务 7][sybase_190 -LAB_DB_9] - Table patient_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:04.854 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: epr_patient_transfer_list 
[TRACE] 2025-02-13 14:01:04.854 - [任务 7][sybase_190 -LAB_DB_9] - Table epr_patient_transfer_list is going to be initial synced 
[INFO ] 2025-02-13 14:01:04.944 - [任务 7][sybase_190 -LAB_DB_9] - Table epr_patient_transfer_list has been completed batch read 
[INFO ] 2025-02-13 14:01:04.944 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: event_reminder_audit 
[TRACE] 2025-02-13 14:01:04.944 - [任务 7][sybase_190 -LAB_DB_9] - Table event_reminder_audit is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.023 - [任务 7][sybase_190 -LAB_DB_9] - Table event_reminder_audit has been completed batch read 
[INFO ] 2025-02-13 14:01:05.023 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: pbcattbl 
[TRACE] 2025-02-13 14:01:05.023 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcattbl is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.117 - [任务 7][sybase_190 -LAB_DB_9] - Table pbcattbl has been completed batch read 
[INFO ] 2025-02-13 14:01:05.118 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: QC_Sample_Map_upd 
[TRACE] 2025-02-13 14:01:05.118 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_Sample_Map_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.234 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_Sample_Map_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:05.234 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: email_workbench 
[TRACE] 2025-02-13 14:01:05.234 - [任务 7][sybase_190 -LAB_DB_9] - Table email_workbench is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.339 - [任务 7][sybase_190 -LAB_DB_9] - Table email_workbench has been completed batch read 
[INFO ] 2025-02-13 14:01:05.339 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: message_dict 
[TRACE] 2025-02-13 14:01:05.339 - [任务 7][sybase_190 -LAB_DB_9] - Table message_dict is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.425 - [任务 7][sybase_190 -LAB_DB_9] - Table message_dict has been completed batch read 
[INFO ] 2025-02-13 14:01:05.425 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: QC_range 
[TRACE] 2025-02-13 14:01:05.425 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_range is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.517 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_range has been completed batch read 
[INFO ] 2025-02-13 14:01:05.517 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: printqueue 
[TRACE] 2025-02-13 14:01:05.517 - [任务 7][sybase_190 -LAB_DB_9] - Table printqueue is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.590 - [任务 7][sybase_190 -LAB_DB_9] - Table printqueue has been completed batch read 
[INFO ] 2025-02-13 14:01:05.590 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: report_count 
[TRACE] 2025-02-13 14:01:05.590 - [任务 7][sybase_190 -LAB_DB_9] - Table report_count is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.657 - [任务 7][sybase_190 -LAB_DB_9] - Table report_count has been completed batch read 
[INFO ] 2025-02-13 14:01:05.657 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lis_body_system_map 
[TRACE] 2025-02-13 14:01:05.657 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_body_system_map is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.736 - [任务 7][sybase_190 -LAB_DB_9] - Table lis_body_system_map has been completed batch read 
[INFO ] 2025-02-13 14:01:05.736 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: loe_out_bound_message 
[TRACE] 2025-02-13 14:01:05.736 - [任务 7][sybase_190 -LAB_DB_9] - Table loe_out_bound_message is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.820 - [任务 7][sybase_190 -LAB_DB_9] - Table loe_out_bound_message has been completed batch read 
[INFO ] 2025-02-13 14:01:05.820 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: z_ccc_big5 
[TRACE] 2025-02-13 14:01:05.820 - [任务 7][sybase_190 -LAB_DB_9] - Table z_ccc_big5 is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.918 - [任务 7][sybase_190 -LAB_DB_9] - Table z_ccc_big5 has been completed batch read 
[INFO ] 2025-02-13 14:01:05.918 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: event_schedule 
[TRACE] 2025-02-13 14:01:05.996 - [任务 7][sybase_190 -LAB_DB_9] - Table event_schedule is going to be initial synced 
[INFO ] 2025-02-13 14:01:05.997 - [任务 7][sybase_190 -LAB_DB_9] - Table event_schedule has been completed batch read 
[INFO ] 2025-02-13 14:01:05.997 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisrep_tab_exclude_list 
[TRACE] 2025-02-13 14:01:05.997 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_tab_exclude_list is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.297 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_tab_exclude_list has been completed batch read 
[INFO ] 2025-02-13 14:01:06.297 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_ap_request 
[TRACE] 2025-02-13 14:01:06.297 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_ap_request is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.368 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_ap_request has been completed batch read 
[INFO ] 2025-02-13 14:01:06.368 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: patient 
[TRACE] 2025-02-13 14:01:06.368 - [任务 7][sybase_190 -LAB_DB_9] - Table patient is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.477 - [任务 7][sybase_190 -LAB_DB_9] - Table patient has been completed batch read 
[INFO ] 2025-02-13 14:01:06.478 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: mb_antibiotics 
[TRACE] 2025-02-13 14:01:06.478 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_antibiotics is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.550 - [任务 7][sybase_190 -LAB_DB_9] - Table mb_antibiotics has been completed batch read 
[INFO ] 2025-02-13 14:01:06.550 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: QC_type 
[TRACE] 2025-02-13 14:01:06.550 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_type is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.620 - [任务 7][sybase_190 -LAB_DB_9] - Table QC_type has been completed batch read 
[INFO ] 2025-02-13 14:01:06.620 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: hospital 
[TRACE] 2025-02-13 14:01:06.620 - [任务 7][sybase_190 -LAB_DB_9] - Table hospital is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.700 - [任务 7][sybase_190 -LAB_DB_9] - Table hospital has been completed batch read 
[INFO ] 2025-02-13 14:01:06.701 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_action_upd 
[TRACE] 2025-02-13 14:01:06.701 - [任务 7][sybase_190 -LAB_DB_9] - Table test_action_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.763 - [任务 7][sybase_190 -LAB_DB_9] - Table test_action_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:06.763 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: labuser_upd 
[TRACE] 2025-02-13 14:01:06.763 - [任务 7][sybase_190 -LAB_DB_9] - Table labuser_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.834 - [任务 7][sybase_190 -LAB_DB_9] - Table labuser_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:06.834 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_profile 
[TRACE] 2025-02-13 14:01:06.834 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_profile is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.903 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_profile has been completed batch read 
[INFO ] 2025-02-13 14:01:06.903 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: testchk_def 
[TRACE] 2025-02-13 14:01:06.903 - [任务 7][sybase_190 -LAB_DB_9] - Table testchk_def is going to be initial synced 
[INFO ] 2025-02-13 14:01:06.975 - [任务 7][sybase_190 -LAB_DB_9] - Table testchk_def has been completed batch read 
[INFO ] 2025-02-13 14:01:06.975 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rs_threads 
[TRACE] 2025-02-13 14:01:06.975 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_threads is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.040 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_threads has been completed batch read 
[INFO ] 2025-02-13 14:01:07.040 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: print_work2 
[TRACE] 2025-02-13 14:01:07.040 - [任务 7][sybase_190 -LAB_DB_9] - Table print_work2 is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.152 - [任务 7][sybase_190 -LAB_DB_9] - Table print_work2 has been completed batch read 
[INFO ] 2025-02-13 14:01:07.152 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: spell_cust_dict 
[TRACE] 2025-02-13 14:01:07.152 - [任务 7][sybase_190 -LAB_DB_9] - Table spell_cust_dict is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.226 - [任务 7][sybase_190 -LAB_DB_9] - Table spell_cust_dict has been completed batch read 
[INFO ] 2025-02-13 14:01:07.226 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: keyword_list_upd 
[TRACE] 2025-02-13 14:01:07.226 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_list_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.308 - [任务 7][sybase_190 -LAB_DB_9] - Table keyword_list_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:07.309 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_dict_upd 
[TRACE] 2025-02-13 14:01:07.309 - [任务 7][sybase_190 -LAB_DB_9] - Table test_dict_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.376 - [任务 7][sybase_190 -LAB_DB_9] - Table test_dict_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:07.376 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: workbench_upd 
[TRACE] 2025-02-13 14:01:07.376 - [任务 7][sybase_190 -LAB_DB_9] - Table workbench_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.459 - [任务 7][sybase_190 -LAB_DB_9] - Table workbench_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:07.459 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_bb_request_inv 
[TRACE] 2025-02-13 14:01:07.459 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_bb_request_inv is going to be initial synced 
[TRACE] 2025-02-13 14:01:07.519 - [任务 7][pg_hdtest - SP9_lab] - Table: cpi_transaction already exists Index: TapIndex indexFields: [TapIndexField name hospital_code fieldAsc true indexType null; TapIndexField name transaction_datetime fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2025-02-13 14:01:07.524 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_bb_request_inv has been completed batch read 
[INFO ] 2025-02-13 14:01:07.524 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: crs_followup 
[TRACE] 2025-02-13 14:01:07.524 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_followup is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.599 - [任务 7][sybase_190 -LAB_DB_9] - Table crs_followup has been completed batch read 
[INFO ] 2025-02-13 14:01:07.599 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisrep_build_uidx_list 
[TRACE] 2025-02-13 14:01:07.599 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_build_uidx_list is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.687 - [任务 7][sybase_190 -LAB_DB_9] - Table lisrep_build_uidx_list has been completed batch read 
[INFO ] 2025-02-13 14:01:07.687 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_profile_upd 
[TRACE] 2025-02-13 14:01:07.687 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_profile_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.763 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_profile_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:07.763 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: report_setup 
[TRACE] 2025-02-13 14:01:07.763 - [任务 7][sybase_190 -LAB_DB_9] - Table report_setup is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.838 - [任务 7][sybase_190 -LAB_DB_9] - Table report_setup has been completed batch read 
[INFO ] 2025-02-13 14:01:07.838 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ccc_big5_130813_OLD 
[TRACE] 2025-02-13 14:01:07.838 - [任务 7][sybase_190 -LAB_DB_9] - Table ccc_big5_130813_OLD is going to be initial synced 
[INFO ] 2025-02-13 14:01:07.992 - [任务 7][sybase_190 -LAB_DB_9] - Table ccc_big5_130813_OLD has been completed batch read 
[INFO ] 2025-02-13 14:01:07.992 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_color 
[TRACE] 2025-02-13 14:01:07.992 - [任务 7][sybase_190 -LAB_DB_9] - Table test_color is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.241 - [任务 7][sybase_190 -LAB_DB_9] - Table test_color has been completed batch read 
[INFO ] 2025-02-13 14:01:08.241 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rsltflow_upd 
[TRACE] 2025-02-13 14:01:08.241 - [任务 7][sybase_190 -LAB_DB_9] - Table rsltflow_upd is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.401 - [任务 7][sybase_190 -LAB_DB_9] - Table rsltflow_upd has been completed batch read 
[INFO ] 2025-02-13 14:01:08.401 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: audit_trail 
[TRACE] 2025-02-13 14:01:08.401 - [任务 7][sybase_190 -LAB_DB_9] - Table audit_trail is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.502 - [任务 7][sybase_190 -LAB_DB_9] - Table audit_trail has been completed batch read 
[INFO ] 2025-02-13 14:01:08.502 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: rs_ticket_history 
[TRACE] 2025-02-13 14:01:08.502 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_ticket_history is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.576 - [任务 7][sybase_190 -LAB_DB_9] - Table rs_ticket_history has been completed batch read 
[INFO ] 2025-02-13 14:01:08.577 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: test_reference_supp 
[TRACE] 2025-02-13 14:01:08.577 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference_supp is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.628 - [任务 7][sybase_190 -LAB_DB_9] - Table test_reference_supp has been completed batch read 
[INFO ] 2025-02-13 14:01:08.628 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: analyser_type 
[TRACE] 2025-02-13 14:01:08.628 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_type is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.710 - [任务 7][sybase_190 -LAB_DB_9] - Table analyser_type has been completed batch read 
[INFO ] 2025-02-13 14:01:08.710 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lab_spec_ctr 
[TRACE] 2025-02-13 14:01:08.710 - [任务 7][sybase_190 -LAB_DB_9] - Table lab_spec_ctr is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.789 - [任务 7][sybase_190 -LAB_DB_9] - Table lab_spec_ctr has been completed batch read 
[INFO ] 2025-02-13 14:01:08.789 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: lisg_tasklist 
[TRACE] 2025-02-13 14:01:08.789 - [任务 7][sybase_190 -LAB_DB_9] - Table lisg_tasklist is going to be initial synced 
[TRACE] 2025-02-13 14:01:08.848 - [任务 7][sybase_190 -LAB_DB_9] - Query snapshot row size completed: sybase_190 -LAB_DB_9(d08e6019-4c23-419b-91c6-0580ccd3f230) 
[INFO ] 2025-02-13 14:01:08.849 - [任务 7][sybase_190 -LAB_DB_9] - Table lisg_tasklist has been completed batch read 
[INFO ] 2025-02-13 14:01:08.849 - [任务 7][sybase_190 -LAB_DB_9] - Starting batch read from table: ap_standard_list_alias 
[TRACE] 2025-02-13 14:01:08.849 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_alias is going to be initial synced 
[INFO ] 2025-02-13 14:01:08.916 - [任务 7][sybase_190 -LAB_DB_9] - Table ap_standard_list_alias has been completed batch read 
[TRACE] 2025-02-13 14:01:08.916 - [任务 7][sybase_190 -LAB_DB_9] - Initial sync completed 
[INFO ] 2025-02-13 14:01:08.916 - [任务 7][sybase_190 -LAB_DB_9] - Batch read completed. 
[INFO ] 2025-02-13 14:01:08.916 - [任务 7][sybase_190 -LAB_DB_9] - Task run completed 
[TRACE] 2025-02-13 14:01:11.739 - [任务 7][pg_hdtest - SP9_lab] - Table: rs_threads already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name conn_id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-02-13 14:01:13.104 - [任务 7][pg_hdtest - SP9_lab] - Table: rs_ticket_history already exists Index: TapIndex indexFields: [TapIndexField name cnt fieldAsc true indexType null; ] and will no longer create index 
[WARN ] 2025-02-13 14:01:14.252 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.253 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8c9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8ca, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a90a93b9d13c66fc8cb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8cc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8cd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8ce, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8cf, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.254 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8d0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8d1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a91a93b9d13c66fc8d2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.255 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8d9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8da, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8db, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8dc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a92a93b9d13c66fc8dd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8de, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.256 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8df, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.257 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8e9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8ea, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8eb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8ec, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a93a93b9d13c66fc8ed, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8ee, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.258 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8ef, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.259 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8f9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8fa, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a94a93b9d13c66fc8fb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc8fc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc8fd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc8fe, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.260 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc8ff, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.261 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc900, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.261 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc901, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.261 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc902, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.261 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc903, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.261 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc904, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.261 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc905, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc906, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc907, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc908, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a95a93b9d13c66fc909, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc90a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc90b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.262 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc90c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc90d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc90e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc90f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc910, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc911, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc912, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.263 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc913, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc914, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc915, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc916, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a96a93b9d13c66fc917, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc918, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc919, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc91a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.264 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc91b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc91c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc91d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc91e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc91f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc920, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc921, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.265 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc922, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc923, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc924, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc925, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a97a93b9d13c66fc926, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc927, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc928, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.266 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc929, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.267 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc92a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.267 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc92b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.272 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc92c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.272 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc92d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.272 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc92e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.272 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc92f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.272 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc930, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc931, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc932, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc933, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a98a93b9d13c66fc934, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc935, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc936, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc937, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.273 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc938, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc939, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc93a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc93b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc93c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc93d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc93e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc93f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a99a93b9d13c66fc940, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.274 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc941, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.275 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc942, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc943, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc944, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc945, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc946, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc947, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc948, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc949, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc94a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9aa93b9d13c66fc94b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.276 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc94c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc94d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc94e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc94f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc950, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc951, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc952, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc953, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc954, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.277 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ba93b9d13c66fc955, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc956, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc957, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc958, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc959, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc95a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc95b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.278 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc95c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.279 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ca93b9d13c66fc95d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.279 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9da93b9d13c66fc95e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.279 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9da93b9d13c66fc95f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.279 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9da93b9d13c66fc960, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.280 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9da93b9d13c66fc961, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.280 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9da93b9d13c66fc962, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.280 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9da93b9d13c66fc963, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc964, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc965, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc966, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc967, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc968, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc969, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.281 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc96a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9ea93b9d13c66fc96b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc96c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc96d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc96e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc96f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc970, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.282 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc971, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.283 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc972, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.283 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc973, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.283 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc974, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.283 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc975, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.283 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc976, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.283 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8a9fa93b9d13c66fc977, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc978, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc979, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc97a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc97b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc97c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc97d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc97e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.284 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc97f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc980, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc981, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc982, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa0a93b9d13c66fc983, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc984, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc985, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc986, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.285 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc987, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.791 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc988, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.791 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc989, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.791 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc98a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.791 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc98b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.791 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc98c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc98d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc98e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa1a93b9d13c66fc98f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc990, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc991, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc992, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc993, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc994, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc995, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.792 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc996, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc997, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc998, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa2a93b9d13c66fc999, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc99a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc99b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc99c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc99d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc99e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc99f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc9a0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc9a1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc9a2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc9a3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc9a4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa3a93b9d13c66fc9a5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9a6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.793 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9a7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9a8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9a9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9aa, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9ab, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9ac, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9ad, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-13 14:01:14.794 - [任务 7][pg_hdtest - SP9_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67ad8aa4a93b9d13c66fc9ae, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d08e6019-4c23-419b-91c6-0580ccd3f230], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-13 14:01:14.891 - [任务 7][sybase_190 -LAB_DB_9] - Node sybase_190 -LAB_DB_9[d08e6019-4c23-419b-91c6-0580ccd3f230] running status set to false 
[TRACE] 2025-02-13 14:01:14.891 - [任务 7][pg_hdtest - SP9_lab] - Node pg_hdtest - SP9_lab[bed9d12c-bbd0-4f68-9ff2-02ee1e67b248] running status set to false 
[TRACE] 2025-02-13 14:01:14.891 - [任务 7][pg_hdtest - SP9_lab] - PDK connector node stopped: HazelcastTargetPdkDataNode_bed9d12c-bbd0-4f68-9ff2-02ee1e67b248_1739426447240 
[TRACE] 2025-02-13 14:01:14.891 - [任务 7][pg_hdtest - SP9_lab] - PDK connector node released: HazelcastTargetPdkDataNode_bed9d12c-bbd0-4f68-9ff2-02ee1e67b248_1739426447240 
[TRACE] 2025-02-13 14:01:14.891 - [任务 7][pg_hdtest - SP9_lab] - Node pg_hdtest - SP9_lab[bed9d12c-bbd0-4f68-9ff2-02ee1e67b248] schema data cleaned 
[TRACE] 2025-02-13 14:01:14.892 - [任务 7][pg_hdtest - SP9_lab] - Node pg_hdtest - SP9_lab[bed9d12c-bbd0-4f68-9ff2-02ee1e67b248] monitor closed 
[TRACE] 2025-02-13 14:01:14.892 - [任务 7][pg_hdtest - SP9_lab] - Node pg_hdtest - SP9_lab[bed9d12c-bbd0-4f68-9ff2-02ee1e67b248] close complete, cost 17 ms 
[TRACE] 2025-02-13 14:01:15.024 - [任务 7][sybase_190 -LAB_DB_9] - PDK connector node stopped: HazelcastSourcePdkDataNode_d08e6019-4c23-419b-91c6-0580ccd3f230_1739426447209 
[TRACE] 2025-02-13 14:01:15.024 - [任务 7][sybase_190 -LAB_DB_9] - PDK connector node released: HazelcastSourcePdkDataNode_d08e6019-4c23-419b-91c6-0580ccd3f230_1739426447209 
[TRACE] 2025-02-13 14:01:15.024 - [任务 7][sybase_190 -LAB_DB_9] - Node sybase_190 -LAB_DB_9[d08e6019-4c23-419b-91c6-0580ccd3f230] schema data cleaned 
[TRACE] 2025-02-13 14:01:15.025 - [任务 7][sybase_190 -LAB_DB_9] - Node sybase_190 -LAB_DB_9[d08e6019-4c23-419b-91c6-0580ccd3f230] monitor closed 
[TRACE] 2025-02-13 14:01:15.025 - [任务 7][sybase_190 -LAB_DB_9] - Node sybase_190 -LAB_DB_9[d08e6019-4c23-419b-91c6-0580ccd3f230] close complete, cost 150 ms 
[TRACE] 2025-02-13 14:01:15.074 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-13 14:01:15.074 - [任务 7] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3e7c9839 
[TRACE] 2025-02-13 14:01:15.202 - [任务 7] - Stop task milestones: 67ad8a65870d74413f2b9242(任务 7)  
[TRACE] 2025-02-13 14:01:15.202 - [任务 7] - Stopped task aspect(s) 
[TRACE] 2025-02-13 14:01:15.202 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2025-02-13 14:01:15.202 - [任务 7] - Task stopped. 
[TRACE] 2025-02-13 14:01:15.235 - [任务 7] - Remove memory task client succeed, task: 任务 7[67ad8a65870d74413f2b9242] 
[TRACE] 2025-02-13 14:01:15.235 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[67ad8a65870d74413f2b9242] 
