[TRACE] 2025-03-20 18:24:41.928 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Task initialization... 
[TRACE] 2025-03-20 18:24:41.928 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Start task milestones: 67dbecdf64ab95611fe27d06(t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588) 
[INFO ] 2025-03-20 18:24:42.077 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Loading table structure completed 
[TRACE] 2025-03-20 18:24:42.077 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-03-20 18:24:42.077 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-20 18:24:42.081 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@247d7a1b 
[TRACE] 2025-03-20 18:24:42.172 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Stop task milestones: 67dbecdf64ab95611fe27d06(t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588)  
[TRACE] 2025-03-20 18:24:42.208 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Stopped task aspect(s) 
[TRACE] 2025-03-20 18:24:42.208 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - Snapshot order controller have been removed 
[INFO ] 2025-03-20 18:25:51.190 - [t_7.2.1-1-sybase_to_pg_bmsql_config_1742442514217_8588] - This task already stopped. 
