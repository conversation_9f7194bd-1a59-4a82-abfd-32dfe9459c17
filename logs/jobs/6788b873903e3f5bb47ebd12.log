[TRACE] 2025-01-16 15:42:44.309 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - load tapTable task 6788b873903e3f5bb47ebd11-c358a50a-4168-4a10-98b7-ff7d99b4d712 complete, cost 659ms 
[ERROR] 2025-01-16 15:42:44.319 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:44.328 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:44.336 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:44.345 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 15:42:44.360 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - load tapTable task 6788b873903e3f5bb47ebd11-ed579ec5-5d14-4cad-bd08-7d65f0f94ccc complete, cost 530ms 
[ERROR] 2025-01-16 15:42:44.369 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:44.377 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:44.385 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:44.393 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 15:42:54.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Task initialization... 
[TRACE] 2025-01-16 15:42:54.004 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Start task milestones: 6788b873903e3f5bb47ebd12(t_89.4-MongoDB->Many's_1736997187904_1988-1737013278) 
[TRACE] 2025-01-16 15:42:54.418 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - load tapTable task 6788b873903e3f5bb47ebd11-56c966bc-cd48-4496-bb43-68bd72ab87b1 complete, cost 312ms 
[ERROR] 2025-01-16 15:42:54.430 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:54.441 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:54.456 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:42:54.467 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2025-01-16 15:42:54.502 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Loading table structure completed 
[TRACE] 2025-01-16 15:42:54.503 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-16 15:42:54.650 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - The engine receives t_89.4-MongoDB->Many's_1736997187904_1988-1737013278 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-16 15:42:54.650 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Task started 
[TRACE] 2025-01-16 15:42:54.707 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[10a2902c-0557-4fa3-8690-709ae3abc170] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.707 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[77cf6bbf-8000-47b3-91ee-222cb445d6d9] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.707 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[10a2902c-0557-4fa3-8690-709ae3abc170] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.707 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[bb51f2cc-f4f3-49e8-bb91-2408246217a2] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.707 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[77cf6bbf-8000-47b3-91ee-222cb445d6d9] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node js_processor(JS: 10a2902c-0557-4fa3-8690-709ae3abc170) enable batch process 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[9b7de206-0cd5-4203-aa8b-b3f023e42825] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[bb51f2cc-f4f3-49e8-bb91-2408246217a2] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[9b7de206-0cd5-4203-aa8b-b3f023e42825] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node field_rename_processor(Field Rename: 77cf6bbf-8000-47b3-91ee-222cb445d6d9) enable batch process 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node js_processor(JS: bb51f2cc-f4f3-49e8-bb91-2408246217a2) enable batch process 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[18b20c67-4454-41d7-9cf2-59720e61d21c] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[6d525038-9886-425e-8f9a-ffde68b7b967] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[18b20c67-4454-41d7-9cf2-59720e61d21c] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[6d525038-9886-425e-8f9a-ffde68b7b967] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[2d548ea0-8d29-462b-a56d-ab5b51eddcf2] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node field_rename_processor(Field Rename: 18b20c67-4454-41d7-9cf2-59720e61d21c) enable batch process 
[TRACE] 2025-01-16 15:42:54.708 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[2d548ea0-8d29-462b-a56d-ab5b51eddcf2] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.709 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node js_processor(JS: 6d525038-9886-425e-8f9a-ffde68b7b967) enable batch process 
[TRACE] 2025-01-16 15:42:54.709 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node js_processor(JS: 2d548ea0-8d29-462b-a56d-ab5b51eddcf2) enable batch process 
[TRACE] 2025-01-16 15:42:54.710 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[2f78309c-d20d-4107-9cfc-89678033243c] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.710 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8805396f-77a3-433e-bd5f-8507256e5cd1] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.710 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8805396f-77a3-433e-bd5f-8507256e5cd1] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[2f78309c-d20d-4107-9cfc-89678033243c] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[a0ab0c26-7f0b-4a35-a719-038214845206] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[a0ab0c26-7f0b-4a35-a719-038214845206] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node js_processor(JS: a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36) enable batch process 
[TRACE] 2025-01-16 15:42:54.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node field_rename_processor(Field Rename: a0ab0c26-7f0b-4a35-a719-038214845206) enable batch process 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[0ef4c39e-bf16-4d1c-964c-4e4940da8d11] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[5d7e378b-0ac3-4db3-be63-fd143ab1980b] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[0ef4c39e-bf16-4d1c-964c-4e4940da8d11] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[5d7e378b-0ac3-4db3-be63-fd143ab1980b] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[1148830f-1523-4a20-8345-d49c273b9c3d] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[1148830f-1523-4a20-8345-d49c273b9c3d] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node field_rename_processor(Field Rename: 0ef4c39e-bf16-4d1c-964c-4e4940da8d11) enable batch process 
[TRACE] 2025-01-16 15:42:54.712 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node field_rename_processor(Field Rename: 1148830f-1523-4a20-8345-d49c273b9c3d) enable batch process 
[TRACE] 2025-01-16 15:42:54.717 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.718 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 15:42:54.718 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[eccdba2d-b769-4013-8a9e-ecd3877d34c7] start preload schema,table counts: 1 
[TRACE] 2025-01-16 15:42:54.718 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[eccdba2d-b769-4013-8a9e-ecd3877d34c7] preload schema finished, cost 0 ms 
[INFO ] 2025-01-16 15:42:54.964 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Source connector(qa_mongodb_repl_42240_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 15:42:54.964 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Source node "qa_mongodb_repl_42240_1736997187904_1988" read batch size: 500 
[TRACE] 2025-01-16 15:42:54.964 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Source node "qa_mongodb_repl_42240_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-01-16 15:42:54.964 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-01-16 15:42:55.289 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Use existing stream offset: {"cdcOffset":1737013374,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-01-16 15:42:55.289 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Sink connector(qa_mongodb_repl_36230_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 15:42:55.289 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 15:42:55.293 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 15:42:55.455 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-01-16 15:42:55.455 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync started 
[INFO ] 2025-01-16 15:42:55.455 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Starting batch read from table: qa_auto_test_one_many_89_4_1737013278787_9747 
[TRACE] 2025-01-16 15:42:55.455 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Table qa_auto_test_one_many_89_4_1737013278787_9747 is going to be initial synced 
[TRACE] 2025-01-16 15:42:55.493 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Query snapshot row size completed: qa_mongodb_repl_42240_1736997187904_1988(9b7de206-0cd5-4203-aa8b-b3f023e42825) 
[INFO ] 2025-01-16 15:42:55.493 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Sink connector(qa_sqlserver_1443_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 15:42:55.493 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 15:42:55.494 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 15:42:55.667 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Sink connector(qa_pg_5432_1736997187904_1988) initialization completed 
[INFO ] 2025-01-16 15:42:55.667 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Sink connector(qa_oracle_11g_single_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 15:42:55.667 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[TRACE] 2025-01-16 15:42:55.667 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 15:42:55.668 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 15:42:55.770 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 15:42:55.770 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Sink connector(qa_mysql_184_3306_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 15:42:55.770 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 15:42:55.771 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-01-16 15:42:55.973 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 15:42:56.787 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 15:42:57.395 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 15:42:58.234 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Use the first node as the default script executor, please use it with caution. 
[INFO ] 2025-01-16 15:43:02.639 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Table qa_auto_test_one_many_89_4_1737013278787_9747 has been completed batch read 
[TRACE] 2025-01-16 15:43:02.639 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-01-16 15:43:02.640 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Batch read completed. 
[TRACE] 2025-01-16 15:43:02.640 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Incremental sync starting... 
[TRACE] 2025-01-16 15:43:02.640 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync completed 
[TRACE] 2025-01-16 15:43:02.640 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Starting stream read, table list: [qa_auto_test_one_many_89_4_1737013278787_9747, _tapdata_heartbeat_table], offset: {"cdcOffset":1737013374,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-01-16 15:43:02.640 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Starting incremental sync using database log parser 
[TRACE] 2025-01-16 15:43:02.641 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Connector MongoDB incremental start succeed, tables: [qa_auto_test_one_many_89_4_1737013278787_9747, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-01-16 15:43:09.652 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [1148830f-1523-4a20-8345-d49c273b9c3d-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:09.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:09.721 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:09.721 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:09.758 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:09.758 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:19.349 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.350 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 15:43:19.366 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.366 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 15:43:19.383 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.383 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 15:43:19.397 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.397 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 15:43:19.409 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.409 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:19.613 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [0ef4c39e-bf16-4d1c-964c-4e4940da8d11-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.721 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.721 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 15:43:19.725 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.725 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 15:43:19.740 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.740 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 15:43:19.744 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.744 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 15:43:19.756 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.756 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 15:43:19.758 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.759 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 15:43:19.772 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.772 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.772 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 15:43:19.772 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 15:43:19.785 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.785 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.785 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:19.785 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:19.797 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [77cf6bbf-8000-47b3-91ee-222cb445d6d9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.797 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [a0ab0c26-7f0b-4a35-a719-038214845206-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.817 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [1148830f-1523-4a20-8345-d49c273b9c3d-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.817 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [1148830f-1523-4a20-8345-d49c273b9c3d-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 15:43:19.831 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [1148830f-1523-4a20-8345-d49c273b9c3d-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.831 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [1148830f-1523-4a20-8345-d49c273b9c3d-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:19.871 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [1148830f-1523-4a20-8345-d49c273b9c3d-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.871 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.889 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 15:43:19.889 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.905 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 15:43:19.905 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.920 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 15:43:19.920 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.933 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 15:43:19.934 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:19.945 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:19.945 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - [a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:20.213 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:20.213 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 15:43:20.272 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:20.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:20.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:20.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:20.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:20.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:20.341 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:20.341 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:20.350 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:20.352 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:20.352 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:20.371 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 15:43:20.371 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 15:43:20.445 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 15:43:20.446 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:20.528 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 15:43:20.529 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:20.538 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:20.538 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 15:43:20.551 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 15:43:20.551 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 15:43:20.756 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 15:43:21.342 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 15:43:21.342 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 15:43:21.366 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:21.366 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:21.366 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:21.366 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[TRACE] 2025-01-16 15:43:21.384 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-01-16 15:43:21.384 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 15 
[TRACE] 2025-01-16 15:43:22.119 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:22.119 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 15:43:22.241 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:22.301 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:22.301 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 15:43:22.389 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 15:43:22.389 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 15:43:22.423 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 15:43:22.423 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 15:43:22.614 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 15:43:22.614 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:22.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[TRACE] 2025-01-16 15:43:22.711 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 15:43:22.770 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[TRACE] 2025-01-16 15:43:22.770 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-01-16 15:43:22.971 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 15 
[TRACE] 2025-01-16 15:43:23.418 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 15:43:23.463 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 15:43:23.463 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:23.506 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[TRACE] 2025-01-16 15:43:23.506 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 15:43:23.528 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[TRACE] 2025-01-16 15:43:23.528 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-01-16 15:43:23.534 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 15 
[TRACE] 2025-01-16 15:43:23.534 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 15:43:23.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 15:43:23.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:23.650 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[TRACE] 2025-01-16 15:43:23.650 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 15:43:23.650 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 15:43:23.703 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-01-16 15:43:23.703 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 15 
[TRACE] 2025-01-16 15:43:24.307 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 15:43:25.520 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 15:43:25.554 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.554 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 18 millisecond 
[TRACE] 2025-01-16 15:43:25.573 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.573 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 16 millisecond 
[TRACE] 2025-01-16 15:43:25.591 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.591 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 15:43:25.611 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.611 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 15:43:25.625 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.625 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 15:43:25.645 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.645 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 15:43:25.847 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - [18b20c67-4454-41d7-9cf2-59720e61d21c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 15:43:25.968 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 15:43:26.172 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 15:43:27.853 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:28.258 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 15:43:28.319 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 15:43:28.319 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 15:43:28.324 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 15:43:28.324 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 15:43:28.527 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 15:43:28.682 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 15:43:29.417 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 15:43:29.825 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 15:43:30.075 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:30.075 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 15:43:30.126 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 15:43:30.126 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 15:43:30.126 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 15:43:30.330 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[TRACE] 2025-01-16 15:43:30.330 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737013278787_9747' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 15:43:59.968 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[9b7de206-0cd5-4203-aa8b-b3f023e42825] running status set to false 
[TRACE] 2025-01-16 15:43:59.982 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_9b7de206-0cd5-4203-aa8b-b3f023e42825_1737013374864 
[TRACE] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_9b7de206-0cd5-4203-aa8b-b3f023e42825_1737013374864 
[TRACE] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[9b7de206-0cd5-4203-aa8b-b3f023e42825] schema data cleaned 
[TRACE] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[9b7de206-0cd5-4203-aa8b-b3f023e42825] monitor closed 
[TRACE] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[9b7de206-0cd5-4203-aa8b-b3f023e42825] close complete, cost 15 ms 
[TRACE] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36] running status set to false 
[INFO ] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-361c025c-7730-4367-be19-dc8587ef9fce 
[INFO ] 2025-01-16 15:43:59.985 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-361c025c-7730-4367-be19-dc8587ef9fce 
[INFO ] 2025-01-16 15:43:59.989 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 15:43:59.994 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mysql_184_3306_1736997187904_1988-a3a73444-7c19-4f9f-af2a-d74694e1c4c3 
[INFO ] 2025-01-16 15:43:59.994 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mysql_184_3306_1736997187904_1988-a3a73444-7c19-4f9f-af2a-d74694e1c4c3 
[INFO ] 2025-01-16 15:43:59.994 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36-67887cfd5703b91f391e6919] schema data cleaned 
[TRACE] 2025-01-16 15:43:59.995 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36] schema data cleaned 
[TRACE] 2025-01-16 15:43:59.998 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36] monitor closed 
[TRACE] 2025-01-16 15:43:59.998 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[a7be9fc4-bf69-4acc-8ab6-48ac0ad4af36] close complete, cost 11 ms 
[TRACE] 2025-01-16 15:43:59.998 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[bb51f2cc-f4f3-49e8-bb91-2408246217a2] running status set to false 
[INFO ] 2025-01-16 15:43:59.998 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-b3110f6f-6006-4c8a-a610-af4f29f219ce 
[INFO ] 2025-01-16 15:43:59.998 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-b3110f6f-6006-4c8a-a610-af4f29f219ce 
[INFO ] 2025-01-16 15:43:59.998 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-bb51f2cc-f4f3-49e8-bb91-2408246217a2-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 15:44:00.000 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_36230_1736997187904_1988-dd0647ea-1264-4cb7-814f-2ec4f593aabe 
[INFO ] 2025-01-16 15:44:00.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_36230_1736997187904_1988-dd0647ea-1264-4cb7-814f-2ec4f593aabe 
[INFO ] 2025-01-16 15:44:00.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-bb51f2cc-f4f3-49e8-bb91-2408246217a2-6788a2fc903e3f5bb47eb998] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[bb51f2cc-f4f3-49e8-bb91-2408246217a2] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[bb51f2cc-f4f3-49e8-bb91-2408246217a2] monitor closed 
[TRACE] 2025-01-16 15:44:00.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[bb51f2cc-f4f3-49e8-bb91-2408246217a2] close complete, cost 5 ms 
[TRACE] 2025-01-16 15:44:00.002 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[0ef4c39e-bf16-4d1c-964c-4e4940da8d11] running status set to false 
[TRACE] 2025-01-16 15:44:00.080 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[0ef4c39e-bf16-4d1c-964c-4e4940da8d11] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.081 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[0ef4c39e-bf16-4d1c-964c-4e4940da8d11] monitor closed 
[TRACE] 2025-01-16 15:44:00.081 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[0ef4c39e-bf16-4d1c-964c-4e4940da8d11] close complete, cost 80 ms 
[TRACE] 2025-01-16 15:44:00.081 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8805396f-77a3-433e-bd5f-8507256e5cd1] running status set to false 
[TRACE] 2025-01-16 15:44:00.121 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_8805396f-77a3-433e-bd5f-8507256e5cd1_1737013375182 
[TRACE] 2025-01-16 15:44:00.121 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_8805396f-77a3-433e-bd5f-8507256e5cd1_1737013375182 
[TRACE] 2025-01-16 15:44:00.121 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8805396f-77a3-433e-bd5f-8507256e5cd1] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.121 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8805396f-77a3-433e-bd5f-8507256e5cd1] monitor closed 
[TRACE] 2025-01-16 15:44:00.121 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8805396f-77a3-433e-bd5f-8507256e5cd1] close complete, cost 40 ms 
[TRACE] 2025-01-16 15:44:00.121 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[6d525038-9886-425e-8f9a-ffde68b7b967] running status set to false 
[INFO ] 2025-01-16 15:44:00.125 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-9b0ab6b5-4c31-4a6a-aab6-c23fed0b3c70 
[INFO ] 2025-01-16 15:44:00.125 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-9b0ab6b5-4c31-4a6a-aab6-c23fed0b3c70 
[INFO ] 2025-01-16 15:44:00.125 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-6d525038-9886-425e-8f9a-ffde68b7b967-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 15:44:00.154 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_oracle_11g_single_1736997187904_1988-b028ef0a-823e-479b-89be-c525e4068b87 
[INFO ] 2025-01-16 15:44:00.157 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_oracle_11g_single_1736997187904_1988-b028ef0a-823e-479b-89be-c525e4068b87 
[INFO ] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-6d525038-9886-425e-8f9a-ffde68b7b967-67887d025703b91f391e691e] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[6d525038-9886-425e-8f9a-ffde68b7b967] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[6d525038-9886-425e-8f9a-ffde68b7b967] monitor closed 
[TRACE] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[6d525038-9886-425e-8f9a-ffde68b7b967] close complete, cost 33 ms 
[TRACE] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[10a2902c-0557-4fa3-8690-709ae3abc170] running status set to false 
[INFO ] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-4d792c0a-a2bb-484d-b2a3-6815df470055 
[INFO ] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-4d792c0a-a2bb-484d-b2a3-6815df470055 
[INFO ] 2025-01-16 15:44:00.161 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-10a2902c-0557-4fa3-8690-709ae3abc170-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 15:44:00.164 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_pg_5432_1736997187904_1988-228a1d0a-aba0-46b3-a5f3-27ddbad23fb0 
[INFO ] 2025-01-16 15:44:00.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_pg_5432_1736997187904_1988-228a1d0a-aba0-46b3-a5f3-27ddbad23fb0 
[INFO ] 2025-01-16 15:44:00.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-10a2902c-0557-4fa3-8690-709ae3abc170-67887d0d5703b91f391e6923] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[10a2902c-0557-4fa3-8690-709ae3abc170] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[10a2902c-0557-4fa3-8690-709ae3abc170] monitor closed 
[TRACE] 2025-01-16 15:44:00.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[10a2902c-0557-4fa3-8690-709ae3abc170] close complete, cost 4 ms 
[TRACE] 2025-01-16 15:44:00.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[77cf6bbf-8000-47b3-91ee-222cb445d6d9] running status set to false 
[TRACE] 2025-01-16 15:44:00.194 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[77cf6bbf-8000-47b3-91ee-222cb445d6d9] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.197 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[77cf6bbf-8000-47b3-91ee-222cb445d6d9] monitor closed 
[TRACE] 2025-01-16 15:44:00.197 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[77cf6bbf-8000-47b3-91ee-222cb445d6d9] close complete, cost 34 ms 
[TRACE] 2025-01-16 15:44:00.197 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[2f78309c-d20d-4107-9cfc-89678033243c] running status set to false 
[TRACE] 2025-01-16 15:44:00.204 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_2f78309c-d20d-4107-9cfc-89678033243c_1737013375442 
[TRACE] 2025-01-16 15:44:00.204 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_2f78309c-d20d-4107-9cfc-89678033243c_1737013375442 
[TRACE] 2025-01-16 15:44:00.204 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[2f78309c-d20d-4107-9cfc-89678033243c] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.204 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[2f78309c-d20d-4107-9cfc-89678033243c] monitor closed 
[TRACE] 2025-01-16 15:44:00.204 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[2f78309c-d20d-4107-9cfc-89678033243c] close complete, cost 10 ms 
[TRACE] 2025-01-16 15:44:00.204 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[2d548ea0-8d29-462b-a56d-ab5b51eddcf2] running status set to false 
[INFO ] 2025-01-16 15:44:00.206 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-ed2ec703-c3c0-417b-9fd5-047a3df60862 
[INFO ] 2025-01-16 15:44:00.206 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-ed2ec703-c3c0-417b-9fd5-047a3df60862 
[INFO ] 2025-01-16 15:44:00.206 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-2d548ea0-8d29-462b-a56d-ab5b51eddcf2-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 15:44:00.208 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_sqlserver_1443_1736997187904_1988-b2478cee-03e7-4f21-b304-d9c0f9d5a38b 
[INFO ] 2025-01-16 15:44:00.208 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_sqlserver_1443_1736997187904_1988-b2478cee-03e7-4f21-b304-d9c0f9d5a38b 
[INFO ] 2025-01-16 15:44:00.208 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS][src=user_script]  - [ScriptExecutorsManager-6788b873903e3f5bb47ebd12-2d548ea0-8d29-462b-a56d-ab5b51eddcf2-67887d135703b91f391e6928] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.208 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[2d548ea0-8d29-462b-a56d-ab5b51eddcf2] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.208 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[2d548ea0-8d29-462b-a56d-ab5b51eddcf2] monitor closed 
[TRACE] 2025-01-16 15:44:00.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][JS] - Node JS[2d548ea0-8d29-462b-a56d-ab5b51eddcf2] close complete, cost 4 ms 
[TRACE] 2025-01-16 15:44:00.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[18b20c67-4454-41d7-9cf2-59720e61d21c] running status set to false 
[TRACE] 2025-01-16 15:44:00.246 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[18b20c67-4454-41d7-9cf2-59720e61d21c] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.247 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[18b20c67-4454-41d7-9cf2-59720e61d21c] monitor closed 
[TRACE] 2025-01-16 15:44:00.248 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[18b20c67-4454-41d7-9cf2-59720e61d21c] close complete, cost 37 ms 
[TRACE] 2025-01-16 15:44:00.248 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1] running status set to false 
[TRACE] 2025-01-16 15:44:00.252 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1_1737013375356 
[TRACE] 2025-01-16 15:44:00.252 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1_1737013375356 
[TRACE] 2025-01-16 15:44:00.252 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.252 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1] monitor closed 
[TRACE] 2025-01-16 15:44:00.252 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4a4aa928-8a06-4227-ac5d-a9dbc6d50dd1] close complete, cost 6 ms 
[TRACE] 2025-01-16 15:44:00.252 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[a0ab0c26-7f0b-4a35-a719-038214845206] running status set to false 
[TRACE] 2025-01-16 15:44:00.286 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[a0ab0c26-7f0b-4a35-a719-038214845206] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.286 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[a0ab0c26-7f0b-4a35-a719-038214845206] monitor closed 
[TRACE] 2025-01-16 15:44:00.286 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[a0ab0c26-7f0b-4a35-a719-038214845206] close complete, cost 33 ms 
[TRACE] 2025-01-16 15:44:00.286 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[eccdba2d-b769-4013-8a9e-ecd3877d34c7] running status set to false 
[TRACE] 2025-01-16 15:44:00.421 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_eccdba2d-b769-4013-8a9e-ecd3877d34c7_1737013375257 
[TRACE] 2025-01-16 15:44:00.421 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_eccdba2d-b769-4013-8a9e-ecd3877d34c7_1737013375257 
[TRACE] 2025-01-16 15:44:00.421 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[eccdba2d-b769-4013-8a9e-ecd3877d34c7] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.421 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[eccdba2d-b769-4013-8a9e-ecd3877d34c7] monitor closed 
[TRACE] 2025-01-16 15:44:00.421 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[eccdba2d-b769-4013-8a9e-ecd3877d34c7] close complete, cost 134 ms 
[TRACE] 2025-01-16 15:44:00.421 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[1148830f-1523-4a20-8345-d49c273b9c3d] running status set to false 
[TRACE] 2025-01-16 15:44:00.461 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[1148830f-1523-4a20-8345-d49c273b9c3d] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.461 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[1148830f-1523-4a20-8345-d49c273b9c3d] monitor closed 
[TRACE] 2025-01-16 15:44:00.461 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][Field Rename] - Node Field Rename[1148830f-1523-4a20-8345-d49c273b9c3d] close complete, cost 39 ms 
[TRACE] 2025-01-16 15:44:00.508 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[5d7e378b-0ac3-4db3-be63-fd143ab1980b] running status set to false 
[TRACE] 2025-01-16 15:44:00.508 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_5d7e378b-0ac3-4db3-be63-fd143ab1980b_1737013375405 
[TRACE] 2025-01-16 15:44:00.508 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_5d7e378b-0ac3-4db3-be63-fd143ab1980b_1737013375405 
[TRACE] 2025-01-16 15:44:00.508 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[5d7e378b-0ac3-4db3-be63-fd143ab1980b] schema data cleaned 
[TRACE] 2025-01-16 15:44:00.508 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[5d7e378b-0ac3-4db3-be63-fd143ab1980b] monitor closed 
[TRACE] 2025-01-16 15:44:00.540 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[5d7e378b-0ac3-4db3-be63-fd143ab1980b] close complete, cost 47 ms 
[TRACE] 2025-01-16 15:44:00.540 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278][qa_mongodb_repl_42240_1736997187904_1988] - Incremental sync completed 
[TRACE] 2025-01-16 15:44:04.533 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-16 15:44:04.533 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@729d1141 
[TRACE] 2025-01-16 15:44:04.648 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Stop task milestones: 6788b873903e3f5bb47ebd12(t_89.4-MongoDB->Many's_1736997187904_1988-1737013278)  
[TRACE] 2025-01-16 15:44:04.648 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Stopped task aspect(s) 
[TRACE] 2025-01-16 15:44:04.648 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Snapshot order controller have been removed 
[INFO ] 2025-01-16 15:44:04.648 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Task stopped. 
[TRACE] 2025-01-16 15:44:04.681 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Remove memory task client succeed, task: t_89.4-MongoDB->Many's_1736997187904_1988-1737013278[6788b873903e3f5bb47ebd12] 
[TRACE] 2025-01-16 15:44:04.681 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737013278] - Destroy memory task client cache succeed, task: t_89.4-MongoDB->Many's_1736997187904_1988-1737013278[6788b873903e3f5bb47ebd12] 
