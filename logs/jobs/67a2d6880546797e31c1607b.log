[TRACE] 2025-02-05 11:10:11.203 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:10:11.203 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:10:11.307 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:10:11.307 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:10:11.338 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:10:11.338 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:10:11.349 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:10:11.349 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:10:11.349 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:10:11.349 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:10:11.720 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:10:11.720 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:10:11.720 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:10:11.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:10:11.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:10:11.762 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:10:11.762 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:10:11.762 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:10:11.817 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:10:11.818 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:10:11.818 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:10:11.818 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[TRACE] 2025-02-05 11:10:11.818 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:10:11.818 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 11:10:12.279 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:10:12.279 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:10:12.279 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:10:12.282 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:10:18.355 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2d693697f7f5991a29974, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:10:18.356 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:10:18.365 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:10:18.365 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738725011617 
[TRACE] 2025-02-05 11:10:18.365 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738725011617 
[TRACE] 2025-02-05 11:10:18.365 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:10:18.365 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:10:18.434 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 9 ms 
[TRACE] 2025-02-05 11:10:18.434 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738725011647 
[TRACE] 2025-02-05 11:10:18.434 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738725011647 
[TRACE] 2025-02-05 11:10:18.435 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:10:18.436 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:10:18.640 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 82 ms 
[TRACE] 2025-02-05 11:10:22.385 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:10:22.498 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56070bc3 
[TRACE] 2025-02-05 11:10:22.498 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:10:22.512 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:10:22.513 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:10:22.513 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:10:22.543 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:10:22.544 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:12:59.716 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:12:59.810 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:12:59.811 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:12:59.847 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:12:59.847 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:12:59.869 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:12:59.869 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:12:59.870 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:12:59.870 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:12:59.870 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:12:59.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:12:59.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:12:59.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:12:59.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:12:59.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:12:59.987 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:12:59.987 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:12:59.988 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:12:59.988 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:13:00.040 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:13:00.040 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:13:00.040 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:13:00.040 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 11:13:00.040 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:13:00.471 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:13:00.472 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:13:00.472 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:13:00.677 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:13:06.544 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2d73c697f7f5991a29980, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:13:06.601 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:13:06.602 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:13:06.616 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738725179886 
[TRACE] 2025-02-05 11:13:06.616 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738725179886 
[TRACE] 2025-02-05 11:13:06.616 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:13:06.616 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:13:06.675 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 15 ms 
[TRACE] 2025-02-05 11:13:06.675 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738725179892 
[TRACE] 2025-02-05 11:13:06.675 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738725179892 
[TRACE] 2025-02-05 11:13:06.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:13:06.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:13:06.677 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 76 ms 
[TRACE] 2025-02-05 11:13:07.666 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:13:07.666 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7383ef5d 
[TRACE] 2025-02-05 11:13:07.797 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:13:07.798 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:13:07.798 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:13:07.799 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:13:07.829 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:13:07.832 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:23:48.144 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:23:48.237 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:23:48.237 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:23:48.277 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:23:48.277 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:23:48.298 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:23:48.298 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:23:48.298 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:23:48.298 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:23:48.298 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:23:48.383 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:23:48.383 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:23:48.383 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:23:48.383 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:23:48.383 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:23:48.420 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:23:48.420 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:23:48.421 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:23:48.421 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 11:23:48.471 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:23:48.471 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:23:48.471 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:23:48.471 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:23:48.472 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 11:23:48.799 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:23:48.800 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:23:48.800 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:23:48.805 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:23:54.813 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2d9c4697f7f5991a2998c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:23:54.891 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:23:54.891 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:23:54.899 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738725828316 
[TRACE] 2025-02-05 11:23:54.900 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738725828316 
[TRACE] 2025-02-05 11:23:54.900 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:23:54.900 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:23:54.900 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 9 ms 
[TRACE] 2025-02-05 11:23:54.938 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738725828325 
[TRACE] 2025-02-05 11:23:54.938 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738725828325 
[TRACE] 2025-02-05 11:23:54.938 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:23:54.939 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:23:54.939 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 49 ms 
[TRACE] 2025-02-05 11:23:58.258 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:23:58.371 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@583b960c 
[TRACE] 2025-02-05 11:23:58.371 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:23:58.397 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:23:58.397 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:23:58.428 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:23:58.429 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:23:58.429 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:35:07.180 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:35:07.254 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:35:07.254 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:35:07.282 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:35:07.282 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:35:07.300 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:35:07.300 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:35:07.300 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:35:07.300 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:35:07.300 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:35:07.379 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:35:07.379 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:35:07.379 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:35:07.379 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:35:07.414 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:35:07.414 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:35:07.417 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:35:07.417 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:35:07.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:35:07.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:35:07.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:35:07.466 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:35:07.466 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 11:35:07.466 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:35:07.850 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:35:07.850 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:35:07.850 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:35:08.055 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:35:13.867 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2dc6b697f7f5991a29998, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:35:13.907 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:35:13.912 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:35:13.924 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726507316 
[TRACE] 2025-02-05 11:35:13.924 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726507316 
[TRACE] 2025-02-05 11:35:13.925 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:35:13.925 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:35:13.971 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 18 ms 
[TRACE] 2025-02-05 11:35:13.972 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726507322 
[TRACE] 2025-02-05 11:35:13.972 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726507322 
[TRACE] 2025-02-05 11:35:13.972 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:35:13.974 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:35:13.974 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 70 ms 
[TRACE] 2025-02-05 11:35:18.810 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:35:18.810 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76b35d04 
[TRACE] 2025-02-05 11:35:18.932 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:35:18.933 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:35:18.933 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:35:18.933 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:35:18.964 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:35:18.964 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:38:29.629 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:38:29.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:38:29.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:38:29.749 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:38:29.749 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:38:29.767 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:38:29.768 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:38:29.768 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:38:29.768 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:38:29.768 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 1 ms 
[INFO ] 2025-02-05 11:38:29.851 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:38:29.852 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:38:29.852 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:38:29.852 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:38:29.902 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:38:29.902 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:38:29.904 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:38:29.904 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:38:29.904 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 11:38:29.962 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:38:29.962 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:38:29.962 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:38:29.962 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:38:29.962 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 11:38:36.464 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:38:36.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:38:36.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:38:36.667 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:38:42.540 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2dd35697f7f5991a299a4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:38:42.580 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:38:42.580 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:38:42.590 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726709783 
[TRACE] 2025-02-05 11:38:42.590 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726709783 
[TRACE] 2025-02-05 11:38:42.591 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:38:42.591 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:38:42.591 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 11 ms 
[TRACE] 2025-02-05 11:38:42.629 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726709791 
[TRACE] 2025-02-05 11:38:42.629 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726709791 
[TRACE] 2025-02-05 11:38:42.629 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:38:42.629 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:38:42.834 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 53 ms 
[TRACE] 2025-02-05 11:38:44.099 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:38:44.099 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6d8026f2 
[TRACE] 2025-02-05 11:38:44.226 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:38:44.226 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:38:44.226 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:38:44.259 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:38:44.262 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:38:44.262 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:40:14.849 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:40:14.851 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:40:14.939 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:40:14.939 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:40:14.975 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:40:14.975 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:40:14.986 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:40:14.986 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:40:14.986 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:40:14.986 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:40:15.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:40:15.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:40:15.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:40:15.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:40:15.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:40:15.113 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:40:15.113 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:40:15.113 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:40:15.113 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:40:15.159 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:40:15.159 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:40:15.160 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:40:15.160 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 11:40:15.365 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:40:15.511 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:40:15.511 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:40:15.512 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:40:15.717 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:40:21.524 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2dd9f697f7f5991a299b0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:40:21.601 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:40:21.602 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:40:21.617 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726815009 
[TRACE] 2025-02-05 11:40:21.617 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726815009 
[TRACE] 2025-02-05 11:40:21.617 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:40:21.618 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:40:21.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 17 ms 
[TRACE] 2025-02-05 11:40:21.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726815016 
[TRACE] 2025-02-05 11:40:21.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726815016 
[TRACE] 2025-02-05 11:40:21.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:40:21.677 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:40:21.677 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 75 ms 
[TRACE] 2025-02-05 11:40:24.324 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:40:24.437 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3252f441 
[TRACE] 2025-02-05 11:40:24.437 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:40:24.455 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:40:24.456 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:40:24.456 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:40:24.660 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:40:24.660 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:42:18.894 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:42:18.895 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:42:18.985 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:42:18.985 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:42:19.021 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:42:19.021 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:42:19.035 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:42:19.035 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:42:19.043 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:42:19.043 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:42:19.132 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:42:19.132 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:42:19.132 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:42:19.132 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:42:19.132 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:42:19.169 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:42:19.169 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:42:19.170 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:42:19.220 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:42:19.220 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:42:19.220 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:42:19.220 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:42:19.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 11:42:19.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:42:19.558 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:42:19.558 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:42:19.558 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:42:19.763 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:42:25.628 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2de1b697f7f5991a299bc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:42:25.661 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:42:25.663 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:42:25.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726939059 
[TRACE] 2025-02-05 11:42:25.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738726939059 
[TRACE] 2025-02-05 11:42:25.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:42:25.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:42:25.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 16 ms 
[TRACE] 2025-02-05 11:42:25.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726939066 
[TRACE] 2025-02-05 11:42:25.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738726939066 
[TRACE] 2025-02-05 11:42:25.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:42:25.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:42:25.951 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 88 ms 
[TRACE] 2025-02-05 11:42:29.571 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:42:29.684 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@384122c2 
[TRACE] 2025-02-05 11:42:29.685 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:42:29.701 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:42:29.701 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:42:29.701 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:42:29.905 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:42:29.906 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:44:04.643 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:44:04.717 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:44:04.718 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:44:04.748 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:44:04.748 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:44:04.764 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:44:04.764 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:44:04.764 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:44:04.764 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:44:04.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:44:04.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:44:04.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:44:04.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:44:04.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:44:04.881 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:44:04.881 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:44:04.883 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:44:04.883 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:44:04.944 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:44:04.945 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:44:04.945 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:44:04.945 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:44:04.945 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 11:44:05.145 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:44:05.490 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:44:05.490 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:44:05.490 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:44:05.695 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:44:11.577 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2de84697f7f5991a299c8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:44:11.607 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:44:11.608 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:44:11.624 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727044783 
[TRACE] 2025-02-05 11:44:11.624 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727044783 
[TRACE] 2025-02-05 11:44:11.624 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:44:11.624 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:44:11.679 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 17 ms 
[TRACE] 2025-02-05 11:44:11.680 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727044789 
[TRACE] 2025-02-05 11:44:11.680 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727044789 
[TRACE] 2025-02-05 11:44:11.680 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:44:11.681 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:44:11.882 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 77 ms 
[TRACE] 2025-02-05 11:44:14.795 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:44:14.795 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@18592bc5 
[TRACE] 2025-02-05 11:44:14.925 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:44:14.926 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:44:14.926 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:44:14.926 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:44:14.958 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:44:14.959 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:47:35.086 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:47:35.162 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:47:35.162 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:47:35.195 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:47:35.195 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:47:35.223 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:47:35.223 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:47:35.223 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:47:35.224 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:47:35.224 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:47:35.311 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:47:35.312 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:47:35.312 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:47:35.312 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:47:35.349 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:47:35.349 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:47:35.351 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:47:35.351 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:47:35.351 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 11:47:35.404 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:47:35.404 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:47:35.404 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:47:35.404 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:47:35.609 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 11:47:35.779 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:47:35.779 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:47:35.779 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:47:35.983 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:47:41.794 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2df57697f7f5991a299d4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:47:41.850 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:47:41.861 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:47:41.866 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727255246 
[TRACE] 2025-02-05 11:47:41.866 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727255246 
[TRACE] 2025-02-05 11:47:41.866 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:47:41.867 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:47:41.867 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 17 ms 
[TRACE] 2025-02-05 11:47:41.911 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727255250 
[TRACE] 2025-02-05 11:47:41.912 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727255250 
[TRACE] 2025-02-05 11:47:41.912 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:47:41.913 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:47:42.114 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 70 ms 
[TRACE] 2025-02-05 11:47:45.081 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:47:45.197 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60fea6de 
[TRACE] 2025-02-05 11:47:45.197 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:47:45.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:47:45.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:47:45.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:47:45.248 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:47:45.251 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:49:11.732 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:49:11.732 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:49:11.808 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:49:11.809 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:49:11.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:49:11.845 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:49:11.859 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:49:11.859 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:49:11.859 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:49:11.859 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:49:11.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:49:11.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:49:11.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:49:11.948 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:49:11.983 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:49:11.983 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:49:11.985 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:49:11.985 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:49:11.985 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 11:49:12.035 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:49:12.035 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:49:12.035 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:49:12.035 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:49:12.237 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 11:49:12.348 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:49:12.348 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:49:12.348 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:49:12.352 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:49:18.415 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2dfb8697f7f5991a299e0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:49:18.474 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:49:18.477 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:49:18.500 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727351880 
[TRACE] 2025-02-05 11:49:18.500 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727351880 
[TRACE] 2025-02-05 11:49:18.500 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:49:18.500 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:49:18.500 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 26 ms 
[TRACE] 2025-02-05 11:49:18.550 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727351884 
[TRACE] 2025-02-05 11:49:18.550 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727351884 
[TRACE] 2025-02-05 11:49:18.551 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:49:18.551 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:49:18.756 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 78 ms 
[TRACE] 2025-02-05 11:49:20.315 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:49:20.315 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58f38bde 
[TRACE] 2025-02-05 11:49:20.428 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:49:20.451 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:49:20.452 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:49:20.452 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:49:20.483 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:49:20.486 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:56:06.976 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:56:06.976 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:56:07.057 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:56:07.057 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:56:07.093 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:56:07.093 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:56:07.105 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:56:07.105 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:56:07.105 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:56:07.105 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:56:07.203 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:56:07.203 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:56:07.203 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:56:07.203 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:56:07.237 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:56:07.237 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:56:07.240 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:56:07.240 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:56:07.291 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 11:56:07.291 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:56:07.291 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:56:07.291 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:56:07.291 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 11:56:07.496 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:56:12.760 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:56:12.761 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:56:12.761 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:56:12.964 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:56:18.774 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e157697f7f5991a299ec, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:56:18.808 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:56:18.808 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:56:18.820 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727767135 
[TRACE] 2025-02-05 11:56:18.821 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727767135 
[TRACE] 2025-02-05 11:56:18.821 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:56:18.825 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:56:18.825 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 18 ms 
[TRACE] 2025-02-05 11:56:18.858 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727767139 
[TRACE] 2025-02-05 11:56:18.858 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727767139 
[TRACE] 2025-02-05 11:56:18.858 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:56:18.859 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:56:19.062 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 57 ms 
[TRACE] 2025-02-05 11:56:20.749 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:56:20.749 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2a62ab7a 
[TRACE] 2025-02-05 11:56:20.868 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:56:20.868 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:56:20.868 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:56:20.868 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:56:20.887 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:56:20.887 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:58:31.920 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 11:58:32.001 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 11:58:32.001 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 11:58:32.032 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 11:58:32.032 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 11:58:32.050 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 11:58:32.051 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:58:32.051 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 11:58:32.051 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 11:58:32.051 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 11:58:32.131 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:58:32.131 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 11:58:32.131 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 11:58:32.131 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 11:58:32.168 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 11:58:32.169 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 11:58:32.171 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 11:58:32.171 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 11:58:32.171 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 11:58:32.225 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 11:58:32.226 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 11:58:32.226 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 11:58:32.226 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 11:58:32.427 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 11:58:32.550 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 11:58:32.550 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 11:58:32.550 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 11:58:32.751 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 11:58:38.623 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e1e8697f7f5991a299f8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 11:58:38.657 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 11:58:38.657 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 11:58:38.671 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727912067 
[TRACE] 2025-02-05 11:58:38.672 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738727912067 
[TRACE] 2025-02-05 11:58:38.672 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 11:58:38.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 11:58:38.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 17 ms 
[TRACE] 2025-02-05 11:58:38.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727912074 
[TRACE] 2025-02-05 11:58:38.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738727912074 
[TRACE] 2025-02-05 11:58:38.732 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 11:58:38.732 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 11:58:38.932 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 79 ms 
[TRACE] 2025-02-05 11:58:40.980 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 11:58:40.981 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3293bc59 
[TRACE] 2025-02-05 11:58:41.112 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 11:58:41.112 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 11:58:41.112 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 11:58:41.140 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 11:58:41.142 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 11:58:41.142 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:03:10.144 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 12:03:10.286 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 12:03:10.286 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 12:03:10.356 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 12:03:10.356 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 12:03:10.374 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 12:03:10.374 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:03:10.374 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 12:03:10.376 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:03:10.376 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 12:03:10.464 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:03:10.464 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 12:03:10.464 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 12:03:10.464 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 12:03:10.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 12:03:10.519 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 12:03:10.519 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 12:03:10.519 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 12:03:10.566 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 12:03:10.566 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 12:03:10.567 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 12:03:10.567 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 12:03:10.567 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 12:03:10.567 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 12:03:10.933 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:03:10.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 12:03:10.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 12:03:11.139 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 12:03:16.945 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e2fe697f7f5991a29a04, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 12:03:16.997 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 12:03:16.999 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 12:03:17.012 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728190404 
[TRACE] 2025-02-05 12:03:17.013 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728190404 
[TRACE] 2025-02-05 12:03:17.014 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 12:03:17.014 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 12:03:17.063 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 17 ms 
[TRACE] 2025-02-05 12:03:17.064 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728190409 
[TRACE] 2025-02-05 12:03:17.064 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728190409 
[TRACE] 2025-02-05 12:03:17.064 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 12:03:17.065 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 12:03:17.065 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 77 ms 
[TRACE] 2025-02-05 12:03:21.326 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 12:03:21.330 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@71f0dddd 
[TRACE] 2025-02-05 12:03:21.447 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 12:03:21.450 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 12:03:21.454 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 12:03:21.455 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 12:03:21.486 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:03:21.493 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:08:12.644 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 12:08:12.717 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 12:08:12.717 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 12:08:12.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 12:08:12.746 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 12:08:12.767 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 12:08:12.767 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:08:12.767 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:08:12.767 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 12:08:12.767 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 12:08:12.851 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:08:12.851 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 12:08:12.851 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 12:08:12.851 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 12:08:12.888 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 12:08:12.888 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 12:08:12.890 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 12:08:12.890 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 12:08:12.890 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 12:08:12.941 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 12:08:12.941 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 12:08:12.941 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 12:08:12.941 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 12:08:12.941 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 12:08:13.257 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:08:13.257 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 12:08:13.257 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 12:08:13.458 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 12:08:19.339 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e42c697f7f5991a29a10, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 12:08:19.391 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 12:08:19.391 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 12:08:19.391 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728492783 
[TRACE] 2025-02-05 12:08:19.391 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728492783 
[TRACE] 2025-02-05 12:08:19.391 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 12:08:19.392 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 12:08:19.392 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 25 ms 
[TRACE] 2025-02-05 12:08:19.444 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728492790 
[TRACE] 2025-02-05 12:08:19.444 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728492790 
[TRACE] 2025-02-05 12:08:19.444 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 12:08:19.444 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 12:08:19.647 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 80 ms 
[TRACE] 2025-02-05 12:08:21.693 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 12:08:21.694 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@32a54269 
[TRACE] 2025-02-05 12:08:21.824 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 12:08:21.824 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 12:08:21.825 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 12:08:21.825 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 12:08:22.030 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:08:22.030 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:13:36.626 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 12:13:36.626 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 12:13:36.687 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 12:13:36.687 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 12:13:36.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 12:13:36.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 12:13:36.741 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:13:36.741 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:13:36.741 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 12:13:36.741 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 12:13:36.831 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:13:36.831 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 12:13:36.832 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 12:13:36.832 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 12:13:36.867 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 12:13:36.867 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 12:13:36.869 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 12:13:36.869 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 12:13:36.869 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 12:13:36.915 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 12:13:36.915 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 12:13:36.915 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 12:13:36.915 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 12:13:37.116 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 12:13:37.270 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:13:37.270 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 12:13:37.271 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 12:13:37.276 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 12:13:43.344 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e570697f7f5991a29a1c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 12:13:43.362 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 12:13:43.362 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 12:13:43.378 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728816758 
[TRACE] 2025-02-05 12:13:43.378 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728816758 
[TRACE] 2025-02-05 12:13:43.378 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 12:13:43.380 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 12:13:43.442 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 18 ms 
[TRACE] 2025-02-05 12:13:43.442 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728816765 
[TRACE] 2025-02-05 12:13:43.442 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728816765 
[TRACE] 2025-02-05 12:13:43.442 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 12:13:43.442 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 12:13:43.644 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 83 ms 
[TRACE] 2025-02-05 12:13:47.062 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 12:13:47.062 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68ff8415 
[TRACE] 2025-02-05 12:13:47.194 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 12:13:47.194 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 12:13:47.194 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 12:13:47.194 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 12:13:47.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:13:47.221 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:15:56.730 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 12:15:56.730 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 12:15:56.810 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 12:15:56.810 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 12:15:56.844 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 12:15:56.844 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 12:15:56.855 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:15:56.855 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:15:56.855 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 12:15:56.855 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 12:15:56.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:15:56.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 12:15:56.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 12:15:56.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 12:15:56.934 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 12:15:56.969 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 12:15:56.969 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 12:15:56.970 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 12:15:57.018 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[INFO ] 2025-02-05 12:15:57.018 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 12:15:57.018 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 12:15:57.018 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 12:15:57.019 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[TRACE] 2025-02-05 12:15:57.019 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 12:15:57.593 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:15:57.593 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 12:15:57.593 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 12:15:57.798 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 12:16:03.604 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e5fd697f7f5991a29a28, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 12:16:03.661 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 12:16:03.661 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 12:16:03.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728956873 
[TRACE] 2025-02-05 12:16:03.674 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738728956873 
[TRACE] 2025-02-05 12:16:03.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 12:16:03.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 12:16:03.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 15 ms 
[TRACE] 2025-02-05 12:16:03.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728956880 
[TRACE] 2025-02-05 12:16:03.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738728956880 
[TRACE] 2025-02-05 12:16:03.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 12:16:03.731 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 12:16:03.732 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 74 ms 
[TRACE] 2025-02-05 12:16:07.313 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 12:16:07.313 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@27979b58 
[TRACE] 2025-02-05 12:16:07.421 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 12:16:07.436 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 12:16:07.436 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 12:16:07.465 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 12:16:07.469 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:16:07.470 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:19:44.969 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task initialization... 
[TRACE] 2025-02-05 12:19:44.969 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Start task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988) 
[INFO ] 2025-02-05 12:19:45.042 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Loading table structure completed 
[TRACE] 2025-02-05 12:19:45.042 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-05 12:19:45.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - The engine receives t_7_10_sybase_to_pg_all_type_1736997187904_1988 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-05 12:19:45.074 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task started 
[TRACE] 2025-02-05 12:19:45.087 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:19:45.087 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] start preload schema,table counts: 1 
[TRACE] 2025-02-05 12:19:45.087 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] preload schema finished, cost 0 ms 
[TRACE] 2025-02-05 12:19:45.087 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] preload schema finished, cost 0 ms 
[INFO ] 2025-02-05 12:19:45.168 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source connector(qa_sybase16_15001_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:19:45.168 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" read batch size: 500 
[TRACE] 2025-02-05 12:19:45.168 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Source node "qa_sybase16_15001_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-02-05 12:19:45.169 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-05 12:19:45.169 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-05 12:19:45.207 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-02-05 12:19:45.207 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync started 
[INFO ] 2025-02-05 12:19:45.207 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Starting batch read from table: t_7_10_all_type 
[TRACE] 2025-02-05 12:19:45.207 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type is going to be initial synced 
[TRACE] 2025-02-05 12:19:45.261 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Query snapshot row size completed: qa_sybase16_15001_1736997187904_1988(d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea) 
[INFO ] 2025-02-05 12:19:45.261 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Table t_7_10_all_type has been completed batch read 
[TRACE] 2025-02-05 12:19:45.261 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-02-05 12:19:45.261 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Batch read completed. 
[INFO ] 2025-02-05 12:19:45.462 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Task run completed 
[INFO ] 2025-02-05 12:19:45.593 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Sink connector(qa_pg_5432_t_sybase_1736997187904_1988) initialization completed 
[TRACE] 2025-02-05 12:19:45.593 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node(qa_pg_5432_t_sybase_1736997187904_1988) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-05 12:19:45.594 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-02-05 12:19:45.796 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-02-05 12:19:51.666 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67a2e6e1697f7f5991a29a34, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-05 12:19:51.705 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] running status set to false 
[TRACE] 2025-02-05 12:19:51.705 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] running status set to false 
[TRACE] 2025-02-05 12:19:51.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738729185104 
[TRACE] 2025-02-05 12:19:51.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_25a21d92-4bff-464b-83df-0c98f546f091_1738729185104 
[TRACE] 2025-02-05 12:19:51.721 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] schema data cleaned 
[TRACE] 2025-02-05 12:19:51.722 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] monitor closed 
[TRACE] 2025-02-05 12:19:51.722 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_pg_5432_t_sybase_1736997187904_1988] - Node qa_pg_5432_t_sybase_1736997187904_1988[25a21d92-4bff-464b-83df-0c98f546f091] close complete, cost 16 ms 
[TRACE] 2025-02-05 12:19:51.771 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738729185112 
[TRACE] 2025-02-05 12:19:51.771 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea_1738729185112 
[TRACE] 2025-02-05 12:19:51.772 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] schema data cleaned 
[TRACE] 2025-02-05 12:19:51.774 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] monitor closed 
[TRACE] 2025-02-05 12:19:51.775 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988][qa_sybase16_15001_1736997187904_1988] - Node qa_sybase16_15001_1736997187904_1988[d5e5f05d-ebbd-4550-aaa9-21ab4fd626ea] close complete, cost 74 ms 
[TRACE] 2025-02-05 12:19:52.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-05 12:19:52.676 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@69365bd1 
[TRACE] 2025-02-05 12:19:52.808 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stop task milestones: 67a2d6880546797e31c1607b(t_7_10_sybase_to_pg_all_type_1736997187904_1988)  
[TRACE] 2025-02-05 12:19:52.808 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Stopped task aspect(s) 
[TRACE] 2025-02-05 12:19:52.809 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Snapshot order controller have been removed 
[INFO ] 2025-02-05 12:19:52.809 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Task stopped. 
[TRACE] 2025-02-05 12:19:53.013 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Remove memory task client succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
[TRACE] 2025-02-05 12:19:53.014 - [t_7_10_sybase_to_pg_all_type_1736997187904_1988] - Destroy memory task client cache succeed, task: t_7_10_sybase_to_pg_all_type_1736997187904_1988[67a2d6880546797e31c1607b] 
