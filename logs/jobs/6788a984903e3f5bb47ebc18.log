[TRACE] 2025-01-16 14:39:01.500 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-47e47fd3-ff2e-4614-9209-973bd0f4fcd7 complete, cost 856ms 
[ERROR] 2025-01-16 14:39:01.519 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 14:39:01.529 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-73cc8743-a576-4886-9b1c-b54d0e2b2a00 complete, cost 683ms 
[ERROR] 2025-01-16 14:39:01.531 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:01.541 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:01.543 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:01.552 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:01.554 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:01.562 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:01.578 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 14:39:10.933 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Task initialization... 
[TRACE] 2025-01-16 14:39:10.935 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Start task milestones: 6788a984903e3f5bb47ebc18(t_89.4-MongoDB->Many's_1736997187904_1988-1737009463) 
[TRACE] 2025-01-16 14:39:11.359 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-f89eda0d-208c-472d-b9ba-d203e54f8e62 complete, cost 318ms 
[ERROR] 2025-01-16 14:39:11.391 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:11.391 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:11.400 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:39:11.416 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2025-01-16 14:39:11.438 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Loading table structure completed 
[TRACE] 2025-01-16 14:39:11.438 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-16 14:39:11.562 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - The engine receives t_89.4-MongoDB->Many's_1736997187904_1988-1737009463 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-16 14:39:11.563 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Task started 
[TRACE] 2025-01-16 14:39:11.614 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a3670b3a-a97f-4467-a671-51266de85fa5] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a3670b3a-a97f-4467-a671-51266de85fa5] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[ea263746-e489-4b84-9f4b-be6855f8ed53] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[f7426c91-80f1-49d6-a0de-d72130374d85] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[ea263746-e489-4b84-9f4b-be6855f8ed53] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[f7426c91-80f1-49d6-a0de-d72130374d85] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node field_rename_processor(Field Rename: a3670b3a-a97f-4467-a671-51266de85fa5) enable batch process 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[5450f191-d536-4a48-95af-60d7b495c101] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[6924ab54-9c79-4099-bd2b-d508cc1d8141] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[5450f191-d536-4a48-95af-60d7b495c101] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[e9591b3f-ad30-4f47-a201-34f5094d0a7f] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[6924ab54-9c79-4099-bd2b-d508cc1d8141] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[e9591b3f-ad30-4f47-a201-34f5094d0a7f] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node js_processor(JS: 5450f191-d536-4a48-95af-60d7b495c101) enable batch process 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[3280c992-6f28-43f0-9030-2175fd00220a] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[994190fd-8f42-4da2-b30b-4aa1ef314338] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[4e4afd9a-cb26-4d06-a95a-9d364d985b93] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[3280c992-6f28-43f0-9030-2175fd00220a] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[994190fd-8f42-4da2-b30b-4aa1ef314338] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[53663e7c-4b3e-45c4-bbba-c3f05b82e060] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node field_rename_processor(Field Rename: ea263746-e489-4b84-9f4b-be6855f8ed53) enable batch process 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node field_rename_processor(Field Rename: e9591b3f-ad30-4f47-a201-34f5094d0a7f) enable batch process 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[4e4afd9a-cb26-4d06-a95a-9d364d985b93] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node js_processor(JS: dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768) enable batch process 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node js_processor(JS: 6924ab54-9c79-4099-bd2b-d508cc1d8141) enable batch process 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[53663e7c-4b3e-45c4-bbba-c3f05b82e060] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a137f9c5-5813-43ff-864e-26d8862cd3f8] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node js_processor(JS: 3280c992-6f28-43f0-9030-2175fd00220a) enable batch process 
[TRACE] 2025-01-16 14:39:11.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[17222fb0-a3a0-4fc9-9272-87ce9efe1c21] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a137f9c5-5813-43ff-864e-26d8862cd3f8] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node field_rename_processor(Field Rename: 53663e7c-4b3e-45c4-bbba-c3f05b82e060) enable batch process 
[TRACE] 2025-01-16 14:39:11.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node field_rename_processor(Field Rename: a137f9c5-5813-43ff-864e-26d8862cd3f8) enable batch process 
[TRACE] 2025-01-16 14:39:11.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[17222fb0-a3a0-4fc9-9272-87ce9efe1c21] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node js_processor(JS: 17222fb0-a3a0-4fc9-9272-87ce9efe1c21) enable batch process 
[TRACE] 2025-01-16 14:39:11.618 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4e5ccd85-35e2-4858-a0e7-998053f5c5c7] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.618 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4e5ccd85-35e2-4858-a0e7-998053f5c5c7] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.821 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[d8da65e9-9c61-4046-a0bc-ba76fcf1af5f] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.821 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[16145f37-6b85-4da0-8416-67a1a46b7ca2] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:39:11.821 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[d8da65e9-9c61-4046-a0bc-ba76fcf1af5f] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:39:11.821 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[16145f37-6b85-4da0-8416-67a1a46b7ca2] preload schema finished, cost 0 ms 
[INFO ] 2025-01-16 14:39:11.844 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Source connector(qa_mongodb_repl_42240_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:39:11.844 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Source node "qa_mongodb_repl_42240_1736997187904_1988" read batch size: 500 
[TRACE] 2025-01-16 14:39:11.844 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Source node "qa_mongodb_repl_42240_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-01-16 14:39:11.845 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-01-16 14:39:12.250 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Use existing stream offset: {"cdcOffset":1737009551,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-01-16 14:39:12.308 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-01-16 14:39:12.312 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync started 
[INFO ] 2025-01-16 14:39:12.312 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Starting batch read from table: qa_auto_test_one_many_89_4_1737009463381_9384 
[TRACE] 2025-01-16 14:39:12.312 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Table qa_auto_test_one_many_89_4_1737009463381_9384 is going to be initial synced 
[TRACE] 2025-01-16 14:39:12.408 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Query snapshot row size completed: qa_mongodb_repl_42240_1736997187904_1988(994190fd-8f42-4da2-b30b-4aa1ef314338) 
[INFO ] 2025-01-16 14:39:12.408 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Sink connector(qa_sqlserver_1443_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:39:12.408 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:39:12.426 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:39:12.426 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Sink connector(qa_mongodb_repl_36230_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:39:12.426 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:39:12.428 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:39:12.600 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Sink connector(qa_oracle_11g_single_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:39:12.602 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:39:12.669 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:39:12.669 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Sink connector(qa_pg_5432_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:39:12.669 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:39:12.670 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-01-16 14:39:12.833 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Use the first node as the default script executor, please use it with caution. 
[INFO ] 2025-01-16 14:39:12.833 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Sink connector(qa_mysql_184_3306_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:39:12.834 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:39:13.034 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-01-16 14:39:13.675 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 14:39:14.275 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 14:39:15.486 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Use the first node as the default script executor, please use it with caution. 
[INFO ] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Table qa_auto_test_one_many_89_4_1737009463381_9384 has been completed batch read 
[TRACE] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Batch read completed. 
[TRACE] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Incremental sync starting... 
[TRACE] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync completed 
[TRACE] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Starting stream read, table list: [qa_auto_test_one_many_89_4_1737009463381_9384, _tapdata_heartbeat_table], offset: {"cdcOffset":1737009551,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-01-16 14:39:18.936 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Starting incremental sync using database log parser 
[TRACE] 2025-01-16 14:39:19.141 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Connector MongoDB incremental start succeed, tables: [qa_auto_test_one_many_89_4_1737009463381_9384, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-01-16 14:39:25.778 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:25.840 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:25.840 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:25.879 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:25.879 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:26.079 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:36.485 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.487 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:39:36.503 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.503 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:39:36.519 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.519 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:39:36.534 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.534 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 14:39:36.548 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.548 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:36.659 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [e9591b3f-ad30-4f47-a201-34f5094d0a7f-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.660 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.660 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:39:36.677 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.677 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:39:36.696 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.697 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:39:36.713 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.713 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:39:36.729 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.729 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:36.729 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.742 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:39:36.742 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [ea263746-e489-4b84-9f4b-be6855f8ed53-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.746 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.746 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:39:36.766 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.766 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:39:36.782 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.782 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:39:36.795 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.795 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:36.889 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a137f9c5-5813-43ff-864e-26d8862cd3f8-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.889 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:36.891 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.891 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed is limited, about to delay 16 millisecond 
[TRACE] 2025-01-16 14:39:36.909 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.909 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:39:36.927 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:39:36.927 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.927 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:39:36.944 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.944 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:39:36.956 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.957 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:36.979 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [a3670b3a-a97f-4467-a671-51266de85fa5-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.979 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.983 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:39:36.983 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:39:36.997 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:36.997 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:39:37.010 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 14:39:37.010 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 14:39:37.013 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:37.013 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:39:37.028 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:37.028 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:39:37.041 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:37.041 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:39:37.046 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:37.046 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:37.057 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - [5450f191-d536-4a48-95af-60d7b495c101-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:37.057 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:39:37.057 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:39:37.057 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:39:37.145 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:37.145 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:37.153 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:39:37.154 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 14:39:37.154 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 14:39:37.490 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:39:37.490 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:39:37.507 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:39:37.507 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:39:37.516 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:39:37.516 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:39:37.561 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:39:37.561 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:39:37.766 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:39:39.476 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:39.476 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed is limited, about to delay 17 millisecond 
[TRACE] 2025-01-16 14:39:39.496 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:39.496 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:39:39.515 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:39.515 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:39:39.531 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:39.532 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:39:39.546 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:39.546 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:39:39.750 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - [53663e7c-4b3e-45c4-bbba-c3f05b82e060-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:39:41.525 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:41.545 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:39:41.545 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:39:41.786 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:39:42.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 14:39:42.165 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 14:39:42.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:39:42.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:39:42.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:39:42.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:39:52.134 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737009463381_9384' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:39:52.134 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1213): when operate table: qa_auto_test_one_many_89_4_1737009463381_9384, com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction 
[ERROR] 2025-01-16 14:39:52.144 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:124)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:132)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:505)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:124)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:124)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:105)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:112)
	... 26 more

[TRACE] 2025-01-16 14:39:52.144 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Job suspend in error handle 
[TRACE] 2025-01-16 14:39:52.148 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1213): when operate table: qa_auto_test_one_many_89_4_1737009463381_9384, com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction 
[TRACE] 2025-01-16 14:39:52.148 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1213): when operate table: qa_auto_test_one_many_89_4_1737009463381_9384, com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction 
[TRACE] 2025-01-16 14:39:52.186 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1213): when operate table: qa_auto_test_one_many_89_4_1737009463381_9384, com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction 
[TRACE] 2025-01-16 14:39:52.186 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[994190fd-8f42-4da2-b30b-4aa1ef314338] running status set to false 
[TRACE] 2025-01-16 14:39:52.198 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_994190fd-8f42-4da2-b30b-4aa1ef314338_1737009551744 
[TRACE] 2025-01-16 14:39:52.198 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_994190fd-8f42-4da2-b30b-4aa1ef314338_1737009551744 
[TRACE] 2025-01-16 14:39:52.198 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[994190fd-8f42-4da2-b30b-4aa1ef314338] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.198 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[994190fd-8f42-4da2-b30b-4aa1ef314338] monitor closed 
[TRACE] 2025-01-16 14:39:52.198 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[994190fd-8f42-4da2-b30b-4aa1ef314338] close complete, cost 12 ms 
[TRACE] 2025-01-16 14:39:52.198 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[5450f191-d536-4a48-95af-60d7b495c101] running status set to false 
[INFO ] 2025-01-16 14:39:52.200 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-82eaac17-3756-4c0f-b03b-1f543937b4cd 
[INFO ] 2025-01-16 14:39:52.200 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-82eaac17-3756-4c0f-b03b-1f543937b4cd 
[INFO ] 2025-01-16 14:39:52.200 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-5450f191-d536-4a48-95af-60d7b495c101-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:39:52.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mysql_184_3306_1736997187904_1988-78aa60a9-8d3e-42b2-9a94-edf9d31b4cef 
[INFO ] 2025-01-16 14:39:52.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mysql_184_3306_1736997187904_1988-78aa60a9-8d3e-42b2-9a94-edf9d31b4cef 
[INFO ] 2025-01-16 14:39:52.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-5450f191-d536-4a48-95af-60d7b495c101-67887cfd5703b91f391e6919] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.209 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[5450f191-d536-4a48-95af-60d7b495c101] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.210 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[5450f191-d536-4a48-95af-60d7b495c101] monitor closed 
[TRACE] 2025-01-16 14:39:52.210 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[5450f191-d536-4a48-95af-60d7b495c101] close complete, cost 11 ms 
[TRACE] 2025-01-16 14:39:52.210 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a3670b3a-a97f-4467-a671-51266de85fa5] running status set to false 
[TRACE] 2025-01-16 14:39:52.261 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a3670b3a-a97f-4467-a671-51266de85fa5] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.261 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a3670b3a-a97f-4467-a671-51266de85fa5] monitor closed 
[TRACE] 2025-01-16 14:39:52.261 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a3670b3a-a97f-4467-a671-51266de85fa5] close complete, cost 51 ms 
[TRACE] 2025-01-16 14:39:52.270 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[f7426c91-80f1-49d6-a0de-d72130374d85] running status set to false 
[TRACE] 2025-01-16 14:39:52.270 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737009463381_9384
 - TapdataEvent{eventId=6788a998e84e1d5f49b2a8b4, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@f81a9a4: {"after":{"created":"2025-01-16 06:39:16.914000","_id":"6788a9972aee356a5a3f7517","id":"88d9e78c-8221-480e-be66-3b4df7895632","v2":"xTEhsQF4"},"containsIllegalDate":false,"referenceTime":1737009559000,"tableId":"qa_auto_test_one_many_89_4_1737009463381_9384","time":1737009560117,"type":300}, nodeIds=[994190fd-8f42-4da2-b30b-4aa1ef314338, 5450f191-d536-4a48-95af-60d7b495c101, a3670b3a-a97f-4467-a671-51266de85fa5], sourceTime=1737009559000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:39:52.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737009463381_9384
 - TapdataEvent{eventId=6788a9a9e84e1d5f49b4347a, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@4741044c: {"after":{"created":"2025-01-16 06:39:24.933000","_id":"6788a99e2aee356a5a3fa8b5","id":"338f3090-ff76-49b8-8b6d-38f4da32df66","v2":"xTEhsQF4"},"containsIllegalDate":false,"referenceTime":1737009566000,"tableId":"qa_auto_test_one_many_89_4_1737009463381_9384","time":1737009577873,"type":300}, nodeIds=[994190fd-8f42-4da2-b30b-4aa1ef314338, 5450f191-d536-4a48-95af-60d7b495c101, a3670b3a-a97f-4467-a671-51266de85fa5], sourceTime=1737009566000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:39:52.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737009463381_9384
 - TapdataEvent{eventId=6788a9a9e84e1d5f49b43587, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6aa6cd28: {"after":{"created":"2025-01-16 06:39:24.933000","_id":"6788a99e2aee356a5a3fa8a0","id":"c39881e1-d5de-4c57-8dca-af92540d23e0","v2":"xTEhsQF4"},"containsIllegalDate":false,"referenceTime":1737009566000,"tableId":"qa_auto_test_one_many_89_4_1737009463381_9384","time":1737009577877,"type":300}, nodeIds=[994190fd-8f42-4da2-b30b-4aa1ef314338, 5450f191-d536-4a48-95af-60d7b495c101, a3670b3a-a97f-4467-a671-51266de85fa5], sourceTime=1737009566000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:39:52.302 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737009463381_9384
 - TapdataEvent{eventId=6788a9a9e84e1d5f49b4326b, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@44696908: {"after":{"created":"2025-01-16 06:39:24.934000","_id":"6788a99e2aee356a5a3fa687","id":"ccfc493c-e254-4ad7-a824-55936060e3ce","v2":"xTEhsQF4"},"containsIllegalDate":false,"referenceTime":1737009566000,"tableId":"qa_auto_test_one_many_89_4_1737009463381_9384","time":1737009577865,"type":300}, nodeIds=[994190fd-8f42-4da2-b30b-4aa1ef314338, 5450f191-d536-4a48-95af-60d7b495c101, a3670b3a-a97f-4467-a671-51266de85fa5], sourceTime=1737009566000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:39:52.302 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_f7426c91-80f1-49d6-a0de-d72130374d85_1737009552398 
[TRACE] 2025-01-16 14:39:52.302 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_f7426c91-80f1-49d6-a0de-d72130374d85_1737009552398 
[TRACE] 2025-01-16 14:39:52.303 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[f7426c91-80f1-49d6-a0de-d72130374d85] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.303 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[f7426c91-80f1-49d6-a0de-d72130374d85] monitor closed 
[TRACE] 2025-01-16 14:39:52.303 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[f7426c91-80f1-49d6-a0de-d72130374d85] close complete, cost 41 ms 
[TRACE] 2025-01-16 14:39:52.303 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[3280c992-6f28-43f0-9030-2175fd00220a] running status set to false 
[INFO ] 2025-01-16 14:39:52.305 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-142b85a4-61d4-4559-9fdd-c526c0feea7f 
[INFO ] 2025-01-16 14:39:52.305 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-142b85a4-61d4-4559-9fdd-c526c0feea7f 
[INFO ] 2025-01-16 14:39:52.305 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-3280c992-6f28-43f0-9030-2175fd00220a-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:39:52.307 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_36230_1736997187904_1988-e410c2c3-238e-4252-be0f-d6f21e192b5c 
[INFO ] 2025-01-16 14:39:52.307 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_36230_1736997187904_1988-e410c2c3-238e-4252-be0f-d6f21e192b5c 
[INFO ] 2025-01-16 14:39:52.307 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-3280c992-6f28-43f0-9030-2175fd00220a-6788a2fc903e3f5bb47eb998] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.307 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[3280c992-6f28-43f0-9030-2175fd00220a] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.307 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[3280c992-6f28-43f0-9030-2175fd00220a] monitor closed 
[TRACE] 2025-01-16 14:39:52.365 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[3280c992-6f28-43f0-9030-2175fd00220a] close complete, cost 4 ms 
[TRACE] 2025-01-16 14:39:52.365 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[e9591b3f-ad30-4f47-a201-34f5094d0a7f] running status set to false 
[TRACE] 2025-01-16 14:39:52.365 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_42240_1736997187904_1988] - Incremental sync completed 
[TRACE] 2025-01-16 14:39:52.367 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[e9591b3f-ad30-4f47-a201-34f5094d0a7f] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.367 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[e9591b3f-ad30-4f47-a201-34f5094d0a7f] monitor closed 
[TRACE] 2025-01-16 14:39:52.367 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[e9591b3f-ad30-4f47-a201-34f5094d0a7f] close complete, cost 59 ms 
[TRACE] 2025-01-16 14:39:52.367 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[d8da65e9-9c61-4046-a0bc-ba76fcf1af5f] running status set to false 
[TRACE] 2025-01-16 14:39:52.384 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737009463381_9384
 - TapdataEvent{eventId=6788a9b6e84e1d5f49b94328, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6d3063ad: {"after":{"created":1737009589002,"id":"67f2df50-756d-4ab4-89f1-831422f7c65b","v2":"xTEhsQF4"},"containsIllegalDate":false,"referenceTime":1737009590000,"tableId":"qa_auto_test_one_many_89_4_1737009463381_9384","time":1737009590795,"type":300}, nodeIds=[994190fd-8f42-4da2-b30b-4aa1ef314338, 5450f191-d536-4a48-95af-60d7b495c101, 3280c992-6f28-43f0-9030-2175fd00220a, e9591b3f-ad30-4f47-a201-34f5094d0a7f], sourceTime=1737009590000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:39:52.385 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_d8da65e9-9c61-4046-a0bc-ba76fcf1af5f_1737009552295 
[TRACE] 2025-01-16 14:39:52.388 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_d8da65e9-9c61-4046-a0bc-ba76fcf1af5f_1737009552295 
[TRACE] 2025-01-16 14:39:52.388 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[d8da65e9-9c61-4046-a0bc-ba76fcf1af5f] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.388 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[d8da65e9-9c61-4046-a0bc-ba76fcf1af5f] monitor closed 
[TRACE] 2025-01-16 14:39:52.388 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[d8da65e9-9c61-4046-a0bc-ba76fcf1af5f] close complete, cost 17 ms 
[TRACE] 2025-01-16 14:39:52.388 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[6924ab54-9c79-4099-bd2b-d508cc1d8141] running status set to false 
[INFO ] 2025-01-16 14:39:52.389 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-731f9791-8e3b-4f58-a437-bf9c3a703f74 
[INFO ] 2025-01-16 14:39:52.389 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-731f9791-8e3b-4f58-a437-bf9c3a703f74 
[INFO ] 2025-01-16 14:39:52.390 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-6924ab54-9c79-4099-bd2b-d508cc1d8141-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:39:52.424 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_oracle_11g_single_1736997187904_1988-32e09ca4-4186-4372-bc13-7d5505df9ad8 
[INFO ] 2025-01-16 14:39:52.427 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_oracle_11g_single_1736997187904_1988-32e09ca4-4186-4372-bc13-7d5505df9ad8 
[INFO ] 2025-01-16 14:39:52.427 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-6924ab54-9c79-4099-bd2b-d508cc1d8141-67887d025703b91f391e691e] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.427 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[6924ab54-9c79-4099-bd2b-d508cc1d8141] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.427 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[6924ab54-9c79-4099-bd2b-d508cc1d8141] monitor closed 
[TRACE] 2025-01-16 14:39:52.428 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[6924ab54-9c79-4099-bd2b-d508cc1d8141] close complete, cost 42 ms 
[TRACE] 2025-01-16 14:39:52.428 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[ea263746-e489-4b84-9f4b-be6855f8ed53] running status set to false 
[TRACE] 2025-01-16 14:39:52.466 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[ea263746-e489-4b84-9f4b-be6855f8ed53] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.466 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[ea263746-e489-4b84-9f4b-be6855f8ed53] monitor closed 
[TRACE] 2025-01-16 14:39:52.466 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[ea263746-e489-4b84-9f4b-be6855f8ed53] close complete, cost 38 ms 
[TRACE] 2025-01-16 14:39:52.466 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[16145f37-6b85-4da0-8416-67a1a46b7ca2] running status set to false 
[TRACE] 2025-01-16 14:39:52.604 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_16145f37-6b85-4da0-8416-67a1a46b7ca2_1737009552170 
[TRACE] 2025-01-16 14:39:52.604 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_16145f37-6b85-4da0-8416-67a1a46b7ca2_1737009552170 
[TRACE] 2025-01-16 14:39:52.604 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[16145f37-6b85-4da0-8416-67a1a46b7ca2] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.604 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[16145f37-6b85-4da0-8416-67a1a46b7ca2] monitor closed 
[TRACE] 2025-01-16 14:39:52.604 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[16145f37-6b85-4da0-8416-67a1a46b7ca2] close complete, cost 138 ms 
[TRACE] 2025-01-16 14:39:52.604 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[17222fb0-a3a0-4fc9-9272-87ce9efe1c21] running status set to false 
[INFO ] 2025-01-16 14:39:52.606 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-2d9beaf5-9666-42e4-9c83-a308cfce7041 
[INFO ] 2025-01-16 14:39:52.607 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-2d9beaf5-9666-42e4-9c83-a308cfce7041 
[INFO ] 2025-01-16 14:39:52.607 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-17222fb0-a3a0-4fc9-9272-87ce9efe1c21-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:39:52.611 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_pg_5432_1736997187904_1988-8440680f-3fa2-4f5f-a240-7ae8f3316d16 
[INFO ] 2025-01-16 14:39:52.611 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_pg_5432_1736997187904_1988-8440680f-3fa2-4f5f-a240-7ae8f3316d16 
[INFO ] 2025-01-16 14:39:52.611 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-17222fb0-a3a0-4fc9-9272-87ce9efe1c21-67887d0d5703b91f391e6923] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.612 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[17222fb0-a3a0-4fc9-9272-87ce9efe1c21] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.612 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[17222fb0-a3a0-4fc9-9272-87ce9efe1c21] monitor closed 
[TRACE] 2025-01-16 14:39:52.612 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[17222fb0-a3a0-4fc9-9272-87ce9efe1c21] close complete, cost 7 ms 
[TRACE] 2025-01-16 14:39:52.612 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768] running status set to false 
[INFO ] 2025-01-16 14:39:52.614 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-b8163dd7-93a8-46ad-b351-267c2b470a7e 
[INFO ] 2025-01-16 14:39:52.614 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-b8163dd7-93a8-46ad-b351-267c2b470a7e 
[INFO ] 2025-01-16 14:39:52.614 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:39:52.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_sqlserver_1443_1736997187904_1988-5050736e-f4c1-491a-9673-53328d1656f9 
[INFO ] 2025-01-16 14:39:52.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_sqlserver_1443_1736997187904_1988-5050736e-f4c1-491a-9673-53328d1656f9 
[INFO ] 2025-01-16 14:39:52.616 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS][src=user_script]  - [ScriptExecutorsManager-6788a984903e3f5bb47ebc18-dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768-67887d135703b91f391e6928] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768] monitor closed 
[TRACE] 2025-01-16 14:39:52.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][JS] - Node JS[dfe3e7ef-ab6b-46aa-9b6c-b572cca7d768] close complete, cost 4 ms 
[TRACE] 2025-01-16 14:39:52.617 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[53663e7c-4b3e-45c4-bbba-c3f05b82e060] running status set to false 
[TRACE] 2025-01-16 14:39:52.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[53663e7c-4b3e-45c4-bbba-c3f05b82e060] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[53663e7c-4b3e-45c4-bbba-c3f05b82e060] monitor closed 
[TRACE] 2025-01-16 14:39:52.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[53663e7c-4b3e-45c4-bbba-c3f05b82e060] close complete, cost 36 ms 
[TRACE] 2025-01-16 14:39:52.654 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a137f9c5-5813-43ff-864e-26d8862cd3f8] running status set to false 
[TRACE] 2025-01-16 14:39:52.688 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a137f9c5-5813-43ff-864e-26d8862cd3f8] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.688 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a137f9c5-5813-43ff-864e-26d8862cd3f8] monitor closed 
[TRACE] 2025-01-16 14:39:52.689 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][Field Rename] - Node Field Rename[a137f9c5-5813-43ff-864e-26d8862cd3f8] close complete, cost 34 ms 
[TRACE] 2025-01-16 14:39:52.689 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[4e4afd9a-cb26-4d06-a95a-9d364d985b93] running status set to false 
[TRACE] 2025-01-16 14:39:52.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_4e4afd9a-cb26-4d06-a95a-9d364d985b93_1737009552414 
[TRACE] 2025-01-16 14:39:52.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_4e4afd9a-cb26-4d06-a95a-9d364d985b93_1737009552414 
[TRACE] 2025-01-16 14:39:52.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[4e4afd9a-cb26-4d06-a95a-9d364d985b93] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[4e4afd9a-cb26-4d06-a95a-9d364d985b93] monitor closed 
[TRACE] 2025-01-16 14:39:52.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[4e4afd9a-cb26-4d06-a95a-9d364d985b93] close complete, cost 13 ms 
[TRACE] 2025-01-16 14:39:52.713 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4e5ccd85-35e2-4858-a0e7-998053f5c5c7] running status set to false 
[TRACE] 2025-01-16 14:39:52.713 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_4e5ccd85-35e2-4858-a0e7-998053f5c5c7_1737009552244 
[TRACE] 2025-01-16 14:39:52.713 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_4e5ccd85-35e2-4858-a0e7-998053f5c5c7_1737009552244 
[TRACE] 2025-01-16 14:39:52.714 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4e5ccd85-35e2-4858-a0e7-998053f5c5c7] schema data cleaned 
[TRACE] 2025-01-16 14:39:52.714 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4e5ccd85-35e2-4858-a0e7-998053f5c5c7] monitor closed 
[TRACE] 2025-01-16 14:39:52.714 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[4e5ccd85-35e2-4858-a0e7-998053f5c5c7] close complete, cost 11 ms 
[INFO ] 2025-01-16 14:39:56.673 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Task [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-01-16 14:39:56.675 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-16 14:39:56.675 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2ac7e70e 
[TRACE] 2025-01-16 14:39:56.792 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Stop task milestones: 6788a984903e3f5bb47ebc18(t_89.4-MongoDB->Many's_1736997187904_1988-1737009463)  
[TRACE] 2025-01-16 14:39:56.792 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Stopped task aspect(s) 
[TRACE] 2025-01-16 14:39:56.792 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Snapshot order controller have been removed 
[INFO ] 2025-01-16 14:39:56.792 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Task stopped. 
[TRACE] 2025-01-16 14:39:56.813 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Remove memory task client succeed, task: t_89.4-MongoDB->Many's_1736997187904_1988-1737009463[6788a984903e3f5bb47ebc18] 
[TRACE] 2025-01-16 14:39:56.813 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - Destroy memory task client cache succeed, task: t_89.4-MongoDB->Many's_1736997187904_1988-1737009463[6788a984903e3f5bb47ebc18] 
[TRACE] 2025-01-16 15:17:41.922 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-5f7ae7eb-dbff-443b-8623-6f7d5c67b5db complete, cost 415ms 
[ERROR] 2025-01-16 15:17:41.941 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:41.953 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:41.963 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:41.980 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 15:17:44.533 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-d6f24438-e4c0-476b-abdb-2a67e0ae07d0 complete, cost 343ms 
[ERROR] 2025-01-16 15:17:44.551 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:44.565 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:44.576 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:44.596 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 15:17:44.607 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-fdf216ee-b236-4bb1-b04f-843e72e856b8 complete, cost 262ms 
[ERROR] 2025-01-16 15:17:44.623 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:44.647 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:44.647 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:17:44.663 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 15:20:11.065 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - load tapTable task 6788a984903e3f5bb47ebc17-963ebdee-aa21-45fb-b0a1-1bad5aa8ff64 complete, cost 367ms 
[ERROR] 2025-01-16 15:20:11.085 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:20:11.098 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:20:11.111 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 15:20:11.128 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737009463] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

