[TRACE] 2025-01-16 14:12:55.534 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - load tapTable task 6788a366903e3f5bb47eba73-a1ff13e9-97c3-4f49-99df-08b5b40602d2 complete, cost 969ms 
[ERROR] 2025-01-16 14:12:55.568 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:12:55.568 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 14:12:55.586 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - load tapTable task 6788a366903e3f5bb47eba73-cd92b95a-97a8-4a95-b3b6-2eb00de4e64d complete, cost 1145ms 
[ERROR] 2025-01-16 14:12:55.611 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:12:55.615 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:12:55.621 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:12:55.637 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:12:55.642 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:12:55.660 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:506)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:71)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:36)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:291)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[TRACE] 2025-01-16 14:13:05.024 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Task initialization... 
[TRACE] 2025-01-16 14:13:05.024 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Start task milestones: 6788a366903e3f5bb47eba74(t_89.4-MongoDB->Many's_1736997187904_1988-1737007897) 
[TRACE] 2025-01-16 14:13:05.645 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - load tapTable task 6788a366903e3f5bb47eba73-3482bdfe-0bd6-4bf9-ae49-e9592b13ba40 complete, cost 387ms 
[ERROR] 2025-01-16 14:13:05.656 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:13:05.682 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:13:05.700 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[ERROR] 2025-01-16 14:13:05.717 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.lang.NullPointerException
	at java.util.stream.Collectors.lambda$toMap$58(Collectors.java:1320)
	at java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTestTask(HazelcastTaskService.java:220)
	at io.tapdata.common.DAGDataEngineServiceImpl.execTask(DAGDataEngineServiceImpl.java:139)
	at io.tapdata.common.DAGDataEngineServiceImpl.loadTapTable(DAGDataEngineServiceImpl.java:90)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:118)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:177)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:325)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:322)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:315)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:616)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:616)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.engineTransformSchema(HazelcastTaskService.java:1014)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:269)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:192)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:179)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:114)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:345)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:196)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$6(TapdataTaskScheduler.java:196)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2025-01-16 14:13:05.749 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Loading table structure completed 
[TRACE] 2025-01-16 14:13:05.749 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Node performs snapshot read asynchronously 
[TRACE] 2025-01-16 14:13:05.855 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - The engine receives t_89.4-MongoDB->Many's_1736997187904_1988-1737007897 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-01-16 14:13:05.855 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Task started 
[TRACE] 2025-01-16 14:13:05.907 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.907 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.909 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[082dd897-714c-4b02-8c67-0cb3a807b6d4] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.909 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[082dd897-714c-4b02-8c67-0cb3a807b6d4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[bc32bb34-d9e4-4241-817e-fbd95a1d45c9] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node js_processor(JS: 082dd897-714c-4b02-8c67-0cb3a807b6d4) enable batch process 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[bc32bb34-d9e4-4241-817e-fbd95a1d45c9] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[e8f212df-65ea-4fd4-9d2e-c622ef5c3838] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[e8f212df-65ea-4fd4-9d2e-c622ef5c3838] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node field_rename_processor(Field Rename: bc32bb34-d9e4-4241-817e-fbd95a1d45c9) enable batch process 
[TRACE] 2025-01-16 14:13:05.910 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node js_processor(JS: e8f212df-65ea-4fd4-9d2e-c622ef5c3838) enable batch process 
[TRACE] 2025-01-16 14:13:05.912 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[507c6edf-5467-4ea0-9bf2-ca36a0221dab] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.912 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[1e9a5f51-dacc-44ee-b97f-4f495c63df0f] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.912 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[e8bf87ce-2bbf-4974-ab90-89ded3af2658] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.912 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[507c6edf-5467-4ea0-9bf2-ca36a0221dab] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[baf7b50a-ac21-4fe9-a558-00cb2c03a893] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[1e9a5f51-dacc-44ee-b97f-4f495c63df0f] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[e8bf87ce-2bbf-4974-ab90-89ded3af2658] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[baf7b50a-ac21-4fe9-a558-00cb2c03a893] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[0aaa86f7-9fde-4f9f-a438-0e5809dedf1f] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[f45889f9-f4f8-4835-bcea-8dc45a4f38df] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node js_processor(JS: 1e9a5f51-dacc-44ee-b97f-4f495c63df0f) enable batch process 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[592a776c-0d75-48c3-9a99-3891f1faebe4] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.913 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[f45889f9-f4f8-4835-bcea-8dc45a4f38df] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[592a776c-0d75-48c3-9a99-3891f1faebe4] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[0aaa86f7-9fde-4f9f-a438-0e5809dedf1f] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node js_processor(JS: f45889f9-f4f8-4835-bcea-8dc45a4f38df) enable batch process 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[746fddd7-37a2-4b05-aa40-45acd862c43c] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node field_rename_processor(Field Rename: 507c6edf-5467-4ea0-9bf2-ca36a0221dab) enable batch process 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[746fddd7-37a2-4b05-aa40-45acd862c43c] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node field_rename_processor(Field Rename: 592a776c-0d75-48c3-9a99-3891f1faebe4) enable batch process 
[TRACE] 2025-01-16 14:13:05.914 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node js_processor(JS: 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f) enable batch process 
[TRACE] 2025-01-16 14:13:05.915 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node field_rename_processor(Field Rename: 746fddd7-37a2-4b05-aa40-45acd862c43c) enable batch process 
[TRACE] 2025-01-16 14:13:05.916 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[c7dbe642-0c8e-4a2c-acb5-55f5f915bf16] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.916 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8d70946e-5f78-4b1a-8f7b-eeea9184aac9] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.916 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[c7dbe642-0c8e-4a2c-acb5-55f5f915bf16] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.916 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8d70946e-5f78-4b1a-8f7b-eeea9184aac9] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.918 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node field_rename_processor(Field Rename: c7dbe642-0c8e-4a2c-acb5-55f5f915bf16) enable batch process 
[TRACE] 2025-01-16 14:13:05.920 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[772c70b2-db5b-42b9-8377-9245df2b15fc] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.920 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[772c70b2-db5b-42b9-8377-9245df2b15fc] preload schema finished, cost 0 ms 
[TRACE] 2025-01-16 14:13:05.920 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[962c93f2-c64c-4017-bff4-b9c8496f6b97] start preload schema,table counts: 1 
[TRACE] 2025-01-16 14:13:05.920 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[962c93f2-c64c-4017-bff4-b9c8496f6b97] preload schema finished, cost 0 ms 
[INFO ] 2025-01-16 14:13:06.167 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Source connector(qa_mongodb_repl_42240_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:13:06.167 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Source node "qa_mongodb_repl_42240_1736997187904_1988" read batch size: 500 
[TRACE] 2025-01-16 14:13:06.167 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Source node "qa_mongodb_repl_42240_1736997187904_1988" event queue capacity: 1000 
[TRACE] 2025-01-16 14:13:06.370 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-01-16 14:13:06.509 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Use existing stream offset: {"cdcOffset":1737007984,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-01-16 14:13:06.509 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Sink connector(qa_mongodb_repl_36230_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:13:06.509 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:13:06.510 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:13:06.625 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Starting batch read from 1 tables 
[TRACE] 2025-01-16 14:13:06.625 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync started 
[INFO ] 2025-01-16 14:13:06.625 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Starting batch read from table: qa_auto_test_one_many_89_4_1737007897364_2936 
[TRACE] 2025-01-16 14:13:06.625 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Table qa_auto_test_one_many_89_4_1737007897364_2936 is going to be initial synced 
[TRACE] 2025-01-16 14:13:06.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Query snapshot row size completed: qa_mongodb_repl_42240_1736997187904_1988(e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4) 
[INFO ] 2025-01-16 14:13:06.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Sink connector(qa_pg_5432_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:13:06.653 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:13:06.754 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:13:06.754 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Sink connector(qa_sqlserver_1443_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:13:06.754 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:13:06.755 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:13:06.958 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Sink connector(qa_oracle_11g_single_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:13:06.958 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:13:06.991 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Apply table structure to target database 
[INFO ] 2025-01-16 14:13:06.991 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Sink connector(qa_mysql_184_3306_1736997187904_1988) initialization completed 
[TRACE] 2025-01-16 14:13:06.991 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-01-16 14:13:07.174 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Apply table structure to target database 
[WARN ] 2025-01-16 14:13:07.174 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 14:13:08.189 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 14:13:08.850 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Use the first node as the default script executor, please use it with caution. 
[WARN ] 2025-01-16 14:13:09.864 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Use the first node as the default script executor, please use it with caution. 
[INFO ] 2025-01-16 14:13:13.004 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Table qa_auto_test_one_many_89_4_1737007897364_2936 has been completed batch read 
[TRACE] 2025-01-16 14:13:13.004 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync completed 
[INFO ] 2025-01-16 14:13:13.004 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Batch read completed. 
[TRACE] 2025-01-16 14:13:13.004 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Incremental sync starting... 
[TRACE] 2025-01-16 14:13:13.005 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Initial sync completed 
[TRACE] 2025-01-16 14:13:13.005 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Starting stream read, table list: [qa_auto_test_one_many_89_4_1737007897364_2936, _tapdata_heartbeat_table], offset: {"cdcOffset":1737007984,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-01-16 14:13:13.020 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Starting incremental sync using database log parser 
[TRACE] 2025-01-16 14:13:13.021 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Connector MongoDB incremental start succeed, tables: [qa_auto_test_one_many_89_4_1737007897364_2936, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-01-16 14:13:19.839 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:19.840 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:19.873 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:19.873 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:19.939 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:19.939 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:21.013 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:21.014 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:30.575 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.575 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:30.592 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.592 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:30.607 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.607 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:30.623 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.623 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 14:13:30.639 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.639 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:30.654 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [592a776c-0d75-48c3-9a99-3891f1faebe4-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.655 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.702 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:30.721 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.721 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:30.739 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.739 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:30.941 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [507c6edf-5467-4ea0-9bf2-ca36a0221dab-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:30.982 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:13:31.019 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.020 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:13:31.034 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 14:13:31.034 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 14:13:31.057 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:13:31.057 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.058 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:31.065 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.065 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:31.074 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.074 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:31.085 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.085 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:31.090 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.090 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:31.098 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.098 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.098 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.098 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.103 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.103 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:31.103 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.118 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:13:31.118 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [bc32bb34-d9e4-4241-817e-fbd95a1d45c9-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.118 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.118 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:31.131 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.131 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:31.135 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [c7dbe642-0c8e-4a2c-acb5-55f5f915bf16-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.135 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.151 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.151 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:31.167 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.167 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:31.195 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.195 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:13:31.195 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.195 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:31.207 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [082dd897-714c-4b02-8c67-0cb3a807b6d4-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.290 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:31.290 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.308 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:31.308 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.322 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:13:31.322 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.335 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 14:13:31.335 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.347 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:31.347 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [e8f212df-65ea-4fd4-9d2e-c622ef5c3838-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.457 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:31.457 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.473 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:31.474 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.474 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:13:31.490 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.490 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed is limited, about to delay 11 millisecond 
[TRACE] 2025-01-16 14:13:31.503 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.503 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed is limited, about to delay 10 millisecond 
[TRACE] 2025-01-16 14:13:31.569 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - [f45889f9-f4f8-4835-bcea-8dc45a4f38df-JS] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:31.569 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:13:31.608 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.608 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.608 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:31.715 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:13:31.715 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:13:31.759 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:13:31.759 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:13:31.781 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 14:13:31.781 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-01-16 14:13:31.902 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:13:31.902 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:13:32.170 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 14:13:32.170 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:32.273 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-01-16 14:13:32.275 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[TRACE] 2025-01-16 14:13:32.275 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[TRACE] 2025-01-16 14:13:32.687 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-01-16 14:13:32.892 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-01-16 14:13:33.801 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 14:13:33.802 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:33.802 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed is limited, about to delay 17 millisecond 
[TRACE] 2025-01-16 14:13:33.820 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:33.838 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed is limited, about to delay 15 millisecond 
[TRACE] 2025-01-16 14:13:33.838 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:33.855 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed is limited, about to delay 14 millisecond 
[TRACE] 2025-01-16 14:13:33.856 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:33.876 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed is limited, about to delay 13 millisecond 
[TRACE] 2025-01-16 14:13:33.876 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:33.876 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed is limited, about to delay 12 millisecond 
[TRACE] 2025-01-16 14:13:33.889 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - [746fddd7-37a2-4b05-aa40-45acd862c43c-Field Rename] Successor node processing speed recovery 
[TRACE] 2025-01-16 14:13:34.425 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 14:13:34.425 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:34.425 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:34.495 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 14:13:34.495 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[TRACE] 2025-01-16 14:13:34.613 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 12 
[TRACE] 2025-01-16 14:13:34.613 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-01-16 14:13:34.613 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:13:34.706 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:34.706 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:34.706 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:34.706 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:34.706 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-01-16 14:13:34.706 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-01-16 14:13:34.714 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[TRACE] 2025-01-16 14:13:34.715 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[TRACE] 2025-01-16 14:13:34.826 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:13:34.826 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 15 
[TRACE] 2025-01-16 14:13:35.607 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:13:35.767 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-01-16 14:13:35.767 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-01-16 14:13:35.925 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-01-16 14:13:36.080 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-01-16 14:13:36.080 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1213): when operate table: qa_auto_test_one_many_89_4_1737007897364_2936, com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction 
[ERROR] 2025-01-16 14:13:36.094 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:124)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:132)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:505)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:124)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException: Deadlock found when trying to get lock; try restarting transaction
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:124)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:105)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:112)
	... 26 more

[TRACE] 2025-01-16 14:13:36.094 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Job suspend in error handle 
[TRACE] 2025-01-16 14:13:36.156 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4] running status set to false 
[TRACE] 2025-01-16 14:13:36.156 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - PDK connector node stopped: HazelcastSourcePdkDataNode_e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4_1737007986039 
[TRACE] 2025-01-16 14:13:36.156 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - PDK connector node released: HazelcastSourcePdkDataNode_e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4_1737007986039 
[TRACE] 2025-01-16 14:13:36.156 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.156 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4] monitor closed 
[TRACE] 2025-01-16 14:13:36.156 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Node qa_mongodb_repl_42240_1736997187904_1988[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4] close complete, cost 22 ms 
[TRACE] 2025-01-16 14:13:36.158 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[082dd897-714c-4b02-8c67-0cb3a807b6d4] running status set to false 
[INFO ] 2025-01-16 14:13:36.158 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-3fb2e3cb-7ee6-47d8-9a40-c5bf72009f2e 
[INFO ] 2025-01-16 14:13:36.158 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-3fb2e3cb-7ee6-47d8-9a40-c5bf72009f2e 
[INFO ] 2025-01-16 14:13:36.158 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-082dd897-714c-4b02-8c67-0cb3a807b6d4-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:13:36.212 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mysql_184_3306_1736997187904_1988-6724a044-65e0-4d1c-bb7b-ea9898cf6d93 
[INFO ] 2025-01-16 14:13:36.212 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mysql_184_3306_1736997187904_1988-6724a044-65e0-4d1c-bb7b-ea9898cf6d93 
[INFO ] 2025-01-16 14:13:36.213 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-082dd897-714c-4b02-8c67-0cb3a807b6d4-67887cfd5703b91f391e6919] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.213 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[082dd897-714c-4b02-8c67-0cb3a807b6d4] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.213 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[082dd897-714c-4b02-8c67-0cb3a807b6d4] monitor closed 
[TRACE] 2025-01-16 14:13:36.213 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[082dd897-714c-4b02-8c67-0cb3a807b6d4] close complete, cost 56 ms 
[TRACE] 2025-01-16 14:13:36.258 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[bc32bb34-d9e4-4241-817e-fbd95a1d45c9] running status set to false 
[TRACE] 2025-01-16 14:13:36.258 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[bc32bb34-d9e4-4241-817e-fbd95a1d45c9] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.258 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[bc32bb34-d9e4-4241-817e-fbd95a1d45c9] monitor closed 
[TRACE] 2025-01-16 14:13:36.258 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[bc32bb34-d9e4-4241-817e-fbd95a1d45c9] close complete, cost 44 ms 
[TRACE] 2025-01-16 14:13:36.274 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[e8bf87ce-2bbf-4974-ab90-89ded3af2658] running status set to false 
[TRACE] 2025-01-16 14:13:36.274 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_e8bf87ce-2bbf-4974-ab90-89ded3af2658_1737007986609 
[TRACE] 2025-01-16 14:13:36.274 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_e8bf87ce-2bbf-4974-ab90-89ded3af2658_1737007986609 
[TRACE] 2025-01-16 14:13:36.275 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[e8bf87ce-2bbf-4974-ab90-89ded3af2658] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.275 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[e8bf87ce-2bbf-4974-ab90-89ded3af2658] monitor closed 
[TRACE] 2025-01-16 14:13:36.275 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Node qa_mysql_184_3306_1736997187904_1988[e8bf87ce-2bbf-4974-ab90-89ded3af2658] close complete, cost 16 ms 
[TRACE] 2025-01-16 14:13:36.277 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[e8f212df-65ea-4fd4-9d2e-c622ef5c3838] running status set to false 
[INFO ] 2025-01-16 14:13:36.277 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-2b442907-68b3-41de-9144-fa174c63ffaf 
[INFO ] 2025-01-16 14:13:36.277 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-2b442907-68b3-41de-9144-fa174c63ffaf 
[INFO ] 2025-01-16 14:13:36.278 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-e8f212df-65ea-4fd4-9d2e-c622ef5c3838-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:13:36.280 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_36230_1736997187904_1988-2db9e1ed-07a6-4681-9f07-162864566840 
[INFO ] 2025-01-16 14:13:36.280 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_36230_1736997187904_1988-2db9e1ed-07a6-4681-9f07-162864566840 
[INFO ] 2025-01-16 14:13:36.280 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-e8f212df-65ea-4fd4-9d2e-c622ef5c3838-6788a2fc903e3f5bb47eb998] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.280 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[e8f212df-65ea-4fd4-9d2e-c622ef5c3838] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.280 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[e8f212df-65ea-4fd4-9d2e-c622ef5c3838] monitor closed 
[TRACE] 2025-01-16 14:13:36.281 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[e8f212df-65ea-4fd4-9d2e-c622ef5c3838] close complete, cost 5 ms 
[TRACE] 2025-01-16 14:13:36.281 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[f45889f9-f4f8-4835-bcea-8dc45a4f38df] running status set to false 
[INFO ] 2025-01-16 14:13:36.282 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-fbc91e47-03af-493a-8c8a-a49d575ec18c 
[INFO ] 2025-01-16 14:13:36.283 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-fbc91e47-03af-493a-8c8a-a49d575ec18c 
[INFO ] 2025-01-16 14:13:36.283 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-f45889f9-f4f8-4835-bcea-8dc45a4f38df-67887ced5703b91f391e6914] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.284 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b12df, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3768ced0: {"after":{"created":"2025-01-16 06:13:07.006000","_id":"6788a3772aee356a5a39bda5","id":"99029333-7ea2-4cf6-9fbe-8b6bb42eb8b0","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994013,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.285 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a38be84e1d5f498ced9a, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@49996fa2: {"after":{"created":"2025-01-16 06:13:16.034000","_id":"6788a37d2aee356a5a3a7b6f","id":"501b6587-f69a-4fb9-959b-21a6e6adb3c0","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007997000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737008011215,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007997000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.285 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b2ffe, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@b69eded: {"after":{"created":"2025-01-16 06:13:08.007000","_id":"6788a3772aee356a5a39c20f","id":"9afbffdb-3ed6-4e6c-b0d9-c9f7fd2753fb","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994027,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.285 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b2af9, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3154d9f3: {"after":{"created":"2025-01-16 06:13:07.007000","_id":"6788a3772aee356a5a39bfc7","id":"621a6493-a055-4af7-aac9-0b5a7bd90b05","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994024,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.285 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b5cdf, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@24d96371: {"after":{"created":"2025-01-16 06:13:07.007000","_id":"6788a3772aee356a5a39c9c6","id":"a0900731-4c94-4054-8af0-a72de5409abe","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994059,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.286 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b9321, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7f69418b: {"after":{"created":"2025-01-16 06:13:08.005000","_id":"6788a3772aee356a5a39bbf4","id":"fb7438f8-5b02-40e0-892b-39ec45e6b7cd","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994095,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.286 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mysql_184_3306_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b8b51, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@2e576441: {"after":{"created":"2025-01-16 06:13:08.006000","_id":"6788a3772aee356a5a39cb9c","id":"13a2ba56-1217-40c2-8751-e79770df539b","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994094,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, bc32bb34-d9e4-4241-817e-fbd95a1d45c9], sourceTime=1737007991000, sourceSerialNo=null} 
[INFO ] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_oracle_11g_single_1736997187904_1988-e6a4b867-0f7e-4cff-b3b5-8f18f592a531 
[INFO ] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_oracle_11g_single_1736997187904_1988-e6a4b867-0f7e-4cff-b3b5-8f18f592a531 
[INFO ] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-f45889f9-f4f8-4835-bcea-8dc45a4f38df-67887d025703b91f391e691e] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[f45889f9-f4f8-4835-bcea-8dc45a4f38df] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[f45889f9-f4f8-4835-bcea-8dc45a4f38df] monitor closed 
[TRACE] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[f45889f9-f4f8-4835-bcea-8dc45a4f38df] close complete, cost 30 ms 
[TRACE] 2025-01-16 14:13:36.311 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[0aaa86f7-9fde-4f9f-a438-0e5809dedf1f] running status set to false 
[INFO ] 2025-01-16 14:13:36.314 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-a168334f-c338-444d-92a6-f0d9d44d87cc 
[INFO ] 2025-01-16 14:13:36.314 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-a168334f-c338-444d-92a6-f0d9d44d87cc 
[INFO ] 2025-01-16 14:13:36.316 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-0aaa86f7-9fde-4f9f-a438-0e5809dedf1f-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_pg_5432_1736997187904_1988-b64ce216-0688-441b-ac75-504bdd45f9d8 
[INFO ] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_pg_5432_1736997187904_1988-b64ce216-0688-441b-ac75-504bdd45f9d8 
[INFO ] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-0aaa86f7-9fde-4f9f-a438-0e5809dedf1f-67887d0d5703b91f391e6923] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[0aaa86f7-9fde-4f9f-a438-0e5809dedf1f] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[0aaa86f7-9fde-4f9f-a438-0e5809dedf1f] monitor closed 
[TRACE] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[0aaa86f7-9fde-4f9f-a438-0e5809dedf1f] close complete, cost 5 ms 
[TRACE] 2025-01-16 14:13:36.317 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[507c6edf-5467-4ea0-9bf2-ca36a0221dab] running status set to false 
[TRACE] 2025-01-16 14:13:36.347 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[507c6edf-5467-4ea0-9bf2-ca36a0221dab] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.347 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[507c6edf-5467-4ea0-9bf2-ca36a0221dab] monitor closed 
[TRACE] 2025-01-16 14:13:36.347 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[507c6edf-5467-4ea0-9bf2-ca36a0221dab] close complete, cost 30 ms 
[TRACE] 2025-01-16 14:13:36.348 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[772c70b2-db5b-42b9-8377-9245df2b15fc] running status set to false 
[TRACE] 2025-01-16 14:13:36.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_772c70b2-db5b-42b9-8377-9245df2b15fc_1737007986440 
[TRACE] 2025-01-16 14:13:36.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_772c70b2-db5b-42b9-8377-9245df2b15fc_1737007986440 
[TRACE] 2025-01-16 14:13:36.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[772c70b2-db5b-42b9-8377-9245df2b15fc] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[772c70b2-db5b-42b9-8377-9245df2b15fc] monitor closed 
[TRACE] 2025-01-16 14:13:36.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_pg_5432_1736997187904_1988] - Node qa_pg_5432_1736997187904_1988[772c70b2-db5b-42b9-8377-9245df2b15fc] close complete, cost 15 ms 
[TRACE] 2025-01-16 14:13:36.363 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[1e9a5f51-dacc-44ee-b97f-4f495c63df0f] running status set to false 
[INFO ] 2025-01-16 14:13:36.365 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-f904a084-aec4-4c28-86ac-a5e506a9e0aa 
[INFO ] 2025-01-16 14:13:36.365 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_mongodb_repl_42240_1736997187904_1988-f904a084-aec4-4c28-86ac-a5e506a9e0aa 
[INFO ] 2025-01-16 14:13:36.365 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-1e9a5f51-dacc-44ee-b97f-4f495c63df0f-67887ced5703b91f391e6914] schema data cleaned 
[INFO ] 2025-01-16 14:13:36.367 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-qa_sqlserver_1443_1736997187904_1988-cbc0c850-7fc4-4d50-bbcf-dac8ab2d0b35 
[INFO ] 2025-01-16 14:13:36.368 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - PDK connector node released: ScriptExecutor-qa_sqlserver_1443_1736997187904_1988-cbc0c850-7fc4-4d50-bbcf-dac8ab2d0b35 
[INFO ] 2025-01-16 14:13:36.368 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS][src=user_script]  - [ScriptExecutorsManager-6788a366903e3f5bb47eba74-1e9a5f51-dacc-44ee-b97f-4f495c63df0f-67887d135703b91f391e6928] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.368 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[1e9a5f51-dacc-44ee-b97f-4f495c63df0f] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.368 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[1e9a5f51-dacc-44ee-b97f-4f495c63df0f] monitor closed 
[TRACE] 2025-01-16 14:13:36.368 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][JS] - Node JS[1e9a5f51-dacc-44ee-b97f-4f495c63df0f] close complete, cost 4 ms 
[TRACE] 2025-01-16 14:13:36.368 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[746fddd7-37a2-4b05-aa40-45acd862c43c] running status set to false 
[TRACE] 2025-01-16 14:13:36.410 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[746fddd7-37a2-4b05-aa40-45acd862c43c] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.410 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[746fddd7-37a2-4b05-aa40-45acd862c43c] monitor closed 
[TRACE] 2025-01-16 14:13:36.410 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[746fddd7-37a2-4b05-aa40-45acd862c43c] close complete, cost 41 ms 
[TRACE] 2025-01-16 14:13:36.410 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[baf7b50a-ac21-4fe9-a558-00cb2c03a893] running status set to false 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Table 'qa_auto_test_one_many_89_4_1737007897364_2936' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_baf7b50a-ac21-4fe9-a558-00cb2c03a893_1737007986581 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_baf7b50a-ac21-4fe9-a558-00cb2c03a893_1737007986581 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[baf7b50a-ac21-4fe9-a558-00cb2c03a893] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[baf7b50a-ac21-4fe9-a558-00cb2c03a893] monitor closed 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Node qa_sqlserver_1443_1736997187904_1988[baf7b50a-ac21-4fe9-a558-00cb2c03a893] close complete, cost 12 ms 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[c7dbe642-0c8e-4a2c-acb5-55f5f915bf16] running status set to false 
[TRACE] 2025-01-16 14:13:36.429 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a38be84e1d5f498d1cac, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@164c4ccb: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:16.033Z","timestamp":1737007996033},"_id":"6788a37d2aee356a5a3a8847","id":"0aa0722f-de2c-4de0-ad94-b2ab84f6e9f1","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007997000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737008011372,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007997000, sourceSerialNo=null} 
[ERROR] 2025-01-16 14:13:36.469 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - target write record(s) failed <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:782)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:664)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:173)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:124)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:855)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	... 6 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:876)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$30(HazelcastTargetPdkDataNode.java:575)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	... 8 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:964)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$64(HazelcastTargetPdkDataNode.java:901)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 15 more
Caused by: java.lang.NullPointerException
	at com.zaxxer.hikari.pool.PoolBase.resetConnectionState(PoolBase.java:219)
	at com.zaxxer.hikari.pool.PoolEntry.resetConnectionState(PoolEntry.java:107)
	at com.zaxxer.hikari.pool.ProxyConnection.close(ProxyConnection.java:263)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:139)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:463)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$62(HazelcastTargetPdkDataNode.java:950)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$63(HazelcastTargetPdkDataNode.java:946)
	... 22 more

[TRACE] 2025-01-16 14:13:36.469 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[c7dbe642-0c8e-4a2c-acb5-55f5f915bf16] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.469 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[c7dbe642-0c8e-4a2c-acb5-55f5f915bf16] monitor closed 
[TRACE] 2025-01-16 14:13:36.469 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[c7dbe642-0c8e-4a2c-acb5-55f5f915bf16] close complete, cost 46 ms 
[TRACE] 2025-01-16 14:13:36.469 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[962c93f2-c64c-4017-bff4-b9c8496f6b97] running status set to false 
[TRACE] 2025-01-16 14:13:36.605 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_962c93f2-c64c-4017-bff4-b9c8496f6b97_1737007986520 
[TRACE] 2025-01-16 14:13:36.606 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_962c93f2-c64c-4017-bff4-b9c8496f6b97_1737007986520 
[TRACE] 2025-01-16 14:13:36.606 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[962c93f2-c64c-4017-bff4-b9c8496f6b97] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.606 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[962c93f2-c64c-4017-bff4-b9c8496f6b97] monitor closed 
[TRACE] 2025-01-16 14:13:36.606 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_oracle_11g_single_1736997187904_1988] - Node qa_oracle_11g_single_1736997187904_1988[962c93f2-c64c-4017-bff4-b9c8496f6b97] close complete, cost 136 ms 
[TRACE] 2025-01-16 14:13:36.606 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[592a776c-0d75-48c3-9a99-3891f1faebe4] running status set to false 
[TRACE] 2025-01-16 14:13:36.638 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[592a776c-0d75-48c3-9a99-3891f1faebe4] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.638 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[592a776c-0d75-48c3-9a99-3891f1faebe4] monitor closed 
[TRACE] 2025-01-16 14:13:36.638 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][Field Rename] - Node Field Rename[592a776c-0d75-48c3-9a99-3891f1faebe4] close complete, cost 31 ms 
[TRACE] 2025-01-16 14:13:36.638 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8d70946e-5f78-4b1a-8f7b-eeea9184aac9] running status set to false 
[TRACE] 2025-01-16 14:13:36.655 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - PDK connector node stopped: HazelcastTargetPdkDataNode_8d70946e-5f78-4b1a-8f7b-eeea9184aac9_1737007986377 
[TRACE] 2025-01-16 14:13:36.655 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - PDK connector node released: HazelcastTargetPdkDataNode_8d70946e-5f78-4b1a-8f7b-eeea9184aac9_1737007986377 
[TRACE] 2025-01-16 14:13:36.655 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8d70946e-5f78-4b1a-8f7b-eeea9184aac9] schema data cleaned 
[TRACE] 2025-01-16 14:13:36.656 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8d70946e-5f78-4b1a-8f7b-eeea9184aac9] monitor closed 
[TRACE] 2025-01-16 14:13:36.656 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_36230_1736997187904_1988] - Node qa_mongodb_repl_36230_1736997187904_1988[8d70946e-5f78-4b1a-8f7b-eeea9184aac9] close complete, cost 17 ms 
[TRACE] 2025-01-16 14:13:36.657 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a38be84e1d5f498ced9a, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3a0cc9d0: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:16.034Z","timestamp":1737007996034},"_id":"6788a37d2aee356a5a3a7b6f","id":"501b6587-f69a-4fb9-959b-21a6e6adb3c0","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007997000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737008011215,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007997000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.658 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a38be84e1d5f498d0bbb, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@4fe3f027: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:16.034Z","timestamp":1737007996034},"_id":"6788a37d2aee356a5a3a82b2","id":"be5960e4-81ca-4aff-baa0-91e10d852eda","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007997000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737008011308,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007997000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.658 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b9321, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7b4ae8d8: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:08.005Z","timestamp":1737007988005},"_id":"6788a3772aee356a5a39bbf4","id":"fb7438f8-5b02-40e0-892b-39ec45e6b7cd","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994095,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.658 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b312d, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@46bc6611: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:07.006Z","timestamp":1737007987006},"_id":"6788a3772aee356a5a39c78d","id":"1e36cfd6-a237-47f0-95c2-a605080a72aa","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994029,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.659 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a38be84e1d5f498d0827, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@40fdc5a1: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:15.030Z","timestamp":1737007995030},"_id":"6788a37d2aee356a5a3a8369","id":"ab9a1a56-38a1-41ac-8647-6cbf5841143f","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007997000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737008011278,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007997000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.659 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_sqlserver_1443_1736997187904_1988] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: qa_auto_test_one_many_89_4_1737007897364_2936
 - TapdataEvent{eventId=6788a37ae84e1d5f498b5cdf, syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3bfc561e: {"after":{"created":{"minutesOffset":0,"offsetDateTime":"2025-01-16T06:13:07.007Z","timestamp":1737007987007},"_id":"6788a3772aee356a5a39c9c6","id":"a0900731-4c94-4054-8af0-a72de5409abe","v2":"8JVzaGpR"},"containsIllegalDate":false,"referenceTime":1737007991000,"tableId":"qa_auto_test_one_many_89_4_1737007897364_2936","time":1737007994059,"type":300}, nodeIds=[e65f96ec-6b14-46f3-9b27-c2e4bf4cb8b4, 082dd897-714c-4b02-8c67-0cb3a807b6d4, e8f212df-65ea-4fd4-9d2e-c622ef5c3838, f45889f9-f4f8-4835-bcea-8dc45a4f38df, 0aaa86f7-9fde-4f9f-a438-0e5809dedf1f, 1e9a5f51-dacc-44ee-b97f-4f495c63df0f, 746fddd7-37a2-4b05-aa40-45acd862c43c], sourceTime=1737007991000, sourceSerialNo=null} 
[TRACE] 2025-01-16 14:13:36.864 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897][qa_mongodb_repl_42240_1736997187904_1988] - Incremental sync completed 
[INFO ] 2025-01-16 14:13:39.878 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Task [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-01-16 14:13:39.881 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-01-16 14:13:39.881 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@44a4ad35 
[TRACE] 2025-01-16 14:13:40.010 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Stop task milestones: 6788a366903e3f5bb47eba74(t_89.4-MongoDB->Many's_1736997187904_1988-1737007897)  
[TRACE] 2025-01-16 14:13:40.011 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Stopped task aspect(s) 
[TRACE] 2025-01-16 14:13:40.012 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Snapshot order controller have been removed 
[INFO ] 2025-01-16 14:13:40.012 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Task stopped. 
[TRACE] 2025-01-16 14:13:40.038 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Remove memory task client succeed, task: t_89.4-MongoDB->Many's_1736997187904_1988-1737007897[6788a366903e3f5bb47eba74] 
[TRACE] 2025-01-16 14:13:40.041 - [t_89.4-MongoDB->Many's_1736997187904_1988-1737007897] - Destroy memory task client cache succeed, task: t_89.4-MongoDB->Many's_1736997187904_1988-1737007897[6788a366903e3f5bb47eba74] 
