[INFO ] 2024-12-09 12:49:27.969 - [T11FieldRenameTask] - Start task milestones: 675676c6733dd10e02888b17(T11FieldRenameTask) 
[INFO ] 2024-12-09 12:49:28.175 - [T11FieldRenameTask] - Task initialization... 
[INFO ] 2024-12-09 12:49:28.199 - [T11FieldRenameTask] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-09 12:49:28.393 - [T11FieldRenameTask] - The engine receives T11FieldRenameTask task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-09 12:49:28.394 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[3197bfc3-a812-43f6-b3c6-7e26e73b034d] start preload schema,table counts: 1 
[INFO ] 2024-12-09 12:49:28.394 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[3197bfc3-a812-43f6-b3c6-7e26e73b034d] preload schema finished, cost 1 ms 
[INFO ] 2024-12-09 12:49:28.408 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[7dfa54a1-cf15-407b-bfd3-3326d31550ac] start preload schema,table counts: 1 
[INFO ] 2024-12-09 12:49:28.408 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[7dfa54a1-cf15-407b-bfd3-3326d31550ac] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 12:49:28.428 - [T11FieldRenameTask][Field Rename] - Node Field Rename[80aacc70-bc9a-4a35-b807-f85ba71eef26] start preload schema,table counts: 1 
[INFO ] 2024-12-09 12:49:28.428 - [T11FieldRenameTask][Field Rename] - Node Field Rename[80aacc70-bc9a-4a35-b807-f85ba71eef26] preload schema finished, cost 1 ms 
[INFO ] 2024-12-09 12:49:28.429 - [T11FieldRenameTask][Field Rename] - Node field_rename_processor(Field Rename: 80aacc70-bc9a-4a35-b807-f85ba71eef26) enable batch process 
[INFO ] 2024-12-09 12:49:28.912 - [T11FieldRenameTask][T11FieldRename] - Source node "T11FieldRename" read batch size: 100 
[INFO ] 2024-12-09 12:49:28.913 - [T11FieldRenameTask][T11FieldRename] - Source node "T11FieldRename" event queue capacity: 200 
[INFO ] 2024-12-09 12:49:28.913 - [T11FieldRenameTask][T11FieldRename] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-09 12:49:28.913 - [T11FieldRenameTask][T11FieldRename] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-09 12:49:28.994 - [T11FieldRenameTask][T11FieldRename] - Initial sync started 
[INFO ] 2024-12-09 12:49:29.000 - [T11FieldRenameTask][T11FieldRename] - Starting batch read, table name: T11FieldRename 
[INFO ] 2024-12-09 12:49:29.001 - [T11FieldRenameTask][T11FieldRename] - Table T11FieldRename is going to be initial synced 
[INFO ] 2024-12-09 12:49:29.017 - [T11FieldRenameTask][T11FieldRename] - Query snapshot row size completed: T11FieldRename(3197bfc3-a812-43f6-b3c6-7e26e73b034d) 
[INFO ] 2024-12-09 12:49:29.024 - [T11FieldRenameTask][T11FieldRename] - Table [T11FieldRename] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-09 12:49:29.024 - [T11FieldRenameTask][T11FieldRename] - Initial sync completed 
[INFO ] 2024-12-09 12:49:29.260 - [T11FieldRenameTask][T11FieldRename] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-09 12:49:29.345 - [T11FieldRenameTask][T11FieldRename] - Table "autoTest.T11FieldRename" exists, skip auto create table 
[INFO ] 2024-12-09 12:49:29.346 - [T11FieldRenameTask][T11FieldRename] - The table T11FieldRename has already exist. 
[INFO ] 2024-12-09 12:49:30.501 - [T11FieldRenameTask][T11FieldRename] - Table 'T11FieldRename' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-12-09 12:49:30.556 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[3197bfc3-a812-43f6-b3c6-7e26e73b034d] running status set to false 
[INFO ] 2024-12-09 12:49:30.556 - [T11FieldRenameTask][Field Rename] - Node Field Rename[80aacc70-bc9a-4a35-b807-f85ba71eef26] running status set to false 
[INFO ] 2024-12-09 12:49:30.561 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[7dfa54a1-cf15-407b-bfd3-3326d31550ac] running status set to false 
[INFO ] 2024-12-09 12:49:30.605 - [T11FieldRenameTask][T11FieldRename] - PDK connector node stopped: HazelcastSourcePdkDataNode_3197bfc3-a812-43f6-b3c6-7e26e73b034d_1733719768700 
[INFO ] 2024-12-09 12:49:30.605 - [T11FieldRenameTask][T11FieldRename] - PDK connector node released: HazelcastSourcePdkDataNode_3197bfc3-a812-43f6-b3c6-7e26e73b034d_1733719768700 
[INFO ] 2024-12-09 12:49:30.626 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[3197bfc3-a812-43f6-b3c6-7e26e73b034d] schema data cleaned 
[INFO ] 2024-12-09 12:49:30.635 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[3197bfc3-a812-43f6-b3c6-7e26e73b034d] monitor closed 
[INFO ] 2024-12-09 12:49:30.637 - [T11FieldRenameTask][T11FieldRename] - PDK connector node stopped: HazelcastTargetPdkDataNode_7dfa54a1-cf15-407b-bfd3-3326d31550ac_1733719768740 
[INFO ] 2024-12-09 12:49:30.638 - [T11FieldRenameTask][T11FieldRename] - PDK connector node released: HazelcastTargetPdkDataNode_7dfa54a1-cf15-407b-bfd3-3326d31550ac_1733719768740 
[INFO ] 2024-12-09 12:49:30.638 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[7dfa54a1-cf15-407b-bfd3-3326d31550ac] schema data cleaned 
[INFO ] 2024-12-09 12:49:30.638 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[7dfa54a1-cf15-407b-bfd3-3326d31550ac] monitor closed 
[INFO ] 2024-12-09 12:49:30.658 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[3197bfc3-a812-43f6-b3c6-7e26e73b034d] close complete, cost 99 ms 
[INFO ] 2024-12-09 12:49:30.658 - [T11FieldRenameTask][T11FieldRename] - Node T11FieldRename[7dfa54a1-cf15-407b-bfd3-3326d31550ac] close complete, cost 96 ms 
[INFO ] 2024-12-09 12:49:30.710 - [T11FieldRenameTask][Field Rename] - Node Field Rename[80aacc70-bc9a-4a35-b807-f85ba71eef26] schema data cleaned 
[INFO ] 2024-12-09 12:49:30.711 - [T11FieldRenameTask][Field Rename] - Node Field Rename[80aacc70-bc9a-4a35-b807-f85ba71eef26] monitor closed 
[INFO ] 2024-12-09 12:49:30.915 - [T11FieldRenameTask][Field Rename] - Node Field Rename[80aacc70-bc9a-4a35-b807-f85ba71eef26] close complete, cost 155 ms 
[INFO ] 2024-12-09 12:49:37.705 - [T11FieldRenameTask] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-09 12:49:37.716 - [T11FieldRenameTask] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57b4a7cb 
[INFO ] 2024-12-09 12:49:37.717 - [T11FieldRenameTask] - Stop task milestones: 675676c6733dd10e02888b17(T11FieldRenameTask)  
[INFO ] 2024-12-09 12:49:37.838 - [T11FieldRenameTask] - Stopped task aspect(s) 
[INFO ] 2024-12-09 12:49:37.838 - [T11FieldRenameTask] - Snapshot order controller have been removed 
[INFO ] 2024-12-09 12:49:37.873 - [T11FieldRenameTask] - Remove memory task client succeed, task: T11FieldRenameTask[675676c6733dd10e02888b17] 
[INFO ] 2024-12-09 12:49:37.874 - [T11FieldRenameTask] - Destroy memory task client cache succeed, task: T11FieldRenameTask[675676c6733dd10e02888b17] 
