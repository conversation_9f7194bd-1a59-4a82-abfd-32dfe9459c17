[TRACE] 2025-05-08 12:20:02.281 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task initialization... 
[TRACE] 2025-05-08 12:20:02.306 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Start task milestones: 681c30e7b653e714a619b19c(t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991) 
[INFO ] 2025-05-08 12:20:02.408 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Loading table structure completed 
[TRACE] 2025-05-08 12:20:02.412 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-05-08 12:20:02.472 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - The engine receives t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 12:20:02.472 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task started 
[TRACE] 2025-05-08 12:20:02.493 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] start preload schema,table counts: 1 
[TRACE] 2025-05-08 12:20:02.496 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] start preload schema,table counts: 1 
[TRACE] 2025-05-08 12:20:02.496 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 12:20:02.496 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 12:20:02.496 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node table_rename_processor(TableRename: 9817ae37-cd90-4d85-b3f0-8ac7ee185358) enable batch process 
[TRACE] 2025-05-08 12:20:02.502 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] start preload schema,table counts: 1 
[TRACE] 2025-05-08 12:20:02.504 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] preload schema finished, cost 0 ms 
[INFO ] 2025-05-08 12:20:03.143 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Sink connector(qa_mysql_repl_33306_t_1742442514217_8588) initialization completed 
[TRACE] 2025-05-08 12:20:03.143 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node(qa_mysql_repl_33306_t_1742442514217_8588) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 12:20:03.143 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-08 12:20:03.154 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Apply table structure to target database 
[WARN ] 2025-05-08 12:20:03.157 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Table F25111_T_25_11_1_DDL not exists, skip drop 
[INFO ] 2025-05-08 12:20:03.158 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Source connector(qa_oceanbase_oracle_2883_1742442514217_8588) initialization completed 
[TRACE] 2025-05-08 12:20:03.158 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Source node "qa_oceanbase_oracle_2883_1742442514217_8588" read batch size: 500 
[TRACE] 2025-05-08 12:20:03.158 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Source node "qa_oceanbase_oracle_2883_1742442514217_8588" event queue capacity: 1000 
[TRACE] 2025-05-08 12:20:03.158 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 12:20:03.264 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Use existing stream offset: 1746678003 
[INFO ] 2025-05-08 12:20:03.264 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting batch read from 1 tables 
[TRACE] 2025-05-08 12:20:03.266 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Initial sync started 
[INFO ] 2025-05-08 12:20:03.266 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting batch read from table: T_25_11_1_DDL 
[TRACE] 2025-05-08 12:20:03.266 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Table T_25_11_1_DDL is going to be initial synced 
[TRACE] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Query snapshot row size completed: qa_oceanbase_oracle_2883_1742442514217_8588(86de1a09-a6ef-47ce-9b16-a16e8e51aa05) 
[INFO ] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Table T_25_11_1_DDL has been completed batch read 
[TRACE] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Initial sync completed 
[INFO ] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Batch read completed. 
[TRACE] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Incremental sync starting... 
[TRACE] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Initial sync completed 
[TRACE] 2025-05-08 12:20:03.299 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting stream read, table list: [T_25_11_1_DDL, _tapdata_heartbeat_table], offset: 1746678003 
[INFO ] 2025-05-08 12:20:03.300 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting incremental sync using database log parser 
[TRACE] 2025-05-08 12:20:03.483 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Connector Oceanbase(Oracle) incremental start succeed, tables: [T_25_11_1_DDL, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-05-08 12:20:03.483 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Incremental sync completed 
[TRACE] 2025-05-08 12:20:03.494 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception 
[ERROR] 2025-05-08 12:20:03.494 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - io.grpc.StatusRuntimeException: UNAVAILABLE: io exception <-- Error Message -->
io.grpc.StatusRuntimeException: UNAVAILABLE: io exception

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Connection reset
	java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	io.grpc.netty.shaded.io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	io.grpc.netty.shaded.io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	...

<-- Full Stack Trace -->
io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.oceanbase.cdc.OceanbaseOracleReader.start(OceanbaseOracleReader.java:144)
	at io.tapdata.oceanbase.connector.OceanbaseOracleConnector.streamRead(OceanbaseOracleConnector.java:275)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.grpc.netty.shaded.io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.grpc.netty.shaded.io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 more

[TRACE] 2025-05-08 12:20:03.495 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Job suspend in error handle 
[TRACE] 2025-05-08 12:20:03.571 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 12:20:03.571 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] running status set to false 
[WARN ] 2025-05-08 12:20:03.576 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_86de1a09-a6ef-47ce-9b16-a16e8e51aa05_1746678002996 
[TRACE] 2025-05-08 12:20:03.576 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - PDK connector node released: HazelcastSourcePdkDataNode_86de1a09-a6ef-47ce-9b16-a16e8e51aa05_1746678002996 
[TRACE] 2025-05-08 12:20:03.576 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] schema data cleaned 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] monitor closed 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] close complete, cost 6 ms 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] running status set to false 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] schema data cleaned 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] monitor closed 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] close complete, cost 0 ms 
[TRACE] 2025-05-08 12:20:03.577 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] running status set to false 
[TRACE] 2025-05-08 12:20:03.579 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - PDK connector node stopped: HazelcastTargetPdkDataNode_5379f64a-f68f-4962-b20c-934bda3b01f9_1746678002962 
[TRACE] 2025-05-08 12:20:03.579 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - PDK connector node released: HazelcastTargetPdkDataNode_5379f64a-f68f-4962-b20c-934bda3b01f9_1746678002962 
[TRACE] 2025-05-08 12:20:03.579 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] schema data cleaned 
[TRACE] 2025-05-08 12:20:03.579 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] monitor closed 
[TRACE] 2025-05-08 12:20:03.579 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] close complete, cost 1 ms 
[INFO ] 2025-05-08 12:20:08.332 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 12:20:08.332 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 12:20:08.332 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@da3aae8 
[TRACE] 2025-05-08 12:20:08.332 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Stop task milestones: 681c30e7b653e714a619b19c(t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991)  
[TRACE] 2025-05-08 12:20:08.449 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Stopped task aspect(s) 
[TRACE] 2025-05-08 12:20:08.449 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 12:20:08.449 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task stopped. 
[INFO ] 2025-05-08 12:20:13.459 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 12:20:13.459 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 12:20:13.459 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@da3aae8 
[TRACE] 2025-05-08 12:20:13.459 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Stopped task aspect(s) 
[INFO ] 2025-05-08 12:20:13.489 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task stopped. 
[TRACE] 2025-05-08 12:20:13.489 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Remove memory task client succeed, task: t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991[681c30e7b653e714a619b19c] 
[TRACE] 2025-05-08 12:20:13.490 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Destroy memory task client cache succeed, task: t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991[681c30e7b653e714a619b19c] 
[TRACE] 2025-05-08 14:04:37.364 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task initialization... 
[TRACE] 2025-05-08 14:04:37.445 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Start task milestones: 681c30e7b653e714a619b19c(t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991) 
[INFO ] 2025-05-08 14:04:37.445 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Loading table structure completed 
[TRACE] 2025-05-08 14:04:37.519 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-08 14:04:37.519 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - The engine receives t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task started 
[TRACE] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] start preload schema,table counts: 1 
[TRACE] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] start preload schema,table counts: 1 
[TRACE] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] start preload schema,table counts: 1 
[TRACE] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 14:04:37.568 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] preload schema finished, cost 0 ms 
[TRACE] 2025-05-08 14:04:37.569 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node table_rename_processor(TableRename: 9817ae37-cd90-4d85-b3f0-8ac7ee185358) enable batch process 
[INFO ] 2025-05-08 14:04:37.761 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Source connector(qa_oceanbase_oracle_2883_1742442514217_8588) initialization completed 
[TRACE] 2025-05-08 14:04:37.761 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Source node "qa_oceanbase_oracle_2883_1742442514217_8588" read batch size: 500 
[TRACE] 2025-05-08 14:04:37.761 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Source node "qa_oceanbase_oracle_2883_1742442514217_8588" event queue capacity: 1000 
[TRACE] 2025-05-08 14:04:37.761 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Sync progress not exists, will run task as first time 
[TRACE] 2025-05-08 14:04:37.761 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-08 14:04:37.771 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Sink connector(qa_mysql_repl_33306_t_1742442514217_8588) initialization completed 
[TRACE] 2025-05-08 14:04:37.771 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node(qa_mysql_repl_33306_t_1742442514217_8588) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-08 14:04:37.771 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Write batch size: 1000, max wait ms per batch: 1000 
[TRACE] 2025-05-08 14:04:37.781 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Sync progress not exists, will run task as first time 
[INFO ] 2025-05-08 14:04:37.781 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Apply table structure to target database 
[TRACE] 2025-05-08 14:04:37.781 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Sync progress not exists, will run task as first time 
[INFO ] 2025-05-08 14:04:37.892 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Use existing stream offset: 1746684277 
[INFO ] 2025-05-08 14:04:37.930 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting batch read from 1 tables 
[TRACE] 2025-05-08 14:04:37.931 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Initial sync started 
[INFO ] 2025-05-08 14:04:37.931 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting batch read from table: T_25_11_1_DDL 
[TRACE] 2025-05-08 14:04:37.931 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Table T_25_11_1_DDL is going to be initial synced 
[TRACE] 2025-05-08 14:04:37.980 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Query snapshot row size completed: qa_oceanbase_oracle_2883_1742442514217_8588(86de1a09-a6ef-47ce-9b16-a16e8e51aa05) 
[INFO ] 2025-05-08 14:04:37.980 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Table T_25_11_1_DDL has been completed batch read 
[TRACE] 2025-05-08 14:04:37.980 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Initial sync completed 
[INFO ] 2025-05-08 14:04:37.980 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Batch read completed. 
[TRACE] 2025-05-08 14:04:37.981 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Incremental sync starting... 
[TRACE] 2025-05-08 14:04:37.981 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Initial sync completed 
[TRACE] 2025-05-08 14:04:37.981 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting stream read, table list: [T_25_11_1_DDL, _tapdata_heartbeat_table], offset: 1746684277 
[INFO ] 2025-05-08 14:04:37.981 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Starting incremental sync using database log parser 
[TRACE] 2025-05-08 14:04:52.123 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Connector Oceanbase(Oracle) incremental start succeed, tables: [T_25_11_1_DDL, _tapdata_heartbeat_table], data change syncing 
[TRACE] 2025-05-08 14:05:06.045 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Incremental sync completed 
[TRACE] 2025-05-08 14:05:06.045 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception 
[ERROR] 2025-05-08 14:05:06.059 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - io.grpc.StatusRuntimeException: UNAVAILABLE: io exception <-- Error Message -->
io.grpc.StatusRuntimeException: UNAVAILABLE: io exception

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Connection reset
	java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	io.grpc.netty.shaded.io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	io.grpc.netty.shaded.io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	...

<-- Full Stack Trace -->
io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: io.grpc.StatusRuntimeException: UNAVAILABLE: io exception
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.oceanbase.cdc.OceanbaseOracleReader.start(OceanbaseOracleReader.java:144)
	at io.tapdata.oceanbase.connector.OceanbaseOracleConnector.streamRead(OceanbaseOracleConnector.java:275)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.grpc.netty.shaded.io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.grpc.netty.shaded.io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.grpc.netty.shaded.io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 more

[TRACE] 2025-05-08 14:05:06.059 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Job suspend in error handle 
[TRACE] 2025-05-08 14:05:06.143 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-05-08 14:05:06.143 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] running status set to false 
[WARN ] 2025-05-08 14:05:06.145 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Stop PDK connector node failed: Unknown PDK exception occur, io.grpc.StatusRuntimeException: UNAVAILABLE: io exception | Associate id: HazelcastSourcePdkDataNode_86de1a09-a6ef-47ce-9b16-a16e8e51aa05_1746684277595 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - PDK connector node released: HazelcastSourcePdkDataNode_86de1a09-a6ef-47ce-9b16-a16e8e51aa05_1746684277595 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] schema data cleaned 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] monitor closed 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_oceanbase_oracle_2883_1742442514217_8588] - Node qa_oceanbase_oracle_2883_1742442514217_8588[86de1a09-a6ef-47ce-9b16-a16e8e51aa05] close complete, cost 2 ms 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] running status set to false 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] schema data cleaned 
[TRACE] 2025-05-08 14:05:06.146 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] monitor closed 
[TRACE] 2025-05-08 14:05:06.147 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][TableRename] - Node TableRename[9817ae37-cd90-4d85-b3f0-8ac7ee185358] close complete, cost 0 ms 
[TRACE] 2025-05-08 14:05:06.148 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] running status set to false 
[TRACE] 2025-05-08 14:05:06.152 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - PDK connector node stopped: HazelcastTargetPdkDataNode_5379f64a-f68f-4962-b20c-934bda3b01f9_1746684277586 
[TRACE] 2025-05-08 14:05:06.152 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - PDK connector node released: HazelcastTargetPdkDataNode_5379f64a-f68f-4962-b20c-934bda3b01f9_1746684277586 
[TRACE] 2025-05-08 14:05:06.152 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] schema data cleaned 
[TRACE] 2025-05-08 14:05:06.152 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] monitor closed 
[TRACE] 2025-05-08 14:05:06.152 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991][qa_mysql_repl_33306_t_1742442514217_8588] - Node qa_mysql_repl_33306_t_1742442514217_8588[5379f64a-f68f-4962-b20c-934bda3b01f9] close complete, cost 1 ms 
[INFO ] 2025-05-08 14:05:10.094 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 14:05:10.096 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 14:05:10.096 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4f8e5598 
[TRACE] 2025-05-08 14:05:10.219 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Stop task milestones: 681c30e7b653e714a619b19c(t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991)  
[TRACE] 2025-05-08 14:05:10.219 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Stopped task aspect(s) 
[TRACE] 2025-05-08 14:05:10.219 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Snapshot order controller have been removed 
[INFO ] 2025-05-08 14:05:10.219 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task stopped. 
[INFO ] 2025-05-08 14:05:15.227 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-05-08 14:05:15.227 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-08 14:05:15.227 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4f8e5598 
[TRACE] 2025-05-08 14:05:15.227 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Stopped task aspect(s) 
[INFO ] 2025-05-08 14:05:15.227 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Task stopped. 
[TRACE] 2025-05-08 14:05:15.246 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Remove memory task client succeed, task: t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991[681c30e7b653e714a619b19c] 
[TRACE] 2025-05-08 14:05:15.246 - [t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991] - Destroy memory task client cache succeed, task: t_25.11.1-oceanbase(oracle)_to_mysql_ddl_new_1742442514217_8588-1746677991[681c30e7b653e714a619b19c] 
