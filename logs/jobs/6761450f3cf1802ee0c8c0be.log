[INFO ] 2024-12-18 12:06:56.497 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 12:06:56.499 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 12:06:56.874 - [主从合并全量] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 12:06:57.476 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:06:57.670 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:06:57.670 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:06:57.671 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:06:57.671 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 12:06:57.672 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 12:06:57.672 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:06:57.673 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:06:57.673 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:06:57.675 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 12:06:57.676 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 12:06:57.697 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 12:06:57.698 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 12:06:58.308 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 12:06:58.329 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 12:06:58.330 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 12:06:58.330 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 12:06:58.332 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 12:06:58.514 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 12:06:58.515 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 12:06:58.515 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 12:06:58.515 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 12:06:58.580 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 12:06:58.580 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 12:06:58.582 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 12:06:58.582 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 12:06:58.621 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 12:06:58.621 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:06:58.708 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 12:06:58.708 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7667b492: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 12:06:59.319 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7667b492: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 12:06:59.753 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 12:06:59.754 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 12:06:59.755 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 12:06:59.755 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 12:06:59.768 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[WARN ] 2024-12-18 12:06:59.768 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67624a62cf261d794ec72dae, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 12:06:59.800 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:06:59.801 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 12:07:00.005 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 12:07:00.809 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67624a63cf261d794ec72db1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 12:07:00.919 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 12:07:00.920 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 12:07:00.924 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 12:07:00.924 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 12:07:00.927 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 12:07:00.929 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 12:07:00.934 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734494818060 
[INFO ] 2024-12-18 12:07:00.934 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734494817988 
[INFO ] 2024-12-18 12:07:00.935 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734494818060 
[INFO ] 2024-12-18 12:07:00.935 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734494817988 
[INFO ] 2024-12-18 12:07:00.936 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734494818094 
[INFO ] 2024-12-18 12:07:00.936 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 12:07:00.936 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 12:07:00.936 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734494818094 
[INFO ] 2024-12-18 12:07:00.937 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 12:07:00.937 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 12:07:00.938 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 11 ms 
[INFO ] 2024-12-18 12:07:00.938 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 12:07:00.938 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 35 ms 
[INFO ] 2024-12-18 12:07:00.940 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 12:07:00.940 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 20 ms 
[INFO ] 2024-12-18 12:07:00.940 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 17 ms 
[INFO ] 2024-12-18 12:07:04.941 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 12:07:05.056 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@55384ba5 
[INFO ] 2024-12-18 12:07:05.056 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 12:07:05.087 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 12:07:05.087 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 12:07:05.113 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 12:07:05.115 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 12:10:11.269 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 12:10:11.270 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 12:10:11.472 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 12:10:11.915 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:10:11.915 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 12:10:11.915 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:10:11.916 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:10:11.916 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:10:11.916 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:10:11.917 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:10:11.917 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:10:11.917 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 12:10:11.917 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:10:11.934 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 12:10:11.934 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 12:10:11.934 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 12:10:12.540 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 12:10:12.648 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 12:10:12.648 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 12:10:12.648 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 12:10:12.649 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 12:10:12.711 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 12:10:12.711 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 12:10:12.785 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 12:10:12.785 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:10:12.785 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 12:10:12.785 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 12:10:12.854 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 12:10:12.854 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 12:10:12.854 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 12:10:12.854 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 12:10:12.938 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 12:10:12.939 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7de50d7e: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 12:10:13.547 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7de50d7e: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 12:10:13.982 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 12:10:13.982 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 12:10:13.985 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 12:10:13.985 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 12:10:14.001 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[WARN ] 2024-12-18 12:10:14.002 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67624b24cf261d794ec72dd7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 12:10:14.032 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:10:14.032 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 12:10:14.033 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 12:10:15.020 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67624b26cf261d794ec72dda, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 12:10:15.054 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 12:10:15.101 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734495012220 
[INFO ] 2024-12-18 12:10:15.101 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734495012220 
[INFO ] 2024-12-18 12:10:15.101 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 12:10:15.101 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 12:10:15.122 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 48 ms 
[INFO ] 2024-12-18 12:10:15.122 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 12:10:15.128 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 12:10:15.128 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 12:10:15.129 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 12:10:15.130 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 1 ms 
[INFO ] 2024-12-18 12:10:15.141 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 12:10:15.141 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734495012286 
[INFO ] 2024-12-18 12:10:15.141 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734495012286 
[INFO ] 2024-12-18 12:10:15.142 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 12:10:15.142 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 12:10:15.143 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 12 ms 
[INFO ] 2024-12-18 12:10:15.149 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734495012320 
[INFO ] 2024-12-18 12:10:15.149 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734495012320 
[INFO ] 2024-12-18 12:10:15.149 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 12:10:15.149 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 12:10:15.352 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 28 ms 
[INFO ] 2024-12-18 12:10:20.255 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 12:10:20.256 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45f07530 
[INFO ] 2024-12-18 12:10:20.381 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 12:10:20.381 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 12:10:20.381 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 12:10:20.407 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 12:10:20.407 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 12:18:51.387 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 12:18:51.388 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 12:18:51.592 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 12:18:52.000 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 12:18:52.035 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:18:52.035 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:18:52.035 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:18:52.036 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 12:18:52.036 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 12:18:52.036 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:18:52.036 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 12:18:52.036 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 12:18:52.037 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 12:18:52.037 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 12:18:52.069 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 12:18:52.069 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 12:18:52.675 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 12:18:52.801 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 12:18:52.801 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 12:18:52.801 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 12:18:52.801 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 12:18:52.857 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 12:18:52.857 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 12:18:52.885 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 12:18:52.885 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 12:18:52.892 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:18:52.892 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 12:18:52.955 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 12:18:52.955 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 12:18:52.955 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 12:18:52.955 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 12:18:53.156 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 12:18:53.514 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@1ddd0735: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 12:18:55.518 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@1ddd0735: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 12:19:01.559 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 12:19:01.559 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 12:19:01.560 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 12:19:01.600 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 12:19:01.600 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 12:19:01.601 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 12:19:01.601 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[INFO ] 2024-12-18 12:19:01.802 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[WARN ] 2024-12-18 12:19:04.587 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67624d2ccf261d794ec72ed8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 12:19:10.622 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67624d35cf261d794ec72edb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 12:19:10.681 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 12:19:10.681 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734495532481 
[INFO ] 2024-12-18 12:19:10.682 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734495532481 
[INFO ] 2024-12-18 12:19:10.682 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 12:19:10.682 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 12:19:10.693 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 24 ms 
[INFO ] 2024-12-18 12:19:10.693 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 12:19:10.695 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 12:19:10.696 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 12:19:10.696 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 12:19:10.696 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 0 ms 
[INFO ] 2024-12-18 12:19:10.707 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 12:19:10.707 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734495532426 
[INFO ] 2024-12-18 12:19:10.707 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734495532426 
[INFO ] 2024-12-18 12:19:10.707 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 12:19:10.708 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 12:19:10.708 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734495532364 
[INFO ] 2024-12-18 12:19:10.708 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 14 ms 
[INFO ] 2024-12-18 12:19:10.708 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734495532364 
[INFO ] 2024-12-18 12:19:10.708 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 12:19:10.708 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 12:19:10.913 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 10 ms 
[INFO ] 2024-12-18 12:19:11.247 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 12:19:11.247 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5223c9d4 
[INFO ] 2024-12-18 12:19:11.355 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 12:19:11.379 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 12:19:11.379 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 12:19:11.404 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 12:19:11.404 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:10:53.077 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:10:53.078 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:10:53.483 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:10:54.089 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:10:54.103 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:10:54.103 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:10:54.104 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:10:54.104 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:10:54.104 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 1 ms 
[INFO ] 2024-12-18 14:10:54.105 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:10:54.105 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:10:54.106 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:10:54.106 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:10:54.107 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:10:54.130 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:10:54.131 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:10:54.737 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:10:54.780 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:10:54.781 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:10:54.781 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:10:54.783 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:10:54.931 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:10:54.931 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:10:54.931 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:10:54.931 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:10:55.020 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:10:55.020 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:10:55.022 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:10:55.022 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:10:55.053 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:10:55.053 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:10:55.255 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:10:56.121 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@778151a4: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:10:57.742 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@778151a4: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:11:03.667 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 14:11:03.669 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 14:11:03.670 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:11:03.670 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 14:11:03.724 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 14:11:03.724 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:11:03.724 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:11:03.725 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 14:11:06.759 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 14:11:12.804 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 14:11:12.805 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:11:12.831 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502254405 
[INFO ] 2024-12-18 14:11:12.831 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:11:12.833 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502254405 
[INFO ] 2024-12-18 14:11:12.834 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:11:12.834 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:11:12.835 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:11:12.836 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:11:12.837 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:11:12.841 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:11:12.841 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 3 ms 
[INFO ] 2024-12-18 14:11:12.844 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 34 ms 
[INFO ] 2024-12-18 14:11:12.845 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502254501 
[INFO ] 2024-12-18 14:11:12.845 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502254501 
[INFO ] 2024-12-18 14:11:12.845 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:11:12.847 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:11:12.847 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 17 ms 
[INFO ] 2024-12-18 14:11:12.849 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502254489 
[INFO ] 2024-12-18 14:11:12.850 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502254489 
[INFO ] 2024-12-18 14:11:12.850 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:11:12.850 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:11:13.054 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 13 ms 
[INFO ] 2024-12-18 14:11:17.282 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:11:17.282 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@124b96bb 
[INFO ] 2024-12-18 14:11:17.400 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:11:17.400 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:11:17.421 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:11:17.425 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:11:17.425 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:11:45.761 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:11:45.762 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:11:45.965 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:11:46.469 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:11:46.469 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:11:46.469 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:11:46.470 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:11:46.470 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:11:46.470 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:11:46.470 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:11:46.471 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:11:46.471 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:11:46.472 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:11:46.472 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:11:46.494 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:11:46.494 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:11:46.985 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:11:47.157 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:11:47.158 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:11:47.158 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:11:47.158 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:11:47.214 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:11:47.214 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:11:47.214 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:11:47.259 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:11:47.259 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:11:47.259 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:11:47.307 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:11:47.307 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:11:47.307 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:11:47.307 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:11:47.487 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:11:47.488 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6ac4c891: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:11:50.127 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6ac4c891: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:11:56.017 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 14:11:56.018 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 14:11:56.019 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:11:56.020 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 14:11:56.071 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 14:11:56.071 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:11:56.075 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:11:56.075 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 14:11:59.034 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 14:12:05.056 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 14:12:05.110 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:12:05.111 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:12:05.112 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:12:05.112 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:12:05.113 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:12:05.113 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 1 ms 
[INFO ] 2024-12-18 14:12:05.130 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:12:05.130 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502306868 
[INFO ] 2024-12-18 14:12:05.130 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502306868 
[INFO ] 2024-12-18 14:12:05.130 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:12:05.131 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:12:05.131 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 15 ms 
[INFO ] 2024-12-18 14:12:05.144 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502306766 
[INFO ] 2024-12-18 14:12:05.144 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502306837 
[INFO ] 2024-12-18 14:12:05.144 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502306766 
[INFO ] 2024-12-18 14:12:05.145 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502306837 
[INFO ] 2024-12-18 14:12:05.145 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:12:05.145 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:12:05.145 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:12:05.145 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:12:05.146 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 50 ms 
[INFO ] 2024-12-18 14:12:05.146 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 35 ms 
[INFO ] 2024-12-18 14:12:07.453 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:12:07.559 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@290e55d6 
[INFO ] 2024-12-18 14:12:07.559 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:12:07.568 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:12:07.568 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:12:07.585 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:12:07.585 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:16:33.841 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:16:33.960 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:16:33.960 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:16:34.471 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:16:34.471 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:16:34.471 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:16:34.471 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:16:34.472 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:16:34.472 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:34.472 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:34.472 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:34.472 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:34.473 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:16:34.473 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:16:34.489 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:16:34.490 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:16:35.092 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:16:35.237 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:16:35.238 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:16:35.238 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:16:35.297 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:16:35.297 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:16:35.297 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:16:35.298 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:16:35.325 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:16:35.325 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:16:35.325 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:16:35.342 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:16:35.342 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:16:35.342 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:16:35.389 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:16:35.390 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:16:35.992 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@60f28464: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:16:38.012 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@60f28464: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:16:43.942 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 14:16:43.942 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 14:16:43.944 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:16:43.949 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 14:16:43.950 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 14:16:44.017 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:16:44.017 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:16:44.018 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 14:16:46.970 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 14:16:53.000 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 14:16:53.079 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:16:53.079 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502594889 
[INFO ] 2024-12-18 14:16:53.079 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502594889 
[INFO ] 2024-12-18 14:16:53.079 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:16:53.081 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:16:53.081 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 33 ms 
[INFO ] 2024-12-18 14:16:53.110 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:16:53.110 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:16:53.110 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:16:53.110 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:16:53.112 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 1 ms 
[INFO ] 2024-12-18 14:16:53.113 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:16:53.121 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502594795 
[INFO ] 2024-12-18 14:16:53.121 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502594795 
[INFO ] 2024-12-18 14:16:53.121 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:16:53.121 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:16:53.129 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 8 ms 
[INFO ] 2024-12-18 14:16:53.129 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502594859 
[INFO ] 2024-12-18 14:16:53.129 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502594859 
[INFO ] 2024-12-18 14:16:53.129 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:16:53.130 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:16:53.130 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 21 ms 
[INFO ] 2024-12-18 14:16:57.807 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:16:57.807 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@563c0797 
[INFO ] 2024-12-18 14:16:57.921 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:16:57.936 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:16:57.936 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:16:57.960 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:16:57.960 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:16:59.345 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:16:59.451 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:16:59.451 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:16:59.977 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:16:59.977 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:16:59.977 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:16:59.978 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:16:59.978 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:59.978 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:59.978 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:59.979 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:16:59.979 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:16:59.979 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:16:59.979 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:16:59.995 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:16:59.997 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:17:00.202 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:17:00.341 - [主从合并全量][Merge_Test] - Table: Merge_Test already exists Index: TapIndex indexFields: [TapIndexField name CLAIM_ID fieldAsc true indexType null; TapIndexField name POLICY_ID fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2024-12-18 14:17:00.341 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:17:00.341 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:17:00.341 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:17:00.342 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:17:00.506 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:17:00.506 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:17:00.506 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:17:00.506 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:17:00.558 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:17:00.559 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:17:00.559 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:17:00.559 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:17:00.612 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:17:00.613 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:17:00.613 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:17:01.236 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@4a5dbddc: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:17:03.263 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@4a5dbddc: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:17:03.745 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:17:03.745 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502620004 
[INFO ] 2024-12-18 14:17:03.745 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502620004 
[INFO ] 2024-12-18 14:17:03.745 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:17:03.745 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:17:03.746 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 22 ms 
[INFO ] 2024-12-18 14:17:03.747 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:17:03.748 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:17:03.748 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[INFO ] 2024-12-18 14:17:03.759 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:17:03.759 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502619997 
[INFO ] 2024-12-18 14:17:03.759 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502619997 
[INFO ] 2024-12-18 14:17:03.759 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:17:03.760 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:17:03.760 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 13 ms 
[INFO ] 2024-12-18 14:17:03.760 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:17:03.760 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:17:03.760 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:17:03.760 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:17:03.761 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:17:03.769 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502620033 
[INFO ] 2024-12-18 14:17:03.770 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502620033 
[INFO ] 2024-12-18 14:17:03.770 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:17:03.770 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:17:03.770 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 9 ms 
[INFO ] 2024-12-18 14:17:07.988 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:17:07.989 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c2c7107 
[INFO ] 2024-12-18 14:17:08.118 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:17:08.119 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:17:08.119 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:17:08.196 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:17:08.197 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:17:29.026 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:17:29.026 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:17:29.229 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:17:29.581 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:17:29.614 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:17:29.614 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:17:29.614 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:17:29.614 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:29.614 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:17:29.615 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:29.615 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:29.615 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:29.615 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:17:29.615 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:17:29.632 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:17:29.634 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:17:30.197 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:17:30.255 - [主从合并全量][Merge_Test] - Table: Merge_Test already exists Index: TapIndex indexFields: [TapIndexField name CLAIM_ID fieldAsc true indexType null; TapIndexField name POLICY_ID fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2024-12-18 14:17:30.325 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:17:30.325 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:17:30.325 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:17:30.380 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:17:30.380 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:17:30.380 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:17:30.381 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:17:30.416 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:17:30.441 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:17:30.441 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:17:30.441 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:17:30.441 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:17:30.457 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:17:30.457 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:17:30.658 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:17:30.701 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@31ec8953: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:17:33.205 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@31ec8953: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:17:36.657 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:17:36.657 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:17:36.657 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:17:36.657 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[INFO ] 2024-12-18 14:17:36.683 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502650003 
[INFO ] 2024-12-18 14:17:36.683 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502650003 
[INFO ] 2024-12-18 14:17:36.684 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:17:36.684 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:17:36.684 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 29 ms 
[INFO ] 2024-12-18 14:17:36.684 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:17:36.705 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502649916 
[INFO ] 2024-12-18 14:17:36.705 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502649916 
[INFO ] 2024-12-18 14:17:36.705 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:17:36.706 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:17:36.706 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 21 ms 
[INFO ] 2024-12-18 14:17:36.706 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:17:36.706 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:17:36.706 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:17:36.707 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:17:36.707 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:17:36.715 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502650036 
[INFO ] 2024-12-18 14:17:36.715 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502650036 
[INFO ] 2024-12-18 14:17:36.715 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:17:36.715 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:17:36.918 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 8 ms 
[INFO ] 2024-12-18 14:17:38.222 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:17:38.222 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6950abf5 
[INFO ] 2024-12-18 14:17:38.348 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:17:38.348 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:17:38.348 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:17:38.388 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:17:38.390 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:17:57.164 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:17:57.165 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:17:57.366 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:17:57.727 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:17:57.756 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:17:57.756 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:17:57.756 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:57.756 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:57.757 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:17:57.757 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:17:57.757 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:17:57.757 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:57.757 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:17:57.772 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:17:57.774 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:17:57.774 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:17:58.375 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:17:58.409 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:17:58.409 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:17:58.409 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:17:58.410 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:17:58.460 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:17:58.461 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:17:58.461 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:17:58.494 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:17:58.526 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:17:58.526 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:17:58.545 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:17:58.545 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:17:58.545 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:17:58.546 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:17:58.751 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:17:59.242 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@4e61f111: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:18:01.237 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@4e61f111: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:18:07.279 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 14:18:07.279 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 14:18:07.282 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:18:07.282 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 14:18:07.325 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 14:18:07.325 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:18:07.368 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:18:07.369 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 14:18:10.307 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 14:18:16.339 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 14:18:16.372 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:18:16.372 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:18:16.374 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502678157 
[INFO ] 2024-12-18 14:18:16.374 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502678157 
[INFO ] 2024-12-18 14:18:16.374 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:18:16.374 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:18:16.378 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 25 ms 
[INFO ] 2024-12-18 14:18:16.378 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:18:16.378 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:18:16.378 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:18:16.383 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:18:16.383 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502678124 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502678054 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502678124 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502678054 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:18:16.392 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 10 ms 
[INFO ] 2024-12-18 14:18:16.596 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 21 ms 
[INFO ] 2024-12-18 14:18:18.420 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:18:18.532 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@64bd05e4 
[INFO ] 2024-12-18 14:18:18.532 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:18:18.545 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:18:18.545 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:18:18.566 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:18:18.566 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:18:55.054 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:18:55.054 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:18:55.257 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:18:55.592 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:18:55.621 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:18:55.621 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:18:55.622 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:18:55.639 - [主从合并全量][主从合并] - 
Merge lookup relation{
  0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085)
    ->0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48)
} 
[INFO ] 2024-12-18 14:18:55.641 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:18:55.641 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:18:56.045 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:18:56.350 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:18:56.350 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:18:56.350 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:18:56.404 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:18:56.404 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:18:56.478 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:18:56.480 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:18:56.480 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:18:56.480 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:18:56.532 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:18:56.532 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:18:56.532 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:18:56.562 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:18:56.586 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:18:56.586 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:18:58.210 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7eaf11cf: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:18:59.013 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7eaf11cf: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:19:05.042 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 14:19:05.043 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 14:19:05.046 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:19:05.046 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 14:19:05.101 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 14:19:05.101 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:19:05.101 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:19:05.101 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 14:19:08.067 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 14:19:14.098 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 14:19:14.127 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:19:14.127 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502735992 
[INFO ] 2024-12-18 14:19:14.127 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734502735992 
[INFO ] 2024-12-18 14:19:14.127 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:19:14.127 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:19:14.127 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 22 ms 
[INFO ] 2024-12-18 14:19:14.196 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:19:14.196 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:19:14.196 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:19:14.196 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:19:14.199 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:19:14.199 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:19:14.206 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502735972 
[INFO ] 2024-12-18 14:19:14.206 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734502735972 
[INFO ] 2024-12-18 14:19:14.206 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:19:14.206 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:19:14.207 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 15 ms 
[INFO ] 2024-12-18 14:19:14.207 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502735903 
[INFO ] 2024-12-18 14:19:14.207 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734502735903 
[INFO ] 2024-12-18 14:19:14.207 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:19:14.208 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:19:14.208 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 8 ms 
[INFO ] 2024-12-18 14:19:18.606 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:19:18.716 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@97bf6af 
[INFO ] 2024-12-18 14:19:18.716 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:19:18.725 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:19:18.725 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:19:18.743 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:19:18.743 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:24:52.573 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-afc8924b-fc83-4d76-9b4f-821ccfeb7169 complete, cost 1459ms 
[INFO ] 2024-12-18 14:24:54.516 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-b93468f4-1cb8-4a35-bcb7-1650a0916a3f complete, cost 1172ms 
[INFO ] 2024-12-18 14:24:55.429 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-4544f368-d33a-49f9-81f3-0659719094f5 complete, cost 893ms 
[INFO ] 2024-12-18 14:25:07.337 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-c6d1acc2-dcfc-470a-a4a3-0b714da1cb63 complete, cost 965ms 
[INFO ] 2024-12-18 14:25:08.418 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-7773246b-2ece-4419-87c1-8cba7083340d complete, cost 1066ms 
[INFO ] 2024-12-18 14:25:11.350 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-4f6de442-f459-425d-8200-43854e76bfcf complete, cost 777ms 
[INFO ] 2024-12-18 14:25:11.701 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-230bab27-1b69-4ff7-8950-c73f6769aa8c complete, cost 1050ms 
[INFO ] 2024-12-18 14:25:12.273 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-a39a2415-4f35-4769-82c8-f85a2730fd59 complete, cost 910ms 
[INFO ] 2024-12-18 14:25:12.586 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-dd8b1f69-432e-4484-ab06-154d288e3801 complete, cost 869ms 
[INFO ] 2024-12-18 14:25:15.717 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-f7613a9a-887b-4b46-bfe0-669ebb3ec4e0 complete, cost 929ms 
[INFO ] 2024-12-18 14:25:16.568 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-1201a393-1f6d-40b2-a3d2-acaa0d387ad1 complete, cost 835ms 
[INFO ] 2024-12-18 14:25:17.753 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-1a9204b1-5dcd-413a-9073-515d23dda705 complete, cost 818ms 
[INFO ] 2024-12-18 14:25:18.635 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-ab6479f7-2779-454c-9d4d-94491a0e71c4 complete, cost 866ms 
[INFO ] 2024-12-18 14:25:19.232 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-c127351a-f771-48fa-b750-520b0e76836c complete, cost 953ms 
[INFO ] 2024-12-18 14:25:20.126 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-bd3eab77-d737-4d3e-b7a0-4784f596f0aa complete, cost 879ms 
[INFO ] 2024-12-18 14:25:20.921 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-c84b67bf-04c2-4f3b-b73d-32379e07ce6f complete, cost 902ms 
[INFO ] 2024-12-18 14:25:22.002 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-ff93adbd-819f-481a-a7fd-c6b2653599be complete, cost 1066ms 
[INFO ] 2024-12-18 14:25:22.935 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-8010b8c6-00b0-4359-b54d-4831aa6e1f37 complete, cost 860ms 
[INFO ] 2024-12-18 14:25:23.753 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-551e6c4a-859b-4844-80f8-4adc3d82fba0 complete, cost 806ms 
[INFO ] 2024-12-18 14:25:26.958 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-f73ce905-9e75-452d-aa1a-d553a686e1b2 complete, cost 776ms 
[INFO ] 2024-12-18 14:25:27.761 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-f1315872-7b19-4c82-ba13-b7715e39d895 complete, cost 790ms 
[INFO ] 2024-12-18 14:25:32.164 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-dc0edb99-7bdf-4144-9935-bf84d46501c2 complete, cost 826ms 
[INFO ] 2024-12-18 14:25:33.168 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-36430502-5f35-4da6-85a8-0459a81edaa8 complete, cost 991ms 
[INFO ] 2024-12-18 14:25:33.334 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-e28f58ea-cf70-4abc-9e13-036ee7036ec1 complete, cost 968ms 
[INFO ] 2024-12-18 14:25:33.946 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-fb647a0f-02d4-4f18-9edc-dd828a1d1161 complete, cost 847ms 
[INFO ] 2024-12-18 14:25:34.215 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-3c836deb-1f6f-4f38-bc78-5da279105b32 complete, cost 867ms 
[INFO ] 2024-12-18 14:25:34.796 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-eb49107d-7f44-4640-8b5d-13283a8e089c complete, cost 837ms 
[INFO ] 2024-12-18 14:25:36.456 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-9a02cce2-bfd2-411b-a4b9-6d0314c5427d complete, cost 877ms 
[INFO ] 2024-12-18 14:25:37.369 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-44f16db9-cbcf-425b-bd03-bc890f572d29 complete, cost 901ms 
[INFO ] 2024-12-18 14:25:41.296 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-dae9002e-6a18-4e13-b586-804ede9c2842 complete, cost 861ms 
[INFO ] 2024-12-18 14:25:42.192 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-f82f95f9-c22e-48f1-806c-1c9ae941d4c7 complete, cost 884ms 
[INFO ] 2024-12-18 14:25:42.568 - [主从合并全量] - Task initialization... 
[INFO ] 2024-12-18 14:25:42.641 - [主从合并全量] - Start task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量) 
[INFO ] 2024-12-18 14:25:43.711 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-4c88a1ce-9a73-4ec6-9153-b70828e0d8d1 complete, cost 1092ms 
[INFO ] 2024-12-18 14:25:44.566 - [主从合并全量] - load tapTable task 6761450f3cf1802ee0c8c0bd-521ba19e-cd9f-4cb7-9d26-e39a258b7cd1 complete, cost 841ms 
[INFO ] 2024-12-18 14:25:44.771 - [主从合并全量] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-12-18 14:25:45.065 - [主从合并全量] - The engine receives 主从合并全量 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-18 14:25:45.102 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] start preload schema,table counts: 3 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][增强JS] - Node 增强JS[9cd454c6-74d7-4d67-9180-7e2c1ecb83a5] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][增强JS] - Node 增强JS[9cd454c6-74d7-4d67-9180-7e2c1ecb83a5] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][增强JS] - Node js_processor(增强JS: 9cd454c6-74d7-4d67-9180-7e2c1ecb83a5) enable batch process 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][主从合并] - Node merge_table_processor(主从合并: db2634a7-50ef-4728-a3af-83da63949195) enable batch process 
[INFO ] 2024-12-18 14:25:45.105 - [主从合并全量][主从合并] - 
Merge lookup relation{
  增强JS(9cd454c6-74d7-4d67-9180-7e2c1ecb83a5)
    ->增强JS(13575fbe-7697-4f61-9437-9873ae37ae73)
} 
[INFO ] 2024-12-18 14:25:45.107 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:25:45.107 - [主从合并全量][增强JSss] - Node 增强JSss[13575fbe-7697-4f61-9437-9873ae37ae73] start preload schema,table counts: 1 
[INFO ] 2024-12-18 14:25:45.107 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:25:45.107 - [主从合并全量][增强JSss] - Node 增强JSss[13575fbe-7697-4f61-9437-9873ae37ae73] preload schema finished, cost 0 ms 
[INFO ] 2024-12-18 14:25:45.120 - [主从合并全量][增强JSss] - Node js_processor(增强JSss: 13575fbe-7697-4f61-9437-9873ae37ae73) enable batch process 
[INFO ] 2024-12-18 14:25:45.120 - [主从合并全量][主从合并] - Merge table processor lookup thread num: 8 
[INFO ] 2024-12-18 14:25:45.120 - [主从合并全量][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-12-18 14:25:45.355 - [主从合并全量][Merge_Test] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-18 14:25:45.502 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" read batch size: 100 
[INFO ] 2024-12-18 14:25:45.502 - [主从合并全量][0827_0827_CLAIM] - Source node "0827_0827_CLAIM" event queue capacity: 200 
[INFO ] 2024-12-18 14:25:45.502 - [主从合并全量][0827_0827_CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:25:45.560 - [主从合并全量][0827_0827_CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:25:45.560 - [主从合并全量][0827_0827_CLAIM] - Initial sync started 
[INFO ] 2024-12-18 14:25:45.561 - [主从合并全量][0827_0827_CLAIM] - Starting batch read, table name: 0827_0827_CLAIM 
[INFO ] 2024-12-18 14:25:45.561 - [主从合并全量][0827_0827_CLAIM] - Table 0827_0827_CLAIM is going to be initial synced 
[INFO ] 2024-12-18 14:25:45.635 - [主从合并全量][0827_0827_CLAIM] - Query snapshot row size completed: 0827_0827_CLAIM(21658fc5-47ae-4bd4-802b-bc5df73a0085) 
[INFO ] 2024-12-18 14:25:45.635 - [主从合并全量][0827_0827_CLAIM] - Table [0827_0827_CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:25:45.635 - [主从合并全量][0827_0827_CLAIM] - Initial sync completed 
[INFO ] 2024-12-18 14:25:45.673 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" read batch size: 100 
[INFO ] 2024-12-18 14:25:45.673 - [主从合并全量][0827_0827_CAR_POLICY] - Source node "0827_0827_CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-12-18 14:25:45.673 - [主从合并全量][0827_0827_CAR_POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-18 14:25:45.673 - [主从合并全量][0827_0827_CAR_POLICY] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-18 14:25:45.760 - [主从合并全量] - Node[0827_0827_CAR_POLICY] is waiting for running 
[INFO ] 2024-12-18 14:25:47.367 - [主从合并全量][Merge_Test] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@57ca764d: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}). Wait for all previous events to be processed 
[INFO ] 2024-12-18 14:25:48.358 - [主从合并全量][Merge_Test] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@57ca764d: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"POLICY_ID"}]}],"tableId":"db2634a7-50ef-4728-a3af-83da63949195","type":101}) 
[INFO ] 2024-12-18 14:25:54.395 - [主从合并全量] - Node[0827_0827_CLAIM] finish, notify next layer to run 
[INFO ] 2024-12-18 14:25:54.395 - [主从合并全量] - Next layer have been notified: [null] 
[INFO ] 2024-12-18 14:25:54.395 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync started 
[INFO ] 2024-12-18 14:25:54.396 - [主从合并全量][0827_0827_CAR_POLICY] - Starting batch read, table name: 0827_0827_CAR_POLICY 
[INFO ] 2024-12-18 14:25:54.396 - [主从合并全量][0827_0827_CAR_POLICY] - Table 0827_0827_CAR_POLICY is going to be initial synced 
[INFO ] 2024-12-18 14:25:54.469 - [主从合并全量][0827_0827_CAR_POLICY] - Query snapshot row size completed: 0827_0827_CAR_POLICY(627b464a-ffab-4576-a72c-2b69b8eeaa48) 
[INFO ] 2024-12-18 14:25:54.469 - [主从合并全量][0827_0827_CAR_POLICY] - Table [0827_0827_CAR_POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-18 14:25:54.469 - [主从合并全量][0827_0827_CAR_POLICY] - Initial sync completed 
[WARN ] 2024-12-18 14:25:57.414 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[21658fc5-47ae-4bd4-802b-bc5df73a0085, 9cd454c6-74d7-4d67-9180-7e2c1ecb83a5, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2024-12-18 14:26:03.435 - [主从合并全量][Merge_Test] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[627b464a-ffab-4576-a72c-2b69b8eeaa48, 13575fbe-7697-4f61-9437-9873ae37ae73, db2634a7-50ef-4728-a3af-83da63949195], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-12-18 14:26:03.506 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] running status set to false 
[INFO ] 2024-12-18 14:26:03.507 - [主从合并全量][增强JSss] - Node 增强JSss[13575fbe-7697-4f61-9437-9873ae37ae73] running status set to false 
[INFO ] 2024-12-18 14:26:03.507 - [主从合并全量][增强JSss] - Node 增强JSss[13575fbe-7697-4f61-9437-9873ae37ae73] schema data cleaned 
[INFO ] 2024-12-18 14:26:03.507 - [主从合并全量][增强JSss] - Node 增强JSss[13575fbe-7697-4f61-9437-9873ae37ae73] monitor closed 
[INFO ] 2024-12-18 14:26:03.508 - [主从合并全量][增强JSss] - Node 增强JSss[13575fbe-7697-4f61-9437-9873ae37ae73] close complete, cost 1 ms 
[INFO ] 2024-12-18 14:26:03.529 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734503145182 
[INFO ] 2024-12-18 14:26:03.529 - [主从合并全量][0827_0827_CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode_627b464a-ffab-4576-a72c-2b69b8eeaa48_1734503145182 
[INFO ] 2024-12-18 14:26:03.529 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] schema data cleaned 
[INFO ] 2024-12-18 14:26:03.529 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] monitor closed 
[INFO ] 2024-12-18 14:26:03.529 - [主从合并全量][0827_0827_CAR_POLICY] - Node 0827_0827_CAR_POLICY[627b464a-ffab-4576-a72c-2b69b8eeaa48] close complete, cost 29 ms 
[INFO ] 2024-12-18 14:26:03.550 - [主从合并全量][增强JS] - Node 增强JS[9cd454c6-74d7-4d67-9180-7e2c1ecb83a5] running status set to false 
[INFO ] 2024-12-18 14:26:03.550 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] running status set to false 
[INFO ] 2024-12-18 14:26:03.551 - [主从合并全量][增强JS] - Node 增强JS[9cd454c6-74d7-4d67-9180-7e2c1ecb83a5] schema data cleaned 
[INFO ] 2024-12-18 14:26:03.551 - [主从合并全量][增强JS] - Node 增强JS[9cd454c6-74d7-4d67-9180-7e2c1ecb83a5] monitor closed 
[INFO ] 2024-12-18 14:26:03.552 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] running status set to false 
[INFO ] 2024-12-18 14:26:03.552 - [主从合并全量][增强JS] - Node 增强JS[9cd454c6-74d7-4d67-9180-7e2c1ecb83a5] close complete, cost 1 ms 
[INFO ] 2024-12-18 14:26:03.552 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] schema data cleaned 
[INFO ] 2024-12-18 14:26:03.553 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] monitor closed 
[INFO ] 2024-12-18 14:26:03.553 - [主从合并全量][主从合并] - Node 主从合并[db2634a7-50ef-4728-a3af-83da63949195] close complete, cost 0 ms 
[INFO ] 2024-12-18 14:26:03.573 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] running status set to false 
[INFO ] 2024-12-18 14:26:03.573 - [主从合并全量][Merge_Test] - PDK connector node stopped: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734503145248 
[INFO ] 2024-12-18 14:26:03.573 - [主从合并全量][Merge_Test] - PDK connector node released: HazelcastTargetPdkDataNode_9b8318d8-2f8a-4d69-86d5-28931b3ffd7a_1734503145248 
[INFO ] 2024-12-18 14:26:03.573 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] schema data cleaned 
[INFO ] 2024-12-18 14:26:03.574 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] monitor closed 
[INFO ] 2024-12-18 14:26:03.574 - [主从合并全量][Merge_Test] - Node Merge_Test[9b8318d8-2f8a-4d69-86d5-28931b3ffd7a] close complete, cost 20 ms 
[INFO ] 2024-12-18 14:26:03.586 - [主从合并全量][0827_0827_CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734503145173 
[INFO ] 2024-12-18 14:26:03.586 - [主从合并全量][0827_0827_CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode_21658fc5-47ae-4bd4-802b-bc5df73a0085_1734503145173 
[INFO ] 2024-12-18 14:26:03.586 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] schema data cleaned 
[INFO ] 2024-12-18 14:26:03.586 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] monitor closed 
[INFO ] 2024-12-18 14:26:03.791 - [主从合并全量][0827_0827_CLAIM] - Node 0827_0827_CLAIM[21658fc5-47ae-4bd4-802b-bc5df73a0085] close complete, cost 36 ms 
[INFO ] 2024-12-18 14:26:04.036 - [主从合并全量] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-18 14:26:04.147 - [主从合并全量] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1aeab765 
[INFO ] 2024-12-18 14:26:04.147 - [主从合并全量] - Stop task milestones: 6761450f3cf1802ee0c8c0be(主从合并全量)  
[INFO ] 2024-12-18 14:26:04.159 - [主从合并全量] - Stopped task aspect(s) 
[INFO ] 2024-12-18 14:26:04.159 - [主从合并全量] - Snapshot order controller have been removed 
[INFO ] 2024-12-18 14:26:04.182 - [主从合并全量] - Remove memory task client succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
[INFO ] 2024-12-18 14:26:04.182 - [主从合并全量] - Destroy memory task client cache succeed, task: 主从合并全量[6761450f3cf1802ee0c8c0be] 
