[TRACE] 2025-02-14 15:04:44.501 - [任务 12] - Task initialization... 
[TRACE] 2025-02-14 15:04:44.703 - [任务 12] - Start task milestones: 67aeea28a250d87287e29d72(任务 12) 
[INFO ] 2025-02-14 15:04:46.416 - [任务 12] - Loading table structure completed 
[TRACE] 2025-02-14 15:04:46.416 - [任务 12] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-02-14 15:04:46.621 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-14 15:04:46.823 - [任务 12] - Task started 
[TRACE] 2025-02-14 15:04:46.857 - [任务 12][sybase_190 - LAB_DB_6] - Node sybase_190 - LAB_DB_6[6b709892-6eff-48d4-98dc-4865c6bcf78d] start preload schema,table counts: 297 
[TRACE] 2025-02-14 15:04:46.857 - [任务 12][pg_hdtest - SP6_lab] - Node pg_hdtest - SP6_lab[d0e0bad5-2d1c-42d5-9901-2be97342c387] start preload schema,table counts: 306 
[TRACE] 2025-02-14 15:04:46.857 - [任务 12][sybase_190 - LAB_DB_6] - Node sybase_190 - LAB_DB_6[6b709892-6eff-48d4-98dc-4865c6bcf78d] preload schema finished, cost 0 ms 
[TRACE] 2025-02-14 15:04:47.062 - [任务 12][pg_hdtest - SP6_lab] - Node pg_hdtest - SP6_lab[d0e0bad5-2d1c-42d5-9901-2be97342c387] preload schema finished, cost 0 ms 
[INFO ] 2025-02-14 15:04:47.228 - [任务 12][sybase_190 - LAB_DB_6] - Source connector(sybase_190 - LAB_DB_6) initialization completed 
[TRACE] 2025-02-14 15:04:47.228 - [任务 12][sybase_190 - LAB_DB_6] - Source node "sybase_190 - LAB_DB_6" read batch size: 100 
[TRACE] 2025-02-14 15:04:47.228 - [任务 12][sybase_190 - LAB_DB_6] - Source node "sybase_190 - LAB_DB_6" event queue capacity: 200 
[TRACE] 2025-02-14 15:04:47.228 - [任务 12][sybase_190 - LAB_DB_6] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-14 15:04:47.420 - [任务 12][sybase_190 - LAB_DB_6] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-14 15:04:47.420 - [任务 12][pg_hdtest - SP6_lab] - Sink connector(pg_hdtest - SP6_lab) initialization completed 
[TRACE] 2025-02-14 15:04:47.420 - [任务 12][pg_hdtest - SP6_lab] - Node(pg_hdtest - SP6_lab) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-14 15:04:47.420 - [任务 12][pg_hdtest - SP6_lab] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-14 15:04:47.620 - [任务 12][pg_hdtest - SP6_lab] - Apply table structure to target database 
[INFO ] 2025-02-14 15:04:47.974 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from 297 tables 
[TRACE] 2025-02-14 15:04:47.974 - [任务 12][sybase_190 - LAB_DB_6] - Initial sync started 
[INFO ] 2025-02-14 15:04:47.975 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_blood_inv 
[TRACE] 2025-02-14 15:04:47.975 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_inv is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.121 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_inv has been completed batch read 
[INFO ] 2025-02-14 15:04:48.121 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: usid_profile_relation 
[TRACE] 2025-02-14 15:04:48.121 - [任务 12][sybase_190 - LAB_DB_6] - Table usid_profile_relation is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.217 - [任务 12][sybase_190 - LAB_DB_6] - Table usid_profile_relation has been completed batch read 
[INFO ] 2025-02-14 15:04:48.217 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: crs_wrk_test_reg_try 
[TRACE] 2025-02-14 15:04:48.217 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_wrk_test_reg_try is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.323 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_wrk_test_reg_try has been completed batch read 
[INFO ] 2025-02-14 15:04:48.323 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: user_contact_list 
[TRACE] 2025-02-14 15:04:48.323 - [任务 12][sybase_190 - LAB_DB_6] - Table user_contact_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.410 - [任务 12][sybase_190 - LAB_DB_6] - Table user_contact_list has been completed batch read 
[INFO ] 2025-02-14 15:04:48.414 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: ecsearch_login_log 
[TRACE] 2025-02-14 15:04:48.414 - [任务 12][sybase_190 - LAB_DB_6] - Table ecsearch_login_log is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.478 - [任务 12][sybase_190 - LAB_DB_6] - Table ecsearch_login_log has been completed batch read 
[INFO ] 2025-02-14 15:04:48.478 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_request_code 
[TRACE] 2025-02-14 15:04:48.478 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_request_code is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.575 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_request_code has been completed batch read 
[INFO ] 2025-02-14 15:04:48.577 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_ot_print_audit 
[TRACE] 2025-02-14 15:04:48.577 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_print_audit is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.659 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_print_audit has been completed batch read 
[INFO ] 2025-02-14 15:04:48.659 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_indication_code 
[TRACE] 2025-02-14 15:04:48.659 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_indication_code is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.748 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_indication_code has been completed batch read 
[INFO ] 2025-02-14 15:04:48.748 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule 
[TRACE] 2025-02-14 15:04:48.748 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.844 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule has been completed batch read 
[INFO ] 2025-02-14 15:04:48.845 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: usid_test_relation 
[TRACE] 2025-02-14 15:04:48.845 - [任务 12][sybase_190 - LAB_DB_6] - Table usid_test_relation is going to be initial synced 
[INFO ] 2025-02-14 15:04:48.930 - [任务 12][sybase_190 - LAB_DB_6] - Table usid_test_relation has been completed batch read 
[INFO ] 2025-02-14 15:04:48.930 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_worksheet 
[TRACE] 2025-02-14 15:04:48.930 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_worksheet is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.040 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_worksheet has been completed batch read 
[INFO ] 2025-02-14 15:04:49.040 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: chk_table 
[TRACE] 2025-02-14 15:04:49.040 - [任务 12][sybase_190 - LAB_DB_6] - Table chk_table is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.205 - [任务 12][sybase_190 - LAB_DB_6] - Table chk_table has been completed batch read 
[INFO ] 2025-02-14 15:04:49.206 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: authen_token_hist 
[TRACE] 2025-02-14 15:04:49.206 - [任务 12][sybase_190 - LAB_DB_6] - Table authen_token_hist is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.370 - [任务 12][sybase_190 - LAB_DB_6] - Table authen_token_hist has been completed batch read 
[INFO ] 2025-02-14 15:04:49.370 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: comment_upd 
[TRACE] 2025-02-14 15:04:49.370 - [任务 12][sybase_190 - LAB_DB_6] - Table comment_upd is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.508 - [任务 12][sybase_190 - LAB_DB_6] - Table comment_upd has been completed batch read 
[INFO ] 2025-02-14 15:04:49.509 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_ha_chargetable 
[TRACE] 2025-02-14 15:04:49.509 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_ha_chargetable is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.632 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_ha_chargetable has been completed batch read 
[INFO ] 2025-02-14 15:04:49.633 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_lprc 
[TRACE] 2025-02-14 15:04:49.633 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_lprc is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.755 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_lprc has been completed batch read 
[INFO ] 2025-02-14 15:04:49.755 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_console 
[TRACE] 2025-02-14 15:04:49.755 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_console is going to be initial synced 
[INFO ] 2025-02-14 15:04:49.889 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_console has been completed batch read 
[INFO ] 2025-02-14 15:04:49.889 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: operation_audit 
[TRACE] 2025-02-14 15:04:49.889 - [任务 12][sybase_190 - LAB_DB_6] - Table operation_audit is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.009 - [任务 12][sybase_190 - LAB_DB_6] - Table operation_audit has been completed batch read 
[INFO ] 2025-02-14 15:04:50.009 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_map_upd 
[TRACE] 2025-02-14 15:04:50.009 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_upd is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.149 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_upd has been completed batch read 
[INFO ] 2025-02-14 15:04:50.149 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisg_tasklist_arch 
[TRACE] 2025-02-14 15:04:50.149 - [任务 12][sybase_190 - LAB_DB_6] - Table lisg_tasklist_arch is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.286 - [任务 12][sybase_190 - LAB_DB_6] - Table lisg_tasklist_arch has been completed batch read 
[INFO ] 2025-02-14 15:04:50.286 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisrep_trace 
[TRACE] 2025-02-14 15:04:50.286 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_trace is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.416 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_trace has been completed batch read 
[INFO ] 2025-02-14 15:04:50.416 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_enum_result 
[TRACE] 2025-02-14 15:04:50.416 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_enum_result is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.611 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_enum_result has been completed batch read 
[INFO ] 2025-02-14 15:04:50.611 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_user_application 
[TRACE] 2025-02-14 15:04:50.611 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_user_application is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.755 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_user_application has been completed batch read 
[INFO ] 2025-02-14 15:04:50.756 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_ot_work 
[TRACE] 2025-02-14 15:04:50.756 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_work is going to be initial synced 
[INFO ] 2025-02-14 15:04:50.877 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_work has been completed batch read 
[INFO ] 2025-02-14 15:04:50.877 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_pdf_transfer_control 
[TRACE] 2025-02-14 15:04:50.877 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_transfer_control is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.018 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_transfer_control has been completed batch read 
[INFO ] 2025-02-14 15:04:51.018 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_option_relation 
[TRACE] 2025-02-14 15:04:51.018 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_relation is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.173 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_relation has been completed batch read 
[INFO ] 2025-02-14 15:04:51.173 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_corp_blood_transaction 
[TRACE] 2025-02-14 15:04:51.173 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_corp_blood_transaction is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.280 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_corp_blood_transaction has been completed batch read 
[INFO ] 2025-02-14 15:04:51.280 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_result_rule 
[TRACE] 2025-02-14 15:04:51.280 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_result_rule is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.407 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_result_rule has been completed batch read 
[INFO ] 2025-02-14 15:04:51.407 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_ot_global_ctr 
[TRACE] 2025-02-14 15:04:51.407 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_global_ctr is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.526 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_global_ctr has been completed batch read 
[INFO ] 2025-02-14 15:04:51.526 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: prefix_suffix 
[TRACE] 2025-02-14 15:04:51.526 - [任务 12][sybase_190 - LAB_DB_6] - Table prefix_suffix is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.637 - [任务 12][sybase_190 - LAB_DB_6] - Table prefix_suffix has been completed batch read 
[INFO ] 2025-02-14 15:04:51.637 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_transfer_time 
[TRACE] 2025-02-14 15:04:51.637 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_transfer_time is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.815 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_transfer_time has been completed batch read 
[INFO ] 2025-02-14 15:04:51.815 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: send_out 
[TRACE] 2025-02-14 15:04:51.815 - [任务 12][sybase_190 - LAB_DB_6] - Table send_out is going to be initial synced 
[INFO ] 2025-02-14 15:04:51.948 - [任务 12][sybase_190 - LAB_DB_6] - Table send_out has been completed batch read 
[INFO ] 2025-02-14 15:04:51.951 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_chargeable_non_charge_upd 
[TRACE] 2025-02-14 15:04:51.951 - [任务 12][sybase_190 - LAB_DB_6] - Table test_chargeable_non_charge_upd is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.052 - [任务 12][sybase_190 - LAB_DB_6] - Table test_chargeable_non_charge_upd has been completed batch read 
[INFO ] 2025-02-14 15:04:52.052 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pbcatfmt 
[TRACE] 2025-02-14 15:04:52.052 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatfmt is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.254 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatfmt has been completed batch read 
[INFO ] 2025-02-14 15:04:52.254 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_request_inv 
[TRACE] 2025-02-14 15:04:52.254 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_request_inv is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.340 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_request_inv has been completed batch read 
[INFO ] 2025-02-14 15:04:52.340 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_option 
[TRACE] 2025-02-14 15:04:52.340 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.432 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option has been completed batch read 
[INFO ] 2025-02-14 15:04:52.432 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_ot_message 
[TRACE] 2025-02-14 15:04:52.432 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_message is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.555 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_message has been completed batch read 
[INFO ] 2025-02-14 15:04:52.555 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: mis_sql 
[TRACE] 2025-02-14 15:04:52.555 - [任务 12][sybase_190 - LAB_DB_6] - Table mis_sql is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.690 - [任务 12][sybase_190 - LAB_DB_6] - Table mis_sql has been completed batch read 
[INFO ] 2025-02-14 15:04:52.690 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request_format 
[TRACE] 2025-02-14 15:04:52.690 - [任务 12][sybase_190 - LAB_DB_6] - Table request_format is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.804 - [任务 12][sybase_190 - LAB_DB_6] - Table request_format has been completed batch read 
[INFO ] 2025-02-14 15:04:52.804 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: sendout_reqno_map 
[TRACE] 2025-02-14 15:04:52.804 - [任务 12][sybase_190 - LAB_DB_6] - Table sendout_reqno_map is going to be initial synced 
[INFO ] 2025-02-14 15:04:52.899 - [任务 12][sybase_190 - LAB_DB_6] - Table sendout_reqno_map has been completed batch read 
[INFO ] 2025-02-14 15:04:52.899 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_electronic_log 
[TRACE] 2025-02-14 15:04:52.899 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_electronic_log is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.003 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_electronic_log has been completed batch read 
[INFO ] 2025-02-14 15:04:53.004 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_option_def 
[TRACE] 2025-02-14 15:04:53.004 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_def is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.101 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_def has been completed batch read 
[INFO ] 2025-02-14 15:04:53.101 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_audit 
[TRACE] 2025-02-14 15:04:53.101 - [任务 12][sybase_190 - LAB_DB_6] - Table print_audit is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.231 - [任务 12][sybase_190 - LAB_DB_6] - Table print_audit has been completed batch read 
[INFO ] 2025-02-14 15:04:53.231 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: temp_por 
[TRACE] 2025-02-14 15:04:53.231 - [任务 12][sybase_190 - LAB_DB_6] - Table temp_por is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.379 - [任务 12][sybase_190 - LAB_DB_6] - Table temp_por has been completed batch read 
[INFO ] 2025-02-14 15:04:53.379 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: outbound_recipient 
[TRACE] 2025-02-14 15:04:53.379 - [任务 12][sybase_190 - LAB_DB_6] - Table outbound_recipient is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.447 - [任务 12][sybase_190 - LAB_DB_6] - Table outbound_recipient has been completed batch read 
[INFO ] 2025-02-14 15:04:53.447 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: schedule_check 
[TRACE] 2025-02-14 15:04:53.447 - [任务 12][sybase_190 - LAB_DB_6] - Table schedule_check is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.554 - [任务 12][sybase_190 - LAB_DB_6] - Table schedule_check has been completed batch read 
[INFO ] 2025-02-14 15:04:53.554 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: tmp_derived_numeric 
[TRACE] 2025-02-14 15:04:53.554 - [任务 12][sybase_190 - LAB_DB_6] - Table tmp_derived_numeric is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.695 - [任务 12][sybase_190 - LAB_DB_6] - Table tmp_derived_numeric has been completed batch read 
[INFO ] 2025-02-14 15:04:53.696 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: top_menu 
[TRACE] 2025-02-14 15:04:53.696 - [任务 12][sybase_190 - LAB_DB_6] - Table top_menu is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.819 - [任务 12][sybase_190 - LAB_DB_6] - Table top_menu has been completed batch read 
[INFO ] 2025-02-14 15:04:53.819 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_constant 
[TRACE] 2025-02-14 15:04:53.819 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_constant is going to be initial synced 
[INFO ] 2025-02-14 15:04:53.928 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_constant has been completed batch read 
[INFO ] 2025-02-14 15:04:53.928 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_chargeable_non_charge 
[TRACE] 2025-02-14 15:04:53.928 - [任务 12][sybase_190 - LAB_DB_6] - Table test_chargeable_non_charge is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.032 - [任务 12][sybase_190 - LAB_DB_6] - Table test_chargeable_non_charge has been completed batch read 
[INFO ] 2025-02-14 15:04:54.036 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: batch_order 
[TRACE] 2025-02-14 15:04:54.036 - [任务 12][sybase_190 - LAB_DB_6] - Table batch_order is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.134 - [任务 12][sybase_190 - LAB_DB_6] - Table batch_order has been completed batch read 
[INFO ] 2025-02-14 15:04:54.134 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: archive_list 
[TRACE] 2025-02-14 15:04:54.134 - [任务 12][sybase_190 - LAB_DB_6] - Table archive_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.289 - [任务 12][sybase_190 - LAB_DB_6] - Table archive_list has been completed batch read 
[INFO ] 2025-02-14 15:04:54.290 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_map_master_upd 
[TRACE] 2025-02-14 15:04:54.290 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_master_upd is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.408 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_master_upd has been completed batch read 
[INFO ] 2025-02-14 15:04:54.408 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pbcatvld 
[TRACE] 2025-02-14 15:04:54.408 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatvld is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.499 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatvld has been completed batch read 
[INFO ] 2025-02-14 15:04:54.499 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisrep_seed 
[TRACE] 2025-02-14 15:04:54.499 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_seed is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.573 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_seed has been completed batch read 
[INFO ] 2025-02-14 15:04:54.573 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: derived_test 
[TRACE] 2025-02-14 15:04:54.573 - [任务 12][sybase_190 - LAB_DB_6] - Table derived_test is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.676 - [任务 12][sybase_190 - LAB_DB_6] - Table derived_test has been completed batch read 
[INFO ] 2025-02-14 15:04:54.676 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_special_blood_gcd_upload 
[TRACE] 2025-02-14 15:04:54.676 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd_upload is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.797 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd_upload has been completed batch read 
[INFO ] 2025-02-14 15:04:54.797 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: retain_master 
[TRACE] 2025-02-14 15:04:54.797 - [任务 12][sybase_190 - LAB_DB_6] - Table retain_master is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.880 - [任务 12][sybase_190 - LAB_DB_6] - Table retain_master has been completed batch read 
[INFO ] 2025-02-14 15:04:54.880 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: work_misraw 
[TRACE] 2025-02-14 15:04:54.880 - [任务 12][sybase_190 - LAB_DB_6] - Table work_misraw is going to be initial synced 
[INFO ] 2025-02-14 15:04:54.957 - [任务 12][sybase_190 - LAB_DB_6] - Table work_misraw has been completed batch read 
[INFO ] 2025-02-14 15:04:54.957 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: message_code 
[TRACE] 2025-02-14 15:04:54.957 - [任务 12][sybase_190 - LAB_DB_6] - Table message_code is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.049 - [任务 12][sybase_190 - LAB_DB_6] - Table message_code has been completed batch read 
[INFO ] 2025-02-14 15:04:55.050 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_audit 
[TRACE] 2025-02-14 15:04:55.050 - [任务 12][sybase_190 - LAB_DB_6] - Table report_audit is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.174 - [任务 12][sybase_190 - LAB_DB_6] - Table report_audit has been completed batch read 
[INFO ] 2025-02-14 15:04:55.175 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_request_ex 
[TRACE] 2025-02-14 15:04:55.175 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_request_ex is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.270 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_request_ex has been completed batch read 
[INFO ] 2025-02-14 15:04:55.270 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_pilot_tube 
[TRACE] 2025-02-14 15:04:55.270 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_pilot_tube is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.405 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_pilot_tube has been completed batch read 
[INFO ] 2025-02-14 15:04:55.405 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_operation_code 
[TRACE] 2025-02-14 15:04:55.405 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_operation_code is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.528 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_operation_code has been completed batch read 
[INFO ] 2025-02-14 15:04:55.528 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request_copy_hist 
[TRACE] 2025-02-14 15:04:55.528 - [任务 12][sybase_190 - LAB_DB_6] - Table request_copy_hist is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.639 - [任务 12][sybase_190 - LAB_DB_6] - Table request_copy_hist has been completed batch read 
[INFO ] 2025-02-14 15:04:55.640 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_style 
[TRACE] 2025-02-14 15:04:55.640 - [任务 12][sybase_190 - LAB_DB_6] - Table report_style is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.738 - [任务 12][sybase_190 - LAB_DB_6] - Table report_style has been completed batch read 
[INFO ] 2025-02-14 15:04:55.739 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_action 
[TRACE] 2025-02-14 15:04:55.739 - [任务 12][sybase_190 - LAB_DB_6] - Table test_action is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.826 - [任务 12][sybase_190 - LAB_DB_6] - Table test_action has been completed batch read 
[INFO ] 2025-02-14 15:04:55.826 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: ccc_big5 
[TRACE] 2025-02-14 15:04:55.826 - [任务 12][sybase_190 - LAB_DB_6] - Table ccc_big5 is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.909 - [任务 12][sybase_190 - LAB_DB_6] - Table ccc_big5 has been completed batch read 
[INFO ] 2025-02-14 15:04:55.910 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: ts_comment_rule 
[TRACE] 2025-02-14 15:04:55.911 - [任务 12][sybase_190 - LAB_DB_6] - Table ts_comment_rule is going to be initial synced 
[INFO ] 2025-02-14 15:04:55.989 - [任务 12][sybase_190 - LAB_DB_6] - Table ts_comment_rule has been completed batch read 
[INFO ] 2025-02-14 15:04:55.989 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: analyser_capability 
[TRACE] 2025-02-14 15:04:55.989 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser_capability is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.059 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser_capability has been completed batch read 
[INFO ] 2025-02-14 15:04:56.059 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_transfer_list 
[TRACE] 2025-02-14 15:04:56.059 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_transfer_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.184 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_transfer_list has been completed batch read 
[INFO ] 2025-02-14 15:04:56.184 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_xm_list 
[TRACE] 2025-02-14 15:04:56.184 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_xm_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.282 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_xm_list has been completed batch read 
[INFO ] 2025-02-14 15:04:56.282 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: crs_bb_request_code 
[TRACE] 2025-02-14 15:04:56.282 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_bb_request_code is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.390 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_bb_request_code has been completed batch read 
[INFO ] 2025-02-14 15:04:56.391 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_entity 
[TRACE] 2025-02-14 15:04:56.391 - [任务 12][sybase_190 - LAB_DB_6] - Table print_entity is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.506 - [任务 12][sybase_190 - LAB_DB_6] - Table print_entity has been completed batch read 
[INFO ] 2025-02-14 15:04:56.507 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_recent_tmp 
[TRACE] 2025-02-14 15:04:56.507 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_recent_tmp is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.621 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_recent_tmp has been completed batch read 
[INFO ] 2025-02-14 15:04:56.621 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_reference_upd 
[TRACE] 2025-02-14 15:04:56.621 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference_upd is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.709 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference_upd has been completed batch read 
[INFO ] 2025-02-14 15:04:56.709 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: labuser 
[TRACE] 2025-02-14 15:04:56.709 - [任务 12][sybase_190 - LAB_DB_6] - Table labuser is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.817 - [任务 12][sybase_190 - LAB_DB_6] - Table labuser has been completed batch read 
[INFO ] 2025-02-14 15:04:56.817 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: tmp_reqno_list 
[TRACE] 2025-02-14 15:04:56.817 - [任务 12][sybase_190 - LAB_DB_6] - Table tmp_reqno_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.907 - [任务 12][sybase_190 - LAB_DB_6] - Table tmp_reqno_list has been completed batch read 
[INFO ] 2025-02-14 15:04:56.908 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: workbench 
[TRACE] 2025-02-14 15:04:56.908 - [任务 12][sybase_190 - LAB_DB_6] - Table workbench is going to be initial synced 
[INFO ] 2025-02-14 15:04:56.991 - [任务 12][sybase_190 - LAB_DB_6] - Table workbench has been completed batch read 
[INFO ] 2025-02-14 15:04:56.992 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: QC_type_upd 
[TRACE] 2025-02-14 15:04:56.992 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_type_upd is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.095 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_type_upd has been completed batch read 
[INFO ] 2025-02-14 15:04:57.095 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: user_function 
[TRACE] 2025-02-14 15:04:57.095 - [任务 12][sybase_190 - LAB_DB_6] - Table user_function is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.232 - [任务 12][sybase_190 - LAB_DB_6] - Table user_function has been completed batch read 
[INFO ] 2025-02-14 15:04:57.232 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_test_master_EXT 
[TRACE] 2025-02-14 15:04:57.232 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_test_master_EXT is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.325 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_test_master_EXT has been completed batch read 
[INFO ] 2025-02-14 15:04:57.325 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: outbound_request 
[TRACE] 2025-02-14 15:04:57.325 - [任务 12][sybase_190 - LAB_DB_6] - Table outbound_request is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.431 - [任务 12][sybase_190 - LAB_DB_6] - Table outbound_request has been completed batch read 
[INFO ] 2025-02-14 15:04:57.431 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: QC_Sample_Map 
[TRACE] 2025-02-14 15:04:57.431 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_Sample_Map is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.520 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_Sample_Map has been completed batch read 
[INFO ] 2025-02-14 15:04:57.520 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_rbc 
[TRACE] 2025-02-14 15:04:57.520 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_rbc is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.628 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_rbc has been completed batch read 
[INFO ] 2025-02-14 15:04:57.628 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testrslt_print_audit 
[TRACE] 2025-02-14 15:04:57.628 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_print_audit is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.712 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_print_audit has been completed batch read 
[INFO ] 2025-02-14 15:04:57.712 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rsltflow 
[TRACE] 2025-02-14 15:04:57.712 - [任务 12][sybase_190 - LAB_DB_6] - Table rsltflow is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.808 - [任务 12][sybase_190 - LAB_DB_6] - Table rsltflow has been completed batch read 
[INFO ] 2025-02-14 15:04:57.808 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: batch 
[TRACE] 2025-02-14 15:04:57.808 - [任务 12][sybase_190 - LAB_DB_6] - Table batch is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.908 - [任务 12][sybase_190 - LAB_DB_6] - Table batch has been completed batch read 
[INFO ] 2025-02-14 15:04:57.908 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testrslt 
[TRACE] 2025-02-14 15:04:57.909 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt is going to be initial synced 
[INFO ] 2025-02-14 15:04:57.987 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt has been completed batch read 
[INFO ] 2025-02-14 15:04:57.987 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_order 
[TRACE] 2025-02-14 15:04:57.987 - [任务 12][sybase_190 - LAB_DB_6] - Table report_order is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.103 - [任务 12][sybase_190 - LAB_DB_6] - Table report_order has been completed batch read 
[INFO ] 2025-02-14 15:04:58.103 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_map_master 
[TRACE] 2025-02-14 15:04:58.103 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_master is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.210 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_master has been completed batch read 
[INFO ] 2025-02-14 15:04:58.210 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rs_lastcommit 
[TRACE] 2025-02-14 15:04:58.210 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_lastcommit is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.318 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_lastcommit has been completed batch read 
[INFO ] 2025-02-14 15:04:58.318 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: shortcut_map 
[TRACE] 2025-02-14 15:04:58.318 - [任务 12][sybase_190 - LAB_DB_6] - Table shortcut_map is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.418 - [任务 12][sybase_190 - LAB_DB_6] - Table shortcut_map has been completed batch read 
[INFO ] 2025-02-14 15:04:58.418 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_inv_transaction 
[TRACE] 2025-02-14 15:04:58.418 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_inv_transaction is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.492 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_inv_transaction has been completed batch read 
[INFO ] 2025-02-14 15:04:58.492 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: analyser 
[TRACE] 2025-02-14 15:04:58.492 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.582 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser has been completed batch read 
[INFO ] 2025-02-14 15:04:58.582 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_list 
[TRACE] 2025-02-14 15:04:58.583 - [任务 12][sybase_190 - LAB_DB_6] - Table print_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.652 - [任务 12][sybase_190 - LAB_DB_6] - Table print_list has been completed batch read 
[INFO ] 2025-02-14 15:04:58.652 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: function_group 
[TRACE] 2025-02-14 15:04:58.652 - [任务 12][sybase_190 - LAB_DB_6] - Table function_group is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.732 - [任务 12][sybase_190 - LAB_DB_6] - Table function_group has been completed batch read 
[INFO ] 2025-02-14 15:04:58.732 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: authorize_group 
[TRACE] 2025-02-14 15:04:58.732 - [任务 12][sybase_190 - LAB_DB_6] - Table authorize_group is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.810 - [任务 12][sybase_190 - LAB_DB_6] - Table authorize_group has been completed batch read 
[INFO ] 2025-02-14 15:04:58.810 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: laboratory 
[TRACE] 2025-02-14 15:04:58.810 - [任务 12][sybase_190 - LAB_DB_6] - Table laboratory is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.885 - [任务 12][sybase_190 - LAB_DB_6] - Table laboratory has been completed batch read 
[INFO ] 2025-02-14 15:04:58.886 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: check_point_gfs 
[TRACE] 2025-02-14 15:04:58.886 - [任务 12][sybase_190 - LAB_DB_6] - Table check_point_gfs is going to be initial synced 
[INFO ] 2025-02-14 15:04:58.967 - [任务 12][sybase_190 - LAB_DB_6] - Table check_point_gfs has been completed batch read 
[INFO ] 2025-02-14 15:04:58.967 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testrslt_audit 
[TRACE] 2025-02-14 15:04:58.967 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_audit is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.065 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_audit has been completed batch read 
[INFO ] 2025-02-14 15:04:59.065 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: object_attribute 
[TRACE] 2025-02-14 15:04:59.065 - [任务 12][sybase_190 - LAB_DB_6] - Table object_attribute is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.153 - [任务 12][sybase_190 - LAB_DB_6] - Table object_attribute has been completed batch read 
[INFO ] 2025-02-14 15:04:59.153 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_recent 
[TRACE] 2025-02-14 15:04:59.153 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_recent is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.253 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_recent has been completed batch read 
[INFO ] 2025-02-14 15:04:59.253 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_special_blood_gcd_detail 
[TRACE] 2025-02-14 15:04:59.253 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd_detail is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.349 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd_detail has been completed batch read 
[INFO ] 2025-02-14 15:04:59.349 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: component_comment_rule 
[TRACE] 2025-02-14 15:04:59.349 - [任务 12][sybase_190 - LAB_DB_6] - Table component_comment_rule is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.449 - [任务 12][sybase_190 - LAB_DB_6] - Table component_comment_rule has been completed batch read 
[INFO ] 2025-02-14 15:04:59.449 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_transaction_log 
[TRACE] 2025-02-14 15:04:59.449 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_transaction_log is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.651 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_transaction_log has been completed batch read 
[INFO ] 2025-02-14 15:04:59.652 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_pdf_list 
[TRACE] 2025-02-14 15:04:59.652 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_list is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.652 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_list has been completed batch read 
[INFO ] 2025-02-14 15:04:59.652 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: advance_schedule 
[TRACE] 2025-02-14 15:04:59.652 - [任务 12][sybase_190 - LAB_DB_6] - Table advance_schedule is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.759 - [任务 12][sybase_190 - LAB_DB_6] - Table advance_schedule has been completed batch read 
[INFO ] 2025-02-14 15:04:59.759 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_template_profile 
[TRACE] 2025-02-14 15:04:59.759 - [任务 12][sybase_190 - LAB_DB_6] - Table report_template_profile is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.841 - [任务 12][sybase_190 - LAB_DB_6] - Table report_template_profile has been completed batch read 
[INFO ] 2025-02-14 15:04:59.841 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lab_option_master 
[TRACE] 2025-02-14 15:04:59.842 - [任务 12][sybase_190 - LAB_DB_6] - Table lab_option_master is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.910 - [任务 12][sybase_190 - LAB_DB_6] - Table lab_option_master has been completed batch read 
[INFO ] 2025-02-14 15:04:59.910 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_msg 
[TRACE] 2025-02-14 15:04:59.910 - [任务 12][sybase_190 - LAB_DB_6] - Table report_msg is going to be initial synced 
[INFO ] 2025-02-14 15:04:59.998 - [任务 12][sybase_190 - LAB_DB_6] - Table report_msg has been completed batch read 
[INFO ] 2025-02-14 15:04:59.998 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: operation_audit_OLD 
[TRACE] 2025-02-14 15:04:59.998 - [任务 12][sybase_190 - LAB_DB_6] - Table operation_audit_OLD is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.073 - [任务 12][sybase_190 - LAB_DB_6] - Table operation_audit_OLD has been completed batch read 
[INFO ] 2025-02-14 15:05:00.073 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_label_printer_model 
[TRACE] 2025-02-14 15:05:00.073 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_label_printer_model is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.195 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_label_printer_model has been completed batch read 
[INFO ] 2025-02-14 15:05:00.195 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testrslt_save_sequence 
[TRACE] 2025-02-14 15:05:00.195 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_save_sequence is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.312 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_save_sequence has been completed batch read 
[INFO ] 2025-02-14 15:05:00.313 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_bbnk_patient_subgroup 
[TRACE] 2025-02-14 15:05:00.313 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_bbnk_patient_subgroup is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.391 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_bbnk_patient_subgroup has been completed batch read 
[INFO ] 2025-02-14 15:05:00.391 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_msg_upd 
[TRACE] 2025-02-14 15:05:00.391 - [任务 12][sybase_190 - LAB_DB_6] - Table report_msg_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.477 - [任务 12][sybase_190 - LAB_DB_6] - Table report_msg_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:00.477 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: worksheet_link 
[TRACE] 2025-02-14 15:05:00.477 - [任务 12][sybase_190 - LAB_DB_6] - Table worksheet_link is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.558 - [任务 12][sybase_190 - LAB_DB_6] - Table worksheet_link has been completed batch read 
[INFO ] 2025-02-14 15:05:00.558 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_local_blood_category 
[TRACE] 2025-02-14 15:05:00.558 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_local_blood_category is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.645 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_local_blood_category has been completed batch read 
[INFO ] 2025-02-14 15:05:00.645 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: object_class_map 
[TRACE] 2025-02-14 15:05:00.645 - [任务 12][sybase_190 - LAB_DB_6] - Table object_class_map is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.750 - [任务 12][sybase_190 - LAB_DB_6] - Table object_class_map has been completed batch read 
[INFO ] 2025-02-14 15:05:00.750 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_disk_content 
[TRACE] 2025-02-14 15:05:00.750 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_disk_content is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.853 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_disk_content has been completed batch read 
[INFO ] 2025-02-14 15:05:00.853 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rs_dbversion 
[TRACE] 2025-02-14 15:05:00.853 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_dbversion is going to be initial synced 
[INFO ] 2025-02-14 15:05:00.937 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_dbversion has been completed batch read 
[INFO ] 2025-02-14 15:05:00.937 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_dict 
[TRACE] 2025-02-14 15:05:00.937 - [任务 12][sybase_190 - LAB_DB_6] - Table test_dict is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.042 - [任务 12][sybase_190 - LAB_DB_6] - Table test_dict has been completed batch read 
[INFO ] 2025-02-14 15:05:01.042 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: schedule_check_rslt 
[TRACE] 2025-02-14 15:05:01.042 - [任务 12][sybase_190 - LAB_DB_6] - Table schedule_check_rslt is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.195 - [任务 12][sybase_190 - LAB_DB_6] - Table schedule_check_rslt has been completed batch read 
[INFO ] 2025-02-14 15:05:01.195 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_result 
[TRACE] 2025-02-14 15:05:01.195 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_result is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.297 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_result has been completed batch read 
[INFO ] 2025-02-14 15:05:01.297 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: eai_transaction_log 
[TRACE] 2025-02-14 15:05:01.298 - [任务 12][sybase_190 - LAB_DB_6] - Table eai_transaction_log is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.381 - [任务 12][sybase_190 - LAB_DB_6] - Table eai_transaction_log has been completed batch read 
[INFO ] 2025-02-14 15:05:01.381 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: gcrs_request_order 
[TRACE] 2025-02-14 15:05:01.381 - [任务 12][sybase_190 - LAB_DB_6] - Table gcrs_request_order is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.477 - [任务 12][sybase_190 - LAB_DB_6] - Table gcrs_request_order has been completed batch read 
[INFO ] 2025-02-14 15:05:01.478 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: message 
[TRACE] 2025-02-14 15:05:01.478 - [任务 12][sybase_190 - LAB_DB_6] - Table message is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.638 - [任务 12][sybase_190 - LAB_DB_6] - Table message has been completed batch read 
[INFO ] 2025-02-14 15:05:01.638 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_compatibility_transition 
[TRACE] 2025-02-14 15:05:01.638 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_compatibility_transition is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.710 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_compatibility_transition has been completed batch read 
[INFO ] 2025-02-14 15:05:01.710 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: derived_work 
[TRACE] 2025-02-14 15:05:01.710 - [任务 12][sybase_190 - LAB_DB_6] - Table derived_work is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.827 - [任务 12][sybase_190 - LAB_DB_6] - Table derived_work has been completed batch read 
[INFO ] 2025-02-14 15:05:01.827 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: ward_hospital_access 
[TRACE] 2025-02-14 15:05:01.827 - [任务 12][sybase_190 - LAB_DB_6] - Table ward_hospital_access is going to be initial synced 
[INFO ] 2025-02-14 15:05:01.916 - [任务 12][sybase_190 - LAB_DB_6] - Table ward_hospital_access has been completed batch read 
[INFO ] 2025-02-14 15:05:01.916 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: user_function_group 
[TRACE] 2025-02-14 15:05:01.916 - [任务 12][sybase_190 - LAB_DB_6] - Table user_function_group is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.021 - [任务 12][sybase_190 - LAB_DB_6] - Table user_function_group has been completed batch read 
[INFO ] 2025-02-14 15:05:02.023 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_lab_server 
[TRACE] 2025-02-14 15:05:02.023 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_lab_server is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.138 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_lab_server has been completed batch read 
[INFO ] 2025-02-14 15:05:02.138 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_registrable 
[TRACE] 2025-02-14 15:05:02.138 - [任务 12][sybase_190 - LAB_DB_6] - Table test_registrable is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.280 - [任务 12][sybase_190 - LAB_DB_6] - Table test_registrable has been completed batch read 
[INFO ] 2025-02-14 15:05:02.280 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: comment 
[TRACE] 2025-02-14 15:05:02.280 - [任务 12][sybase_190 - LAB_DB_6] - Table comment is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.371 - [任务 12][sybase_190 - LAB_DB_6] - Table comment has been completed batch read 
[INFO ] 2025-02-14 15:05:02.372 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_disk 
[TRACE] 2025-02-14 15:05:02.372 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_disk is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.440 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_disk has been completed batch read 
[INFO ] 2025-02-14 15:05:02.440 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_bbnk_patient 
[TRACE] 2025-02-14 15:05:02.440 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_bbnk_patient is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.507 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_bbnk_patient has been completed batch read 
[INFO ] 2025-02-14 15:05:02.507 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_list_arch 
[TRACE] 2025-02-14 15:05:02.507 - [任务 12][sybase_190 - LAB_DB_6] - Table print_list_arch is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.582 - [任务 12][sybase_190 - LAB_DB_6] - Table print_list_arch has been completed batch read 
[INFO ] 2025-02-14 15:05:02.583 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_profile 
[TRACE] 2025-02-14 15:05:02.583 - [任务 12][sybase_190 - LAB_DB_6] - Table print_profile is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.665 - [任务 12][sybase_190 - LAB_DB_6] - Table print_profile has been completed batch read 
[INFO ] 2025-02-14 15:05:02.665 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_gen_hist 
[TRACE] 2025-02-14 15:05:02.665 - [任务 12][sybase_190 - LAB_DB_6] - Table report_gen_hist is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.784 - [任务 12][sybase_190 - LAB_DB_6] - Table report_gen_hist has been completed batch read 
[INFO ] 2025-02-14 15:05:02.785 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_BTS_product_upd 
[TRACE] 2025-02-14 15:05:02.785 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_BTS_product_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.876 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_BTS_product_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:02.876 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_map_master_OLD 
[TRACE] 2025-02-14 15:05:02.876 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_master_OLD is going to be initial synced 
[INFO ] 2025-02-14 15:05:02.956 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map_master_OLD has been completed batch read 
[INFO ] 2025-02-14 15:05:02.956 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: adm_test_reference_supp 
[TRACE] 2025-02-14 15:05:02.956 - [任务 12][sybase_190 - LAB_DB_6] - Table adm_test_reference_supp is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.013 - [任务 12][sybase_190 - LAB_DB_6] - Table adm_test_reference_supp has been completed batch read 
[INFO ] 2025-02-14 15:05:03.013 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_batch 
[TRACE] 2025-02-14 15:05:03.014 - [任务 12][sybase_190 - LAB_DB_6] - Table report_batch is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.123 - [任务 12][sybase_190 - LAB_DB_6] - Table report_batch has been completed batch read 
[INFO ] 2025-02-14 15:05:03.123 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: cras_transaction 
[TRACE] 2025-02-14 15:05:03.123 - [任务 12][sybase_190 - LAB_DB_6] - Table cras_transaction is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.258 - [任务 12][sybase_190 - LAB_DB_6] - Table cras_transaction has been completed batch read 
[INFO ] 2025-02-14 15:05:03.258 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_reference_supp_upd 
[TRACE] 2025-02-14 15:05:03.259 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference_supp_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.332 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference_supp_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:03.332 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rs_mat_status 
[TRACE] 2025-02-14 15:05:03.332 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_mat_status is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.422 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_mat_status has been completed batch read 
[INFO ] 2025-02-14 15:05:03.422 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rrs_channel 
[TRACE] 2025-02-14 15:05:03.422 - [任务 12][sybase_190 - LAB_DB_6] - Table rrs_channel is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.480 - [任务 12][sybase_190 - LAB_DB_6] - Table rrs_channel has been completed batch read 
[INFO ] 2025-02-14 15:05:03.480 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_crp 
[TRACE] 2025-02-14 15:05:03.480 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_crp is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.585 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_crp has been completed batch read 
[INFO ] 2025-02-14 15:05:03.585 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_user_option 
[TRACE] 2025-02-14 15:05:03.586 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_user_option is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.647 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_user_option has been completed batch read 
[INFO ] 2025-02-14 15:05:03.647 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_antibody_rule 
[TRACE] 2025-02-14 15:05:03.647 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_antibody_rule is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.713 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_antibody_rule has been completed batch read 
[INFO ] 2025-02-14 15:05:03.713 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_prev_tmp 
[TRACE] 2025-02-14 15:05:03.713 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_prev_tmp is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.771 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_prev_tmp has been completed batch read 
[INFO ] 2025-02-14 15:05:03.771 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pid_lab_checked 
[TRACE] 2025-02-14 15:05:03.771 - [任务 12][sybase_190 - LAB_DB_6] - Table pid_lab_checked is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.833 - [任务 12][sybase_190 - LAB_DB_6] - Table pid_lab_checked has been completed batch read 
[INFO ] 2025-02-14 15:05:03.833 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: public_holiday 
[TRACE] 2025-02-14 15:05:03.833 - [任务 12][sybase_190 - LAB_DB_6] - Table public_holiday is going to be initial synced 
[INFO ] 2025-02-14 15:05:03.913 - [任务 12][sybase_190 - LAB_DB_6] - Table public_holiday has been completed batch read 
[INFO ] 2025-02-14 15:05:03.913 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lab_function 
[TRACE] 2025-02-14 15:05:03.913 - [任务 12][sybase_190 - LAB_DB_6] - Table lab_function is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.014 - [任务 12][sybase_190 - LAB_DB_6] - Table lab_function has been completed batch read 
[INFO ] 2025-02-14 15:05:04.014 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_ffp 
[TRACE] 2025-02-14 15:05:04.014 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_ffp is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.142 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_ffp has been completed batch read 
[INFO ] 2025-02-14 15:05:04.142 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_option_control 
[TRACE] 2025-02-14 15:05:04.142 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_control is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.219 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_control has been completed batch read 
[INFO ] 2025-02-14 15:05:04.221 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: access_control 
[TRACE] 2025-02-14 15:05:04.221 - [任务 12][sybase_190 - LAB_DB_6] - Table access_control is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.326 - [任务 12][sybase_190 - LAB_DB_6] - Table access_control has been completed batch read 
[INFO ] 2025-02-14 15:05:04.326 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_pdf_report 
[TRACE] 2025-02-14 15:05:04.326 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_report is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.410 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_report has been completed batch read 
[INFO ] 2025-02-14 15:05:04.414 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: hres_audit 
[TRACE] 2025-02-14 15:05:04.414 - [任务 12][sybase_190 - LAB_DB_6] - Table hres_audit is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.495 - [任务 12][sybase_190 - LAB_DB_6] - Table hres_audit has been completed batch read 
[INFO ] 2025-02-14 15:05:04.496 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisrep_append_ident_list 
[TRACE] 2025-02-14 15:05:04.496 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_append_ident_list is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.581 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_append_ident_list has been completed batch read 
[INFO ] 2025-02-14 15:05:04.581 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_transaction_log 
[TRACE] 2025-02-14 15:05:04.581 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_transaction_log is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.696 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_transaction_log has been completed batch read 
[INFO ] 2025-02-14 15:05:04.696 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: gbl_tmp_testrslt 
[TRACE] 2025-02-14 15:05:04.696 - [任务 12][sybase_190 - LAB_DB_6] - Table gbl_tmp_testrslt is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.778 - [任务 12][sybase_190 - LAB_DB_6] - Table gbl_tmp_testrslt has been completed batch read 
[INFO ] 2025-02-14 15:05:04.778 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: reorg_testrslt_claim_space 
[TRACE] 2025-02-14 15:05:04.779 - [任务 12][sybase_190 - LAB_DB_6] - Table reorg_testrslt_claim_space is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.849 - [任务 12][sybase_190 - LAB_DB_6] - Table reorg_testrslt_claim_space has been completed batch read 
[INFO ] 2025-02-14 15:05:04.849 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_antibiotics 
[TRACE] 2025-02-14 15:05:04.849 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_antibiotics is going to be initial synced 
[INFO ] 2025-02-14 15:05:04.958 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_antibiotics has been completed batch read 
[INFO ] 2025-02-14 15:05:04.958 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: spell_cust_word 
[TRACE] 2025-02-14 15:05:04.958 - [任务 12][sybase_190 - LAB_DB_6] - Table spell_cust_word is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.037 - [任务 12][sybase_190 - LAB_DB_6] - Table spell_cust_word has been completed batch read 
[INFO ] 2025-02-14 15:05:05.037 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: gen_work 
[TRACE] 2025-02-14 15:05:05.037 - [任务 12][sybase_190 - LAB_DB_6] - Table gen_work is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.159 - [任务 12][sybase_190 - LAB_DB_6] - Table gen_work has been completed batch read 
[INFO ] 2025-02-14 15:05:05.160 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: usid_relation_master 
[TRACE] 2025-02-14 15:05:05.160 - [任务 12][sybase_190 - LAB_DB_6] - Table usid_relation_master is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.239 - [任务 12][sybase_190 - LAB_DB_6] - Table usid_relation_master has been completed batch read 
[INFO ] 2025-02-14 15:05:05.239 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: derived_test_upd 
[TRACE] 2025-02-14 15:05:05.239 - [任务 12][sybase_190 - LAB_DB_6] - Table derived_test_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.347 - [任务 12][sybase_190 - LAB_DB_6] - Table derived_test_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:05.348 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_cryo 
[TRACE] 2025-02-14 15:05:05.348 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_cryo is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.421 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_cryo has been completed batch read 
[INFO ] 2025-02-14 15:05:05.421 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request_audit 
[TRACE] 2025-02-14 15:05:05.421 - [任务 12][sybase_190 - LAB_DB_6] - Table request_audit is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.518 - [任务 12][sybase_190 - LAB_DB_6] - Table request_audit has been completed batch read 
[INFO ] 2025-02-14 15:05:05.518 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_entity_group 
[TRACE] 2025-02-14 15:05:05.518 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_entity_group is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.614 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_entity_group has been completed batch read 
[INFO ] 2025-02-14 15:05:05.614 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_organism 
[TRACE] 2025-02-14 15:05:05.614 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_organism is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.702 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_organism has been completed batch read 
[INFO ] 2025-02-14 15:05:05.702 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: wrk_request_copy_hist_loc 
[TRACE] 2025-02-14 15:05:05.702 - [任务 12][sybase_190 - LAB_DB_6] - Table wrk_request_copy_hist_loc is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.799 - [任务 12][sybase_190 - LAB_DB_6] - Table wrk_request_copy_hist_loc has been completed batch read 
[INFO ] 2025-02-14 15:05:05.799 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_label_setup_20150504 
[TRACE] 2025-02-14 15:05:05.799 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_label_setup_20150504 is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.900 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_label_setup_20150504 has been completed batch read 
[INFO ] 2025-02-14 15:05:05.901 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_ot_trans_list 
[TRACE] 2025-02-14 15:05:05.901 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_trans_list is going to be initial synced 
[INFO ] 2025-02-14 15:05:05.989 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_ot_trans_list has been completed batch read 
[INFO ] 2025-02-14 15:05:05.989 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_order_arch 
[TRACE] 2025-02-14 15:05:05.989 - [任务 12][sybase_190 - LAB_DB_6] - Table print_order_arch is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.109 - [任务 12][sybase_190 - LAB_DB_6] - Table print_order_arch has been completed batch read 
[INFO ] 2025-02-14 15:05:06.109 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: qc_location 
[TRACE] 2025-02-14 15:05:06.109 - [任务 12][sybase_190 - LAB_DB_6] - Table qc_location is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.201 - [任务 12][sybase_190 - LAB_DB_6] - Table qc_location has been completed batch read 
[INFO ] 2025-02-14 15:05:06.201 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_testrslt 
[TRACE] 2025-02-14 15:05:06.201 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_testrslt is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.277 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_testrslt has been completed batch read 
[INFO ] 2025-02-14 15:05:06.277 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_special_blood_gcd 
[TRACE] 2025-02-14 15:05:06.277 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.361 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd has been completed batch read 
[INFO ] 2025-02-14 15:05:06.361 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_hsct_donor 
[TRACE] 2025-02-14 15:05:06.361 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_hsct_donor is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.436 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_hsct_donor has been completed batch read 
[INFO ] 2025-02-14 15:05:06.436 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: base_doc_test 
[TRACE] 2025-02-14 15:05:06.436 - [任务 12][sybase_190 - LAB_DB_6] - Table base_doc_test is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.502 - [任务 12][sybase_190 - LAB_DB_6] - Table base_doc_test has been completed batch read 
[INFO ] 2025-02-14 15:05:06.503 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pprofile_style 
[TRACE] 2025-02-14 15:05:06.503 - [任务 12][sybase_190 - LAB_DB_6] - Table pprofile_style is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.581 - [任务 12][sybase_190 - LAB_DB_6] - Table pprofile_style has been completed batch read 
[INFO ] 2025-02-14 15:05:06.581 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_reqno 
[TRACE] 2025-02-14 15:05:06.581 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_reqno is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.651 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_reqno has been completed batch read 
[INFO ] 2025-02-14 15:05:06.651 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_enum_result_master 
[TRACE] 2025-02-14 15:05:06.651 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_enum_result_master is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.738 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_enum_result_master has been completed batch read 
[INFO ] 2025-02-14 15:05:06.738 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: keyword_group 
[TRACE] 2025-02-14 15:05:06.738 - [任务 12][sybase_190 - LAB_DB_6] - Table keyword_group is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.807 - [任务 12][sybase_190 - LAB_DB_6] - Table keyword_group has been completed batch read 
[INFO ] 2025-02-14 15:05:06.807 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: messagebox 
[TRACE] 2025-02-14 15:05:06.807 - [任务 12][sybase_190 - LAB_DB_6] - Table messagebox is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.889 - [任务 12][sybase_190 - LAB_DB_6] - Table messagebox has been completed batch read 
[INFO ] 2025-02-14 15:05:06.889 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_blood_OT_location 
[TRACE] 2025-02-14 15:05:06.889 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_OT_location is going to be initial synced 
[INFO ] 2025-02-14 15:05:06.983 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_OT_location has been completed batch read 
[INFO ] 2025-02-14 15:05:06.983 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_reference 
[TRACE] 2025-02-14 15:05:06.983 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.093 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference has been completed batch read 
[INFO ] 2025-02-14 15:05:07.093 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_phenotype_conv 
[TRACE] 2025-02-14 15:05:07.093 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_phenotype_conv is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.175 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_phenotype_conv has been completed batch read 
[INFO ] 2025-02-14 15:05:07.175 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_hsct_recommendation 
[TRACE] 2025-02-14 15:05:07.175 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_hsct_recommendation is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.239 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_hsct_recommendation has been completed batch read 
[INFO ] 2025-02-14 15:05:07.239 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_chargeable 
[TRACE] 2025-02-14 15:05:07.239 - [任务 12][sybase_190 - LAB_DB_6] - Table test_chargeable is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.303 - [任务 12][sybase_190 - LAB_DB_6] - Table test_chargeable has been completed batch read 
[INFO ] 2025-02-14 15:05:07.303 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: workload 
[TRACE] 2025-02-14 15:05:07.303 - [任务 12][sybase_190 - LAB_DB_6] - Table workload is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.396 - [任务 12][sybase_190 - LAB_DB_6] - Table workload has been completed batch read 
[INFO ] 2025-02-14 15:05:07.396 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_haem_result 
[TRACE] 2025-02-14 15:05:07.396 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_haem_result is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.473 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_haem_result has been completed batch read 
[INFO ] 2025-02-14 15:05:07.473 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_order 
[TRACE] 2025-02-14 15:05:07.473 - [任务 12][sybase_190 - LAB_DB_6] - Table print_order is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.536 - [任务 12][sybase_190 - LAB_DB_6] - Table print_order has been completed batch read 
[INFO ] 2025-02-14 15:05:07.536 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: wrk_request_copy_hist 
[TRACE] 2025-02-14 15:05:07.536 - [任务 12][sybase_190 - LAB_DB_6] - Table wrk_request_copy_hist is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.626 - [任务 12][sybase_190 - LAB_DB_6] - Table wrk_request_copy_hist has been completed batch read 
[INFO ] 2025-02-14 15:05:07.626 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_plt 
[TRACE] 2025-02-14 15:05:07.626 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_plt is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.698 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_plt has been completed batch read 
[INFO ] 2025-02-14 15:05:07.698 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: menu 
[TRACE] 2025-02-14 15:05:07.698 - [任务 12][sybase_190 - LAB_DB_6] - Table menu is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.760 - [任务 12][sybase_190 - LAB_DB_6] - Table menu has been completed batch read 
[INFO ] 2025-02-14 15:05:07.760 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pprofile_style_upd 
[TRACE] 2025-02-14 15:05:07.760 - [任务 12][sybase_190 - LAB_DB_6] - Table pprofile_style_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.838 - [任务 12][sybase_190 - LAB_DB_6] - Table pprofile_style_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:07.838 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: mis_table 
[TRACE] 2025-02-14 15:05:07.838 - [任务 12][sybase_190 - LAB_DB_6] - Table mis_table is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.905 - [任务 12][sybase_190 - LAB_DB_6] - Table mis_table has been completed batch read 
[INFO ] 2025-02-14 15:05:07.905 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: Z_20151215_print_audit 
[TRACE] 2025-02-14 15:05:07.905 - [任务 12][sybase_190 - LAB_DB_6] - Table Z_20151215_print_audit is going to be initial synced 
[INFO ] 2025-02-14 15:05:07.964 - [任务 12][sybase_190 - LAB_DB_6] - Table Z_20151215_print_audit has been completed batch read 
[INFO ] 2025-02-14 15:05:07.964 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: base_doc_analyser 
[TRACE] 2025-02-14 15:05:07.964 - [任务 12][sybase_190 - LAB_DB_6] - Table base_doc_analyser is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.038 - [任务 12][sybase_190 - LAB_DB_6] - Table base_doc_analyser has been completed batch read 
[INFO ] 2025-02-14 15:05:08.039 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: cras_location 
[TRACE] 2025-02-14 15:05:08.039 - [任务 12][sybase_190 - LAB_DB_6] - Table cras_location is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.146 - [任务 12][sybase_190 - LAB_DB_6] - Table cras_location has been completed batch read 
[INFO ] 2025-02-14 15:05:08.147 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: corp_test_master 
[TRACE] 2025-02-14 15:05:08.147 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_test_master is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.240 - [任务 12][sybase_190 - LAB_DB_6] - Table corp_test_master has been completed batch read 
[INFO ] 2025-02-14 15:05:08.240 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_option_upd 
[TRACE] 2025-02-14 15:05:08.240 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.319 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_option_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:08.319 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: reqlist 
[TRACE] 2025-02-14 15:05:08.319 - [任务 12][sybase_190 - LAB_DB_6] - Table reqlist is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.407 - [任务 12][sybase_190 - LAB_DB_6] - Table reqlist has been completed batch read 
[INFO ] 2025-02-14 15:05:08.407 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_profile_upd 
[TRACE] 2025-02-14 15:05:08.407 - [任务 12][sybase_190 - LAB_DB_6] - Table print_profile_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.510 - [任务 12][sybase_190 - LAB_DB_6] - Table print_profile_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:08.510 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: crs_wrk_level_try 
[TRACE] 2025-02-14 15:05:08.510 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_wrk_level_try is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.633 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_wrk_level_try has been completed batch read 
[INFO ] 2025-02-14 15:05:08.633 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rrs_channel_upd 
[TRACE] 2025-02-14 15:05:08.633 - [任务 12][sybase_190 - LAB_DB_6] - Table rrs_channel_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.728 - [任务 12][sybase_190 - LAB_DB_6] - Table rrs_channel_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:08.728 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request_archive 
[TRACE] 2025-02-14 15:05:08.728 - [任务 12][sybase_190 - LAB_DB_6] - Table request_archive is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.834 - [任务 12][sybase_190 - LAB_DB_6] - Table request_archive has been completed batch read 
[INFO ] 2025-02-14 15:05:08.834 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_bc 
[TRACE] 2025-02-14 15:05:08.834 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_bc is going to be initial synced 
[INFO ] 2025-02-14 15:05:08.942 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_bc has been completed batch read 
[INFO ] 2025-02-14 15:05:08.942 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pbcatcol 
[TRACE] 2025-02-14 15:05:08.942 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatcol is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.024 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatcol has been completed batch read 
[INFO ] 2025-02-14 15:05:09.024 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_testrslt_wkt_ORIG 
[TRACE] 2025-02-14 15:05:09.024 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_testrslt_wkt_ORIG is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.168 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_testrslt_wkt_ORIG has been completed batch read 
[INFO ] 2025-02-14 15:05:09.168 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: Z_20151215_epr_missing_pdf 
[TRACE] 2025-02-14 15:05:09.168 - [任务 12][sybase_190 - LAB_DB_6] - Table Z_20151215_epr_missing_pdf is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.254 - [任务 12][sybase_190 - LAB_DB_6] - Table Z_20151215_epr_missing_pdf has been completed batch read 
[INFO ] 2025-02-14 15:05:09.254 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: chk_epr_expire_blood_kenneth 
[TRACE] 2025-02-14 15:05:09.254 - [任务 12][sybase_190 - LAB_DB_6] - Table chk_epr_expire_blood_kenneth is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.344 - [任务 12][sybase_190 - LAB_DB_6] - Table chk_epr_expire_blood_kenneth has been completed batch read 
[INFO ] 2025-02-14 15:05:09.345 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_map 
[TRACE] 2025-02-14 15:05:09.345 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.443 - [任务 12][sybase_190 - LAB_DB_6] - Table test_map has been completed batch read 
[INFO ] 2025-02-14 15:05:09.443 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_special_blood_gcd_mapping 
[TRACE] 2025-02-14 15:05:09.443 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd_mapping is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.534 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_special_blood_gcd_mapping has been completed batch read 
[INFO ] 2025-02-14 15:05:09.535 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: batch_details 
[TRACE] 2025-02-14 15:05:09.535 - [任务 12][sybase_190 - LAB_DB_6] - Table batch_details is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.612 - [任务 12][sybase_190 - LAB_DB_6] - Table batch_details has been completed batch read 
[INFO ] 2025-02-14 15:05:09.612 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: file_audit 
[TRACE] 2025-02-14 15:05:09.612 - [任务 12][sybase_190 - LAB_DB_6] - Table file_audit is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.702 - [任务 12][sybase_190 - LAB_DB_6] - Table file_audit has been completed batch read 
[INFO ] 2025-02-14 15:05:09.703 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: office 
[TRACE] 2025-02-14 15:05:09.703 - [任务 12][sybase_190 - LAB_DB_6] - Table office is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.810 - [任务 12][sybase_190 - LAB_DB_6] - Table office has been completed batch read 
[INFO ] 2025-02-14 15:05:09.811 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_lis_transfer_control 
[TRACE] 2025-02-14 15:05:09.811 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_lis_transfer_control is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.875 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_lis_transfer_control has been completed batch read 
[INFO ] 2025-02-14 15:05:09.876 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_hsct 
[TRACE] 2025-02-14 15:05:09.876 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_hsct is going to be initial synced 
[INFO ] 2025-02-14 15:05:09.958 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_hsct has been completed batch read 
[INFO ] 2025-02-14 15:05:09.958 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_antibody_reagent 
[TRACE] 2025-02-14 15:05:09.958 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_antibody_reagent is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.048 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_antibody_reagent has been completed batch read 
[INFO ] 2025-02-14 15:05:10.048 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_blood_equivalent_category 
[TRACE] 2025-02-14 15:05:10.049 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_equivalent_category is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.137 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_equivalent_category has been completed batch read 
[INFO ] 2025-02-14 15:05:10.137 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: print_def 
[TRACE] 2025-02-14 15:05:10.137 - [任务 12][sybase_190 - LAB_DB_6] - Table print_def is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.214 - [任务 12][sybase_190 - LAB_DB_6] - Table print_def has been completed batch read 
[INFO ] 2025-02-14 15:05:10.215 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_check 
[TRACE] 2025-02-14 15:05:10.215 - [任务 12][sybase_190 - LAB_DB_6] - Table test_check is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.286 - [任务 12][sybase_190 - LAB_DB_6] - Table test_check has been completed batch read 
[INFO ] 2025-02-14 15:05:10.286 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_wand_batch_seq 
[TRACE] 2025-02-14 15:05:10.286 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_wand_batch_seq is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.459 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_wand_batch_seq has been completed batch read 
[INFO ] 2025-02-14 15:05:10.459 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: mis_table_link 
[TRACE] 2025-02-14 15:05:10.459 - [任务 12][sybase_190 - LAB_DB_6] - Table mis_table_link is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.459 - [任务 12][sybase_190 - LAB_DB_6] - Table mis_table_link has been completed batch read 
[INFO ] 2025-02-14 15:05:10.459 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request_profile_detail 
[TRACE] 2025-02-14 15:05:10.459 - [任务 12][sybase_190 - LAB_DB_6] - Table request_profile_detail is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.551 - [任务 12][sybase_190 - LAB_DB_6] - Table request_profile_detail has been completed batch read 
[INFO ] 2025-02-14 15:05:10.551 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_update_trans 
[TRACE] 2025-02-14 15:05:10.551 - [任务 12][sybase_190 - LAB_DB_6] - Table report_update_trans is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.654 - [任务 12][sybase_190 - LAB_DB_6] - Table report_update_trans has been completed batch read 
[INFO ] 2025-02-14 15:05:10.654 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: gbl_tmp_corp_test 
[TRACE] 2025-02-14 15:05:10.654 - [任务 12][sybase_190 - LAB_DB_6] - Table gbl_tmp_corp_test is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.768 - [任务 12][sybase_190 - LAB_DB_6] - Table gbl_tmp_corp_test has been completed batch read 
[INFO ] 2025-02-14 15:05:10.768 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: base_doc 
[TRACE] 2025-02-14 15:05:10.768 - [任务 12][sybase_190 - LAB_DB_6] - Table base_doc is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.875 - [任务 12][sybase_190 - LAB_DB_6] - Table base_doc has been completed batch read 
[INFO ] 2025-02-14 15:05:10.875 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: poct_location 
[TRACE] 2025-02-14 15:05:10.875 - [任务 12][sybase_190 - LAB_DB_6] - Table poct_location is going to be initial synced 
[INFO ] 2025-02-14 15:05:10.997 - [任务 12][sybase_190 - LAB_DB_6] - Table poct_location has been completed batch read 
[INFO ] 2025-02-14 15:05:10.998 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_check_upd 
[TRACE] 2025-02-14 15:05:10.998 - [任务 12][sybase_190 - LAB_DB_6] - Table test_check_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.153 - [任务 12][sybase_190 - LAB_DB_6] - Table test_check_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:11.153 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_BTS_inv 
[TRACE] 2025-02-14 15:05:11.153 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_BTS_inv is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.273 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_BTS_inv has been completed batch read 
[INFO ] 2025-02-14 15:05:11.273 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: default_print_queue 
[TRACE] 2025-02-14 15:05:11.274 - [任务 12][sybase_190 - LAB_DB_6] - Table default_print_queue is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.390 - [任务 12][sybase_190 - LAB_DB_6] - Table default_print_queue has been completed batch read 
[INFO ] 2025-02-14 15:05:11.390 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: reorg_testrslt_claim_list 
[TRACE] 2025-02-14 15:05:11.390 - [任务 12][sybase_190 - LAB_DB_6] - Table reorg_testrslt_claim_list is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.487 - [任务 12][sybase_190 - LAB_DB_6] - Table reorg_testrslt_claim_list has been completed batch read 
[INFO ] 2025-02-14 15:05:11.487 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testrslt_audit_hist 
[TRACE] 2025-02-14 15:05:11.487 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_audit_hist is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.577 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_audit_hist has been completed batch read 
[INFO ] 2025-02-14 15:05:11.577 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_lis_report 
[TRACE] 2025-02-14 15:05:11.577 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_lis_report is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.686 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_lis_report has been completed batch read 
[INFO ] 2025-02-14 15:05:11.686 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_style_upd 
[TRACE] 2025-02-14 15:05:11.686 - [任务 12][sybase_190 - LAB_DB_6] - Table report_style_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.779 - [任务 12][sybase_190 - LAB_DB_6] - Table report_style_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:11.780 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_igg_rule 
[TRACE] 2025-02-14 15:05:11.780 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_igg_rule is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.880 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_igg_rule has been completed batch read 
[INFO ] 2025-02-14 15:05:11.881 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_exception 
[TRACE] 2025-02-14 15:05:11.881 - [任务 12][sybase_190 - LAB_DB_6] - Table report_exception is going to be initial synced 
[INFO ] 2025-02-14 15:05:11.973 - [任务 12][sybase_190 - LAB_DB_6] - Table report_exception has been completed batch read 
[INFO ] 2025-02-14 15:05:11.973 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pbcatedt 
[TRACE] 2025-02-14 15:05:11.973 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatedt is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.083 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcatedt has been completed batch read 
[INFO ] 2025-02-14 15:05:12.083 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: specimen_tracking_log 
[TRACE] 2025-02-14 15:05:12.083 - [任务 12][sybase_190 - LAB_DB_6] - Table specimen_tracking_log is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.182 - [任务 12][sybase_190 - LAB_DB_6] - Table specimen_tracking_log has been completed batch read 
[INFO ] 2025-02-14 15:05:12.182 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pbcattbl 
[TRACE] 2025-02-14 15:05:12.182 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcattbl is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.290 - [任务 12][sybase_190 - LAB_DB_6] - Table pbcattbl has been completed batch read 
[INFO ] 2025-02-14 15:05:12.291 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: hres_audit_arch 
[TRACE] 2025-02-14 15:05:12.291 - [任务 12][sybase_190 - LAB_DB_6] - Table hres_audit_arch is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.401 - [任务 12][sybase_190 - LAB_DB_6] - Table hres_audit_arch has been completed batch read 
[INFO ] 2025-02-14 15:05:12.401 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_reqno_list 
[TRACE] 2025-02-14 15:05:12.401 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_reqno_list is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.517 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_reqno_list has been completed batch read 
[INFO ] 2025-02-14 15:05:12.517 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: QC_Sample_Map_upd 
[TRACE] 2025-02-14 15:05:12.517 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_Sample_Map_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.594 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_Sample_Map_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:12.594 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: message_dict 
[TRACE] 2025-02-14 15:05:12.594 - [任务 12][sybase_190 - LAB_DB_6] - Table message_dict is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.702 - [任务 12][sybase_190 - LAB_DB_6] - Table message_dict has been completed batch read 
[INFO ] 2025-02-14 15:05:12.702 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_blood_OT_margin 
[TRACE] 2025-02-14 15:05:12.702 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_OT_margin is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.800 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_OT_margin has been completed batch read 
[INFO ] 2025-02-14 15:05:12.800 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: QC_range 
[TRACE] 2025-02-14 15:05:12.800 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_range is going to be initial synced 
[INFO ] 2025-02-14 15:05:12.915 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_range has been completed batch read 
[INFO ] 2025-02-14 15:05:12.916 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: printqueue 
[TRACE] 2025-02-14 15:05:12.916 - [任务 12][sybase_190 - LAB_DB_6] - Table printqueue is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.030 - [任务 12][sybase_190 - LAB_DB_6] - Table printqueue has been completed batch read 
[INFO ] 2025-02-14 15:05:13.030 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request 
[TRACE] 2025-02-14 15:05:13.030 - [任务 12][sybase_190 - LAB_DB_6] - Table request is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.220 - [任务 12][sybase_190 - LAB_DB_6] - Table request has been completed batch read 
[INFO ] 2025-02-14 15:05:13.220 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: signout_link 
[TRACE] 2025-02-14 15:05:13.220 - [任务 12][sybase_190 - LAB_DB_6] - Table signout_link is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.377 - [任务 12][sybase_190 - LAB_DB_6] - Table signout_link has been completed batch read 
[INFO ] 2025-02-14 15:05:13.377 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_prev 
[TRACE] 2025-02-14 15:05:13.378 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_prev is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.506 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_prev has been completed batch read 
[INFO ] 2025-02-14 15:05:13.506 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: z_ccc_big5 
[TRACE] 2025-02-14 15:05:13.506 - [任务 12][sybase_190 - LAB_DB_6] - Table z_ccc_big5 is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.616 - [任务 12][sybase_190 - LAB_DB_6] - Table z_ccc_big5 has been completed batch read 
[INFO ] 2025-02-14 15:05:13.616 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: pdf_order 
[TRACE] 2025-02-14 15:05:13.616 - [任务 12][sybase_190 - LAB_DB_6] - Table pdf_order is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.726 - [任务 12][sybase_190 - LAB_DB_6] - Table pdf_order has been completed batch read 
[INFO ] 2025-02-14 15:05:13.726 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: worksheet_property 
[TRACE] 2025-02-14 15:05:13.726 - [任务 12][sybase_190 - LAB_DB_6] - Table worksheet_property is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.829 - [任务 12][sybase_190 - LAB_DB_6] - Table worksheet_property has been completed batch read 
[INFO ] 2025-02-14 15:05:13.829 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisrep_tab_exclude_list 
[TRACE] 2025-02-14 15:05:13.829 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_tab_exclude_list is going to be initial synced 
[INFO ] 2025-02-14 15:05:13.919 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_tab_exclude_list has been completed batch read 
[INFO ] 2025-02-14 15:05:13.919 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_blood_inv_dorm 
[TRACE] 2025-02-14 15:05:13.919 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_inv_dorm is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.018 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_blood_inv_dorm has been completed batch read 
[INFO ] 2025-02-14 15:05:14.018 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_patient_tfr 
[TRACE] 2025-02-14 15:05:14.018 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_patient_tfr is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.143 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_patient_tfr has been completed batch read 
[INFO ] 2025-02-14 15:05:14.143 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_enquiry_cache 
[TRACE] 2025-02-14 15:05:14.143 - [任务 12][sybase_190 - LAB_DB_6] - Table report_enquiry_cache is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.246 - [任务 12][sybase_190 - LAB_DB_6] - Table report_enquiry_cache has been completed batch read 
[INFO ] 2025-02-14 15:05:14.246 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_relation 
[TRACE] 2025-02-14 15:05:14.247 - [任务 12][sybase_190 - LAB_DB_6] - Table test_relation is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.332 - [任务 12][sybase_190 - LAB_DB_6] - Table test_relation has been completed batch read 
[INFO ] 2025-02-14 15:05:14.332 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: QC_type 
[TRACE] 2025-02-14 15:05:14.332 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_type is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.430 - [任务 12][sybase_190 - LAB_DB_6] - Table QC_type has been completed batch read 
[INFO ] 2025-02-14 15:05:14.430 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: check_point_tws 
[TRACE] 2025-02-14 15:05:14.430 - [任务 12][sybase_190 - LAB_DB_6] - Table check_point_tws is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.548 - [任务 12][sybase_190 - LAB_DB_6] - Table check_point_tws has been completed batch read 
[INFO ] 2025-02-14 15:05:14.548 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: hospital 
[TRACE] 2025-02-14 15:05:14.548 - [任务 12][sybase_190 - LAB_DB_6] - Table hospital is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.634 - [任务 12][sybase_190 - LAB_DB_6] - Table hospital has been completed batch read 
[INFO ] 2025-02-14 15:05:14.635 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: reprint_content 
[TRACE] 2025-02-14 15:05:14.635 - [任务 12][sybase_190 - LAB_DB_6] - Table reprint_content is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.746 - [任务 12][sybase_190 - LAB_DB_6] - Table reprint_content has been completed batch read 
[INFO ] 2025-02-14 15:05:14.746 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: work_table 
[TRACE] 2025-02-14 15:05:14.746 - [任务 12][sybase_190 - LAB_DB_6] - Table work_table is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.810 - [任务 12][sybase_190 - LAB_DB_6] - Table work_table has been completed batch read 
[INFO ] 2025-02-14 15:05:14.810 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: overdue_link 
[TRACE] 2025-02-14 15:05:14.810 - [任务 12][sybase_190 - LAB_DB_6] - Table overdue_link is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.879 - [任务 12][sybase_190 - LAB_DB_6] - Table overdue_link has been completed batch read 
[INFO ] 2025-02-14 15:05:14.879 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lis_lab_option 
[TRACE] 2025-02-14 15:05:14.879 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_lab_option is going to be initial synced 
[INFO ] 2025-02-14 15:05:14.949 - [任务 12][sybase_190 - LAB_DB_6] - Table lis_lab_option has been completed batch read 
[INFO ] 2025-02-14 15:05:14.949 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_action_upd 
[TRACE] 2025-02-14 15:05:14.949 - [任务 12][sybase_190 - LAB_DB_6] - Table test_action_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.018 - [任务 12][sybase_190 - LAB_DB_6] - Table test_action_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:15.018 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_corp_blood_category_status 
[TRACE] 2025-02-14 15:05:15.018 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_corp_blood_category_status is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.110 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_corp_blood_category_status has been completed batch read 
[INFO ] 2025-02-14 15:05:15.111 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_label_setup 
[TRACE] 2025-02-14 15:05:15.111 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_label_setup is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.201 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_label_setup has been completed batch read 
[INFO ] 2025-02-14 15:05:15.201 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: labuser_upd 
[TRACE] 2025-02-14 15:05:15.201 - [任务 12][sybase_190 - LAB_DB_6] - Table labuser_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.289 - [任务 12][sybase_190 - LAB_DB_6] - Table labuser_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:15.289 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: trans_testrslt_wkt 
[TRACE] 2025-02-14 15:05:15.289 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_testrslt_wkt is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.364 - [任务 12][sybase_190 - LAB_DB_6] - Table trans_testrslt_wkt has been completed batch read 
[INFO ] 2025-02-14 15:05:15.364 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: zzz_spec_ctr 
[TRACE] 2025-02-14 15:05:15.364 - [任务 12][sybase_190 - LAB_DB_6] - Table zzz_spec_ctr is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.429 - [任务 12][sybase_190 - LAB_DB_6] - Table zzz_spec_ctr has been completed batch read 
[INFO ] 2025-02-14 15:05:15.430 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_trans_rule_wb 
[TRACE] 2025-02-14 15:05:15.430 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_wb is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.507 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_trans_rule_wb has been completed batch read 
[INFO ] 2025-02-14 15:05:15.507 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: analyser_profile 
[TRACE] 2025-02-14 15:05:15.507 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser_profile is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.589 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser_profile has been completed batch read 
[INFO ] 2025-02-14 15:05:15.589 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testchk_def 
[TRACE] 2025-02-14 15:05:15.589 - [任务 12][sybase_190 - LAB_DB_6] - Table testchk_def is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.702 - [任务 12][sybase_190 - LAB_DB_6] - Table testchk_def has been completed batch read 
[INFO ] 2025-02-14 15:05:15.702 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rs_threads 
[TRACE] 2025-02-14 15:05:15.702 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_threads is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.811 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_threads has been completed batch read 
[INFO ] 2025-02-14 15:05:15.812 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: testrslt_supplement 
[TRACE] 2025-02-14 15:05:15.812 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_supplement is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.908 - [任务 12][sybase_190 - LAB_DB_6] - Table testrslt_supplement has been completed batch read 
[INFO ] 2025-02-14 15:05:15.908 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: workbench_upd 
[TRACE] 2025-02-14 15:05:15.908 - [任务 12][sybase_190 - LAB_DB_6] - Table workbench_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:15.995 - [任务 12][sybase_190 - LAB_DB_6] - Table workbench_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:15.996 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: crs_bb_request_inv 
[TRACE] 2025-02-14 15:05:15.996 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_bb_request_inv is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.100 - [任务 12][sybase_190 - LAB_DB_6] - Table crs_bb_request_inv has been completed batch read 
[INFO ] 2025-02-14 15:05:16.100 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisrep_build_uidx_list 
[TRACE] 2025-02-14 15:05:16.100 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_build_uidx_list is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.263 - [任务 12][sybase_190 - LAB_DB_6] - Table lisrep_build_uidx_list has been completed batch read 
[INFO ] 2025-02-14 15:05:16.263 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: report_setup 
[TRACE] 2025-02-14 15:05:16.263 - [任务 12][sybase_190 - LAB_DB_6] - Table report_setup is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.383 - [任务 12][sybase_190 - LAB_DB_6] - Table report_setup has been completed batch read 
[INFO ] 2025-02-14 15:05:16.383 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_verify_inv 
[TRACE] 2025-02-14 15:05:16.383 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_verify_inv is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.500 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_verify_inv has been completed batch read 
[INFO ] 2025-02-14 15:05:16.500 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: ccc_big5_130813_OLD 
[TRACE] 2025-02-14 15:05:16.500 - [任务 12][sybase_190 - LAB_DB_6] - Table ccc_big5_130813_OLD is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.595 - [任务 12][sybase_190 - LAB_DB_6] - Table ccc_big5_130813_OLD has been completed batch read 
[INFO ] 2025-02-14 15:05:16.595 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: epr_pdf_max 
[TRACE] 2025-02-14 15:05:16.595 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_max is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.685 - [任务 12][sybase_190 - LAB_DB_6] - Table epr_pdf_max has been completed batch read 
[INFO ] 2025-02-14 15:05:16.688 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_color 
[TRACE] 2025-02-14 15:05:16.688 - [任务 12][sybase_190 - LAB_DB_6] - Table test_color is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.775 - [任务 12][sybase_190 - LAB_DB_6] - Table test_color has been completed batch read 
[INFO ] 2025-02-14 15:05:16.775 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: bb_phenotype_result 
[TRACE] 2025-02-14 15:05:16.775 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_phenotype_result is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.890 - [任务 12][sybase_190 - LAB_DB_6] - Table bb_phenotype_result has been completed batch read 
[INFO ] 2025-02-14 15:05:16.890 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rsltflow_upd 
[TRACE] 2025-02-14 15:05:16.890 - [任务 12][sybase_190 - LAB_DB_6] - Table rsltflow_upd is going to be initial synced 
[INFO ] 2025-02-14 15:05:16.982 - [任务 12][sybase_190 - LAB_DB_6] - Table rsltflow_upd has been completed batch read 
[INFO ] 2025-02-14 15:05:16.982 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: rs_ticket_history 
[TRACE] 2025-02-14 15:05:16.982 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_ticket_history is going to be initial synced 
[INFO ] 2025-02-14 15:05:17.110 - [任务 12][sybase_190 - LAB_DB_6] - Table rs_ticket_history has been completed batch read 
[INFO ] 2025-02-14 15:05:17.110 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: test_reference_supp 
[TRACE] 2025-02-14 15:05:17.110 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference_supp is going to be initial synced 
[INFO ] 2025-02-14 15:05:17.243 - [任务 12][sybase_190 - LAB_DB_6] - Table test_reference_supp has been completed batch read 
[INFO ] 2025-02-14 15:05:17.243 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: analyser_type 
[TRACE] 2025-02-14 15:05:17.243 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser_type is going to be initial synced 
[INFO ] 2025-02-14 15:05:17.343 - [任务 12][sybase_190 - LAB_DB_6] - Table analyser_type has been completed batch read 
[INFO ] 2025-02-14 15:05:17.344 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: adm_test_reference 
[TRACE] 2025-02-14 15:05:17.344 - [任务 12][sybase_190 - LAB_DB_6] - Table adm_test_reference is going to be initial synced 
[INFO ] 2025-02-14 15:05:17.434 - [任务 12][sybase_190 - LAB_DB_6] - Table adm_test_reference has been completed batch read 
[INFO ] 2025-02-14 15:05:17.435 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lab_spec_ctr 
[TRACE] 2025-02-14 15:05:17.435 - [任务 12][sybase_190 - LAB_DB_6] - Table lab_spec_ctr is going to be initial synced 
[INFO ] 2025-02-14 15:05:17.503 - [任务 12][sybase_190 - LAB_DB_6] - Table lab_spec_ctr has been completed batch read 
[INFO ] 2025-02-14 15:05:17.503 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: lisg_tasklist 
[TRACE] 2025-02-14 15:05:17.503 - [任务 12][sybase_190 - LAB_DB_6] - Table lisg_tasklist is going to be initial synced 
[INFO ] 2025-02-14 15:05:17.610 - [任务 12][sybase_190 - LAB_DB_6] - Table lisg_tasklist has been completed batch read 
[INFO ] 2025-02-14 15:05:17.610 - [任务 12][sybase_190 - LAB_DB_6] - Starting batch read from table: request_detail 
[TRACE] 2025-02-14 15:05:17.610 - [任务 12][sybase_190 - LAB_DB_6] - Table request_detail is going to be initial synced 
[TRACE] 2025-02-14 15:05:17.695 - [任务 12][sybase_190 - LAB_DB_6] - Query snapshot row size completed: sybase_190 - LAB_DB_6(6b709892-6eff-48d4-98dc-4865c6bcf78d) 
[INFO ] 2025-02-14 15:05:17.695 - [任务 12][sybase_190 - LAB_DB_6] - Table request_detail has been completed batch read 
[TRACE] 2025-02-14 15:05:17.695 - [任务 12][sybase_190 - LAB_DB_6] - Initial sync completed 
[INFO ] 2025-02-14 15:05:17.695 - [任务 12][sybase_190 - LAB_DB_6] - Batch read completed. 
[INFO ] 2025-02-14 15:05:17.895 - [任务 12][sybase_190 - LAB_DB_6] - Task run completed 
[WARN ] 2025-02-14 15:05:26.257 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d7052206e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d7052206f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522070, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522071, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522072, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522073, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522074, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522075, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522076, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb107abfbd6d70522077, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d70522078, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d70522079, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d7052207a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d7052207b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d7052207c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.258 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d7052207d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb117abfbd6d7052207e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d7052207f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d70522080, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d70522081, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d70522082, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d70522083, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d70522084, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb127abfbd6d70522085, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d70522086, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d70522087, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d70522088, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d70522089, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d7052208a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d7052208b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d7052208c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb137abfbd6d7052208d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d7052208e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d7052208f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d70522090, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.259 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d70522091, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d70522092, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d70522093, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d70522094, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb147abfbd6d70522095, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d70522096, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d70522097, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d70522098, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d70522099, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d7052209a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d7052209b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d7052209c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d7052209d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb157abfbd6d7052209e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d7052209f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.260 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb167abfbd6d705220a8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220a9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220aa, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220ab, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220ac, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220ad, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220ae, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220af, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220b0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220b1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb177abfbd6d705220b2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.261 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220b9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220ba, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220bb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb187abfbd6d705220bc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220bd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220be, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220bf, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb197abfbd6d705220c6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220c7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220c8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220c9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220ca, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.262 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220cb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220cc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220cd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220ce, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220cf, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220d0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1a7abfbd6d705220d1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220d9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220da, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220db, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1b7abfbd6d705220dc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.263 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220dd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220de, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220df, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1c7abfbd6d705220e6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220e7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220e8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220e9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220ea, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220eb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220ec, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220ed, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.264 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220ee, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1d7abfbd6d705220ef, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f0, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f1, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f2, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220f9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1e7abfbd6d705220fa, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d705220fb, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d705220fc, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d705220fd, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d705220fe, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d705220ff, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522100, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.265 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522101, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522102, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522103, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522104, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522105, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb1f7abfbd6d70522106, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d70522107, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d70522108, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d70522109, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d7052210a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d7052210b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d7052210c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d7052210d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d7052210e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d7052210f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d70522110, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb207abfbd6d70522111, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522112, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.266 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522113, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522114, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522115, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522116, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522117, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522118, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d70522119, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d7052211a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d7052211b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb217abfbd6d7052211c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d7052211d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d7052211e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d7052211f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522120, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522121, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522122, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522123, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522124, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.267 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522125, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522126, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522127, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb227abfbd6d70522128, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522129, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d7052212a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d7052212b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d7052212c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d7052212d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d7052212e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d7052212f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522130, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522131, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522132, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522133, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522134, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.268 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb237abfbd6d70522135, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.770 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d70522136, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.770 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d70522137, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.770 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d70522138, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.770 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d70522139, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.771 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d7052213a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.771 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d7052213b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.771 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d7052213c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.771 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d7052213d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d7052213e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb247abfbd6d7052213f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522140, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522141, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522142, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522143, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.772 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522144, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.773 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522145, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.773 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522146, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.773 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522147, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.773 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522148, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.773 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d70522149, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb257abfbd6d7052214a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d7052214b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d7052214c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d7052214d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d7052214e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d7052214f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.774 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d70522150, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.775 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d70522151, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.775 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d70522152, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.775 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d70522153, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.775 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d70522154, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.775 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb267abfbd6d70522155, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.775 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d70522156, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d70522157, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d70522158, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d70522159, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d7052215a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d7052215b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d7052215c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.776 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d7052215d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb277abfbd6d7052215e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d7052215f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522160, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522161, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522162, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522163, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522164, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.777 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522165, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522166, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb287abfbd6d70522167, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d70522168, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d70522169, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d7052216a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d7052216b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.778 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d7052216c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d7052216d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d7052216e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb297abfbd6d7052216f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522170, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522171, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522172, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.779 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522173, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522174, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522175, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522176, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522177, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522178, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d70522179, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.780 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2a7abfbd6d7052217a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d7052217b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d7052217c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d7052217d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d7052217e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d7052217f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522180, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522181, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.781 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522182, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522183, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522184, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522185, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2b7abfbd6d70522186, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d70522187, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d70522188, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d70522189, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.782 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d7052218a, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d7052218b, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d7052218c, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d7052218d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d7052218e, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2c7abfbd6d7052218f, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522190, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.783 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522191, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.784 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522192, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.784 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522193, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.784 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522194, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.784 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522195, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-02-14 15:05:26.784 - [任务 12][pg_hdtest - SP6_lab] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67aeeb2d7abfbd6d70522196, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[6b709892-6eff-48d4-98dc-4865c6bcf78d], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-02-14 15:05:26.891 - [任务 12][sybase_190 - LAB_DB_6] - Node sybase_190 - LAB_DB_6[6b709892-6eff-48d4-98dc-4865c6bcf78d] running status set to false 
[TRACE] 2025-02-14 15:05:26.891 - [任务 12][pg_hdtest - SP6_lab] - Node pg_hdtest - SP6_lab[d0e0bad5-2d1c-42d5-9901-2be97342c387] running status set to false 
[TRACE] 2025-02-14 15:05:26.899 - [任务 12][pg_hdtest - SP6_lab] - PDK connector node stopped: HazelcastTargetPdkDataNode_d0e0bad5-2d1c-42d5-9901-2be97342c387_1739516687122 
[TRACE] 2025-02-14 15:05:26.899 - [任务 12][pg_hdtest - SP6_lab] - PDK connector node released: HazelcastTargetPdkDataNode_d0e0bad5-2d1c-42d5-9901-2be97342c387_1739516687122 
[TRACE] 2025-02-14 15:05:26.899 - [任务 12][pg_hdtest - SP6_lab] - Node pg_hdtest - SP6_lab[d0e0bad5-2d1c-42d5-9901-2be97342c387] schema data cleaned 
[TRACE] 2025-02-14 15:05:26.899 - [任务 12][pg_hdtest - SP6_lab] - Node pg_hdtest - SP6_lab[d0e0bad5-2d1c-42d5-9901-2be97342c387] monitor closed 
[TRACE] 2025-02-14 15:05:27.010 - [任务 12][pg_hdtest - SP6_lab] - Node pg_hdtest - SP6_lab[d0e0bad5-2d1c-42d5-9901-2be97342c387] close complete, cost 9 ms 
[TRACE] 2025-02-14 15:05:27.010 - [任务 12][sybase_190 - LAB_DB_6] - PDK connector node stopped: HazelcastSourcePdkDataNode_6b709892-6eff-48d4-98dc-4865c6bcf78d_1739516687162 
[TRACE] 2025-02-14 15:05:27.010 - [任务 12][sybase_190 - LAB_DB_6] - PDK connector node released: HazelcastSourcePdkDataNode_6b709892-6eff-48d4-98dc-4865c6bcf78d_1739516687162 
[TRACE] 2025-02-14 15:05:27.010 - [任务 12][sybase_190 - LAB_DB_6] - Node sybase_190 - LAB_DB_6[6b709892-6eff-48d4-98dc-4865c6bcf78d] schema data cleaned 
[TRACE] 2025-02-14 15:05:27.012 - [任务 12][sybase_190 - LAB_DB_6] - Node sybase_190 - LAB_DB_6[6b709892-6eff-48d4-98dc-4865c6bcf78d] monitor closed 
[TRACE] 2025-02-14 15:05:27.012 - [任务 12][sybase_190 - LAB_DB_6] - Node sybase_190 - LAB_DB_6[6b709892-6eff-48d4-98dc-4865c6bcf78d] close complete, cost 125 ms 
[TRACE] 2025-02-14 15:05:30.369 - [任务 12] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-14 15:05:30.370 - [任务 12] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@19ae570b 
[TRACE] 2025-02-14 15:05:30.370 - [任务 12] - Stop task milestones: 67aeea28a250d87287e29d72(任务 12)  
[TRACE] 2025-02-14 15:05:30.493 - [任务 12] - Stopped task aspect(s) 
[TRACE] 2025-02-14 15:05:30.494 - [任务 12] - Snapshot order controller have been removed 
[INFO ] 2025-02-14 15:05:30.494 - [任务 12] - Task stopped. 
[TRACE] 2025-02-14 15:05:30.516 - [任务 12] - Remove memory task client succeed, task: 任务 12[67aeea28a250d87287e29d72] 
[TRACE] 2025-02-14 15:05:30.516 - [任务 12] - Destroy memory task client cache succeed, task: 任务 12[67aeea28a250d87287e29d72] 
