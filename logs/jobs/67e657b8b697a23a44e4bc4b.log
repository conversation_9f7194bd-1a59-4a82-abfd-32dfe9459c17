[TRACE] 2025-03-28 16:04:50.554 - [SP9_COM] - Task initialization... 
[TRACE] 2025-03-28 16:04:50.747 - [SP9_COM] - Start task milestones: 67e657b8b697a23a44e4bc4b(SP9_COM) 
[INFO ] 2025-03-28 16:04:50.747 - [SP9_COM] - Loading table structure completed 
[TRACE] 2025-03-28 16:04:50.792 - [SP9_COM] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-28 16:04:50.854 - [SP9_COM] - The engine receives SP9_COM task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-28 16:04:50.854 - [SP9_COM] - Task started 
[TRACE] 2025-03-28 16:04:50.915 - [SP9_COM][sybase_190 - COM_DB_9] - Node sybase_190 - COM_DB_9[783c9cdf-3d49-4964-a464-f5c6f020e69c] start preload schema,table counts: 8 
[TRACE] 2025-03-28 16:04:50.915 - [SP9_COM][sybase_190 - COM_DB_9] - Node sybase_190 - COM_DB_9[783c9cdf-3d49-4964-a464-f5c6f020e69c] preload schema finished, cost 0 ms 
[TRACE] 2025-03-28 16:04:50.915 - [SP9_COM][pg_hdtest - SP9_com] - Node pg_hdtest - SP9_com[c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f] start preload schema,table counts: 8 
[TRACE] 2025-03-28 16:04:50.915 - [SP9_COM][pg_hdtest - SP9_com] - Node pg_hdtest - SP9_com[c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f] preload schema finished, cost 0 ms 
[INFO ] 2025-03-28 16:04:51.236 - [SP9_COM][sybase_190 - COM_DB_9] - Source connector(sybase_190 - COM_DB_9) initialization completed 
[TRACE] 2025-03-28 16:04:51.236 - [SP9_COM][sybase_190 - COM_DB_9] - Source node "sybase_190 - COM_DB_9" read batch size: 100 
[TRACE] 2025-03-28 16:04:51.236 - [SP9_COM][sybase_190 - COM_DB_9] - Source node "sybase_190 - COM_DB_9" event queue capacity: 200 
[TRACE] 2025-03-28 16:04:51.236 - [SP9_COM][sybase_190 - COM_DB_9] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-28 16:04:51.304 - [SP9_COM][sybase_190 - COM_DB_9] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='COM_DB_9') 
[INFO ] 2025-03-28 16:04:51.305 - [SP9_COM][sybase_190 - COM_DB_9] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-03-28 16:04:51.414 - [SP9_COM][pg_hdtest - SP9_com] - Sink connector(pg_hdtest - SP9_com) initialization completed 
[TRACE] 2025-03-28 16:04:51.414 - [SP9_COM][pg_hdtest - SP9_com] - Node(pg_hdtest - SP9_com) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-28 16:04:51.414 - [SP9_COM][pg_hdtest - SP9_com] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-28 16:04:51.431 - [SP9_COM][pg_hdtest - SP9_com] - Apply table structure to target database 
[INFO ] 2025-03-28 16:04:51.431 - [SP9_COM][sybase_190 - COM_DB_9] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-28 16:04:51.522 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from 8 tables 
[TRACE] 2025-03-28 16:04:51.522 - [SP9_COM][sybase_190 - COM_DB_9] - Initial sync started 
[INFO ] 2025-03-28 16:04:51.522 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: rs_dbversion 
[TRACE] 2025-03-28 16:04:51.522 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_dbversion is going to be initial synced 
[TRACE] 2025-03-28 16:04:51.638 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_dbversion already exists Index list: [] 
[INFO ] 2025-03-28 16:04:51.638 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_dbversion has been completed batch read 
[INFO ] 2025-03-28 16:04:51.638 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: rs_mat_status 
[TRACE] 2025-03-28 16:04:51.638 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_mat_status is going to be initial synced 
[TRACE] 2025-03-28 16:04:51.734 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_mat_status already exists Index: rs_mat_status_idx and will no longer create index 
[TRACE] 2025-03-28 16:04:51.734 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_mat_status already exists Index list: [TapIndex name rs_mat_status_idx indexFields: [TapIndexField name subid fieldAsc true indexType null; TapIndexField name action fieldAsc true indexType null; ]] 
[INFO ] 2025-03-28 16:04:51.734 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_mat_status has been completed batch read 
[INFO ] 2025-03-28 16:04:51.734 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: rs_ticket_history 
[TRACE] 2025-03-28 16:04:51.734 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_ticket_history is going to be initial synced 
[INFO ] 2025-03-28 16:04:51.815 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_ticket_history has been completed batch read 
[INFO ] 2025-03-28 16:04:51.815 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: cpi_transaction 
[TRACE] 2025-03-28 16:04:51.815 - [SP9_COM][sybase_190 - COM_DB_9] - Table cpi_transaction is going to be initial synced 
[INFO ] 2025-03-28 16:04:51.906 - [SP9_COM][sybase_190 - COM_DB_9] - Table cpi_transaction has been completed batch read 
[INFO ] 2025-03-28 16:04:51.906 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: common_location 
[TRACE] 2025-03-28 16:04:51.906 - [SP9_COM][sybase_190 - COM_DB_9] - Table common_location is going to be initial synced 
[TRACE] 2025-03-28 16:04:51.928 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_ticket_history already exists Index: rs_ticket_idx and will no longer create index 
[TRACE] 2025-03-28 16:04:51.928 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_ticket_history already exists Index list: [TapIndex name rs_ticket_idx indexFields: [TapIndexField name cnt fieldAsc true indexType null; ]] 
[INFO ] 2025-03-28 16:04:52.000 - [SP9_COM][sybase_190 - COM_DB_9] - Table common_location has been completed batch read 
[INFO ] 2025-03-28 16:04:52.000 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: rs_threads 
[TRACE] 2025-03-28 16:04:52.000 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_threads is going to be initial synced 
[INFO ] 2025-03-28 16:04:52.122 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_threads has been completed batch read 
[INFO ] 2025-03-28 16:04:52.122 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: rs_lastcommit 
[TRACE] 2025-03-28 16:04:52.122 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_lastcommit is going to be initial synced 
[TRACE] 2025-03-28 16:04:52.173 - [SP9_COM][pg_hdtest - SP9_com] - Table: cpi_transaction already exists Index: cpi_hosp_trans_dtm_Cidx and will no longer create index 
[TRACE] 2025-03-28 16:04:52.173 - [SP9_COM][pg_hdtest - SP9_com] - Table: cpi_transaction will create Index: TapIndex name cpi_txn_process_idx2 indexFields: [TapIndexField name ioi_process fieldAsc true indexType null; TapIndexField name transaction_datetime fieldAsc true indexType null; ] 
[INFO ] 2025-03-28 16:04:52.213 - [SP9_COM][sybase_190 - COM_DB_9] - Table rs_lastcommit has been completed batch read 
[INFO ] 2025-03-28 16:04:52.213 - [SP9_COM][sybase_190 - COM_DB_9] - Starting batch read from table: transaction_log 
[TRACE] 2025-03-28 16:04:52.213 - [SP9_COM][sybase_190 - COM_DB_9] - Table transaction_log is going to be initial synced 
[TRACE] 2025-03-28 16:04:52.217 - [SP9_COM][pg_hdtest - SP9_com] - Table: cpi_transaction create Index: cpi_txn_process_idx2 successfully, cost 43ms 
[TRACE] 2025-03-28 16:04:52.217 - [SP9_COM][pg_hdtest - SP9_com] - Table: cpi_transaction will create Index: TapIndex name cpi_txn_upload_idx indexFields: [TapIndexField name upload_status fieldAsc true indexType null; TapIndexField name transaction_datetime fieldAsc true indexType null; ] 
[TRACE] 2025-03-28 16:04:52.251 - [SP9_COM][pg_hdtest - SP9_com] - Table: cpi_transaction create Index: cpi_txn_upload_idx successfully, cost 33ms 
[TRACE] 2025-03-28 16:04:52.251 - [SP9_COM][pg_hdtest - SP9_com] - Table: cpi_transaction synchronize indexes completed, cost 117ms totally 
[INFO ] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Table transaction_log has been completed batch read 
[TRACE] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Query snapshot row size completed: sybase_190 - COM_DB_9(783c9cdf-3d49-4964-a464-f5c6f020e69c) 
[TRACE] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Initial sync completed 
[INFO ] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Batch read completed. 
[TRACE] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Incremental sync starting... 
[TRACE] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Initial sync completed 
[TRACE] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Starting stream read, table list: [rs_dbversion, rs_mat_status, rs_ticket_history, cpi_transaction, common_location, rs_threads, rs_lastcommit, transaction_log], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-28 16:04:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - Starting incremental sync using database log parser 
[INFO ] 2025-03-28 16:04:52.364 - [SP9_COM][sybase_190 - COM_DB_9] - startRid: 65572, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-28 16:04:52.364 - [SP9_COM][sybase_190 - COM_DB_9] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-28 16:04:52.364 - [SP9_COM][sybase_190 - COM_DB_9] - sybase offset in database is: startRid: 65572, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-28 16:04:52.372 - [SP9_COM][sybase_190 - COM_DB_9] - we will use offset in database, how ever, this is safe: startRid: 65572, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-28 16:04:52.372 - [SP9_COM][sybase_190 - COM_DB_9] - sybase cdc work with mode v1: auto rescan 
[TRACE] 2025-03-28 16:04:52.433 - [SP9_COM][pg_hdtest - SP9_com] - Table: common_location already exists Index: CLT_CIdx and will no longer create index 
[TRACE] 2025-03-28 16:04:52.433 - [SP9_COM][pg_hdtest - SP9_com] - Table: common_location already exists Index list: [TapIndex name CLT_CIdx indexFields: [TapIndexField name hospital_code fieldAsc true indexType null; TapIndexField name specialty_code fieldAsc true indexType null; TapIndexField name ward_clinic fieldAsc true indexType null; ]] 
[INFO ] 2025-03-28 16:04:52.511 - [SP9_COM][sybase_190 - COM_DB_9] - sp_config_rep_agent disabled, database: COM_DB_9 
[INFO ] 2025-03-28 16:04:52.511 - [SP9_COM][sybase_190 - COM_DB_9] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-28 16:04:52.598 - [SP9_COM][sybase_190 - COM_DB_9] - opened cdc for tables: {dbo=[rs_dbversion, rs_mat_status, rs_ticket_history, cpi_transaction, common_location, rs_threads, rs_lastcommit, transaction_log]} 
[INFO ] 2025-03-28 16:04:52.598 - [SP9_COM][sybase_190 - COM_DB_9] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='COM_DB_9') 
[TRACE] 2025-03-28 16:04:52.682 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_threads already exists Index: rs_threads_idx and will no longer create index 
[TRACE] 2025-03-28 16:04:52.682 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_threads already exists Index list: [TapIndex name rs_threads_idx indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name conn_id fieldAsc true indexType null; ]] 
[INFO ] 2025-03-28 16:04:52.682 - [SP9_COM][sybase_190 - COM_DB_9] - logs holder exists, will skip valid operation 
[TRACE] 2025-03-28 16:04:52.842 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_lastcommit already exists Index: rs_lastcommit_idx and will no longer create index 
[INFO ] 2025-03-28 16:04:52.842 - [SP9_COM][sybase_190 - COM_DB_9] - trans timestamp offset: 28800000 
[TRACE] 2025-03-28 16:04:52.842 - [SP9_COM][pg_hdtest - SP9_com] - Table: rs_lastcommit already exists Index list: [TapIndex name rs_lastcommit_idx indexFields: [TapIndexField name origin fieldAsc true indexType null; TapIndexField name conn_id fieldAsc true indexType null; ]] 
[INFO ] 2025-03-28 16:04:52.843 - [SP9_COM][sybase_190 - COM_DB_9] - sybase cdc debug log is disabled 
[INFO ] 2025-03-28 16:04:52.843 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65572, rowId: 0 
[TRACE] 2025-03-28 16:04:53.124 - [SP9_COM][pg_hdtest - SP9_com] - Table: transaction_log already exists Index list: [] 
[WARN ] 2025-03-28 16:04:53.925 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658234fb93c42ce5a72d3, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.925 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658234fb93c42ce5a72d4, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.925 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658234fb93c42ce5a72d5, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.925 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658234fb93c42ce5a72d6, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.925 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658244fb93c42ce5a72d7, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.926 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658244fb93c42ce5a72d8, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.926 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658244fb93c42ce5a72d9, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[WARN ] 2025-03-28 16:04:53.926 - [SP9_COM][pg_hdtest - SP9_com] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=67e658244fb93c42ce5a72da, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[783c9cdf-3d49-4964-a464-f5c6f020e69c], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-03-28 16:04:54.029 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "rs_mat_status" initial sync finished, cost: 59 ms 
[TRACE] 2025-03-28 16:04:54.029 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "common_location" initial sync finished, cost: 101 ms 
[TRACE] 2025-03-28 16:04:54.057 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "rs_dbversion" initial sync finished, cost: 130 ms 
[TRACE] 2025-03-28 16:04:54.057 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "rs_lastcommit" initial sync finished, cost: 129 ms 
[TRACE] 2025-03-28 16:04:54.089 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "cpi_transaction" initial sync finished, cost: 159 ms 
[TRACE] 2025-03-28 16:04:54.089 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "transaction_log" initial sync finished, cost: 160 ms 
[TRACE] 2025-03-28 16:04:54.123 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "rs_ticket_history" initial sync finished, cost: 194 ms 
[TRACE] 2025-03-28 16:04:54.124 - [SP9_COM][pg_hdtest - SP9_com] - Process after table "rs_threads" initial sync finished, cost: 194 ms 
[INFO ] 2025-03-28 16:04:54.124 - [SP9_COM][pg_hdtest - SP9_com] - Process after all table(s) initial sync are finished，table number: 8 
[INFO ] 2025-03-28 16:04:55.941 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65572, 0 
[INFO ] 2025-03-28 16:04:55.941 - [SP9_COM][sybase_190 - COM_DB_9] - uncommit trans size: 0 
[INFO ] 2025-03-28 16:04:55.942 - [SP9_COM][sybase_190 - COM_DB_9] - uncommit trans: {} 
[INFO ] 2025-03-28 16:04:55.942 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.942 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.942 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.942 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.942 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.943 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.943 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 1 
[INFO ] 2025-03-28 16:04:55.943 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.943 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.943 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 2 
[INFO ] 2025-03-28 16:04:55.944 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.945 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.945 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 72 
[INFO ] 2025-03-28 16:04:55.945 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.945 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.945 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 96 
[INFO ] 2025-03-28 16:04:55.945 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 3 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 72 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.946 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.947 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 80 
[INFO ] 2025-03-28 16:04:55.947 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.948 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.948 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 4 
[INFO ] 2025-03-28 16:04:55.948 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.948 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.949 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 71 
[INFO ] 2025-03-28 16:04:55.949 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.949 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.949 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 96 
[INFO ] 2025-03-28 16:04:55.949 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.949 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 6 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 71 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.950 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 80 
[INFO ] 2025-03-28 16:04:55.951 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.951 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.951 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 7 
[INFO ] 2025-03-28 16:04:55.951 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.951 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 30 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65571 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 53 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 65572 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 9 
[INFO ] 2025-03-28 16:04:55.952 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:55.953 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 8092 
[INFO ] 2025-03-28 16:04:55.953 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 2025-02-20T09:19:55.626+0800 
[INFO ] 2025-03-28 16:04:55.953 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 0 
[INFO ] 2025-03-28 16:04:55.954 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.954 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 10 
[INFO ] 2025-03-28 16:04:55.954 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2025-02-20T09:20:13.626+0800 
[INFO ] 2025-03-28 16:04:55.954 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.954 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 8092 
[INFO ] 2025-03-28 16:04:55.955 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: _dmpxact 
[INFO ] 2025-03-28 16:04:55.955 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: sa 
[INFO ] 2025-03-28 16:04:55.955 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: myPassword 
[INFO ] 2025-03-28 16:04:55.957 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 17 
[INFO ] 2025-03-28 16:04:55.957 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 8202 
[INFO ] 2025-03-28 16:04:55.957 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 2025-02-20T09:20:13.626+0800 
[INFO ] 2025-03-28 16:04:55.957 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: [0, 0, 0, 0, -85, 31, 0, 0] 
[INFO ] 2025-03-28 16:04:55.957 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 65572 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 10 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 8092 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 65572 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 10, value: 11 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 11, value: 0 
[INFO ] 2025-03-28 16:04:55.958 - [SP9_COM][sybase_190 - COM_DB_9] - column: 12, value: 8092 
[INFO ] 2025-03-28 16:04:55.959 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 54 
[INFO ] 2025-03-28 16:04:55.959 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.959 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 10 
[INFO ] 2025-03-28 16:04:55.959 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 48 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 576 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 12 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 30 
[INFO ] 2025-03-28 16:04:55.960 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.961 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 10 
[INFO ] 2025-03-28 16:04:55.961 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2 
[INFO ] 2025-03-28 16:04:55.961 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 65572 
[INFO ] 2025-03-28 16:04:55.961 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 20 
[INFO ] 2025-03-28 16:04:55.961 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 8092 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 2025-02-20T09:20:13.626+0800 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 17 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 8208 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 2025-03-27T09:44:56.293+0800 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: [0, 0, 0, 0, -75, 31, 0, 0] 
[INFO ] 2025-03-28 16:04:55.962 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.963 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 0 
[INFO ] 2025-03-28 16:04:55.964 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:55.964 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.964 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 65572 
[INFO ] 2025-03-28 16:04:55.965 - [SP9_COM][sybase_190 - COM_DB_9] - column: 10, value: 21 
[INFO ] 2025-03-28 16:04:55.965 - [SP9_COM][sybase_190 - COM_DB_9] - column: 11, value: 0 
[INFO ] 2025-03-28 16:04:55.965 - [SP9_COM][sybase_190 - COM_DB_9] - column: 12, value: 8092 
[INFO ] 2025-03-28 16:04:55.965 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 0 
[INFO ] 2025-03-28 16:04:55.965 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.965 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2025-03-28T08:04:52.410+0800 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 8092 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: rs_logexec 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: sa 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: myPassword 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 58 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 344 
[INFO ] 2025-03-28 16:04:55.966 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 23 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 24 
[INFO ] 2025-03-28 16:04:55.967 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 25 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 72 
[INFO ] 2025-03-28 16:04:55.968 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 88 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 26 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 72 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.969 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 80 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 27 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 71 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 88 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 29 
[INFO ] 2025-03-28 16:04:55.970 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 71 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 80 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 30 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 30 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 22 
[INFO ] 2025-03-28 16:04:55.971 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 65572 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 32 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 8092 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 2025-03-28T08:04:52.410+0800 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 0 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2025-03-28T08:04:52.410+0800 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 8092 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: rs_logexec 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: sa 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: myPassword 
[INFO ] 2025-03-28 16:04:55.972 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 58 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 344 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 34 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.973 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 35 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 36 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.974 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 37 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.975 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 38 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.976 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 39 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 40 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.977 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 41 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.978 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 42 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.979 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 43 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 44 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.980 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 45 
[INFO ] 2025-03-28 16:04:55.981 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.982 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 46 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.985 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 47 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 48 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 49 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.986 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 50 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 51 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.987 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 52 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.988 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 53 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.989 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 54 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 55 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.990 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 56 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.991 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 57 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.995 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 58 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 59 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 60 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.996 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 61 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 62 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.997 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 63 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 64 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 65 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.998 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 66 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 67 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:55.999 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65572 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 68 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8092 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.000 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.001 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 1 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.002 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 2 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 3 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.006 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 4 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 5 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.007 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 6 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 7 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 8 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 9 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.008 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 10 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 11 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 12 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.009 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 13 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.010 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 14 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 15 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.011 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 16 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.012 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 17 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 18 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.013 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 19 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.014 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 20 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 21 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.015 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 22 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 23 
[INFO ] 2025-03-28 16:04:56.016 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 24 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.017 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 25 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.018 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 26 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 27 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.019 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 28 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 29 
[INFO ] 2025-03-28 16:04:56.020 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 30 
[INFO ] 2025-03-28 16:04:56.021 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 31 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 32 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.025 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 33 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 34 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 35 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 36 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.026 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 37 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 38 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 39 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 40 
[INFO ] 2025-03-28 16:04:56.027 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 41 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 42 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 43 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 44 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 45 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.029 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 46 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 47 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.030 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65573 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 48 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8134 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.031 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 1 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 2 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.032 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 3 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 4 
[INFO ] 2025-03-28 16:04:56.033 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 5 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.034 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 6 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 7 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 59 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 328 
[INFO ] 2025-03-28 16:04:56.035 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 8 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 72 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 96 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 9 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 72 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.036 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 80 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 10 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 71 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 96 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 12 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 71 
[INFO ] 2025-03-28 16:04:56.037 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 80 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 0 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 65574 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 13 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 0 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 8135 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 1, value: 30 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 2, value: 65572 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 3, value: 33 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 4, value: 2 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 5, value: 65574 
[INFO ] 2025-03-28 16:04:56.038 - [SP9_COM][sybase_190 - COM_DB_9] - column: 6, value: 15 
[INFO ] 2025-03-28 16:04:56.042 - [SP9_COM][sybase_190 - COM_DB_9] - column: 7, value: 0 
[INFO ] 2025-03-28 16:04:56.042 - [SP9_COM][sybase_190 - COM_DB_9] - column: 8, value: 8135 
[INFO ] 2025-03-28 16:04:56.042 - [SP9_COM][sybase_190 - COM_DB_9] - column: 9, value: 2025-03-28T08:04:52.410+0800 
[INFO ] 2025-03-28 16:04:56.042 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:04:59.067 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:04:59.271 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:02.271 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:02.271 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:05.277 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:05.440 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:08.483 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:08.686 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:11.718 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:11.920 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:14.907 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:14.907 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:17.907 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:18.112 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:21.124 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:21.124 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:24.128 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:24.334 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:27.358 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:27.359 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:30.362 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:30.565 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:33.592 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:33.593 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:36.594 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:36.797 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:39.836 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:39.836 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:42.842 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:42.990 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:45.993 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:46.198 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:49.237 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:49.443 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:52.274 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:52.389 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:55.394 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:55.598 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:05:58.627 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:05:58.627 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:01.631 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:01.831 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:04.882 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:04.884 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:07.883 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:08.016 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:11.020 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:11.223 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:14.259 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:14.259 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:17.260 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:17.440 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:20.491 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:20.695 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:23.743 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:23.948 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:26.787 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:26.992 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:30.011 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:30.011 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:33.013 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:33.167 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:36.206 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:36.412 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:39.419 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:39.420 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:42.421 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:42.624 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:45.591 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:45.592 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:48.597 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:48.799 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:51.860 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:51.860 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:54.866 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:55.014 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:06:58.019 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:06:58.327 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:01.331 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:01.533 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:04.582 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:04.782 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:07.821 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:07.822 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:10.824 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:10.973 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:14.023 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:14.227 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:17.264 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:17.468 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:20.496 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:20.496 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:23.499 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:23.660 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:26.662 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:26.866 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:29.907 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:30.112 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:32.931 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:33.114 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:36.120 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:36.525 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:39.496 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:39.497 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:42.499 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:42.642 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:45.649 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:45.841 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:48.844 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:48.963 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:52.004 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:52.208 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:55.251 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:55.455 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:07:58.455 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:07:58.455 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:01.457 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:01.862 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:04.891 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:04.892 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:07.895 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:08.086 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:11.087 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:11.252 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:14.297 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:14.502 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:17.544 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:17.747 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:20.585 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:20.788 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:23.835 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:23.835 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:26.839 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:26.954 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:29.959 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:30.369 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:33.172 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:33.311 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:36.317 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:36.521 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:39.565 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:39.770 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:42.599 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:42.801 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:45.843 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:46.048 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:49.097 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:49.303 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:52.110 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:52.287 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:55.290 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:55.454 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:08:58.458 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:08:58.659 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:01.670 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:01.670 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:04.675 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:04.876 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:07.910 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:07.911 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:10.913 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:11.114 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:14.153 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:14.359 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:17.200 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:17.407 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:20.432 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:20.433 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:23.438 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:23.646 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:26.694 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:26.899 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:29.866 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:29.866 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:32.871 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:33.072 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:36.111 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:36.111 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:39.115 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:39.231 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:42.259 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:42.461 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:45.492 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:45.492 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:48.498 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:48.630 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:51.682 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:51.887 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:54.927 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:55.128 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:09:57.972 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:09:58.587 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:01.419 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:01.545 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:04.548 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:04.753 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:07.804 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:08.007 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:10.814 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:10.911 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:13.916 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:14.120 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:17.137 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:17.138 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:20.143 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:20.277 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:23.281 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:23.434 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:26.438 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:26.583 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:29.625 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:29.827 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:32.855 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:32.856 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:35.859 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:36.062 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:39.098 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:39.198 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:42.242 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:42.448 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:45.496 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:45.699 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:48.527 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:48.643 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:51.646 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:51.852 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:54.890 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:54.891 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:10:57.891 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:10:58.006 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:01.009 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:01.169 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:04.174 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:04.293 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:07.294 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:07.410 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:10.417 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:10.621 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:13.676 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:13.879 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:16.683 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:16.796 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:19.801 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:20.007 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:23.008 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:23.009 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:26.011 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:26.216 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:29.186 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:29.187 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:32.193 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:32.394 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:35.434 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:35.638 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:38.464 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:38.627 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:41.629 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:41.786 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:44.830 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:45.033 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:48.080 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:48.288 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:51.265 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:51.266 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:54.270 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:54.475 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:11:57.508 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:11:57.509 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:00.514 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:00.717 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:03.747 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:03.747 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:06.748 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:06.952 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:09.942 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:09.942 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:12.946 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:13.113 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:16.160 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:16.365 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:19.381 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:19.381 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:22.385 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:22.538 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:25.599 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:25.802 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:28.853 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:29.054 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:31.887 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:31.982 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:35.037 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:35.242 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:38.285 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:38.426 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:41.431 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:41.619 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:44.625 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:44.829 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:47.807 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:47.807 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:50.809 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:51.012 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:54.038 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:54.039 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:12:57.039 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:12:57.243 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:00.266 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:00.268 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:03.268 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:03.473 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:06.514 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:06.514 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:09.518 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:09.719 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:12.762 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:12.963 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:15.785 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:15.942 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:18.948 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:19.075 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:22.115 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:22.321 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:25.373 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:25.782 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:28.622 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:29.027 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:31.868 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:32.183 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:35.190 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:35.596 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:38.642 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:38.780 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:41.785 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:42.193 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:45.222 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:45.390 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:48.393 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:48.596 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:51.658 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:51.864 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:54.695 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:54.898 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:13:57.944 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:13:58.147 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:00.963 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:01.104 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:04.108 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:04.311 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:07.365 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:07.493 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:10.499 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:10.700 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:13.743 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:13.744 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:16.749 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:17.155 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:20.190 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:20.395 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:23.445 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:23.647 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:26.670 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:26.670 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:29.672 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:29.849 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:32.854 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:33.058 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:36.103 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:36.104 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:39.108 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:39.274 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:42.277 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:42.482 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:45.538 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:45.743 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:48.557 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:48.966 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:51.799 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:52.206 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:55.247 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:55.248 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:14:58.285 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:14:58.490 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:01.518 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:01.518 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:04.523 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:04.670 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:07.722 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:07.927 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:10.960 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:10.960 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:13.965 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:14.109 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:17.115 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:17.322 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:20.368 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:20.573 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:23.399 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:23.499 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:26.500 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:26.705 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:29.702 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:29.702 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:32.707 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:32.909 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:35.920 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:35.921 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:38.926 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:39.129 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:42.168 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:42.373 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:45.194 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:45.599 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:48.454 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:48.655 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:51.647 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:51.648 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:54.649 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:54.851 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:15:57.899 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:15:58.105 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:00.933 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:01.039 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:04.043 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:04.244 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:07.281 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:07.281 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:10.284 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:10.453 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:13.496 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:13.700 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:16.729 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:16.730 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:19.731 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:19.936 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:22.977 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:23.180 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:26.024 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:26.226 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:29.271 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:29.473 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:32.514 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:32.719 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:35.707 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:35.707 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:38.713 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:38.822 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:41.824 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:41.979 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:44.983 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:45.174 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:48.180 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:48.386 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:51.392 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:51.392 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:54.399 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:54.602 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:16:57.619 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:16:57.619 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:00.624 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:00.792 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:03.838 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:04.041 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:07.086 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:07.087 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:10.127 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:10.331 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:13.299 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:13.299 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:16.303 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:16.434 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:19.442 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:19.569 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:22.572 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:22.773 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:25.810 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:26.011 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:28.834 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:28.968 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:31.970 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:32.136 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:35.139 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:35.319 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:38.321 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:38.476 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:41.482 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:41.886 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:44.731 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:44.872 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:47.878 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:48.039 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:51.096 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:51.249 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:54.284 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:54.488 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:17:57.533 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:17:57.741 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:00.554 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:00.715 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:03.721 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:03.872 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:06.917 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:07.121 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:10.169 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:10.283 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:13.334 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:13.540 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:16.587 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:16.790 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:19.788 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:19.789 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:22.794 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:22.956 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:25.961 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:26.134 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:29.172 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:29.378 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:32.419 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:32.624 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:35.635 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:35.636 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:38.639 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:38.741 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:41.745 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:41.890 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:44.895 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:45.100 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:48.143 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:48.282 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:51.332 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:51.533 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:54.495 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:54.497 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:18:57.504 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:18:57.709 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:00.760 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:00.965 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:03.967 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:03.968 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:06.968 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:07.144 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:10.151 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:10.355 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:13.394 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:13.598 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:16.584 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:16.585 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:19.590 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:19.725 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:22.761 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:22.966 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:25.999 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:25.999 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:29.001 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:29.195 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:32.196 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:32.402 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:35.456 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:35.661 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:38.628 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:38.629 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:41.633 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:41.803 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:44.842 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:45.047 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:48.103 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:48.306 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:51.145 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:51.295 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:54.297 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:54.423 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:19:57.427 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:19:57.536 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:00.541 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:00.747 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:03.782 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:03.991 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:06.818 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:06.908 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:09.913 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:10.120 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:13.147 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:13.350 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:16.351 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:16.352 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:19.357 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:19.558 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:22.571 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:22.571 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:25.575 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:25.709 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:28.734 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:28.939 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:31.996 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:32.202 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:35.038 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:35.281 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:38.324 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:38.528 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:41.496 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:41.496 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:44.498 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:44.703 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:47.736 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:47.738 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:50.740 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:50.930 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:53.939 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:54.113 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:20:57.181 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:20:57.599 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:00.620 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:00.620 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:03.673 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:03.878 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:06.884 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:06.885 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:09.887 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:10.041 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:13.045 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:13.450 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:16.283 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:16.468 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:19.471 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:19.584 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:22.620 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:22.823 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:25.838 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:25.838 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:28.843 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:28.985 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:31.990 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:32.193 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:35.241 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:35.445 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:38.435 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:38.435 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:41.441 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:41.579 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:44.623 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:44.824 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:47.867 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:47.868 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:50.871 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:51.019 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:54.019 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:54.224 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:21:57.270 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:21:57.472 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:00.487 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:00.487 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:03.488 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:03.594 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:06.630 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:06.831 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:09.878 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:10.080 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:13.057 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:13.058 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:16.061 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:16.183 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:19.186 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:19.299 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:22.302 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:22.471 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:25.472 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:25.635 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:28.641 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:28.772 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:31.808 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:32.011 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:35.048 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:35.254 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:38.282 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:38.283 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:41.284 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:41.489 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:44.537 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:44.740 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:47.561 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:47.761 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:50.767 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:50.927 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:53.929 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:54.073 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:22:57.106 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:22:57.314 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:00.370 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:00.575 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:03.401 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:04.826 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:07.664 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:08.881 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:11.929 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:12.941 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:15.995 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:17.061 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:20.067 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:21.315 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:24.350 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:25.775 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:28.818 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:29.630 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:32.664 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:34.088 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:37.120 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:37.921 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:40.968 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:41.174 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:44.220 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:44.220 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:47.228 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:47.402 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:50.408 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:50.561 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:53.591 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:53.793 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:23:56.842 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:23:57.044 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:00.024 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:00.024 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:03.029 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:03.234 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:06.242 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:06.242 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:09.247 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:09.452 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:12.463 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:12.466 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:15.469 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:15.626 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:18.632 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:18.740 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:21.741 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:21.949 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:25.001 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:25.002 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:28.005 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:28.410 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:31.212 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:31.342 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:34.347 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:34.550 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:37.585 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:37.791 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:40.630 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:40.838 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:43.822 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:43.823 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:46.824 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:47.025 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:50.068 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:50.138 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:53.145 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:53.305 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:56.309 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:56.515 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:24:59.539 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:24:59.540 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:02.541 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:02.702 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:05.746 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:05.950 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:08.980 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:08.981 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:11.983 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:12.146 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:15.151 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:15.352 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:18.403 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:18.607 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:21.435 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:21.597 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:24.602 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:24.757 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:27.776 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:28.184 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:31.225 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:31.427 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:34.253 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:34.429 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:37.432 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:37.585 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:40.587 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:40.800 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:43.842 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:43.995 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:46.999 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:47.167 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:50.167 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:50.574 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:53.606 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:53.606 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:56.611 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:25:57.017 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:25:59.855 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:00.057 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:03.106 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:03.310 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:06.351 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:06.379 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:09.417 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:09.623 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:12.667 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:13.070 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:15.901 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:16.299 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:19.300 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:19.696 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:22.699 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:22.894 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:25.900 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:26.105 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:29.163 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:29.368 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:32.354 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:32.354 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:35.360 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:35.559 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:38.563 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:38.743 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:41.783 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:41.987 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:45.037 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:45.199 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:48.206 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:48.411 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:51.445 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:51.651 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:54.490 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:54.693 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:26:57.690 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:26:57.692 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:00.693 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:00.895 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:03.934 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:04.137 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:06.987 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:07.396 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:10.416 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:10.419 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:13.419 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:13.515 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:16.568 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:16.771 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:19.818 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:19.820 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:22.824 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:22.984 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:25.989 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:26.193 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:29.243 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:29.448 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:32.260 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:32.386 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:35.387 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:35.570 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:38.616 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:38.819 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:41.849 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:41.849 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:44.855 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:45.010 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:48.072 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:48.273 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:51.325 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:51.449 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:54.454 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:54.858 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:27:57.902 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:27:58.257 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:01.258 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:01.666 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:04.646 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:04.646 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:07.648 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:07.827 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:10.828 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:11.033 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:14.077 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:14.278 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:17.285 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:17.288 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:20.290 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:20.472 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:23.475 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:23.881 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:26.696 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:26.824 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:29.826 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:29.994 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:33.033 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:33.238 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:36.255 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:36.255 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:39.257 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:39.413 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:42.419 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:42.580 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:45.582 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:45.701 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:48.706 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:48.835 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:51.841 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:52.006 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:55.054 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:55.260 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:28:58.317 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:28:58.524 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:01.502 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:01.502 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:04.506 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:04.636 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:07.639 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:07.782 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:10.784 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:10.990 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:14.034 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:14.036 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:17.035 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:17.173 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:20.178 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:20.380 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:23.389 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:23.590 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:26.623 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:26.824 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:29.823 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:29.823 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:32.829 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:33.218 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:36.223 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:36.388 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:39.389 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:39.577 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:42.578 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:42.692 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:45.697 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:46.106 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:48.952 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:49.156 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:52.209 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:52.414 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:55.247 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:55.449 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:29:58.500 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:29:58.704 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:01.513 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:01.660 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:04.666 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:04.815 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:07.867 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:08.074 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:11.053 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:11.054 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:14.058 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:14.237 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:17.243 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:17.447 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:20.494 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:20.701 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:23.527 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:23.669 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:26.675 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:26.881 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:29.920 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:30.121 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:32.954 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:33.107 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:36.111 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:36.244 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:39.248 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:39.378 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:42.382 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:42.535 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:45.581 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:45.785 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:48.826 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:49.028 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:51.837 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:51.968 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:54.973 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:55.379 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:30:58.199 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:30:58.341 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:01.342 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:01.542 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:04.593 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:04.794 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:07.773 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:07.774 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:10.775 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:10.980 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:14.015 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:14.016 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:17.021 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:17.183 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:20.187 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:20.391 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:23.440 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:23.648 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:26.467 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:26.625 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:29.630 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:29.772 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:32.811 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:33.015 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:36.056 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:36.259 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:39.249 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:39.250 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:42.256 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:42.400 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:45.439 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:45.644 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:48.679 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:48.885 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:51.686 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:51.797 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:54.802 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:55.006 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:31:58.065 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:31:58.271 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:01.272 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:01.272 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:04.278 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:04.420 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:07.427 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:07.547 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:10.549 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:10.726 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:13.732 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:13.933 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:16.988 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:17.194 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:20.246 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:20.449 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:23.276 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:23.397 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:26.402 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:26.605 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:29.648 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:29.850 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:32.701 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:32.906 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:35.915 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:35.915 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:38.917 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:39.123 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:42.171 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:42.373 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:45.181 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:45.309 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:48.315 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:48.519 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:51.545 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:51.545 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:54.549 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:54.667 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:32:57.668 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:32:57.815 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:00.816 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:01.019 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:04.065 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:04.270 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:07.111 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:07.227 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:10.232 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:10.435 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:13.469 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:13.674 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:16.508 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:16.638 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:19.641 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:19.811 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:22.833 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:23.039 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:26.087 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:26.289 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:29.341 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:29.543 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:32.351 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:32.483 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:35.488 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:35.631 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:38.672 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:38.877 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:41.918 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:42.119 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:44.947 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:45.056 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:48.062 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:48.264 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:51.310 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:51.512 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:54.343 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:54.425 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:33:57.463 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:33:57.664 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:00.691 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:00.693 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:03.695 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:03.838 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:06.841 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:07.045 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:10.094 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:10.298 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:13.223 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:13.223 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:16.229 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:16.430 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:19.468 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:19.673 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:22.507 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:22.633 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:25.636 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:25.831 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:28.832 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:29.036 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:32.077 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:32.283 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:35.221 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:35.222 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:38.225 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:38.430 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:41.458 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:41.458 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:44.459 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:44.633 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:47.639 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:47.840 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:50.890 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:51.097 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:54.122 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:54.122 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:34:57.123 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:34:57.249 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:00.288 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:00.493 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:03.498 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:03.498 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:06.502 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:06.669 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:09.671 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:09.872 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:12.916 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:13.123 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:16.142 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:16.142 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:19.142 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:19.548 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:22.451 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:22.452 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:25.457 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:25.658 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:28.685 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:28.885 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:31.890 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:31.892 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:34.890 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:34.997 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:38.009 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:38.410 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:41.256 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:41.458 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:44.505 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:44.505 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:47.509 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:47.616 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:50.640 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:50.843 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:53.881 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:54.085 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:35:56.905 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:35:57.042 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:00.043 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:00.206 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:03.209 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:03.303 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:06.309 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:06.511 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:09.536 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:09.536 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:12.541 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:12.748 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:15.783 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:15.783 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:18.790 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:18.952 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:21.999 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:22.200 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:25.242 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:25.448 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:28.449 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:28.451 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:31.451 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:31.653 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:34.664 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:34.664 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:37.667 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:37.873 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:40.873 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:40.873 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:43.873 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:44.078 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:47.121 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:47.322 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:50.351 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:50.557 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:53.523 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:53.524 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:56.523 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:56.657 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:36:59.662 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:36:59.833 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:02.834 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:02.944 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:05.969 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:06.173 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:09.198 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:09.289 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:12.334 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:12.541 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:15.582 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:15.786 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:18.616 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:18.729 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:21.732 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:21.937 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:24.979 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:25.185 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:28.168 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:28.169 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:31.173 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:31.304 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:34.307 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:34.412 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:37.415 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:37.616 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:40.664 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:40.869 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:43.701 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:43.818 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:46.819 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:47.024 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:50.059 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:50.265 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:53.094 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:53.298 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:56.319 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:56.320 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:37:59.321 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:37:59.523 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:02.564 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:02.769 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:05.605 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:05.699 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:08.703 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:08.881 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:11.911 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:12.320 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:15.358 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:15.559 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:18.388 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:18.536 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:21.537 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:21.670 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:24.675 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:24.802 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:27.808 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:27.987 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:31.019 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:31.205 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:34.227 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:34.432 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:37.480 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:37.683 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:40.719 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:40.812 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:43.814 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:44.020 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:47.078 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:47.194 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:50.229 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:50.433 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:53.417 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:53.419 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:56.418 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:56.542 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:38:59.544 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:38:59.848 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:02.853 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:03.058 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:06.091 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:06.295 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:09.297 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:09.298 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:12.303 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:12.416 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:15.420 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:15.570 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:18.575 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:18.761 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:21.765 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:21.972 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:25.015 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:25.220 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:28.223 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:28.224 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:31.228 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:31.371 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:34.411 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:34.811 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:37.852 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:38.261 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:41.101 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:41.282 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:44.309 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:44.513 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:47.528 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:47.528 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:50.533 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:50.674 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:53.678 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:53.791 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:39:56.792 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:39:56.995 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:00.038 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:00.245 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:03.060 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:03.222 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:06.226 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:06.340 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:09.346 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:09.445 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:12.450 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:12.655 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:15.707 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:15.910 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:18.736 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:18.877 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:21.878 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:22.081 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:25.106 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:25.307 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:28.135 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:28.268 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:31.274 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:31.477 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:34.474 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:34.478 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:37.478 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:37.682 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:40.717 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:40.921 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:43.759 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:43.888 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:46.892 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:47.086 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:50.087 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:50.292 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:53.325 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:53.529 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:56.356 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:56.481 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:40:59.486 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:40:59.692 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:02.730 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:02.932 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:05.771 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:05.915 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:08.916 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:09.095 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:12.100 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:12.302 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:15.343 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:15.544 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:18.373 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:18.482 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:21.488 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:21.692 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:24.727 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:24.931 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:27.978 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:28.182 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:30.999 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:31.137 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:34.142 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:34.264 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:37.291 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:37.496 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:40.506 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:40.506 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:43.507 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:43.646 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:46.679 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:46.882 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:49.842 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:49.843 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:52.848 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:53.054 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:56.028 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:41:59.033 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:41:59.189 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:02.194 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:02.304 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:05.306 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:05.511 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:08.521 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:08.521 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:11.522 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:11.726 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:14.771 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:14.973 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:17.790 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:17.946 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:20.948 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:21.072 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:24.114 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:24.315 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:27.353 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:27.556 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:30.384 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:30.587 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:33.584 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:33.584 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:36.588 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:36.793 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:39.783 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:39.783 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:42.788 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:42.954 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:45.984 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:46.189 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:49.211 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:49.213 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:52.213 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:52.338 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:55.345 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:55.549 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:42:58.586 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:42:58.587 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:01.588 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:01.677 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:04.681 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:04.884 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:07.914 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:08.116 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:10.938 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:11.112 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:14.114 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:14.258 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:17.262 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:17.465 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:20.496 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:20.497 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:23.502 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:23.705 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:26.768 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:26.768 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:29.773 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:29.885 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:32.938 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:33.140 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:36.184 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:36.184 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:39.189 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:39.286 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:42.304 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:42.509 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:45.560 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:45.765 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:48.764 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:48.765 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:51.769 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:51.964 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:54.969 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:55.171 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:43:58.223 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:43:58.425 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:01.258 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:01.369 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:04.373 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:04.574 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:07.621 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:07.826 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:10.800 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:10.800 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:13.804 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:13.918 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:16.923 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:17.067 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:20.069 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:20.207 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:23.235 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:23.438 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:26.471 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:26.674 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:29.637 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:29.637 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:32.642 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:32.845 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:35.881 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:36.085 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:39.050 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:39.050 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:42.054 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:42.225 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:45.231 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:45.422 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:48.427 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:48.560 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:51.563 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:51.766 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:54.810 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:55.014 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:44:57.841 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:44:58.045 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:01.089 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:01.291 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:04.128 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:04.289 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:07.295 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:07.421 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:10.457 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:10.658 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:13.688 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:13.891 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:16.864 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:16.864 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:19.866 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:20.011 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:23.046 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:23.250 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:26.284 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:26.485 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:29.521 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:29.726 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:32.561 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:32.762 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:35.794 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:35.996 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:38.809 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:38.935 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:41.936 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:42.139 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:45.169 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:45.171 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:48.175 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:48.380 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:51.422 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:51.514 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:54.545 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:54.749 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:45:57.726 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:45:57.726 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:00.731 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:00.879 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:03.885 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:04.091 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:07.132 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:07.235 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:10.277 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:10.479 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:13.523 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:13.728 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:16.697 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:16.697 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:19.701 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:19.829 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:22.835 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:23.006 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:26.010 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:26.161 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:29.197 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:29.399 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:32.399 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:32.400 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:35.404 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:35.561 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:38.593 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:38.795 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:41.785 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:41.785 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:44.788 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:44.993 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:47.989 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:47.990 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:50.993 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:51.169 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:54.174 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:54.378 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:46:57.413 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:46:57.618 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:00.584 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:00.585 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:03.588 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:03.731 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:06.736 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:06.875 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:09.880 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:10.055 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:13.100 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:13.303 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:16.341 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:16.342 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:19.345 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:19.439 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:22.464 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:22.664 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:25.698 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:25.698 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:28.704 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:28.905 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:31.957 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:32.160 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:34.963 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:35.118 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:38.123 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:38.278 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:41.281 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:41.486 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:44.521 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:44.522 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:47.525 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:47.642 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:50.645 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:50.811 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:53.813 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:53.920 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:47:56.952 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:47:57.155 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:00.206 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:00.411 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:03.226 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:03.355 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:06.360 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:06.565 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:09.613 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:09.818 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:12.841 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:12.842 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:15.843 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:15.946 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:18.950 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:19.154 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:22.193 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:22.397 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:25.363 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:25.364 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:28.367 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:28.569 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:31.596 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:31.597 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:34.601 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:34.728 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:37.758 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:37.963 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:40.935 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:40.935 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:43.936 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:44.139 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:47.180 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:47.383 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:50.348 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:50.348 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:53.352 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:53.507 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:56.511 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:56.714 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:48:59.749 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:48:59.951 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:02.893 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:02.894 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:05.899 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:06.100 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:09.142 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:09.343 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:12.293 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:12.293 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:15.294 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:15.498 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:18.532 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:18.737 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:21.561 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:21.707 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:24.708 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:24.912 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:27.943 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:28.148 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:30.990 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:31.194 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:34.240 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:34.445 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:37.284 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:37.392 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:40.394 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:40.597 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:43.648 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:43.854 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:46.696 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:46.902 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:49.891 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:49.891 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:52.894 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:53.097 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:56.123 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:56.124 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:49:59.125 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:49:59.279 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:02.280 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:02.485 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:05.514 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:05.719 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:08.736 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:08.737 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:11.741 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:11.866 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:14.911 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:15.114 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:18.134 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:18.134 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:21.134 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:21.256 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:24.300 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:24.504 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:27.541 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:27.742 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:30.545 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:30.689 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:33.694 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:33.899 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:36.901 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:36.901 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:39.907 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:40.046 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:43.076 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:43.278 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:46.329 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:46.535 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:49.359 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:49.490 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:52.493 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:52.698 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:55.728 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:55.728 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:50:58.734 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:50:58.935 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:01.980 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:02.183 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:05.011 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:05.416 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:08.223 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:08.413 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:11.416 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:11.506 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:14.525 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:14.726 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:17.766 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:17.767 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:20.772 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:20.885 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:23.888 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:23.972 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:26.974 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:27.175 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:30.206 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:30.206 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:33.210 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:33.414 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:36.457 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:36.662 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:39.492 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:39.696 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:42.664 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:42.665 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:45.669 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:45.821 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:48.824 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:48.954 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:51.956 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:52.105 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:55.119 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:55.323 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:51:58.375 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:51:58.376 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:01.380 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:01.496 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:04.518 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:04.719 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:07.725 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:07.726 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:10.726 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:10.930 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:13.958 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:14.164 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:17.007 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:17.408 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:20.217 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:20.341 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:23.349 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:23.553 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:26.586 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:26.788 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:29.774 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:29.775 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:32.778 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:32.981 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:35.965 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:35.965 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:38.969 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:39.176 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:42.208 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:42.208 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:45.209 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:45.400 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:48.404 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:48.609 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:51.633 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:51.633 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:54.634 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:54.840 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:52:57.868 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:52:57.868 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:00.871 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:00.992 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:03.993 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:04.137 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:07.146 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:07.350 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:10.369 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:10.369 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:13.374 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:13.524 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:16.527 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:16.730 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:19.743 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:19.743 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:22.748 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:22.902 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:25.904 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:26.013 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:29.017 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:29.223 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:32.262 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:32.468 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:35.301 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:35.504 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:38.461 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:38.462 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:41.467 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:41.670 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:44.706 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:44.909 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:47.919 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:47.920 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:50.920 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:51.010 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:54.042 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:54.244 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:53:57.292 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:53:57.498 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:00.481 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:00.481 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:03.485 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:03.613 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:06.620 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:06.732 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:09.737 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:09.941 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:12.941 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:12.941 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:15.947 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:16.151 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:19.191 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:19.394 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:22.224 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:22.430 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:25.449 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:25.450 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:28.451 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:28.652 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:31.652 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:31.652 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:34.655 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:34.861 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:37.868 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:37.868 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:40.870 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:41.075 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:44.088 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:44.088 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:47.092 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:47.250 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:50.287 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:50.492 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:53.505 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:53.506 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:56.512 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:56.717 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:54:59.757 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:54:59.962 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:02.945 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:02.945 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:05.948 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:06.096 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:09.119 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:09.308 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:12.311 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:12.417 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:15.418 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:15.587 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:18.614 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:18.820 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:21.826 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:21.826 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:24.828 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:25.031 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:28.074 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:28.275 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:31.115 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:31.318 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:34.308 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:34.308 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:37.312 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:37.514 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:40.541 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:40.542 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:43.546 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:43.746 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:46.780 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:46.983 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:49.801 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:49.906 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:52.910 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:53.112 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:56.147 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:56.348 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:55:59.320 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:55:59.322 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:02.325 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:02.531 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:05.577 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:05.781 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:08.607 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:08.810 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:11.817 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:11.817 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:14.818 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:15.020 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:18.056 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:18.056 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:21.060 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:21.228 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:24.230 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:24.432 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:27.457 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:27.457 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:30.459 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:30.594 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:33.620 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:33.825 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:36.828 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:36.829 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:39.836 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:40.040 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:43.069 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:43.070 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:46.075 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:46.208 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:49.256 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:49.460 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:52.443 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:52.445 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[INFO ] 2025-03-28 16:56:55.451 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:55.655 - [SP9_COM][sybase_190 - COM_DB_9] - normal rescan, will sleep 3s, and scan from startRid: 65574, rowId: 15 
[TRACE] 2025-03-28 16:56:56.482 - [SP9_COM][sybase_190 - COM_DB_9] - Node sybase_190 - COM_DB_9[783c9cdf-3d49-4964-a464-f5c6f020e69c] running status set to false 
[INFO ] 2025-03-28 16:56:56.482 - [SP9_COM][sybase_190 - COM_DB_9] - rebuild statement with 65574, 15 
[INFO ] 2025-03-28 16:56:56.482 - [SP9_COM][sybase_190 - COM_DB_9] - Log Miner is shutting down... 
[TRACE] 2025-03-28 16:56:56.673 - [SP9_COM][sybase_190 - COM_DB_9] - PDK connector node stopped: HazelcastSourcePdkDataNode_783c9cdf-3d49-4964-a464-f5c6f020e69c_1743149091164 
[TRACE] 2025-03-28 16:56:56.673 - [SP9_COM][sybase_190 - COM_DB_9] - PDK connector node released: HazelcastSourcePdkDataNode_783c9cdf-3d49-4964-a464-f5c6f020e69c_1743149091164 
[TRACE] 2025-03-28 16:56:56.673 - [SP9_COM][sybase_190 - COM_DB_9] - Node sybase_190 - COM_DB_9[783c9cdf-3d49-4964-a464-f5c6f020e69c] schema data cleaned 
[TRACE] 2025-03-28 16:56:56.674 - [SP9_COM][sybase_190 - COM_DB_9] - Node sybase_190 - COM_DB_9[783c9cdf-3d49-4964-a464-f5c6f020e69c] monitor closed 
[TRACE] 2025-03-28 16:56:56.675 - [SP9_COM][sybase_190 - COM_DB_9] - Node sybase_190 - COM_DB_9[783c9cdf-3d49-4964-a464-f5c6f020e69c] close complete, cost 193 ms 
[TRACE] 2025-03-28 16:56:56.676 - [SP9_COM][pg_hdtest - SP9_com] - Node pg_hdtest - SP9_com[c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f] running status set to false 
[TRACE] 2025-03-28 16:56:56.677 - [SP9_COM][sybase_190 - COM_DB_9] - Incremental sync completed 
[TRACE] 2025-03-28 16:56:56.681 - [SP9_COM][pg_hdtest - SP9_com] - PDK connector node stopped: HazelcastTargetPdkDataNode_c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f_1743149091186 
[TRACE] 2025-03-28 16:56:56.681 - [SP9_COM][pg_hdtest - SP9_com] - PDK connector node released: HazelcastTargetPdkDataNode_c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f_1743149091186 
[TRACE] 2025-03-28 16:56:56.681 - [SP9_COM][pg_hdtest - SP9_com] - Node pg_hdtest - SP9_com[c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f] schema data cleaned 
[TRACE] 2025-03-28 16:56:56.682 - [SP9_COM][pg_hdtest - SP9_com] - Node pg_hdtest - SP9_com[c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f] monitor closed 
[TRACE] 2025-03-28 16:56:56.682 - [SP9_COM][pg_hdtest - SP9_com] - Node pg_hdtest - SP9_com[c5d0f65d-5f5d-4849-923a-1cb9a7a9e75f] close complete, cost 7 ms 
[TRACE] 2025-03-28 16:56:57.200 - [SP9_COM] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-28 16:56:57.202 - [SP9_COM] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@288ef313 
[TRACE] 2025-03-28 16:56:57.202 - [SP9_COM] - Stop task milestones: 67e657b8b697a23a44e4bc4b(SP9_COM)  
[TRACE] 2025-03-28 16:56:57.323 - [SP9_COM] - Stopped task aspect(s) 
[TRACE] 2025-03-28 16:56:57.323 - [SP9_COM] - Snapshot order controller have been removed 
[INFO ] 2025-03-28 16:56:57.323 - [SP9_COM] - Task stopped. 
[TRACE] 2025-03-28 16:57:02.331 - [SP9_COM] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-28 16:57:02.332 - [SP9_COM] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@288ef313 
[TRACE] 2025-03-28 16:57:02.332 - [SP9_COM] - Stopped task aspect(s) 
[INFO ] 2025-03-28 16:57:02.332 - [SP9_COM] - Task stopped. 
[TRACE] 2025-03-28 16:57:02.380 - [SP9_COM] - Remove memory task client succeed, task: SP9_COM[67e657b8b697a23a44e4bc4b] 
[TRACE] 2025-03-28 16:57:02.380 - [SP9_COM] - Destroy memory task client cache succeed, task: SP9_COM[67e657b8b697a23a44e4bc4b] 
