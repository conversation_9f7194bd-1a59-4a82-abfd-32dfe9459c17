[TRACE] 2024-12-26 16:22:26.749 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:26.749 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:26.749 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:26.749 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:26.749 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:26.750 - [任务 67(100)][b7b09b05-c9e7-4c81-b995-0ed1441848a6] - Node b7b09b05-c9e7-4c81-b995-0ed1441848a6[b7b09b05-c9e7-4c81-b995-0ed1441848a6] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:26.750 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:26.750 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:26.750 - [任务 67(100)][b7b09b05-c9e7-4c81-b995-0ed1441848a6] - Node b7b09b05-c9e7-4c81-b995-0ed1441848a6[b7b09b05-c9e7-4c81-b995-0ed1441848a6] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:26.750 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:27.007 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:27.022 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:27.022 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201346770 
[TRACE] 2024-12-26 16:22:27.022 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201346770 
[TRACE] 2024-12-26 16:22:27.022 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:27.023 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:27.023 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 16 ms 
[TRACE] 2024-12-26 16:22:32.501 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:32.504 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:32.504 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 5490 ms 
[TRACE] 2024-12-26 16:22:33.010 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:22:33.010 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-7c934d06-9960-43cf-9911-8384294cf9ff 
[INFO ] 2024-12-26 16:22:33.010 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-7c934d06-9960-43cf-9911-8384294cf9ff 
[INFO ] 2024-12-26 16:22:33.010 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:33.013 - [任务 67(100)][b7b09b05-c9e7-4c81-b995-0ed1441848a6] - Node b7b09b05-c9e7-4c81-b995-0ed1441848a6[b7b09b05-c9e7-4c81-b995-0ed1441848a6] running status set to false 
[TRACE] 2024-12-26 16:22:33.013 - [任务 67(100)][b7b09b05-c9e7-4c81-b995-0ed1441848a6] - Node b7b09b05-c9e7-4c81-b995-0ed1441848a6[b7b09b05-c9e7-4c81-b995-0ed1441848a6] schema data cleaned 
[TRACE] 2024-12-26 16:22:33.013 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:33.013 - [任务 67(100)][b7b09b05-c9e7-4c81-b995-0ed1441848a6] - Node b7b09b05-c9e7-4c81-b995-0ed1441848a6[b7b09b05-c9e7-4c81-b995-0ed1441848a6] monitor closed 
[TRACE] 2024-12-26 16:22:33.013 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:33.013 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 7 ms 
[TRACE] 2024-12-26 16:22:33.015 - [任务 67(100)][b7b09b05-c9e7-4c81-b995-0ed1441848a6] - Node b7b09b05-c9e7-4c81-b995-0ed1441848a6[b7b09b05-c9e7-4c81-b995-0ed1441848a6] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:22:33.015 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:33.015 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:33.217 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:35.892 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:35.892 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:35.892 - [任务 67(100)][8abc0add-4e01-46af-9a06-a4d2fb85e1e1] - Node 8abc0add-4e01-46af-9a06-a4d2fb85e1e1[8abc0add-4e01-46af-9a06-a4d2fb85e1e1] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:35.892 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:35.892 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:35.892 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:35.893 - [任务 67(100)][8abc0add-4e01-46af-9a06-a4d2fb85e1e1] - Node 8abc0add-4e01-46af-9a06-a4d2fb85e1e1[8abc0add-4e01-46af-9a06-a4d2fb85e1e1] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:35.893 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:35.893 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:36.012 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:36.012 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:36.014 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:36.014 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201355907 
[TRACE] 2024-12-26 16:22:36.014 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201355907 
[TRACE] 2024-12-26 16:22:36.014 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:36.014 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:36.014 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:22:36.073 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:36.073 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:36.073 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 60 ms 
[TRACE] 2024-12-26 16:22:36.226 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:22:36.226 - [任务 67(100)][8abc0add-4e01-46af-9a06-a4d2fb85e1e1] - Node 8abc0add-4e01-46af-9a06-a4d2fb85e1e1[8abc0add-4e01-46af-9a06-a4d2fb85e1e1] running status set to false 
[TRACE] 2024-12-26 16:22:36.226 - [任务 67(100)][8abc0add-4e01-46af-9a06-a4d2fb85e1e1] - Node 8abc0add-4e01-46af-9a06-a4d2fb85e1e1[8abc0add-4e01-46af-9a06-a4d2fb85e1e1] schema data cleaned 
[TRACE] 2024-12-26 16:22:36.226 - [任务 67(100)][8abc0add-4e01-46af-9a06-a4d2fb85e1e1] - Node 8abc0add-4e01-46af-9a06-a4d2fb85e1e1[8abc0add-4e01-46af-9a06-a4d2fb85e1e1] monitor closed 
[TRACE] 2024-12-26 16:22:36.228 - [任务 67(100)][8abc0add-4e01-46af-9a06-a4d2fb85e1e1] - Node 8abc0add-4e01-46af-9a06-a4d2fb85e1e1[8abc0add-4e01-46af-9a06-a4d2fb85e1e1] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:22:36.228 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-8e7ef22c-9565-4a8d-a5ff-b08ddf76e0a7 
[INFO ] 2024-12-26 16:22:36.228 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-8e7ef22c-9565-4a8d-a5ff-b08ddf76e0a7 
[INFO ] 2024-12-26 16:22:36.228 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:36.229 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:36.229 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:36.229 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 5 ms 
[TRACE] 2024-12-26 16:22:36.230 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:36.230 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:36.230 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:37.619 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:37.619 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:37.619 - [任务 67(100)][70f4537c-daf6-44ea-9a4c-9d4602d0af5f] - Node 70f4537c-daf6-44ea-9a4c-9d4602d0af5f[70f4537c-daf6-44ea-9a4c-9d4602d0af5f] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:37.619 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.619 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.620 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:37.620 - [任务 67(100)][70f4537c-daf6-44ea-9a4c-9d4602d0af5f] - Node 70f4537c-daf6-44ea-9a4c-9d4602d0af5f[70f4537c-daf6-44ea-9a4c-9d4602d0af5f] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.620 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:37.620 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.690 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:37.690 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:37.690 - [任务 67(100)][3adb8e45-d125-4a71-beaf-bfa541adeae3] - Node 3adb8e45-d125-4a71-beaf-bfa541adeae3[3adb8e45-d125-4a71-beaf-bfa541adeae3] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:37.690 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.690 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:37.690 - [任务 67(100)][3adb8e45-d125-4a71-beaf-bfa541adeae3] - Node 3adb8e45-d125-4a71-beaf-bfa541adeae3[3adb8e45-d125-4a71-beaf-bfa541adeae3] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.691 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:37.691 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.691 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.691 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:37.757 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:37.757 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:37.759 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:37.759 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201357639 
[TRACE] 2024-12-26 16:22:37.759 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201357639 
[TRACE] 2024-12-26 16:22:37.759 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.759 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:37.759 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:22:37.807 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.807 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:37.807 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 48 ms 
[TRACE] 2024-12-26 16:22:37.827 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:37.830 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:37.830 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201357701 
[TRACE] 2024-12-26 16:22:37.830 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201357701 
[TRACE] 2024-12-26 16:22:37.830 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.830 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:37.830 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:22:37.875 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.875 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:37.875 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 48 ms 
[TRACE] 2024-12-26 16:22:37.959 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:22:37.961 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-45fbac55-0884-49a1-97bd-a0a51d90e17e 
[INFO ] 2024-12-26 16:22:37.961 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-45fbac55-0884-49a1-97bd-a0a51d90e17e 
[TRACE] 2024-12-26 16:22:37.962 - [任务 67(100)][70f4537c-daf6-44ea-9a4c-9d4602d0af5f] - Node 70f4537c-daf6-44ea-9a4c-9d4602d0af5f[70f4537c-daf6-44ea-9a4c-9d4602d0af5f] running status set to false 
[INFO ] 2024-12-26 16:22:37.962 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.962 - [任务 67(100)][70f4537c-daf6-44ea-9a4c-9d4602d0af5f] - Node 70f4537c-daf6-44ea-9a4c-9d4602d0af5f[70f4537c-daf6-44ea-9a4c-9d4602d0af5f] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.962 - [任务 67(100)][70f4537c-daf6-44ea-9a4c-9d4602d0af5f] - Node 70f4537c-daf6-44ea-9a4c-9d4602d0af5f[70f4537c-daf6-44ea-9a4c-9d4602d0af5f] monitor closed 
[TRACE] 2024-12-26 16:22:37.962 - [任务 67(100)][70f4537c-daf6-44ea-9a4c-9d4602d0af5f] - Node 70f4537c-daf6-44ea-9a4c-9d4602d0af5f[70f4537c-daf6-44ea-9a4c-9d4602d0af5f] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:22:37.962 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:37.963 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:37.963 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:37.963 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:37.963 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:38.035 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:38.035 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:22:38.036 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-be7155fe-a683-4e70-adaa-baa682056b91 
[INFO ] 2024-12-26 16:22:38.036 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-be7155fe-a683-4e70-adaa-baa682056b91 
[INFO ] 2024-12-26 16:22:38.036 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:38.037 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:38.037 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:38.037 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:22:38.037 - [任务 67(100)][3adb8e45-d125-4a71-beaf-bfa541adeae3] - Node 3adb8e45-d125-4a71-beaf-bfa541adeae3[3adb8e45-d125-4a71-beaf-bfa541adeae3] running status set to false 
[TRACE] 2024-12-26 16:22:38.037 - [任务 67(100)][3adb8e45-d125-4a71-beaf-bfa541adeae3] - Node 3adb8e45-d125-4a71-beaf-bfa541adeae3[3adb8e45-d125-4a71-beaf-bfa541adeae3] schema data cleaned 
[TRACE] 2024-12-26 16:22:38.037 - [任务 67(100)][3adb8e45-d125-4a71-beaf-bfa541adeae3] - Node 3adb8e45-d125-4a71-beaf-bfa541adeae3[3adb8e45-d125-4a71-beaf-bfa541adeae3] monitor closed 
[TRACE] 2024-12-26 16:22:38.038 - [任务 67(100)][3adb8e45-d125-4a71-beaf-bfa541adeae3] - Node 3adb8e45-d125-4a71-beaf-bfa541adeae3[3adb8e45-d125-4a71-beaf-bfa541adeae3] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:22:38.038 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:38.038 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:38.038 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 1 ms 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:40.804 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:40.805 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:40.805 - [任务 67(100)][f53a5802-fbc0-42e7-9c8e-47331109925d] - Node f53a5802-fbc0-42e7-9c8e-47331109925d[f53a5802-fbc0-42e7-9c8e-47331109925d] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:40.973 - [任务 67(100)][f53a5802-fbc0-42e7-9c8e-47331109925d] - Node f53a5802-fbc0-42e7-9c8e-47331109925d[f53a5802-fbc0-42e7-9c8e-47331109925d] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:40.973 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:40.975 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:40.975 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201360826 
[TRACE] 2024-12-26 16:22:40.975 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201360826 
[TRACE] 2024-12-26 16:22:40.975 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:40.976 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:40.976 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:41.021 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.021 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:41.021 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 46 ms 
[TRACE] 2024-12-26 16:22:41.184 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:22:41.185 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-66946b57-7901-4045-8fa9-4c8c826909d1 
[TRACE] 2024-12-26 16:22:41.185 - [任务 67(100)][f53a5802-fbc0-42e7-9c8e-47331109925d] - Node f53a5802-fbc0-42e7-9c8e-47331109925d[f53a5802-fbc0-42e7-9c8e-47331109925d] running status set to false 
[INFO ] 2024-12-26 16:22:41.185 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-66946b57-7901-4045-8fa9-4c8c826909d1 
[TRACE] 2024-12-26 16:22:41.185 - [任务 67(100)][f53a5802-fbc0-42e7-9c8e-47331109925d] - Node f53a5802-fbc0-42e7-9c8e-47331109925d[f53a5802-fbc0-42e7-9c8e-47331109925d] schema data cleaned 
[INFO ] 2024-12-26 16:22:41.185 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.185 - [任务 67(100)][f53a5802-fbc0-42e7-9c8e-47331109925d] - Node f53a5802-fbc0-42e7-9c8e-47331109925d[f53a5802-fbc0-42e7-9c8e-47331109925d] monitor closed 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)][f53a5802-fbc0-42e7-9c8e-47331109925d] - Node f53a5802-fbc0-42e7-9c8e-47331109925d[f53a5802-fbc0-42e7-9c8e-47331109925d] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:41.186 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:41.616 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:41.616 - [任务 67(100)][db6569d5-6bc0-4afa-84e8-15d57b5c2396] - Node db6569d5-6bc0-4afa-84e8-15d57b5c2396[db6569d5-6bc0-4afa-84e8-15d57b5c2396] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:41.616 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][db6569d5-6bc0-4afa-84e8-15d57b5c2396] - Node db6569d5-6bc0-4afa-84e8-15d57b5c2396[db6569d5-6bc0-4afa-84e8-15d57b5c2396] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:41.617 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:41.753 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:41.755 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201361634 
[TRACE] 2024-12-26 16:22:41.755 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201361634 
[TRACE] 2024-12-26 16:22:41.755 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.755 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:41.757 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:22:41.757 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:41.801 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.801 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:41.801 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 44 ms 
[TRACE] 2024-12-26 16:22:41.958 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:22:41.958 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-bc9953b4-b481-4e0d-b0ab-32b173ad69f3 
[INFO ] 2024-12-26 16:22:41.958 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-bc9953b4-b481-4e0d-b0ab-32b173ad69f3 
[INFO ] 2024-12-26 16:22:41.958 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.958 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.959 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:41.959 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 4 ms 
[TRACE] 2024-12-26 16:22:41.960 - [任务 67(100)][db6569d5-6bc0-4afa-84e8-15d57b5c2396] - Node db6569d5-6bc0-4afa-84e8-15d57b5c2396[db6569d5-6bc0-4afa-84e8-15d57b5c2396] running status set to false 
[TRACE] 2024-12-26 16:22:41.960 - [任务 67(100)][db6569d5-6bc0-4afa-84e8-15d57b5c2396] - Node db6569d5-6bc0-4afa-84e8-15d57b5c2396[db6569d5-6bc0-4afa-84e8-15d57b5c2396] schema data cleaned 
[TRACE] 2024-12-26 16:22:41.960 - [任务 67(100)][db6569d5-6bc0-4afa-84e8-15d57b5c2396] - Node db6569d5-6bc0-4afa-84e8-15d57b5c2396[db6569d5-6bc0-4afa-84e8-15d57b5c2396] monitor closed 
[TRACE] 2024-12-26 16:22:41.960 - [任务 67(100)][db6569d5-6bc0-4afa-84e8-15d57b5c2396] - Node db6569d5-6bc0-4afa-84e8-15d57b5c2396[db6569d5-6bc0-4afa-84e8-15d57b5c2396] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:22:41.961 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:41.961 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:41.961 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:45.351 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][c702a852-e4c8-42fe-b9fd-2c7ec05919c2] - Node c702a852-e4c8-42fe-b9fd-2c7ec05919c2[c702a852-e4c8-42fe-b9fd-2c7ec05919c2] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][c702a852-e4c8-42fe-b9fd-2c7ec05919c2] - Node c702a852-e4c8-42fe-b9fd-2c7ec05919c2[c702a852-e4c8-42fe-b9fd-2c7ec05919c2] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:45.352 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:45.528 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:45.528 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:45.531 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:45.531 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201365370 
[TRACE] 2024-12-26 16:22:45.531 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201365370 
[TRACE] 2024-12-26 16:22:45.531 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:45.531 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:45.531 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:45.597 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:45.597 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:45.597 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 66 ms 
[TRACE] 2024-12-26 16:22:45.725 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:22:45.725 - [任务 67(100)][c702a852-e4c8-42fe-b9fd-2c7ec05919c2] - Node c702a852-e4c8-42fe-b9fd-2c7ec05919c2[c702a852-e4c8-42fe-b9fd-2c7ec05919c2] running status set to false 
[TRACE] 2024-12-26 16:22:45.725 - [任务 67(100)][c702a852-e4c8-42fe-b9fd-2c7ec05919c2] - Node c702a852-e4c8-42fe-b9fd-2c7ec05919c2[c702a852-e4c8-42fe-b9fd-2c7ec05919c2] schema data cleaned 
[TRACE] 2024-12-26 16:22:45.725 - [任务 67(100)][c702a852-e4c8-42fe-b9fd-2c7ec05919c2] - Node c702a852-e4c8-42fe-b9fd-2c7ec05919c2[c702a852-e4c8-42fe-b9fd-2c7ec05919c2] monitor closed 
[TRACE] 2024-12-26 16:22:45.725 - [任务 67(100)][c702a852-e4c8-42fe-b9fd-2c7ec05919c2] - Node c702a852-e4c8-42fe-b9fd-2c7ec05919c2[c702a852-e4c8-42fe-b9fd-2c7ec05919c2] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:22:45.725 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-613242af-061a-4908-95bb-77a402220ebf 
[INFO ] 2024-12-26 16:22:45.725 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-613242af-061a-4908-95bb-77a402220ebf 
[INFO ] 2024-12-26 16:22:45.725 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:45.726 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:45.726 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:45.726 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 4 ms 
[TRACE] 2024-12-26 16:22:45.726 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:45.726 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:45.927 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:48.370 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:48.370 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] - Node fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b[fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] - Node fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b[fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:48.371 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:48.515 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:48.515 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:48.515 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:48.518 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201368390 
[TRACE] 2024-12-26 16:22:48.518 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201368390 
[TRACE] 2024-12-26 16:22:48.518 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:48.518 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:48.594 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:48.594 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:48.594 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:48.594 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 79 ms 
[TRACE] 2024-12-26 16:22:48.806 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:22:48.808 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-1290b0a1-623f-43ff-ae8f-779923e5bb2f 
[INFO ] 2024-12-26 16:22:48.808 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-1290b0a1-623f-43ff-ae8f-779923e5bb2f 
[INFO ] 2024-12-26 16:22:48.808 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:48.808 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:48.809 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:48.809 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:22:48.812 - [任务 67(100)][fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] - Node fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b[fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] running status set to false 
[TRACE] 2024-12-26 16:22:48.812 - [任务 67(100)][fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] - Node fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b[fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] schema data cleaned 
[TRACE] 2024-12-26 16:22:48.812 - [任务 67(100)][fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] - Node fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b[fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] monitor closed 
[TRACE] 2024-12-26 16:22:48.812 - [任务 67(100)][fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] - Node fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b[fa1837c7-44c6-49f5-8d2a-e85fa94c5d4b] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:22:48.814 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:48.814 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:48.814 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:22:49.383 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:49.383 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:49.383 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:22:49.383 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:49.384 - [任务 67(100)][0542319b-1a36-4614-941b-834fa0449da4] - Node 0542319b-1a36-4614-941b-834fa0449da4[0542319b-1a36-4614-941b-834fa0449da4] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:22:49.384 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:49.384 - [任务 67(100)][0542319b-1a36-4614-941b-834fa0449da4] - Node 0542319b-1a36-4614-941b-834fa0449da4[0542319b-1a36-4614-941b-834fa0449da4] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:49.384 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:22:49.385 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:22:49.587 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:22:49.666 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:22:49.666 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:22:49.668 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201369545 
[TRACE] 2024-12-26 16:22:49.668 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201369545 
[TRACE] 2024-12-26 16:22:49.669 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:22:49.669 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:22:49.714 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:49.714 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:22:49.714 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:22:49.714 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 48 ms 
[TRACE] 2024-12-26 16:22:49.855 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:22:49.855 - [任务 67(100)][0542319b-1a36-4614-941b-834fa0449da4] - Node 0542319b-1a36-4614-941b-834fa0449da4[0542319b-1a36-4614-941b-834fa0449da4] running status set to false 
[INFO ] 2024-12-26 16:22:49.855 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-cb215a07-c0de-4361-8eba-72fe55043967 
[TRACE] 2024-12-26 16:22:49.855 - [任务 67(100)][0542319b-1a36-4614-941b-834fa0449da4] - Node 0542319b-1a36-4614-941b-834fa0449da4[0542319b-1a36-4614-941b-834fa0449da4] schema data cleaned 
[INFO ] 2024-12-26 16:22:49.855 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-cb215a07-c0de-4361-8eba-72fe55043967 
[TRACE] 2024-12-26 16:22:49.855 - [任务 67(100)][0542319b-1a36-4614-941b-834fa0449da4] - Node 0542319b-1a36-4614-941b-834fa0449da4[0542319b-1a36-4614-941b-834fa0449da4] monitor closed 
[INFO ] 2024-12-26 16:22:49.855 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:22:49.855 - [任务 67(100)][0542319b-1a36-4614-941b-834fa0449da4] - Node 0542319b-1a36-4614-941b-834fa0449da4[0542319b-1a36-4614-941b-834fa0449da4] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:22:49.856 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:22:49.856 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:22:49.857 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:22:49.857 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:22:49.857 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:22:49.857 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:24:27.391 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] - Node 436e5fa2-2c6c-4e11-9f2e-959de4e39cc2[436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] - Node 436e5fa2-2c6c-4e11-9f2e-959de4e39cc2[436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:27.392 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:24:27.576 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:24:27.576 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:24:27.583 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201467424 
[TRACE] 2024-12-26 16:24:27.583 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201467424 
[TRACE] 2024-12-26 16:24:27.583 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:24:27.583 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:24:27.583 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 14 ms 
[TRACE] 2024-12-26 16:24:27.647 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:24:27.647 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:24:27.647 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 71 ms 
[TRACE] 2024-12-26 16:24:27.801 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:24:27.801 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-1be4a1c0-5a13-4a95-b7a9-9a1a08c7197a 
[INFO ] 2024-12-26 16:24:27.801 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-1be4a1c0-5a13-4a95-b7a9-9a1a08c7197a 
[INFO ] 2024-12-26 16:24:27.801 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:24:27.802 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:24:27.802 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:24:27.802 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:24:27.803 - [任务 67(100)][436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] - Node 436e5fa2-2c6c-4e11-9f2e-959de4e39cc2[436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] running status set to false 
[TRACE] 2024-12-26 16:24:27.803 - [任务 67(100)][436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] - Node 436e5fa2-2c6c-4e11-9f2e-959de4e39cc2[436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] schema data cleaned 
[TRACE] 2024-12-26 16:24:27.803 - [任务 67(100)][436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] - Node 436e5fa2-2c6c-4e11-9f2e-959de4e39cc2[436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] monitor closed 
[TRACE] 2024-12-26 16:24:27.803 - [任务 67(100)][436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] - Node 436e5fa2-2c6c-4e11-9f2e-959de4e39cc2[436e5fa2-2c6c-4e11-9f2e-959de4e39cc2] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:24:27.804 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:24:27.804 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:24:27.804 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] - Node aa9f72f5-41cf-489d-aa41-bfc58a2f57e5[aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] - Node aa9f72f5-41cf-489d-aa41-bfc58a2f57e5[aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:29.193 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:24:29.340 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:24:29.343 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:24:29.343 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:24:29.344 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201469212 
[TRACE] 2024-12-26 16:24:29.344 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201469212 
[TRACE] 2024-12-26 16:24:29.344 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:24:29.344 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:24:29.344 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 4 ms 
[TRACE] 2024-12-26 16:24:29.422 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:24:29.422 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:24:29.422 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 81 ms 
[TRACE] 2024-12-26 16:24:29.582 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:24:29.582 - [任务 67(100)][aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] - Node aa9f72f5-41cf-489d-aa41-bfc58a2f57e5[aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] running status set to false 
[TRACE] 2024-12-26 16:24:29.582 - [任务 67(100)][aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] - Node aa9f72f5-41cf-489d-aa41-bfc58a2f57e5[aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] schema data cleaned 
[TRACE] 2024-12-26 16:24:29.582 - [任务 67(100)][aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] - Node aa9f72f5-41cf-489d-aa41-bfc58a2f57e5[aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] monitor closed 
[TRACE] 2024-12-26 16:24:29.582 - [任务 67(100)][aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] - Node aa9f72f5-41cf-489d-aa41-bfc58a2f57e5[aa9f72f5-41cf-489d-aa41-bfc58a2f57e5] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:24:29.582 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-d9f300a1-5116-4065-9457-484b92775a49 
[INFO ] 2024-12-26 16:24:29.583 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-d9f300a1-5116-4065-9457-484b92775a49 
[INFO ] 2024-12-26 16:24:29.583 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:24:29.583 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:24:29.583 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:24:29.583 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:24:29.584 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:24:29.584 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:24:29.784 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:24:34.665 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:34.667 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][7d9a58da-2af5-4e6c-971e-d80b167b1fc1] - Node 7d9a58da-2af5-4e6c-971e-d80b167b1fc1[7d9a58da-2af5-4e6c-971e-d80b167b1fc1] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][7d9a58da-2af5-4e6c-971e-d80b167b1fc1] - Node 7d9a58da-2af5-4e6c-971e-d80b167b1fc1[7d9a58da-2af5-4e6c-971e-d80b167b1fc1] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:24:34.668 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:24:34.669 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:24:34.803 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:24:34.803 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:24:34.806 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201474692 
[TRACE] 2024-12-26 16:24:34.806 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201474692 
[TRACE] 2024-12-26 16:24:34.806 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:24:34.806 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:24:34.864 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:24:34.864 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:24:34.864 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:24:34.864 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 61 ms 
[TRACE] 2024-12-26 16:24:35.013 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:24:35.013 - [任务 67(100)][7d9a58da-2af5-4e6c-971e-d80b167b1fc1] - Node 7d9a58da-2af5-4e6c-971e-d80b167b1fc1[7d9a58da-2af5-4e6c-971e-d80b167b1fc1] running status set to false 
[TRACE] 2024-12-26 16:24:35.013 - [任务 67(100)][7d9a58da-2af5-4e6c-971e-d80b167b1fc1] - Node 7d9a58da-2af5-4e6c-971e-d80b167b1fc1[7d9a58da-2af5-4e6c-971e-d80b167b1fc1] schema data cleaned 
[TRACE] 2024-12-26 16:24:35.013 - [任务 67(100)][7d9a58da-2af5-4e6c-971e-d80b167b1fc1] - Node 7d9a58da-2af5-4e6c-971e-d80b167b1fc1[7d9a58da-2af5-4e6c-971e-d80b167b1fc1] monitor closed 
[INFO ] 2024-12-26 16:24:35.013 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-a10271ab-2c3f-4aeb-ae47-0628abc5d753 
[TRACE] 2024-12-26 16:24:35.013 - [任务 67(100)][7d9a58da-2af5-4e6c-971e-d80b167b1fc1] - Node 7d9a58da-2af5-4e6c-971e-d80b167b1fc1[7d9a58da-2af5-4e6c-971e-d80b167b1fc1] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:24:35.013 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-a10271ab-2c3f-4aeb-ae47-0628abc5d753 
[INFO ] 2024-12-26 16:24:35.013 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:24:35.014 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:24:35.014 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:24:35.015 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:24:35.015 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:24:35.015 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:24:35.015 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][f52fd9f8-a806-4b8e-aeed-89707d15a07a] - Node f52fd9f8-a806-4b8e-aeed-89707d15a07a[f52fd9f8-a806-4b8e-aeed-89707d15a07a] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:33.639 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:33.640 - [任务 67(100)][f52fd9f8-a806-4b8e-aeed-89707d15a07a] - Node f52fd9f8-a806-4b8e-aeed-89707d15a07a[f52fd9f8-a806-4b8e-aeed-89707d15a07a] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:33.640 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:25:33.640 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:25:33.967 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:25:33.967 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:25:33.969 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201533842 
[TRACE] 2024-12-26 16:25:33.969 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201533842 
[TRACE] 2024-12-26 16:25:33.969 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:25:33.969 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:25:33.969 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:25:34.024 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:25:34.028 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:25:34.028 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 57 ms 
[TRACE] 2024-12-26 16:25:34.155 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:25:34.155 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-3e51646a-ce22-4df9-9e1c-5631bcce3d1c 
[INFO ] 2024-12-26 16:25:34.155 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-3e51646a-ce22-4df9-9e1c-5631bcce3d1c 
[INFO ] 2024-12-26 16:25:34.155 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:25:34.156 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:25:34.156 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:25:34.156 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:25:34.157 - [任务 67(100)][f52fd9f8-a806-4b8e-aeed-89707d15a07a] - Node f52fd9f8-a806-4b8e-aeed-89707d15a07a[f52fd9f8-a806-4b8e-aeed-89707d15a07a] running status set to false 
[TRACE] 2024-12-26 16:25:34.157 - [任务 67(100)][f52fd9f8-a806-4b8e-aeed-89707d15a07a] - Node f52fd9f8-a806-4b8e-aeed-89707d15a07a[f52fd9f8-a806-4b8e-aeed-89707d15a07a] schema data cleaned 
[TRACE] 2024-12-26 16:25:34.157 - [任务 67(100)][f52fd9f8-a806-4b8e-aeed-89707d15a07a] - Node f52fd9f8-a806-4b8e-aeed-89707d15a07a[f52fd9f8-a806-4b8e-aeed-89707d15a07a] monitor closed 
[TRACE] 2024-12-26 16:25:34.158 - [任务 67(100)][f52fd9f8-a806-4b8e-aeed-89707d15a07a] - Node f52fd9f8-a806-4b8e-aeed-89707d15a07a[f52fd9f8-a806-4b8e-aeed-89707d15a07a] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:25:34.158 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:25:34.158 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:25:34.158 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:25:35.859 - [任务 67(100)][77d78979-4f3a-4c68-aabc-7a170a3495f3] - Node 77d78979-4f3a-4c68-aabc-7a170a3495f3[77d78979-4f3a-4c68-aabc-7a170a3495f3] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:25:35.859 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:25:35.859 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:25:35.859 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:25:35.859 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:35.859 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:35.860 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:35.860 - [任务 67(100)][77d78979-4f3a-4c68-aabc-7a170a3495f3] - Node 77d78979-4f3a-4c68-aabc-7a170a3495f3[77d78979-4f3a-4c68-aabc-7a170a3495f3] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:25:35.860 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:25:35.860 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:25:35.995 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:25:36.007 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:25:36.007 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201535875 
[TRACE] 2024-12-26 16:25:36.007 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201535875 
[TRACE] 2024-12-26 16:25:36.007 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:25:36.007 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:25:36.007 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 12 ms 
[TRACE] 2024-12-26 16:25:36.049 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:25:36.049 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:25:36.049 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 47 ms 
[TRACE] 2024-12-26 16:25:36.212 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:25:36.212 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-1461ef0e-ee1a-4550-9a67-57e7c14b3654 
[INFO ] 2024-12-26 16:25:36.212 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-1461ef0e-ee1a-4550-9a67-57e7c14b3654 
[INFO ] 2024-12-26 16:25:36.212 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][77d78979-4f3a-4c68-aabc-7a170a3495f3] - Node 77d78979-4f3a-4c68-aabc-7a170a3495f3[77d78979-4f3a-4c68-aabc-7a170a3495f3] running status set to false 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][77d78979-4f3a-4c68-aabc-7a170a3495f3] - Node 77d78979-4f3a-4c68-aabc-7a170a3495f3[77d78979-4f3a-4c68-aabc-7a170a3495f3] schema data cleaned 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][77d78979-4f3a-4c68-aabc-7a170a3495f3] - Node 77d78979-4f3a-4c68-aabc-7a170a3495f3[77d78979-4f3a-4c68-aabc-7a170a3495f3] monitor closed 
[TRACE] 2024-12-26 16:25:36.213 - [任务 67(100)][77d78979-4f3a-4c68-aabc-7a170a3495f3] - Node 77d78979-4f3a-4c68-aabc-7a170a3495f3[77d78979-4f3a-4c68-aabc-7a170a3495f3] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:25:36.214 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:25:36.214 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:25:36.231 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:26:57.604 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:26:57.604 - [任务 67(100)][92b7a81c-0415-4ccd-97bb-60bab2ed9258] - Node 92b7a81c-0415-4ccd-97bb-60bab2ed9258[92b7a81c-0415-4ccd-97bb-60bab2ed9258] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:26:57.604 - [任务 67(100)][92b7a81c-0415-4ccd-97bb-60bab2ed9258] - Node 92b7a81c-0415-4ccd-97bb-60bab2ed9258[92b7a81c-0415-4ccd-97bb-60bab2ed9258] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.604 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:26:57.604 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.605 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:26:57.605 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.605 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.605 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:26:57.605 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] - Node 5f9bed7e-cdfd-4769-8c10-8bf8ece697b0[5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.700 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.701 - [任务 67(100)][5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] - Node 5f9bed7e-cdfd-4769-8c10-8bf8ece697b0[5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:26:57.701 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:26:57.701 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:26:57.882 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:26:57.884 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201617752 
[TRACE] 2024-12-26 16:26:57.884 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201617752 
[TRACE] 2024-12-26 16:26:57.884 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:26:57.884 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:26:57.886 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:26:57.886 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:26:57.943 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:26:57.944 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:26:57.944 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 57 ms 
[TRACE] 2024-12-26 16:26:57.949 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:26:57.949 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201617757 
[TRACE] 2024-12-26 16:26:57.949 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201617757 
[TRACE] 2024-12-26 16:26:57.949 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:26:57.950 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:26:57.950 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:26:58.007 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:26:58.007 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.007 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:26:58.007 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 56 ms 
[TRACE] 2024-12-26 16:26:58.066 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:26:58.069 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-e13d9152-f44b-4dff-9e29-421765db366a 
[INFO ] 2024-12-26 16:26:58.069 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-e13d9152-f44b-4dff-9e29-421765db366a 
[INFO ] 2024-12-26 16:26:58.069 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.069 - [任务 67(100)][92b7a81c-0415-4ccd-97bb-60bab2ed9258] - Node 92b7a81c-0415-4ccd-97bb-60bab2ed9258[92b7a81c-0415-4ccd-97bb-60bab2ed9258] running status set to false 
[TRACE] 2024-12-26 16:26:58.069 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.069 - [任务 67(100)][92b7a81c-0415-4ccd-97bb-60bab2ed9258] - Node 92b7a81c-0415-4ccd-97bb-60bab2ed9258[92b7a81c-0415-4ccd-97bb-60bab2ed9258] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.069 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:26:58.069 - [任务 67(100)][92b7a81c-0415-4ccd-97bb-60bab2ed9258] - Node 92b7a81c-0415-4ccd-97bb-60bab2ed9258[92b7a81c-0415-4ccd-97bb-60bab2ed9258] monitor closed 
[TRACE] 2024-12-26 16:26:58.069 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:26:58.070 - [任务 67(100)][92b7a81c-0415-4ccd-97bb-60bab2ed9258] - Node 92b7a81c-0415-4ccd-97bb-60bab2ed9258[92b7a81c-0415-4ccd-97bb-60bab2ed9258] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:26:58.071 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:26:58.071 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:26:58.071 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-731119b2-80d0-4605-9467-967c6b82c035 
[INFO ] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-731119b2-80d0-4605-9467-967c6b82c035 
[INFO ] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:26:58.131 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:26:58.133 - [任务 67(100)][5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] - Node 5f9bed7e-cdfd-4769-8c10-8bf8ece697b0[5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] running status set to false 
[TRACE] 2024-12-26 16:26:58.133 - [任务 67(100)][5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] - Node 5f9bed7e-cdfd-4769-8c10-8bf8ece697b0[5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] schema data cleaned 
[TRACE] 2024-12-26 16:26:58.133 - [任务 67(100)][5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] - Node 5f9bed7e-cdfd-4769-8c10-8bf8ece697b0[5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] monitor closed 
[TRACE] 2024-12-26 16:26:58.133 - [任务 67(100)][5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] - Node 5f9bed7e-cdfd-4769-8c10-8bf8ece697b0[5f9bed7e-cdfd-4769-8c10-8bf8ece697b0] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:26:58.133 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:26:58.133 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:26:58.335 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:00.745 - [任务 67(100)][fa44d570-cad9-4e70-8e98-7c6d36583a00] - Node fa44d570-cad9-4e70-8e98-7c6d36583a00[fa44d570-cad9-4e70-8e98-7c6d36583a00] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:00.745 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][fa44d570-cad9-4e70-8e98-7c6d36583a00] - Node fa44d570-cad9-4e70-8e98-7c6d36583a00[fa44d570-cad9-4e70-8e98-7c6d36583a00] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.746 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:00.902 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:00.902 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:00.903 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:00.904 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201620764 
[TRACE] 2024-12-26 16:27:00.904 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201620764 
[TRACE] 2024-12-26 16:27:00.905 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:00.905 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:00.924 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][32fd9b08-6344-493c-aa1e-9f0b9beb7869] - Node 32fd9b08-6344-493c-aa1e-9f0b9beb7869[32fd9b08-6344-493c-aa1e-9f0b9beb7869] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][32fd9b08-6344-493c-aa1e-9f0b9beb7869] - Node 32fd9b08-6344-493c-aa1e-9f0b9beb7869[32fd9b08-6344-493c-aa1e-9f0b9beb7869] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:00.925 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:00.982 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:00.982 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:00.982 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:00.983 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 80 ms 
[TRACE] 2024-12-26 16:27:01.125 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:01.125 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:01.145 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201620966 
[TRACE] 2024-12-26 16:27:01.145 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201620966 
[TRACE] 2024-12-26 16:27:01.145 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.145 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:01.157 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 20 ms 
[TRACE] 2024-12-26 16:27:01.157 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:27:01.159 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-0f08f55d-c1a3-48c9-ba19-e986159663da 
[INFO ] 2024-12-26 16:27:01.159 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-0f08f55d-c1a3-48c9-ba19-e986159663da 
[INFO ] 2024-12-26 16:27:01.159 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.160 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.160 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)][fa44d570-cad9-4e70-8e98-7c6d36583a00] - Node fa44d570-cad9-4e70-8e98-7c6d36583a00[fa44d570-cad9-4e70-8e98-7c6d36583a00] running status set to false 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)][fa44d570-cad9-4e70-8e98-7c6d36583a00] - Node fa44d570-cad9-4e70-8e98-7c6d36583a00[fa44d570-cad9-4e70-8e98-7c6d36583a00] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)][fa44d570-cad9-4e70-8e98-7c6d36583a00] - Node fa44d570-cad9-4e70-8e98-7c6d36583a00[fa44d570-cad9-4e70-8e98-7c6d36583a00] monitor closed 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)][fa44d570-cad9-4e70-8e98-7c6d36583a00] - Node fa44d570-cad9-4e70-8e98-7c6d36583a00[fa44d570-cad9-4e70-8e98-7c6d36583a00] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:01.161 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:01.190 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:01.190 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.190 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:01.190 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 64 ms 
[TRACE] 2024-12-26 16:27:01.322 - [任务 67(100)][0ec59df0-3718-4875-a9d8-565d6da643e2] - Node 0ec59df0-3718-4875-a9d8-565d6da643e2[0ec59df0-3718-4875-a9d8-565d6da643e2] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][0ec59df0-3718-4875-a9d8-565d6da643e2] - Node 0ec59df0-3718-4875-a9d8-565d6da643e2[0ec59df0-3718-4875-a9d8-565d6da643e2] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:01.323 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:01.356 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:27:01.357 - [任务 67(100)][32fd9b08-6344-493c-aa1e-9f0b9beb7869] - Node 32fd9b08-6344-493c-aa1e-9f0b9beb7869[32fd9b08-6344-493c-aa1e-9f0b9beb7869] running status set to false 
[TRACE] 2024-12-26 16:27:01.357 - [任务 67(100)][32fd9b08-6344-493c-aa1e-9f0b9beb7869] - Node 32fd9b08-6344-493c-aa1e-9f0b9beb7869[32fd9b08-6344-493c-aa1e-9f0b9beb7869] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.357 - [任务 67(100)][32fd9b08-6344-493c-aa1e-9f0b9beb7869] - Node 32fd9b08-6344-493c-aa1e-9f0b9beb7869[32fd9b08-6344-493c-aa1e-9f0b9beb7869] monitor closed 
[TRACE] 2024-12-26 16:27:01.357 - [任务 67(100)][32fd9b08-6344-493c-aa1e-9f0b9beb7869] - Node 32fd9b08-6344-493c-aa1e-9f0b9beb7869[32fd9b08-6344-493c-aa1e-9f0b9beb7869] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:27:01.359 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-6bed83d8-c632-43a1-bbf6-9a30b73a66f0 
[INFO ] 2024-12-26 16:27:01.359 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-6bed83d8-c632-43a1-bbf6-9a30b73a66f0 
[INFO ] 2024-12-26 16:27:01.359 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.360 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.360 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:01.360 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:27:01.361 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:01.361 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][905b552e-4ead-4676-b1e1-66a7e316a260] - Node 905b552e-4ead-4676-b1e1-66a7e316a260[905b552e-4ead-4676-b1e1-66a7e316a260] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][905b552e-4ead-4676-b1e1-66a7e316a260] - Node 905b552e-4ead-4676-b1e1-66a7e316a260[905b552e-4ead-4676-b1e1-66a7e316a260] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.458 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:01.504 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:01.504 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201621380 
[TRACE] 2024-12-26 16:27:01.504 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201621380 
[TRACE] 2024-12-26 16:27:01.504 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.504 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:01.504 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:27:01.560 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:01.561 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.561 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:01.599 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 53 ms 
[TRACE] 2024-12-26 16:27:01.599 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:01.599 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:01.600 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201621468 
[TRACE] 2024-12-26 16:27:01.600 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201621468 
[TRACE] 2024-12-26 16:27:01.600 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.600 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:01.646 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:27:01.646 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.646 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:01.646 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 46 ms 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-accbc4a1-3373-489f-895c-ee2383056f8f 
[INFO ] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-accbc4a1-3373-489f-895c-ee2383056f8f 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][0ec59df0-3718-4875-a9d8-565d6da643e2] - Node 0ec59df0-3718-4875-a9d8-565d6da643e2[0ec59df0-3718-4875-a9d8-565d6da643e2] running status set to false 
[INFO ] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][0ec59df0-3718-4875-a9d8-565d6da643e2] - Node 0ec59df0-3718-4875-a9d8-565d6da643e2[0ec59df0-3718-4875-a9d8-565d6da643e2] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][0ec59df0-3718-4875-a9d8-565d6da643e2] - Node 0ec59df0-3718-4875-a9d8-565d6da643e2[0ec59df0-3718-4875-a9d8-565d6da643e2] monitor closed 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][0ec59df0-3718-4875-a9d8-565d6da643e2] - Node 0ec59df0-3718-4875-a9d8-565d6da643e2[0ec59df0-3718-4875-a9d8-565d6da643e2] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:01.742 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:27:01.743 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:01.743 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:01.743 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-dcc599a8-e4b4-4e52-8cf2-8bde823970dd 
[INFO ] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-dcc599a8-e4b4-4e52-8cf2-8bde823970dd 
[INFO ] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:01.780 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 1 ms 
[TRACE] 2024-12-26 16:27:01.785 - [任务 67(100)][905b552e-4ead-4676-b1e1-66a7e316a260] - Node 905b552e-4ead-4676-b1e1-66a7e316a260[905b552e-4ead-4676-b1e1-66a7e316a260] running status set to false 
[TRACE] 2024-12-26 16:27:01.785 - [任务 67(100)][905b552e-4ead-4676-b1e1-66a7e316a260] - Node 905b552e-4ead-4676-b1e1-66a7e316a260[905b552e-4ead-4676-b1e1-66a7e316a260] schema data cleaned 
[TRACE] 2024-12-26 16:27:01.786 - [任务 67(100)][905b552e-4ead-4676-b1e1-66a7e316a260] - Node 905b552e-4ead-4676-b1e1-66a7e316a260[905b552e-4ead-4676-b1e1-66a7e316a260] monitor closed 
[TRACE] 2024-12-26 16:27:01.786 - [任务 67(100)][905b552e-4ead-4676-b1e1-66a7e316a260] - Node 905b552e-4ead-4676-b1e1-66a7e316a260[905b552e-4ead-4676-b1e1-66a7e316a260] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:27:01.786 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:01.786 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:01.991 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:03.956 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] - Node d0eb9e78-4060-4d4c-aaa2-1487e4958bf8[d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] - Node d0eb9e78-4060-4d4c-aaa2-1487e4958bf8[d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:03.957 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:04.077 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:04.077 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:04.079 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201623971 
[TRACE] 2024-12-26 16:27:04.079 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201623971 
[TRACE] 2024-12-26 16:27:04.079 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.079 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:04.082 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:27:04.082 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:04.134 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.134 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:04.134 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 52 ms 
[TRACE] 2024-12-26 16:27:04.278 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:27:04.279 - [任务 67(100)][d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] - Node d0eb9e78-4060-4d4c-aaa2-1487e4958bf8[d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] running status set to false 
[TRACE] 2024-12-26 16:27:04.279 - [任务 67(100)][d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] - Node d0eb9e78-4060-4d4c-aaa2-1487e4958bf8[d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.279 - [任务 67(100)][d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] - Node d0eb9e78-4060-4d4c-aaa2-1487e4958bf8[d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] monitor closed 
[TRACE] 2024-12-26 16:27:04.279 - [任务 67(100)][d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] - Node d0eb9e78-4060-4d4c-aaa2-1487e4958bf8[d0eb9e78-4060-4d4c-aaa2-1487e4958bf8] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:27:04.279 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-b68ac9a5-eecd-41ac-a80b-e4a84533259a 
[INFO ] 2024-12-26 16:27:04.279 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-b68ac9a5-eecd-41ac-a80b-e4a84533259a 
[INFO ] 2024-12-26 16:27:04.279 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.280 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.280 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:04.280 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:27:04.280 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:04.280 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:04.281 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:04.711 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:04.714 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:04.714 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:04.714 - [任务 67(100)][14f530b3-30f4-4efc-a8e9-19d1f4050dbd] - Node 14f530b3-30f4-4efc-a8e9-19d1f4050dbd[14f530b3-30f4-4efc-a8e9-19d1f4050dbd] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:04.715 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:04.715 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:04.715 - [任务 67(100)][14f530b3-30f4-4efc-a8e9-19d1f4050dbd] - Node 14f530b3-30f4-4efc-a8e9-19d1f4050dbd[14f530b3-30f4-4efc-a8e9-19d1f4050dbd] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:04.718 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:04.718 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:04.718 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:04.861 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:04.863 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:04.865 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201624744 
[TRACE] 2024-12-26 16:27:04.868 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201624744 
[TRACE] 2024-12-26 16:27:04.868 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.868 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:04.868 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 5 ms 
[TRACE] 2024-12-26 16:27:04.927 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:04.930 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:04.930 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 66 ms 
[TRACE] 2024-12-26 16:27:05.121 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:27:05.121 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-5d415926-832a-4dd9-960c-e6b33d204823 
[INFO ] 2024-12-26 16:27:05.121 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-5d415926-832a-4dd9-960c-e6b33d204823 
[INFO ] 2024-12-26 16:27:05.121 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:05.121 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:05.121 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:05.124 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:27:05.124 - [任务 67(100)][14f530b3-30f4-4efc-a8e9-19d1f4050dbd] - Node 14f530b3-30f4-4efc-a8e9-19d1f4050dbd[14f530b3-30f4-4efc-a8e9-19d1f4050dbd] running status set to false 
[TRACE] 2024-12-26 16:27:05.124 - [任务 67(100)][14f530b3-30f4-4efc-a8e9-19d1f4050dbd] - Node 14f530b3-30f4-4efc-a8e9-19d1f4050dbd[14f530b3-30f4-4efc-a8e9-19d1f4050dbd] schema data cleaned 
[TRACE] 2024-12-26 16:27:05.124 - [任务 67(100)][14f530b3-30f4-4efc-a8e9-19d1f4050dbd] - Node 14f530b3-30f4-4efc-a8e9-19d1f4050dbd[14f530b3-30f4-4efc-a8e9-19d1f4050dbd] monitor closed 
[TRACE] 2024-12-26 16:27:05.124 - [任务 67(100)][14f530b3-30f4-4efc-a8e9-19d1f4050dbd] - Node 14f530b3-30f4-4efc-a8e9-19d1f4050dbd[14f530b3-30f4-4efc-a8e9-19d1f4050dbd] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:27:05.125 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:05.125 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:05.125 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][d5dc82bc-d5f6-4c84-991b-09dd9149788a] - Node d5dc82bc-d5f6-4c84-991b-09dd9149788a[d5dc82bc-d5f6-4c84-991b-09dd9149788a] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][d5dc82bc-d5f6-4c84-991b-09dd9149788a] - Node d5dc82bc-d5f6-4c84-991b-09dd9149788a[d5dc82bc-d5f6-4c84-991b-09dd9149788a] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:07.224 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:07.225 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:07.380 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:07.380 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201627240 
[TRACE] 2024-12-26 16:27:07.380 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201627240 
[TRACE] 2024-12-26 16:27:07.380 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:07.381 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:07.381 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:27:07.448 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:07.449 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:07.449 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:07.449 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 67 ms 
[TRACE] 2024-12-26 16:27:07.577 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:27:07.577 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-53229775-d3a1-42e1-9d27-263bba18ff95 
[INFO ] 2024-12-26 16:27:07.577 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-53229775-d3a1-42e1-9d27-263bba18ff95 
[INFO ] 2024-12-26 16:27:07.577 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:07.577 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:07.577 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:07.579 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 2 ms 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)][d5dc82bc-d5f6-4c84-991b-09dd9149788a] - Node d5dc82bc-d5f6-4c84-991b-09dd9149788a[d5dc82bc-d5f6-4c84-991b-09dd9149788a] running status set to false 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)][d5dc82bc-d5f6-4c84-991b-09dd9149788a] - Node d5dc82bc-d5f6-4c84-991b-09dd9149788a[d5dc82bc-d5f6-4c84-991b-09dd9149788a] schema data cleaned 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)][d5dc82bc-d5f6-4c84-991b-09dd9149788a] - Node d5dc82bc-d5f6-4c84-991b-09dd9149788a[d5dc82bc-d5f6-4c84-991b-09dd9149788a] monitor closed 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)][d5dc82bc-d5f6-4c84-991b-09dd9149788a] - Node d5dc82bc-d5f6-4c84-991b-09dd9149788a[d5dc82bc-d5f6-4c84-991b-09dd9149788a] close complete, cost 0 ms 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:07.580 - [任务 67(100)] - Stopped task aspect(s) 
[TRACE] 2024-12-26 16:27:10.673 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:10.673 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:10.673 - [任务 67(100)][e3841459-2779-4165-ac90-bbe5cf4b1e53] - Node e3841459-2779-4165-ac90-bbe5cf4b1e53[e3841459-2779-4165-ac90-bbe5cf4b1e53] start preload schema,table counts: 0 
[TRACE] 2024-12-26 16:27:10.673 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[TRACE] 2024-12-26 16:27:10.673 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:10.674 - [任务 67(100)][e3841459-2779-4165-ac90-bbe5cf4b1e53] - Node e3841459-2779-4165-ac90-bbe5cf4b1e53[e3841459-2779-4165-ac90-bbe5cf4b1e53] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:10.674 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:10.674 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[TRACE] 2024-12-26 16:27:10.674 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[TRACE] 2024-12-26 16:27:10.811 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[TRACE] 2024-12-26 16:27:10.811 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[TRACE] 2024-12-26 16:27:10.815 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[TRACE] 2024-12-26 16:27:10.815 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201630687 
[TRACE] 2024-12-26 16:27:10.815 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735201630687 
[TRACE] 2024-12-26 16:27:10.815 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[TRACE] 2024-12-26 16:27:10.816 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[TRACE] 2024-12-26 16:27:10.816 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 4 ms 
[TRACE] 2024-12-26 16:27:10.879 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[TRACE] 2024-12-26 16:27:10.879 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[TRACE] 2024-12-26 16:27:10.879 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 64 ms 
[TRACE] 2024-12-26 16:27:11.038 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[TRACE] 2024-12-26 16:27:11.038 - [任务 67(100)][e3841459-2779-4165-ac90-bbe5cf4b1e53] - Node e3841459-2779-4165-ac90-bbe5cf4b1e53[e3841459-2779-4165-ac90-bbe5cf4b1e53] running status set to false 
[TRACE] 2024-12-26 16:27:11.038 - [任务 67(100)][e3841459-2779-4165-ac90-bbe5cf4b1e53] - Node e3841459-2779-4165-ac90-bbe5cf4b1e53[e3841459-2779-4165-ac90-bbe5cf4b1e53] schema data cleaned 
[TRACE] 2024-12-26 16:27:11.038 - [任务 67(100)][e3841459-2779-4165-ac90-bbe5cf4b1e53] - Node e3841459-2779-4165-ac90-bbe5cf4b1e53[e3841459-2779-4165-ac90-bbe5cf4b1e53] monitor closed 
[TRACE] 2024-12-26 16:27:11.041 - [任务 67(100)][e3841459-2779-4165-ac90-bbe5cf4b1e53] - Node e3841459-2779-4165-ac90-bbe5cf4b1e53[e3841459-2779-4165-ac90-bbe5cf4b1e53] close complete, cost 0 ms 
[INFO ] 2024-12-26 16:27:11.041 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-8540200e-2f56-423d-adb6-13cf1a3a335d 
[INFO ] 2024-12-26 16:27:11.041 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-8540200e-2f56-423d-adb6-13cf1a3a335d 
[INFO ] 2024-12-26 16:27:11.041 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[TRACE] 2024-12-26 16:27:11.041 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[TRACE] 2024-12-26 16:27:11.041 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[TRACE] 2024-12-26 16:27:11.041 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[TRACE] 2024-12-26 16:27:11.042 - [任务 67(100)] - Closed task monitor(s)
null 
[TRACE] 2024-12-26 16:27:11.042 - [任务 67(100)] - Closed task auto recovery instance
  null 
[TRACE] 2024-12-26 16:27:11.059 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 16:47:19.516 - [任务 67(100)][458b624b-4617-4564-bf92-7dd1a2454213] - Node 458b624b-4617-4564-bf92-7dd1a2454213[458b624b-4617-4564-bf92-7dd1a2454213] start preload schema,table counts: 0 
[INFO ] 2024-12-26 16:47:19.517 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 16:47:19.517 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 16:47:19.518 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 16:47:19.519 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 16:47:19.519 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 16:47:19.520 - [任务 67(100)][458b624b-4617-4564-bf92-7dd1a2454213] - Node 458b624b-4617-4564-bf92-7dd1a2454213[458b624b-4617-4564-bf92-7dd1a2454213] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 16:47:19.521 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 16:47:19.521 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 16:47:19.521 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 16:47:20.009 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 16:47:20.020 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 16:47:20.020 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735202839680 
[INFO ] 2024-12-26 16:47:20.021 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735202839680 
[INFO ] 2024-12-26 16:47:20.023 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 16:47:20.023 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 16:47:20.026 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 15 ms 
[INFO ] 2024-12-26 16:47:20.079 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 16:47:20.080 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 16:47:20.285 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 61 ms 
[INFO ] 2024-12-26 16:47:20.367 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 16:47:20.367 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-ba97ee6f-87ca-45e9-994d-524941840fe5 
[INFO ] 2024-12-26 16:47:20.368 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-ba97ee6f-87ca-45e9-994d-524941840fe5 
[INFO ] 2024-12-26 16:47:20.368 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 16:47:20.368 - [任务 67(100)][458b624b-4617-4564-bf92-7dd1a2454213] - Node 458b624b-4617-4564-bf92-7dd1a2454213[458b624b-4617-4564-bf92-7dd1a2454213] running status set to false 
[INFO ] 2024-12-26 16:47:20.369 - [任务 67(100)][458b624b-4617-4564-bf92-7dd1a2454213] - Node 458b624b-4617-4564-bf92-7dd1a2454213[458b624b-4617-4564-bf92-7dd1a2454213] schema data cleaned 
[INFO ] 2024-12-26 16:47:20.369 - [任务 67(100)][458b624b-4617-4564-bf92-7dd1a2454213] - Node 458b624b-4617-4564-bf92-7dd1a2454213[458b624b-4617-4564-bf92-7dd1a2454213] monitor closed 
[INFO ] 2024-12-26 16:47:20.370 - [任务 67(100)][458b624b-4617-4564-bf92-7dd1a2454213] - Node 458b624b-4617-4564-bf92-7dd1a2454213[458b624b-4617-4564-bf92-7dd1a2454213] close complete, cost 1 ms 
[INFO ] 2024-12-26 16:47:20.370 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 16:47:20.371 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 16:47:20.371 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 8 ms 
[INFO ] 2024-12-26 16:47:20.374 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 16:47:20.376 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 16:47:20.376 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:00:21.912 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:21.913 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:21.913 - [任务 67(100)][da834ce6-09d3-4a19-8a3a-4bdaa9371e93] - Node da834ce6-09d3-4a19-8a3a-4bdaa9371e93[da834ce6-09d3-4a19-8a3a-4bdaa9371e93] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:00:21.913 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:21.913 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:21.913 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:21.914 - [任务 67(100)][da834ce6-09d3-4a19-8a3a-4bdaa9371e93] - Node da834ce6-09d3-4a19-8a3a-4bdaa9371e93[da834ce6-09d3-4a19-8a3a-4bdaa9371e93] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:21.914 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:21.914 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:00:22.114 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:00:22.326 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:00:22.362 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:00:22.362 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203622116 
[INFO ] 2024-12-26 17:00:22.363 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203622116 
[INFO ] 2024-12-26 17:00:22.363 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:00:22.363 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:00:22.363 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 37 ms 
[INFO ] 2024-12-26 17:00:22.471 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:00:22.471 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:00:22.598 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 140 ms 
[INFO ] 2024-12-26 17:00:22.598 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:00:22.600 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-ef9611dd-113d-40a1-b58a-6abb023d64dc 
[INFO ] 2024-12-26 17:00:22.601 - [任务 67(100)][da834ce6-09d3-4a19-8a3a-4bdaa9371e93] - Node da834ce6-09d3-4a19-8a3a-4bdaa9371e93[da834ce6-09d3-4a19-8a3a-4bdaa9371e93] running status set to false 
[INFO ] 2024-12-26 17:00:22.601 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-ef9611dd-113d-40a1-b58a-6abb023d64dc 
[INFO ] 2024-12-26 17:00:22.601 - [任务 67(100)][da834ce6-09d3-4a19-8a3a-4bdaa9371e93] - Node da834ce6-09d3-4a19-8a3a-4bdaa9371e93[da834ce6-09d3-4a19-8a3a-4bdaa9371e93] schema data cleaned 
[INFO ] 2024-12-26 17:00:22.601 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:00:22.601 - [任务 67(100)][da834ce6-09d3-4a19-8a3a-4bdaa9371e93] - Node da834ce6-09d3-4a19-8a3a-4bdaa9371e93[da834ce6-09d3-4a19-8a3a-4bdaa9371e93] monitor closed 
[INFO ] 2024-12-26 17:00:22.602 - [任务 67(100)][da834ce6-09d3-4a19-8a3a-4bdaa9371e93] - Node da834ce6-09d3-4a19-8a3a-4bdaa9371e93[da834ce6-09d3-4a19-8a3a-4bdaa9371e93] close complete, cost 1 ms 
[INFO ] 2024-12-26 17:00:22.602 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:00:22.602 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:00:22.602 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 4 ms 
[INFO ] 2024-12-26 17:00:22.603 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:00:22.604 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:00:22.604 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:00:26.436 - [任务 67(100)][8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] - Node 8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8[8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:00:26.436 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:26.436 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:26.436 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:26.436 - [任务 67(100)][8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] - Node 8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8[8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.437 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.437 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.437 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.437 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:00:26.437 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:00:26.478 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:26.478 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:26.479 - [任务 67(100)][dfb0134f-198e-4722-b5f8-b519fb13afca] - Node dfb0134f-198e-4722-b5f8-b519fb13afca[dfb0134f-198e-4722-b5f8-b519fb13afca] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:00:26.479 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:26.479 - [任务 67(100)][dfb0134f-198e-4722-b5f8-b519fb13afca] - Node dfb0134f-198e-4722-b5f8-b519fb13afca[dfb0134f-198e-4722-b5f8-b519fb13afca] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.479 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.479 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.479 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.480 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:00:26.565 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:00:26.565 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:00:26.568 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203626455 
[INFO ] 2024-12-26 17:00:26.568 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203626455 
[INFO ] 2024-12-26 17:00:26.568 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.569 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:00:26.569 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 4 ms 
[INFO ] 2024-12-26 17:00:26.635 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:00:26.635 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.635 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:00:26.635 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 65 ms 
[INFO ] 2024-12-26 17:00:26.681 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:00:26.682 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203626490 
[INFO ] 2024-12-26 17:00:26.682 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203626490 
[INFO ] 2024-12-26 17:00:26.682 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:00:26.682 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.682 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:00:26.724 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 2 ms 
[INFO ] 2024-12-26 17:00:26.724 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.724 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:00:26.724 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 42 ms 
[INFO ] 2024-12-26 17:00:26.792 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:00:26.792 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-849d2c60-68da-4af8-84c8-b46279ab77dc 
[INFO ] 2024-12-26 17:00:26.792 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-849d2c60-68da-4af8-84c8-b46279ab77dc 
[INFO ] 2024-12-26 17:00:26.792 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.793 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.793 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:00:26.793 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[INFO ] 2024-12-26 17:00:26.795 - [任务 67(100)][8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] - Node 8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8[8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] running status set to false 
[INFO ] 2024-12-26 17:00:26.795 - [任务 67(100)][8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] - Node 8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8[8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.795 - [任务 67(100)][8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] - Node 8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8[8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] monitor closed 
[INFO ] 2024-12-26 17:00:26.795 - [任务 67(100)][8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] - Node 8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8[8f993fa0-9cbb-47eb-91c1-4fe9bf3445b8] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.796 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:00:26.796 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:00:26.796 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:00:26.864 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:00:26.864 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-15f11b04-de14-4696-bcf0-d044bf9b34ad 
[INFO ] 2024-12-26 17:00:26.864 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-15f11b04-de14-4696-bcf0-d044bf9b34ad 
[INFO ] 2024-12-26 17:00:26.864 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.865 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.865 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:00:26.866 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 2 ms 
[INFO ] 2024-12-26 17:00:26.866 - [任务 67(100)][dfb0134f-198e-4722-b5f8-b519fb13afca] - Node dfb0134f-198e-4722-b5f8-b519fb13afca[dfb0134f-198e-4722-b5f8-b519fb13afca] running status set to false 
[INFO ] 2024-12-26 17:00:26.867 - [任务 67(100)][dfb0134f-198e-4722-b5f8-b519fb13afca] - Node dfb0134f-198e-4722-b5f8-b519fb13afca[dfb0134f-198e-4722-b5f8-b519fb13afca] schema data cleaned 
[INFO ] 2024-12-26 17:00:26.867 - [任务 67(100)][dfb0134f-198e-4722-b5f8-b519fb13afca] - Node dfb0134f-198e-4722-b5f8-b519fb13afca[dfb0134f-198e-4722-b5f8-b519fb13afca] monitor closed 
[INFO ] 2024-12-26 17:00:26.867 - [任务 67(100)][dfb0134f-198e-4722-b5f8-b519fb13afca] - Node dfb0134f-198e-4722-b5f8-b519fb13afca[dfb0134f-198e-4722-b5f8-b519fb13afca] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:00:26.868 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:00:26.868 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:00:26.868 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:00:46.851 - [任务 67(100)][a2288184-4048-49f9-9bf1-2c4a05c9acde] - Node a2288184-4048-49f9-9bf1-2c4a05c9acde[a2288184-4048-49f9-9bf1-2c4a05c9acde] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][a2288184-4048-49f9-9bf1-2c4a05c9acde] - Node a2288184-4048-49f9-9bf1-2c4a05c9acde[a2288184-4048-49f9-9bf1-2c4a05c9acde] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:46.852 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:46.853 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:00:46.853 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:00:47.012 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:00:47.012 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:00:47.015 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203646885 
[INFO ] 2024-12-26 17:00:47.015 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203646885 
[INFO ] 2024-12-26 17:00:47.015 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.015 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:00:47.015 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 4 ms 
[INFO ] 2024-12-26 17:00:47.082 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.085 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:00:47.085 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 71 ms 
[WARN ] 2024-12-26 17:00:47.215 - [任务 67(100)][增强JS][src=user_script]  - Document{{created_time=DateTime nano 888000000 seconds 1735202748 timeZone null, updated_time=DateTime nano 888000000 seconds 1735202748 timeZone null, user_id=1.0, ignore_uids=ssss, id=1, note_id=66e39ee57220224479db120b}} 
[INFO ] 2024-12-26 17:00:47.219 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:00:47.220 - [任务 67(100)][a2288184-4048-49f9-9bf1-2c4a05c9acde] - Node a2288184-4048-49f9-9bf1-2c4a05c9acde[a2288184-4048-49f9-9bf1-2c4a05c9acde] running status set to false 
[INFO ] 2024-12-26 17:00:47.220 - [任务 67(100)][a2288184-4048-49f9-9bf1-2c4a05c9acde] - Node a2288184-4048-49f9-9bf1-2c4a05c9acde[a2288184-4048-49f9-9bf1-2c4a05c9acde] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.220 - [任务 67(100)][a2288184-4048-49f9-9bf1-2c4a05c9acde] - Node a2288184-4048-49f9-9bf1-2c4a05c9acde[a2288184-4048-49f9-9bf1-2c4a05c9acde] monitor closed 
[INFO ] 2024-12-26 17:00:47.220 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-6861cf0a-7eee-43dc-93e4-a27030c13aa2 
[INFO ] 2024-12-26 17:00:47.221 - [任务 67(100)][a2288184-4048-49f9-9bf1-2c4a05c9acde] - Node a2288184-4048-49f9-9bf1-2c4a05c9acde[a2288184-4048-49f9-9bf1-2c4a05c9acde] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:00:47.221 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-6861cf0a-7eee-43dc-93e4-a27030c13aa2 
[INFO ] 2024-12-26 17:00:47.221 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.221 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.222 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:00:47.222 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 6 ms 
[INFO ] 2024-12-26 17:00:47.222 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:00:47.222 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:00:47.222 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:00:47.596 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:47.596 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][36db3fbc-15b6-422c-96b3-9f372ef3beb3] - Node 36db3fbc-15b6-422c-96b3-9f372ef3beb3[36db3fbc-15b6-422c-96b3-9f372ef3beb3] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][36db3fbc-15b6-422c-96b3-9f372ef3beb3] - Node 36db3fbc-15b6-422c-96b3-9f372ef3beb3[36db3fbc-15b6-422c-96b3-9f372ef3beb3] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:00:47.597 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:00:47.752 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:00:47.752 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:00:47.755 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203647635 
[INFO ] 2024-12-26 17:00:47.755 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:00:47.755 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203647635 
[INFO ] 2024-12-26 17:00:47.755 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.755 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:00:47.755 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 3 ms 
[INFO ] 2024-12-26 17:00:47.815 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:00:47.815 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:00:47.816 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 61 ms 
[WARN ] 2024-12-26 17:00:48.065 - [任务 67(100)][增强JS][src=user_script]  - Document{{created_time=DateTime nano 888000000 seconds 1735202748 timeZone null, updated_time=DateTime nano 888000000 seconds 1735202748 timeZone null, user_id=1.0, ignore_uids=ssss, id=1, note_id=66e39ee57220224479db120b}} 
[INFO ] 2024-12-26 17:00:48.065 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:00:48.067 - [任务 67(100)][36db3fbc-15b6-422c-96b3-9f372ef3beb3] - Node 36db3fbc-15b6-422c-96b3-9f372ef3beb3[36db3fbc-15b6-422c-96b3-9f372ef3beb3] running status set to false 
[INFO ] 2024-12-26 17:00:48.067 - [任务 67(100)][36db3fbc-15b6-422c-96b3-9f372ef3beb3] - Node 36db3fbc-15b6-422c-96b3-9f372ef3beb3[36db3fbc-15b6-422c-96b3-9f372ef3beb3] schema data cleaned 
[INFO ] 2024-12-26 17:00:48.067 - [任务 67(100)][36db3fbc-15b6-422c-96b3-9f372ef3beb3] - Node 36db3fbc-15b6-422c-96b3-9f372ef3beb3[36db3fbc-15b6-422c-96b3-9f372ef3beb3] monitor closed 
[INFO ] 2024-12-26 17:00:48.067 - [任务 67(100)][36db3fbc-15b6-422c-96b3-9f372ef3beb3] - Node 36db3fbc-15b6-422c-96b3-9f372ef3beb3[36db3fbc-15b6-422c-96b3-9f372ef3beb3] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:00:48.068 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-b892b228-a756-4604-8e95-2e5120c6c322 
[INFO ] 2024-12-26 17:00:48.068 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-b892b228-a756-4604-8e95-2e5120c6c322 
[INFO ] 2024-12-26 17:00:48.069 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:00:48.069 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:00:48.069 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:00:48.069 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 4 ms 
[INFO ] 2024-12-26 17:00:48.069 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:00:48.069 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:00:48.070 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][d74591e0-62b8-4a53-afc8-403eccfe3333] - Node d74591e0-62b8-4a53-afc8-403eccfe3333[d74591e0-62b8-4a53-afc8-403eccfe3333] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][d74591e0-62b8-4a53-afc8-403eccfe3333] - Node d74591e0-62b8-4a53-afc8-403eccfe3333[d74591e0-62b8-4a53-afc8-403eccfe3333] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:09.486 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:09.487 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:09.487 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:01:09.687 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:01:09.711 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:01:09.712 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:01:09.712 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203669502 
[INFO ] 2024-12-26 17:01:09.712 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203669502 
[INFO ] 2024-12-26 17:01:09.713 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:01:09.713 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:01:09.713 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 6 ms 
[INFO ] 2024-12-26 17:01:09.769 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:01:09.769 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:01:09.769 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 58 ms 
[WARN ] 2024-12-26 17:01:09.917 - [任务 67(100)][增强JS][src=user_script]  - Document{{created_time=DateTime nano 888000000 seconds 1735202748 timeZone null, updated_time=DateTime nano 888000000 seconds 1735202748 timeZone null, user_id=1.0, ignore_uids=ssss, id=1, note_id=66e39ee57220224479db120b}} 
[INFO ] 2024-12-26 17:01:09.917 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-0959bb04-8c28-44a6-89b5-0fb3f111c141 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][d74591e0-62b8-4a53-afc8-403eccfe3333] - Node d74591e0-62b8-4a53-afc8-403eccfe3333[d74591e0-62b8-4a53-afc8-403eccfe3333] running status set to false 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-0959bb04-8c28-44a6-89b5-0fb3f111c141 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][d74591e0-62b8-4a53-afc8-403eccfe3333] - Node d74591e0-62b8-4a53-afc8-403eccfe3333[d74591e0-62b8-4a53-afc8-403eccfe3333] schema data cleaned 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][d74591e0-62b8-4a53-afc8-403eccfe3333] - Node d74591e0-62b8-4a53-afc8-403eccfe3333[d74591e0-62b8-4a53-afc8-403eccfe3333] monitor closed 
[INFO ] 2024-12-26 17:01:09.920 - [任务 67(100)][d74591e0-62b8-4a53-afc8-403eccfe3333] - Node d74591e0-62b8-4a53-afc8-403eccfe3333[d74591e0-62b8-4a53-afc8-403eccfe3333] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:01:09.921 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:01:09.921 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:01:09.921 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 4 ms 
[INFO ] 2024-12-26 17:01:09.922 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:01:09.922 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:01:09.922 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:01:11.821 - [任务 67(100)][b09e17d4-0361-4c49-8131-5629733acb21] - Node b09e17d4-0361-4c49-8131-5629733acb21[b09e17d4-0361-4c49-8131-5629733acb21] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:01:11.821 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:01:11.821 - [任务 67(100)][b09e17d4-0361-4c49-8131-5629733acb21] - Node b09e17d4-0361-4c49-8131-5629733acb21[b09e17d4-0361-4c49-8131-5629733acb21] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:11.821 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:01:11.822 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:01:11.822 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:11.822 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:11.822 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:01:11.822 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:01:11.968 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:01:11.969 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:01:11.982 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:01:11.982 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203671837 
[INFO ] 2024-12-26 17:01:11.982 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735203671837 
[INFO ] 2024-12-26 17:01:11.982 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:01:11.982 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:01:11.982 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 15 ms 
[INFO ] 2024-12-26 17:01:12.028 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:01:12.028 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:01:12.028 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 55 ms 
[WARN ] 2024-12-26 17:01:12.295 - [任务 67(100)][增强JS][src=user_script]  - Document{{created_time=DateTime nano 888000000 seconds 1735202748 timeZone null, updated_time=DateTime nano 888000000 seconds 1735202748 timeZone null, user_id=1.0, ignore_uids=ssss, id=1, note_id=66e39ee57220224479db120b}} 
[INFO ] 2024-12-26 17:01:12.296 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:01:12.296 - [任务 67(100)][b09e17d4-0361-4c49-8131-5629733acb21] - Node b09e17d4-0361-4c49-8131-5629733acb21[b09e17d4-0361-4c49-8131-5629733acb21] running status set to false 
[INFO ] 2024-12-26 17:01:12.296 - [任务 67(100)][b09e17d4-0361-4c49-8131-5629733acb21] - Node b09e17d4-0361-4c49-8131-5629733acb21[b09e17d4-0361-4c49-8131-5629733acb21] schema data cleaned 
[INFO ] 2024-12-26 17:01:12.296 - [任务 67(100)][b09e17d4-0361-4c49-8131-5629733acb21] - Node b09e17d4-0361-4c49-8131-5629733acb21[b09e17d4-0361-4c49-8131-5629733acb21] monitor closed 
[INFO ] 2024-12-26 17:01:12.296 - [任务 67(100)][b09e17d4-0361-4c49-8131-5629733acb21] - Node b09e17d4-0361-4c49-8131-5629733acb21[b09e17d4-0361-4c49-8131-5629733acb21] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:01:12.298 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-4277b6ea-c99b-4518-b8c6-1e2ce1e22858 
[INFO ] 2024-12-26 17:01:12.298 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-4277b6ea-c99b-4518-b8c6-1e2ce1e22858 
[INFO ] 2024-12-26 17:01:12.298 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:01:12.299 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:01:12.299 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:01:12.299 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[INFO ] 2024-12-26 17:01:12.299 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:01:12.300 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:01:12.300 - [任务 67(100)] - Stopped task aspect(s) 
[INFO ] 2024-12-26 17:15:05.214 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:15:05.214 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][b62eebd2-8aa2-4189-beba-6ea1b9b41430] - Node b62eebd2-8aa2-4189-beba-6ea1b9b41430[b62eebd2-8aa2-4189-beba-6ea1b9b41430] start preload schema,table counts: 0 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] start preload schema,table counts: 1 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][字段改名] - Node field_rename_processor(字段改名: 1810e9a1-db5a-453e-ad78-2b0fcba9cb13) enable batch process 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][b62eebd2-8aa2-4189-beba-6ea1b9b41430] - Node b62eebd2-8aa2-4189-beba-6ea1b9b41430[b62eebd2-8aa2-4189-beba-6ea1b9b41430] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][增强JS] - Node js_processor(增强JS: 17c4654b-d030-4459-8c4c-1eebd366cc0c) enable batch process 
[INFO ] 2024-12-26 17:15:05.215 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] preload schema finished, cost 0 ms 
[INFO ] 2024-12-26 17:15:05.576 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] running status set to false 
[INFO ] 2024-12-26 17:15:05.585 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] running status set to false 
[INFO ] 2024-12-26 17:15:05.585 - [任务 67(100)][new_notes] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735204505399 
[INFO ] 2024-12-26 17:15:05.586 - [任务 67(100)][new_notes] - PDK connector node released: HazelcastSampleSourcePdkDataNode_5f387217-3f58-4c68-bc80-f95733ad615b_1735204505399 
[INFO ] 2024-12-26 17:15:05.586 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] schema data cleaned 
[INFO ] 2024-12-26 17:15:05.586 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] monitor closed 
[INFO ] 2024-12-26 17:15:05.646 - [任务 67(100)][new_notes] - Node new_notes[5f387217-3f58-4c68-bc80-f95733ad615b] close complete, cost 10 ms 
[INFO ] 2024-12-26 17:15:05.646 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] schema data cleaned 
[INFO ] 2024-12-26 17:15:05.646 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] monitor closed 
[INFO ] 2024-12-26 17:15:05.646 - [任务 67(100)][字段改名] - Node 字段改名[1810e9a1-db5a-453e-ad78-2b0fcba9cb13] close complete, cost 68 ms 
[WARN ] 2024-12-26 17:15:05.775 - [任务 67(100)][增强JS][src=user_script]  - Document{{created_time=DateTime nano 888000000 seconds 1735202748 timeZone null, updated_time=DateTime nano 888000000 seconds 1735202748 timeZone null, user_id=2, ignore_uids=Egk5D273sxsc, id=2, note_id=66e39ee57220224479db120c}} 
[INFO ] 2024-12-26 17:15:05.775 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] running status set to false 
[INFO ] 2024-12-26 17:15:05.777 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-mongo_t-def9d318-9d11-488b-8f2e-63c7abdc6ea4 
[INFO ] 2024-12-26 17:15:05.777 - [任务 67(100)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-mongo_t-def9d318-9d11-488b-8f2e-63c7abdc6ea4 
[INFO ] 2024-12-26 17:15:05.777 - [任务 67(100)][增强JS][src=user_script]  - [ScriptExecutorsManager-676d0c509288dc25f803ba10-17c4654b-d030-4459-8c4c-1eebd366cc0c-676d0c4b9288dc25f803ba0a] schema data cleaned 
[INFO ] 2024-12-26 17:15:05.777 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] schema data cleaned 
[INFO ] 2024-12-26 17:15:05.777 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] monitor closed 
[INFO ] 2024-12-26 17:15:05.777 - [任务 67(100)][增强JS] - Node 增强JS[17c4654b-d030-4459-8c4c-1eebd366cc0c] close complete, cost 3 ms 
[INFO ] 2024-12-26 17:15:05.779 - [任务 67(100)][b62eebd2-8aa2-4189-beba-6ea1b9b41430] - Node b62eebd2-8aa2-4189-beba-6ea1b9b41430[b62eebd2-8aa2-4189-beba-6ea1b9b41430] running status set to false 
[INFO ] 2024-12-26 17:15:05.779 - [任务 67(100)][b62eebd2-8aa2-4189-beba-6ea1b9b41430] - Node b62eebd2-8aa2-4189-beba-6ea1b9b41430[b62eebd2-8aa2-4189-beba-6ea1b9b41430] schema data cleaned 
[INFO ] 2024-12-26 17:15:05.779 - [任务 67(100)][b62eebd2-8aa2-4189-beba-6ea1b9b41430] - Node b62eebd2-8aa2-4189-beba-6ea1b9b41430[b62eebd2-8aa2-4189-beba-6ea1b9b41430] monitor closed 
[INFO ] 2024-12-26 17:15:05.779 - [任务 67(100)][b62eebd2-8aa2-4189-beba-6ea1b9b41430] - Node b62eebd2-8aa2-4189-beba-6ea1b9b41430[b62eebd2-8aa2-4189-beba-6ea1b9b41430] close complete, cost 0 ms 
[INFO ] 2024-12-26 17:15:05.780 - [任务 67(100)] - Closed task monitor(s)
null 
[INFO ] 2024-12-26 17:15:05.780 - [任务 67(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-12-26 17:15:05.780 - [任务 67(100)] - Stopped task aspect(s) 
