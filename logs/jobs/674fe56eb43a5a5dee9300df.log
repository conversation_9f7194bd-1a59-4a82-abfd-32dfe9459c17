[INFO ] 2024-12-04 13:17:10.357 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 13:17:10.362 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 13:17:11.342 - [任务 55] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-12-04 13:17:11.518 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 13:17:12.284 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:17:12.286 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:17:12.290 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 3 ms 
[INFO ] 2024-12-04 13:17:12.509 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 4 ms 
[INFO ] 2024-12-04 13:17:17.272 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 13:17:17.316 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 13:17:17.318 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 13:18:33.250 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:18:33.250 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 13:18:33.392 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 13:18:33.392 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 13:18:33.407 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:18:33.425 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 13:18:33.426 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 13:18:33.958 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 13:18:35.093 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:18:43.970 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:20:06.802 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 13:20:06.821 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 13:20:06.823 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 13:20:06.829 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 13:20:06.851 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 13:20:06.852 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 13:20:26.961 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733289433199 
[INFO ] 2024-12-04 13:20:26.961 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733289433199 
[INFO ] 2024-12-04 13:20:26.962 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 13:20:26.966 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 13:20:26.967 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20175 ms 
[INFO ] 2024-12-04 13:20:27.052 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 13:20:27.053 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733289433023 
[INFO ] 2024-12-04 13:20:27.056 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733289433023 
[INFO ] 2024-12-04 13:20:27.056 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 13:20:27.060 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 13:20:27.061 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 92 ms 
[INFO ] 2024-12-04 13:20:28.496 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 13:20:28.501 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@11687f47 
[INFO ] 2024-12-04 13:20:28.631 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 13:20:28.632 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 13:20:28.632 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 13:20:28.676 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:20:28.676 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:22:14.850 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 13:22:14.853 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 13:22:15.084 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 13:22:15.202 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 13:22:15.203 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:22:15.203 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:22:15.204 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:22:15.204 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:22:15.944 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 13:22:15.944 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 13:25:28.099 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 13:25:28.348 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 13:25:28.371 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 13:25:28.379 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 13:25:28.379 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 13:25:30.152 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138546046,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:25:30.167 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 13:25:30.455 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 13:25:30.455 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 13:25:30.456 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138546046,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:25:30.619 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138546046 
[INFO ] 2024-12-04 13:25:31.797 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:26:15.579 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 13:26:15.580 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 13:26:15.580 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 13:26:15.581 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 13:26:15.612 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 13:26:15.616 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 13:26:35.656 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733289735558 
[INFO ] 2024-12-04 13:26:35.658 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733289735558 
[INFO ] 2024-12-04 13:26:35.659 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 13:26:35.659 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 13:26:35.659 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20111 ms 
[INFO ] 2024-12-04 13:26:35.659 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 13:26:35.676 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733289735510 
[INFO ] 2024-12-04 13:26:35.676 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733289735510 
[INFO ] 2024-12-04 13:26:35.676 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 13:26:35.677 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 13:26:35.679 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 20 ms 
[INFO ] 2024-12-04 13:26:38.358 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 13:26:38.481 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45558fd3 
[INFO ] 2024-12-04 13:26:38.481 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 13:26:38.508 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 13:26:38.508 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 13:26:38.553 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:26:38.755 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:31:08.183 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 13:31:08.189 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 13:31:09.118 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 13:31:09.119 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 13:31:09.591 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:31:09.593 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:31:09.594 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:31:09.595 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:31:10.560 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 13:31:10.561 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 13:31:10.563 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 13:32:03.856 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 13:32:03.868 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 13:32:15.535 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600129,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:32:15.629 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 13:32:15.635 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 13:32:15.635 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 13:32:15.694 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 13:32:15.696 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 13:32:15.707 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600129,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:32:16.112 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 13:32:17.102 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:32:24.986 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:33:13.416 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 13:33:13.420 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 13:33:13.459 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 13:33:13.466 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 13:33:33.554 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733290270044 
[INFO ] 2024-12-04 13:33:33.560 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733290270044 
[INFO ] 2024-12-04 13:33:33.561 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 13:33:33.580 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 13:33:33.580 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20181 ms 
[INFO ] 2024-12-04 13:33:33.609 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 13:33:33.610 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733290269935 
[INFO ] 2024-12-04 13:33:33.612 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733290269935 
[INFO ] 2024-12-04 13:33:33.612 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 13:33:33.612 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 13:33:33.822 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 32 ms 
[INFO ] 2024-12-04 13:33:35.570 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 13:33:35.572 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45e00fe8 
[INFO ] 2024-12-04 13:33:35.727 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 13:33:35.729 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 13:33:35.729 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 13:33:35.798 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:33:36.004 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:34:07.457 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 13:34:07.669 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 13:34:07.751 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 13:34:07.858 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 13:34:07.858 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:34:07.858 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:34:07.859 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:34:08.061 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:34:08.599 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 13:34:08.601 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 13:34:08.669 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 13:34:08.669 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 13:34:08.681 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 13:34:08.681 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 13:34:49.231 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 13:34:53.885 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600133,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:34:53.885 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 13:34:54.128 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 13:34:54.130 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 13:34:54.340 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600133,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:34:54.483 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 13:34:55.602 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:35:03.693 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:35:21.486 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 13:35:21.487 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 13:35:21.487 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 13:35:21.487 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 13:35:21.501 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 13:35:21.705 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 13:37:46.765 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 13:37:46.975 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 13:37:47.994 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 13:37:48.200 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 13:37:48.490 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:37:48.491 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 13:37:48.492 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:37:48.493 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 13:37:49.540 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 13:37:53.892 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 13:37:53.969 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 13:38:03.161 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 13:38:03.163 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:38:03.165 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 13:38:03.169 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 13:38:03.354 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 13:38:03.355 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 13:38:03.438 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 13:38:03.439 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 13:38:03.446 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 13:38:22.947 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 13:38:24.086 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:38:31.811 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 13:39:04.707 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 13:39:04.755 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 13:39:04.755 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 13:39:04.755 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 13:39:24.885 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733290669005 
[INFO ] 2024-12-04 13:39:24.886 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733290669005 
[INFO ] 2024-12-04 13:39:24.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 13:39:24.912 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 13:39:24.914 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20197 ms 
[INFO ] 2024-12-04 13:39:24.914 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 13:39:24.995 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733290668849 
[INFO ] 2024-12-04 13:39:25.000 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733290668849 
[INFO ] 2024-12-04 13:39:25.000 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 13:39:25.002 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 13:39:25.003 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 90 ms 
[INFO ] 2024-12-04 13:39:27.951 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 13:39:27.956 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6e23142b 
[INFO ] 2024-12-04 13:39:28.107 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 13:39:28.107 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 13:39:28.184 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 13:39:28.187 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 13:39:28.187 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:04:51.113 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:04:51.320 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:04:52.272 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:04:52.477 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:04:52.833 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:04:52.835 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:04:52.836 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 14:04:52.836 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:04:53.814 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:05:16.538 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:05:16.567 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:05:24.067 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:05:24.070 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:05:24.077 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:05:24.078 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:05:24.335 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:05:24.336 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:05:24.353 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:05:24.353 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:05:24.371 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:05:27.920 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:05:28.953 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:05:38.196 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:05:58.164 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:05:58.166 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:05:58.167 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 14:05:58.169 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:05:58.225 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 14:05:58.225 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 14:06:18.304 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733292293310 
[INFO ] 2024-12-04 14:06:18.305 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733292293310 
[INFO ] 2024-12-04 14:06:18.306 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:06:18.321 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:06:18.322 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20174 ms 
[INFO ] 2024-12-04 14:06:18.324 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:06:18.363 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733292293124 
[INFO ] 2024-12-04 14:06:18.365 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733292293124 
[INFO ] 2024-12-04 14:06:18.365 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:06:18.367 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:06:18.574 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 46 ms 
[INFO ] 2024-12-04 14:06:19.187 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:06:19.191 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@76c39640 
[INFO ] 2024-12-04 14:06:19.324 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:06:19.353 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:06:19.353 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:06:19.443 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:06:19.445 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:38:12.589 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:38:12.602 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:38:13.579 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:38:13.786 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:38:14.069 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:38:14.071 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:38:14.071 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 2 ms 
[INFO ] 2024-12-04 14:38:14.072 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:38:15.210 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:38:23.340 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:38:23.424 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:38:25.321 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:38:25.324 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:38:25.417 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:38:25.421 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:38:25.523 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:38:25.526 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:38:25.553 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:38:25.560 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:38:25.567 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:38:26.190 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:38:27.604 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:38:37.199 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:39:23.401 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:39:23.403 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:39:23.409 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 14:39:23.425 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:39:23.433 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 14:39:23.644 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 14:39:43.565 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294294640 
[INFO ] 2024-12-04 14:39:43.567 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294294640 
[INFO ] 2024-12-04 14:39:43.570 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:39:43.570 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:39:43.584 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20237 ms 
[INFO ] 2024-12-04 14:39:43.586 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:39:43.658 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294294477 
[INFO ] 2024-12-04 14:39:43.658 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294294477 
[INFO ] 2024-12-04 14:39:43.661 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:39:43.661 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:39:43.867 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 80 ms 
[INFO ] 2024-12-04 14:39:48.611 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:39:48.611 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1d972028 
[INFO ] 2024-12-04 14:39:48.799 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:39:48.800 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:39:48.801 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:39:48.889 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:39:48.891 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:40:25.249 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:40:25.251 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:40:25.429 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:40:25.554 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:40:25.556 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:40:25.556 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:40:25.556 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:40:25.560 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 14:40:26.436 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:40:26.439 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:40:33.078 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:40:33.121 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:40:33.124 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:40:33.133 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:40:33.134 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:40:33.184 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:40:33.184 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:40:33.271 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:40:33.279 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:40:33.280 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:40:33.888 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:40:35.105 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:40:44.414 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:41:29.942 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:41:29.958 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:41:29.958 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:41:29.958 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[INFO ] 2024-12-04 14:41:29.961 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[ERROR] 2024-12-04 14:41:30.167 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 14:41:50.083 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294425884 
[INFO ] 2024-12-04 14:41:50.084 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294425884 
[INFO ] 2024-12-04 14:41:50.085 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:41:50.085 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:41:50.088 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20156 ms 
[INFO ] 2024-12-04 14:41:50.089 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:41:50.135 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294425840 
[INFO ] 2024-12-04 14:41:50.136 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294425840 
[INFO ] 2024-12-04 14:41:50.136 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:41:50.136 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:41:50.341 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 49 ms 
[INFO ] 2024-12-04 14:41:53.193 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:41:53.206 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@648a3d00 
[INFO ] 2024-12-04 14:41:53.206 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:41:53.345 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:41:53.345 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:41:53.414 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:41:53.414 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:42:21.793 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:42:21.794 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:42:22.065 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:42:22.066 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:42:22.127 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:42:22.128 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:42:22.129 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:42:22.129 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:42:32.930 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:42:32.972 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:42:32.975 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:42:32.987 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:42:33.002 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:42:33.003 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:42:33.004 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:42:33.149 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:42:33.150 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:42:33.229 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:42:33.244 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:42:33.245 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:42:33.698 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:42:35.111 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:42:44.891 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:43:08.366 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:43:08.370 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:43:08.371 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 14:43:08.374 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:43:08.374 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 14:43:08.577 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 14:43:28.457 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294542484 
[INFO ] 2024-12-04 14:43:28.458 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294542484 
[INFO ] 2024-12-04 14:43:28.459 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:43:28.459 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:43:28.459 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20114 ms 
[INFO ] 2024-12-04 14:43:28.460 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:43:28.496 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294542446 
[INFO ] 2024-12-04 14:43:28.496 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294542446 
[INFO ] 2024-12-04 14:43:28.496 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:43:28.496 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:43:28.499 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 36 ms 
[INFO ] 2024-12-04 14:43:33.000 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:43:33.000 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@289826dd 
[INFO ] 2024-12-04 14:43:33.163 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:43:33.165 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:43:33.165 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:43:33.216 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:43:33.217 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:44:45.710 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:44:45.711 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:44:45.912 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:44:45.913 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:44:45.974 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:44:45.974 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:44:45.974 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 14:44:45.975 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 14:44:46.806 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:44:46.808 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:44:46.808 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:45:01.756 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:45:01.762 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:45:01.762 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:45:01.762 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:45:01.839 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:45:01.842 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:45:01.901 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:45:01.908 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:45:01.909 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:45:02.352 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:45:03.540 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:45:13.087 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:46:10.753 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:46:10.823 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:46:10.824 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 14:46:11.802 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:46:11.818 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接 
[ERROR] 2024-12-04 14:46:11.819 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接 <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接

<-- Simple Stack Trace -->
Caused by: java.sql.SQLRecoverableException: 关闭的连接
	oracle.jdbc.driver.OracleClosedStatement.executeQuery(OracleClosedStatement.java:2710)
	oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:231)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:241)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:125)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.sql.SQLRecoverableException: 关闭的连接
	at oracle.jdbc.driver.OracleClosedStatement.executeQuery(OracleClosedStatement.java:2710)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:231)
	... 21 more

[INFO ] 2024-12-04 14:46:30.908 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294686341 
[INFO ] 2024-12-04 14:46:30.909 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294686341 
[INFO ] 2024-12-04 14:46:30.909 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:46:30.910 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:46:30.911 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20179 ms 
[INFO ] 2024-12-04 14:46:30.946 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:46:30.946 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294686285 
[INFO ] 2024-12-04 14:46:30.949 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294686285 
[INFO ] 2024-12-04 14:46:30.950 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:46:30.950 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:46:30.950 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 36 ms 
[INFO ] 2024-12-04 14:46:32.153 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:46:32.155 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b70afb2 
[INFO ] 2024-12-04 14:46:32.155 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:46:32.298 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:46:32.298 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:46:32.349 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:46:32.555 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:47:17.383 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:47:17.448 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:47:17.759 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:47:17.760 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:47:17.848 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:47:17.849 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:47:17.849 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:47:17.849 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:47:18.584 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:47:18.585 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:47:18.637 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:47:18.638 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:47:18.720 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:47:25.912 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:47:25.912 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:47:37.947 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:47:38.092 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:47:38.095 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:47:38.095 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:47:38.095 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:47:38.697 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:47:39.905 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:47:48.712 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:48:14.173 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:48:14.173 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:48:14.182 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 14:48:14.182 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:48:14.199 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 14:48:14.200 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 14:48:34.272 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294838164 
[INFO ] 2024-12-04 14:48:34.274 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294838164 
[INFO ] 2024-12-04 14:48:34.274 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:48:34.274 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:48:34.276 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20118 ms 
[INFO ] 2024-12-04 14:48:34.316 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:48:34.316 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294838114 
[INFO ] 2024-12-04 14:48:34.317 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294838114 
[INFO ] 2024-12-04 14:48:34.318 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:48:34.318 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:48:34.319 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 43 ms 
[INFO ] 2024-12-04 14:48:37.904 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:48:37.904 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2f10aa7a 
[INFO ] 2024-12-04 14:48:38.045 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:48:38.045 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:48:38.045 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:48:38.116 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:48:38.116 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:49:09.457 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 14:49:09.638 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 14:49:09.639 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 14:49:09.697 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 14:49:09.769 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:49:09.771 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 14:49:09.778 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 14:49:09.780 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 14:49:10.423 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 14:49:10.424 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 14:49:21.893 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 14:49:21.895 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 14:49:28.279 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 14:49:28.296 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 14:49:28.301 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 14:50:04.597 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:50:04.608 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 14:50:04.717 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 14:50:04.717 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 14:50:04.722 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 14:50:05.331 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 14:50:06.350 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:50:14.726 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 14:50:51.896 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 14:50:51.911 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 14:50:51.911 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 14:50:51.914 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 14:50:51.917 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 14:50:52.121 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:471)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 14:51:11.995 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294950121 
[INFO ] 2024-12-04 14:51:11.997 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733294950121 
[INFO ] 2024-12-04 14:51:11.997 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 14:51:11.997 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 14:51:11.998 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20123 ms 
[INFO ] 2024-12-04 14:51:12.023 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 14:51:12.023 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294950033 
[INFO ] 2024-12-04 14:51:12.024 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733294950033 
[INFO ] 2024-12-04 14:51:12.024 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 14:51:12.024 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 14:51:12.024 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 25 ms 
[INFO ] 2024-12-04 14:51:14.495 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 14:51:14.496 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@26c7ad04 
[INFO ] 2024-12-04 14:51:14.497 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 14:51:14.654 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 14:51:14.655 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 14:51:14.704 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 14:51:14.706 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:23:22.452 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 15:23:22.456 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 15:23:23.066 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 15:23:23.190 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 15:23:23.191 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:23:23.191 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:23:23.191 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:23:23.193 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:23:31.690 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 15:23:31.820 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 15:23:31.837 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 15:23:39.877 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:23:39.888 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 15:23:40.058 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 15:23:40.059 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 15:23:40.177 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600094,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:23:40.177 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 15:23:40.329 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 15:23:40.333 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 15:23:40.333 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 15:23:40.736 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 15:23:41.953 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:23:50.995 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:23:58.272 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - It was found that the transaction[first scn: 138581897, xid: 9.14.78687] that was rolled back did not commit after 50 events, and the modification of this transaction was truly discarded 
[INFO ] 2024-12-04 15:27:00.432 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 15:27:00.468 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 15:27:00.469 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 15:27:00.476 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 15:27:00.478 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 15:27:00.686 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 15:27:20.574 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733297003608 
[INFO ] 2024-12-04 15:27:20.576 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733297003608 
[INFO ] 2024-12-04 15:27:20.581 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 15:27:20.581 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 15:27:20.592 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20166 ms 
[INFO ] 2024-12-04 15:27:20.592 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 15:27:20.666 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733297003511 
[INFO ] 2024-12-04 15:27:20.666 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733297003511 
[INFO ] 2024-12-04 15:27:20.667 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 15:27:20.667 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 15:27:20.667 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 73 ms 
[INFO ] 2024-12-04 15:27:25.008 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 15:27:25.015 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@54bca5db 
[INFO ] 2024-12-04 15:27:25.138 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 15:27:25.172 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 15:27:25.172 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 15:27:25.262 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:27:25.262 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:43:38.652 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 15:43:38.656 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 15:43:38.966 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 15:43:38.966 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 15:43:39.038 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:43:39.039 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:43:39.039 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:43:39.242 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:43:39.922 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 15:43:39.924 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 15:43:39.940 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 15:43:39.941 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 15:43:39.942 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 15:43:39.983 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 15:43:39.984 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 15:43:42.981 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241599987,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:43:42.982 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 15:43:43.108 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 15:43:43.117 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 15:43:43.118 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241599987,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:43:51.595 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 15:43:52.812 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:44:01.293 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:44:09.789 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - It was found that the transaction[first scn: 138581897, xid: 9.14.78687] that was rolled back did not commit after 50 events, and the modification of this transaction was truly discarded 
[INFO ] 2024-12-04 15:44:22.130 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 15:44:22.134 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 15:44:22.135 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 15:44:22.136 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 15:44:22.139 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 15:44:22.342 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 15:44:42.270 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298219408 
[INFO ] 2024-12-04 15:44:42.271 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298219408 
[INFO ] 2024-12-04 15:44:42.275 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 15:44:42.276 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 15:44:42.277 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20159 ms 
[INFO ] 2024-12-04 15:44:42.278 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 15:44:42.309 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298219274 
[INFO ] 2024-12-04 15:44:42.309 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298219274 
[INFO ] 2024-12-04 15:44:42.310 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 15:44:42.311 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 15:44:42.311 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 33 ms 
[INFO ] 2024-12-04 15:44:46.351 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 15:44:46.351 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74c4fc39 
[INFO ] 2024-12-04 15:44:46.476 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 15:44:46.490 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 15:44:46.490 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 15:44:46.552 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:44:46.554 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:44:49.471 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 15:44:49.473 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 15:44:49.842 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 15:44:49.843 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 15:44:49.943 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:44:49.943 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:44:49.943 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:44:49.943 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:44:50.435 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 15:44:50.439 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 15:44:50.465 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 15:44:50.469 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 15:44:50.470 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-04 15:44:50.476 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594092,"timestamp":1733298255000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:44:50.569 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 15:44:50.570 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 15:44:50.570 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 15:44:50.574 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594092,"timestamp":1733298255000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:45:49.899 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138594091 
[INFO ] 2024-12-04 15:45:51.118 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:46:14.793 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 15:46:14.795 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 15:46:14.797 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 15:46:14.797 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 15:46:14.844 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 15:46:14.847 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 15:46:34.885 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298290036 
[INFO ] 2024-12-04 15:46:34.886 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298290036 
[INFO ] 2024-12-04 15:46:34.886 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 15:46:34.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 15:46:34.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20116 ms 
[INFO ] 2024-12-04 15:46:34.887 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 15:46:34.952 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298289995 
[INFO ] 2024-12-04 15:46:34.952 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298289995 
[INFO ] 2024-12-04 15:46:34.953 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 15:46:34.953 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 15:46:34.955 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 69 ms 
[INFO ] 2024-12-04 15:46:39.562 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 15:46:39.565 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@354f263d 
[INFO ] 2024-12-04 15:46:39.677 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 15:46:39.694 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 15:46:39.694 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 15:46:39.746 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:46:39.747 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:47:12.073 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 15:47:12.078 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 15:47:12.447 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 15:47:12.520 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 15:47:12.586 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:47:12.586 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:47:12.586 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:47:12.586 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:47:13.320 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 15:47:13.321 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 15:47:13.379 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 15:47:13.380 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 15:47:13.380 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 15:47:13.380 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 15:47:13.380 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 15:47:16.795 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594092,"timestamp":1733298255000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:47:16.935 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 15:47:16.938 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 15:47:16.939 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 15:47:16.940 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594092,"timestamp":1733298255000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:47:28.936 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138594091 
[INFO ] 2024-12-04 15:47:30.148 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:48:19.728 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 15:48:19.792 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 15:48:19.792 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 15:48:20.793 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 15:48:20.823 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接 
[ERROR] 2024-12-04 15:48:20.827 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接 <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接

<-- Simple Stack Trace -->
Caused by: java.sql.SQLRecoverableException: 关闭的连接
	oracle.jdbc.driver.OracleClosedStatement.executeQuery(OracleClosedStatement.java:2710)
	oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:231)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:241)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:125)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.sql.SQLRecoverableException: 关闭的连接
	at oracle.jdbc.driver.OracleClosedStatement.executeQuery(OracleClosedStatement.java:2710)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:231)
	... 21 more

[INFO ] 2024-12-04 15:48:39.883 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298432891 
[INFO ] 2024-12-04 15:48:39.884 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298432891 
[INFO ] 2024-12-04 15:48:39.884 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 15:48:39.884 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 15:48:39.886 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20174 ms 
[INFO ] 2024-12-04 15:48:39.889 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 15:48:39.943 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298432856 
[INFO ] 2024-12-04 15:48:39.944 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298432856 
[INFO ] 2024-12-04 15:48:39.944 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 15:48:39.944 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 15:48:39.946 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 59 ms 
[INFO ] 2024-12-04 15:48:43.860 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 15:48:43.982 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ce8bf06 
[INFO ] 2024-12-04 15:48:43.983 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 15:48:43.999 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 15:48:43.999 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 15:48:44.043 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:48:44.043 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:48:54.022 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 15:48:54.023 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 15:48:54.234 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 15:48:54.339 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 15:48:54.339 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:48:54.339 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 15:48:54.339 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:48:54.339 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 15:48:54.917 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 15:48:54.917 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 15:48:54.983 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 15:48:54.984 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 15:48:54.984 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-12-04 15:48:54.988 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138595132,"pendingScn":138595133,"timestamp":1733298491000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:48:54.988 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 15:48:55.056 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 15:48:55.056 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 15:48:55.260 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138595132,"pendingScn":138595133,"timestamp":1733298491000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 15:49:03.690 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138595132 
[INFO ] 2024-12-04 15:49:04.911 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 15:49:22.206 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 15:49:22.208 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 15:49:22.208 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 15:49:22.208 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 15:49:22.208 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 15:49:22.229 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 15:49:42.305 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298534494 
[INFO ] 2024-12-04 15:49:42.305 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733298534494 
[INFO ] 2024-12-04 15:49:42.305 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 15:49:42.305 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 15:49:42.307 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20120 ms 
[INFO ] 2024-12-04 15:49:42.307 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 15:49:42.334 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298534400 
[INFO ] 2024-12-04 15:49:42.334 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733298534400 
[INFO ] 2024-12-04 15:49:42.334 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 15:49:42.334 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 15:49:42.544 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 28 ms 
[INFO ] 2024-12-04 15:49:43.622 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 15:49:43.623 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ee1e16a 
[INFO ] 2024-12-04 15:49:43.758 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 15:49:43.758 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 15:49:43.758 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 15:49:43.806 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 15:49:43.806 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 16:54:59.481 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 16:54:59.486 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 16:54:59.892 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 16:55:00.024 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 16:55:00.025 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 16:55:00.026 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 16:55:00.030 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 16:55:00.037 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 3 ms 
[INFO ] 2024-12-04 16:55:00.760 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 16:55:00.760 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 16:55:00.848 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 16:55:00.849 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 16:55:00.850 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 16:55:00.850 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 16:55:01.062 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 16:55:01.237 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600088,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 16:55:01.237 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 16:55:01.319 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 16:55:01.319 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 16:55:01.323 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1733241600088,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 16:55:12.690 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138402073 
[INFO ] 2024-12-04 16:55:14.121 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_12_04/o1_mf_1_2985_mnzb49jm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 16:55:23.105 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 16:55:30.125 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - It was found that the transaction[first scn: 138581897, xid: 9.14.78687] that was rolled back did not commit after 50 events, and the modification of this transaction was truly discarded 
[INFO ] 2024-12-04 16:55:43.016 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 16:55:43.016 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 16:55:43.016 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 16:55:43.020 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 16:55:43.038 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 16:55:43.040 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 16:56:03.091 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733302500348 
[INFO ] 2024-12-04 16:56:03.094 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733302500348 
[INFO ] 2024-12-04 16:56:03.110 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 16:56:03.110 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 16:56:03.117 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20126 ms 
[INFO ] 2024-12-04 16:56:03.117 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 16:56:03.165 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733302500283 
[INFO ] 2024-12-04 16:56:03.166 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733302500283 
[INFO ] 2024-12-04 16:56:03.166 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 16:56:03.166 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 16:56:03.166 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 52 ms 
[INFO ] 2024-12-04 16:56:07.300 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 16:56:07.300 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1998a4ed 
[INFO ] 2024-12-04 16:56:07.435 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 16:56:07.435 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 16:56:07.494 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 16:56:07.495 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 16:56:07.495 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 16:59:47.646 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 16:59:47.647 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 16:59:47.925 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 16:59:48.075 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 16:59:48.076 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 16:59:48.076 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 16:59:48.076 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 16:59:48.077 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 16:59:48.805 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 16:59:48.805 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 16:59:48.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 16:59:57.621 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 16:59:57.623 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 16:59:57.666 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 16:59:57.687 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594092,"timestamp":1733298255000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 16:59:57.687 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 16:59:57.687 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 16:59:57.841 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 16:59:57.844 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 16:59:57.844 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594092,"timestamp":1733298255000,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:00:02.800 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138594091 
[INFO ] 2024-12-04 17:00:03.956 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 17:00:42.180 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:00:42.205 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 17:00:42.205 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 17:00:42.206 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 17:00:42.239 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-12-04 17:00:42.247 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:470)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 17:01:02.316 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733302788396 
[INFO ] 2024-12-04 17:01:02.316 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733302788396 
[INFO ] 2024-12-04 17:01:02.316 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:01:02.316 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:01:02.318 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20142 ms 
[INFO ] 2024-12-04 17:01:02.318 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:01:02.350 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733302788349 
[INFO ] 2024-12-04 17:01:02.351 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733302788349 
[INFO ] 2024-12-04 17:01:02.351 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:01:02.353 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:01:02.354 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 35 ms 
[INFO ] 2024-12-04 17:01:02.751 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:01:02.752 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@e8fdcf4 
[INFO ] 2024-12-04 17:01:02.884 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:01:02.884 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:01:02.884 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:01:02.924 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:01:02.928 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:11:43.820 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:11:43.833 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:11:44.975 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:11:45.184 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:11:45.507 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:11:45.508 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:11:45.509 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:11:45.509 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 17:11:46.659 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:11:46.662 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:11:46.684 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:11:46.685 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:11:46.689 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:11:50.364 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@d2f72c6 failed, java.lang.RuntimeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 
[ERROR] 2024-12-04 17:11:50.368 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@d2f72c6 failed, java.lang.RuntimeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@d2f72c6 failed, java.lang.RuntimeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more


<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@d2f72c6 failed, java.lang.RuntimeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@d2f72c6 failed, java.lang.RuntimeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	... 13 more
Caused by: java.lang.RuntimeException: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: Failed to parse stream offset string: syntax error, expect {, actual int, pos 0, fastjson-version 1.2.83
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

[INFO ] 2024-12-04 17:11:50.392 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:11:50.393 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:11:50.418 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:11:50.422 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:11:50.649 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:11:50.698 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733303505849 
[INFO ] 2024-12-04 17:11:50.698 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733303505849 
[INFO ] 2024-12-04 17:11:50.705 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:11:50.708 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:11:50.918 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 77 ms 
[INFO ] 2024-12-04 17:11:55.266 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:11:55.276 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5743ffcf 
[INFO ] 2024-12-04 17:11:55.277 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:11:55.436 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:11:55.439 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:11:55.490 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:11:55.491 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:12:10.460 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733303505987 
[INFO ] 2024-12-04 17:12:10.461 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733303505987 
[INFO ] 2024-12-04 17:12:10.461 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:12:10.464 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:12:10.672 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20074 ms 
[INFO ] 2024-12-04 17:12:54.940 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:12:54.942 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:12:55.217 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:12:55.552 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:12:55.634 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:12:55.737 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 2 ms 
[INFO ] 2024-12-04 17:12:55.740 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:12:55.740 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:12:56.537 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:12:56.537 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:12:56.597 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:12:56.601 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:12:58.856 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:13:01.273 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:13:01.412 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:13:01.463 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594091,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:13:01.586 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 17:13:01.589 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 17:13:01.597 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 17:13:01.599 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594091,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:13:08.310 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138594091 
[INFO ] 2024-12-04 17:13:09.488 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 17:13:43.389 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:13:43.509 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 17:13:43.510 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 17:13:43.514 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 17:13:43.515 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1306): Error : 1306, Position : 231, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 
[ERROR] 2024-12-04 17:13:43.721 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Error : 1306, Position : 231, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 231, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 231, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:469)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Error : 1306, Position : 231, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 138623395 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='C##TAPDATA' AND TABLE_NAME IN ('AA_0328','_tapdata_heartbeat_table'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 19 more

[INFO ] 2024-12-04 17:14:03.612 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733303576077 
[INFO ] 2024-12-04 17:14:03.613 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733303576077 
[INFO ] 2024-12-04 17:14:03.614 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:14:03.622 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:14:03.622 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20247 ms 
[INFO ] 2024-12-04 17:14:03.664 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:14:03.664 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733303576037 
[INFO ] 2024-12-04 17:14:03.665 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733303576037 
[INFO ] 2024-12-04 17:14:03.665 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:14:03.667 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:14:03.670 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 44 ms 
[INFO ] 2024-12-04 17:14:08.216 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:14:08.218 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@376a808 
[INFO ] 2024-12-04 17:14:08.219 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:14:08.342 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:14:08.403 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:14:08.403 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:14:08.403 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:15:24.763 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:15:24.767 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:15:25.105 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:15:25.239 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:15:25.309 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:15:25.309 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:15:25.311 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:15:25.311 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:15:26.121 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:15:41.725 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:15:41.733 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:15:41.740 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594091,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:15:41.742 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 17:15:41.743 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:15:41.775 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:15:41.886 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:15:41.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 17:15:41.887 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:15:41.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 17:15:41.894 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594091,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:15:47.033 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138594091 
[INFO ] 2024-12-04 17:15:48.214 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 17:16:14.510 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:16:14.511 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 17:16:14.547 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 17:16:14.552 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 17:16:34.668 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733303725628 
[INFO ] 2024-12-04 17:16:34.669 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733303725628 
[INFO ] 2024-12-04 17:16:34.676 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:16:34.682 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:16:34.684 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20197 ms 
[INFO ] 2024-12-04 17:16:34.684 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:16:34.723 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733303725594 
[INFO ] 2024-12-04 17:16:34.724 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733303725594 
[INFO ] 2024-12-04 17:16:34.724 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:16:34.726 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:16:34.726 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 44 ms 
[INFO ] 2024-12-04 17:16:37.004 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:16:37.005 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2ce82f8b 
[INFO ] 2024-12-04 17:16:37.008 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:16:37.163 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:16:37.164 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:16:37.223 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:16:37.223 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:25:46.482 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:25:46.485 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:25:46.891 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:25:46.984 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:25:46.985 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:25:46.985 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:25:46.985 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:25:46.986 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 17:25:47.608 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:25:47.608 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:25:47.682 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:25:47.686 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:25:51.622 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:25:51.738 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:25:51.739 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:25:51.745 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594091,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:25:51.907 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-12-04 17:25:51.914 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync starting... 
[INFO ] 2024-12-04 17:25:51.914 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Initial sync completed 
[INFO ] 2024-12-04 17:25:51.915 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Starting stream read, table list: [AA_0328, _tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":138594091,"pendingScn":138594091,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-12-04 17:25:53.231 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - total start mining scn: 138594091 
[INFO ] 2024-12-04 17:25:54.443 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-12-04 17:26:30.211 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:26:30.213 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner is shutting down... 
[INFO ] 2024-12-04 17:26:30.421 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Log Miner has been closed! 
[INFO ] 2024-12-04 17:26:31.250 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Incremental sync completed 
[INFO ] 2024-12-04 17:26:31.252 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接 
[ERROR] 2024-12-04 17:26:31.459 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接 <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接

<-- Simple Stack Trace -->
Caused by: java.sql.SQLRecoverableException: 关闭的连接
	oracle.jdbc.driver.OracleClosedStatement.executeQuery(OracleClosedStatement.java:2710)
	oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:231)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:918)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:908)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:289)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.RuntimeException: java.sql.SQLRecoverableException: 关闭的连接
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:241)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:125)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:469)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:897)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.sql.SQLRecoverableException: 关闭的连接
	at oracle.jdbc.driver.OracleClosedStatement.executeQuery(OracleClosedStatement.java:2710)
	at oracle.jdbc.driver.OraclePreparedStatementWrapper.executeQuery(OraclePreparedStatementWrapper.java:1098)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.checkArchiveAndOnlineLogWithScn(SingleOracleLogMiner.java:231)
	... 21 more

[INFO ] 2024-12-04 17:26:50.377 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733304347326 
[INFO ] 2024-12-04 17:26:50.378 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733304347326 
[INFO ] 2024-12-04 17:26:50.379 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:26:50.385 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:26:50.386 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20216 ms 
[INFO ] 2024-12-04 17:26:50.426 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:26:50.427 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733304347202 
[INFO ] 2024-12-04 17:26:50.430 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733304347202 
[INFO ] 2024-12-04 17:26:50.431 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:26:50.431 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:26:50.431 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 39 ms 
[INFO ] 2024-12-04 17:26:53.156 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:26:53.161 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@69264baa 
[INFO ] 2024-12-04 17:26:53.162 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:26:53.302 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:26:53.347 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:26:53.347 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:26:53.347 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:29:08.174 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:29:08.185 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:29:08.502 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:29:08.519 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:29:08.589 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:29:08.589 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:29:08.589 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 17:29:08.589 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 17:29:09.361 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:29:09.362 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:29:09.447 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:29:09.447 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:29:09.518 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:29:09.518 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:29:09.518 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:29:13.009 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@3f6fec46 failed, java.lang.RuntimeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 
[ERROR] 2024-12-04 17:29:13.054 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@3f6fec46 failed, java.lang.RuntimeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@3f6fec46 failed, java.lang.RuntimeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more


<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@3f6fec46 failed, java.lang.RuntimeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2800/1937939471@3f6fec46 failed, java.lang.RuntimeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:262)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:182)
	... 13 more
Caused by: java.lang.RuntimeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

[INFO ] 2024-12-04 17:29:13.056 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:29:13.265 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:29:33.318 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733304548962 
[INFO ] 2024-12-04 17:29:33.319 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733304548962 
[INFO ] 2024-12-04 17:29:33.320 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:29:33.320 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:29:33.326 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20264 ms 
[INFO ] 2024-12-04 17:29:33.329 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:29:33.358 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733304548914 
[INFO ] 2024-12-04 17:29:33.358 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733304548914 
[INFO ] 2024-12-04 17:29:33.360 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:29:33.360 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:29:33.361 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 39 ms 
[INFO ] 2024-12-04 17:29:33.473 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:29:33.473 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@35b3c384 
[INFO ] 2024-12-04 17:29:33.474 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:29:33.597 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:29:33.597 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:29:33.631 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:29:33.633 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:38:04.024 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:38:04.032 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:38:04.915 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:38:05.123 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:38:05.675 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:38:05.676 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:38:05.680 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:38:05.684 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:38:08.367 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:38:08.369 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:38:08.377 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:38:08.492 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:38:10.876 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:38:10.891 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:38:10.892 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:38:10.892 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:38:10.954 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz 
[INFO ] 2024-12-04 17:38:10.955 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:38:10.956 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[ERROR] 2024-12-04 17:38:11.154 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz <-- Error Message -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	... 6 more

[INFO ] 2024-12-04 17:38:11.162 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:38:11.260 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:38:11.260 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:38:11.343 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305086773 
[INFO ] 2024-12-04 17:38:11.347 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305086773 
[INFO ] 2024-12-04 17:38:11.347 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:38:11.348 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:38:11.555 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 103 ms 
[INFO ] 2024-12-04 17:38:12.914 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:38:13.058 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@29c46127 
[INFO ] 2024-12-04 17:38:13.060 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:38:13.097 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:38:13.098 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:38:13.198 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:38:13.200 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:38:31.300 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305086773 
[INFO ] 2024-12-04 17:38:31.307 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305086773 
[INFO ] 2024-12-04 17:38:31.308 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:38:31.308 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:38:31.514 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20120 ms 
[INFO ] 2024-12-04 17:39:04.374 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:39:04.377 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:39:04.546 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:39:04.693 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:39:04.797 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:39:04.799 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:39:04.799 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:39:05.010 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:39:05.325 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:39:05.327 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:39:05.328 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:39:05.375 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:39:05.376 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:39:05.378 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:39:05.378 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:39:05.378 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:39:13.801 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:39:13.803 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz 
[INFO ] 2024-12-04 17:39:13.803 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[ERROR] 2024-12-04 17:39:13.839 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz <-- Error Message -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:185)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	... 6 more

[INFO ] 2024-12-04 17:39:13.911 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:39:13.913 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:39:34.053 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305144942 
[INFO ] 2024-12-04 17:39:34.054 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305144942 
[INFO ] 2024-12-04 17:39:34.055 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:39:34.058 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:39:34.059 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20161 ms 
[INFO ] 2024-12-04 17:39:34.060 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:39:34.096 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305144840 
[INFO ] 2024-12-04 17:39:34.096 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305144840 
[INFO ] 2024-12-04 17:39:34.098 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:39:34.099 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:39:34.306 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 40 ms 
[INFO ] 2024-12-04 17:39:38.789 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:39:38.794 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6a1abf46 
[INFO ] 2024-12-04 17:39:38.929 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:39:38.929 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:39:38.956 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:39:38.956 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:39:38.961 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:40:12.366 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:40:12.579 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:40:13.388 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:40:13.462 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:40:14.066 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:40:14.069 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:40:14.073 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:40:14.075 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:40:15.996 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:40:15.999 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:40:16.018 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:40:16.018 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:40:16.144 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:40:16.145 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:40:19.614 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:40:19.657 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:40:19.663 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:40:19.664 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:40:20.527 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz 
[ERROR] 2024-12-04 17:40:20.615 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz <-- Error Message -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[INFO ] 2024-12-04 17:40:20.617 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:40:20.653 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:40:20.654 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:40:20.787 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305214890 
[INFO ] 2024-12-04 17:40:20.794 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305214890 
[INFO ] 2024-12-04 17:40:20.794 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:40:20.816 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:40:20.817 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 168 ms 
[INFO ] 2024-12-04 17:40:21.193 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:40:21.204 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4b4a89ed 
[INFO ] 2024-12-04 17:40:21.367 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:40:21.369 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:40:21.370 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:40:21.429 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:40:21.430 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:40:40.786 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305215052 
[INFO ] 2024-12-04 17:40:40.790 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305215052 
[INFO ] 2024-12-04 17:40:40.790 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:40:40.790 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:40:40.790 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20140 ms 
[INFO ] 2024-12-04 17:42:49.336 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:42:49.418 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:42:50.025 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:42:50.144 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:42:50.279 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:42:50.283 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:42:50.285 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 17:42:50.288 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:42:54.809 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:42:54.886 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:42:54.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:42:54.887 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:42:55.033 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz 
[ERROR] 2024-12-04 17:42:55.035 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz <-- Error Message -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[INFO ] 2024-12-04 17:42:55.090 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:42:55.091 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:42:55.351 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:42:55.352 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:42:55.355 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:42:55.369 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:42:55.511 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:42:55.513 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:42:55.735 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:42:55.917 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305370534 
[INFO ] 2024-12-04 17:42:55.917 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305370534 
[INFO ] 2024-12-04 17:42:55.919 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:42:55.930 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:42:56.145 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 228 ms 
[INFO ] 2024-12-04 17:42:59.892 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:42:59.901 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6b2c106c 
[INFO ] 2024-12-04 17:43:00.023 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:43:00.063 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:43:00.064 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:43:00.124 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:43:00.124 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:43:15.250 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305370619 
[INFO ] 2024-12-04 17:43:15.254 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305370619 
[INFO ] 2024-12-04 17:43:15.260 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:43:15.261 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:43:15.262 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20178 ms 
[INFO ] 2024-12-04 17:45:58.790 - [任务 55] - Task initialization... 
[INFO ] 2024-12-04 17:45:59.006 - [任务 55] - Start task milestones: 674fe56eb43a5a5dee9300df(任务 55) 
[INFO ] 2024-12-04 17:45:59.825 - [任务 55] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-04 17:46:00.029 - [任务 55] - The engine receives 任务 55 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-04 17:46:00.358 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:46:00.359 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] start preload schema,table counts: 1 
[INFO ] 2024-12-04 17:46:00.359 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] preload schema finished, cost 1 ms 
[INFO ] 2024-12-04 17:46:00.360 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] preload schema finished, cost 0 ms 
[INFO ] 2024-12-04 17:46:01.936 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" read batch size: 100 
[INFO ] 2024-12-04 17:46:01.943 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Source node "qa_oracle_11g_single_1718086209358_4120" event queue capacity: 200 
[INFO ] 2024-12-04 17:46:05.360 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:46:05.419 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-04 17:46:05.980 - [任务 55][hardy_184] - Node(hardy_184) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-04 17:46:05.997 - [任务 55][hardy_184] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-04 17:46:06.000 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz 
[INFO ] 2024-12-04 17:46:06.042 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[INFO ] 2024-12-04 17:46:06.042 - [任务 55][hardy_184] - Sync progress not exists, will run task as first time 
[ERROR] 2024-12-04 17:46:06.082 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz <-- Error Message -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	...

<-- Full Stack Trace -->
Failed to parse stream offset string: Oracle use scn as offset, invalid scn: zzzz
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromString$12(HazelcastSourcePdkBaseNode.java:787)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromString(HazelcastSourcePdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetCDC(HazelcastSourcePdkBaseNode.java:720)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:419)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:276)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[INFO ] 2024-12-04 17:46:06.083 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Job suspend in error handle 
[INFO ] 2024-12-04 17:46:06.205 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] running status set to false 
[INFO ] 2024-12-04 17:46:06.208 - [任务 55][hardy_184] - Table "hardy_test.AA_0328" exists, skip auto create table 
[INFO ] 2024-12-04 17:46:06.208 - [任务 55][hardy_184] - The table AA_0328 has already exist. 
[INFO ] 2024-12-04 17:46:06.407 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] running status set to false 
[INFO ] 2024-12-04 17:46:06.409 - [任务 55][hardy_184] - PDK connector node stopped: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305560679 
[INFO ] 2024-12-04 17:46:06.410 - [任务 55][hardy_184] - PDK connector node released: HazelcastTargetPdkDataNode_1fe43b50-65ed-476f-9b5b-8561fd56c2ab_1733305560679 
[INFO ] 2024-12-04 17:46:06.412 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] schema data cleaned 
[INFO ] 2024-12-04 17:46:06.428 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] monitor closed 
[INFO ] 2024-12-04 17:46:06.430 - [任务 55][hardy_184] - Node hardy_184[1fe43b50-65ed-476f-9b5b-8561fd56c2ab] close complete, cost 83 ms 
[INFO ] 2024-12-04 17:46:10.494 - [任务 55] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-04 17:46:10.499 - [任务 55] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c1832b7 
[INFO ] 2024-12-04 17:46:10.612 - [任务 55] - Stop task milestones: 674fe56eb43a5a5dee9300df(任务 55)  
[INFO ] 2024-12-04 17:46:10.644 - [任务 55] - Stopped task aspect(s) 
[INFO ] 2024-12-04 17:46:10.645 - [任务 55] - Snapshot order controller have been removed 
[INFO ] 2024-12-04 17:46:10.723 - [任务 55] - Remove memory task client succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:46:10.723 - [任务 55] - Destroy memory task client cache succeed, task: 任务 55[674fe56eb43a5a5dee9300df] 
[INFO ] 2024-12-04 17:46:26.230 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node stopped: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305560679 
[INFO ] 2024-12-04 17:46:26.233 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - PDK connector node released: HazelcastSourcePdkDataNode_c4d55897-aa30-4729-bd89-d0ceaf89acd1_1733305560679 
[INFO ] 2024-12-04 17:46:26.233 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] schema data cleaned 
[INFO ] 2024-12-04 17:46:26.233 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] monitor closed 
[INFO ] 2024-12-04 17:46:26.439 - [任务 55][qa_oracle_11g_single_1718086209358_4120] - Node qa_oracle_11g_single_1718086209358_4120[c4d55897-aa30-4729-bd89-d0ceaf89acd1] close complete, cost 20142 ms 
