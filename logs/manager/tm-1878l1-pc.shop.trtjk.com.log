2025-04-08 11:22:04.161 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-08 11:22:04.199 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 47754 (/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm/target/classes started by shihua<PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/tapdata_v3)
2025-04-08 11:22:04.199 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-04-08 11:22:05.231 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:22:05.232 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:22:05.391 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 155 ms. Found 2 MongoDB repository interfaces.
2025-04-08 11:22:05.392 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:22:05.392 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:22:05.394 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-04-08 11:22:05.395 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:22:05.395 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:22:05.396 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 MongoDB repository interfaces.
2025-04-08 11:22:05.400 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-04-08 11:22:05.723 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:22:05.724 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-04-08 11:22:05.786 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-04-08 11:22:05.787 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-04-08 11:22:05.787 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 61 ms. Found 0 Elasticsearch repository interfaces.
2025-04-08 11:22:06.508 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3001 (http)
2025-04-08 11:22:06.518 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3001"] connector has been configured to support HTTP upgrade to [h2c]
2025-04-08 11:22:06.519 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3001"]
2025-04-08 11:22:06.520 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-08 11:22:06.520 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-08 11:22:06.587 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-08 11:22:06.588 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2360 ms
2025-04-08 11:22:06.764 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@da9374c, com.mongodb.Jep395RecordCodecProvider@34647f58, com.mongodb.KotlinCodecProvider@14b96c1e]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:22:06.778 [tm] [cluster-ClusterId{value='67f4965eaae0b042175a52e9', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=11017833, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:22:06 CST 2025, lastUpdateTimeNanos=151654331790958}
2025-04-08 11:22:08.447 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@da9374c, com.mongodb.Jep395RecordCodecProvider@34647f58, com.mongodb.KotlinCodecProvider@14b96c1e]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:22:08.460 [tm] [cluster-ClusterId{value='67f49660aae0b042175a52ea', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=13306250, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:22:06 CST 2025, lastUpdateTimeNanos=151656015709833}
2025-04-08 11:22:08.824 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-04-08 11:22:08.835 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@da9374c, com.mongodb.Jep395RecordCodecProvider@34647f58, com.mongodb.KotlinCodecProvider@14b96c1e]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:22:08.839 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-04-08 11:22:08.840 [tm] [cluster-ClusterId{value='67f49660aae0b042175a52eb', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=4274667, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:22:06 CST 2025, lastUpdateTimeNanos=151656395038833}
2025-04-08 11:22:10.729 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-04-08 11:22:10.737 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-08 11:22:10.738 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-04-08 11:22:10.738 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-04-08 11:22:10.738 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-04-08 11:22:10.738 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-04-08 11:22:10.738 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-04-08 11:22:10.738 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@521981d9
2025-04-08 11:22:10.905 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-04-08 11:22:11.139 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-04-08 11:22:11.580 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-04-08 11:22:12.350 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-04-08 11:22:12.351 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-04-08 11:22:12.523 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3001"]
2025-04-08 11:22:12.528 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3001 (http) with context path '/'
2025-04-08 11:22:12.529 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-04-08 11:22:12.529 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-04-08 11:22:12.543 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-04-08 11:22:12.556 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 8.604 seconds (process running for 9.006)
2025-04-08 11:22:12.610 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 3.21-1
2025-04-08 11:22:12.661 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-04-08 11:22:12.662 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-04-08 11:22:12.666 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-04-08 11:22:12.803 [tm] [main] INFO  org.reflections.Reflections - Reflections took 127 ms to scan 18 urls, producing 62 keys and 1097 values
2025-04-08 11:22:12.894 [tm] [main] INFO  org.reflections.Reflections - Reflections took 14 ms to scan 12 urls, producing 15 keys and 147 values
2025-04-08 11:22:12.896 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 10 keys and 24 values
2025-04-08 11:22:12.898 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@da9374c, com.mongodb.Jep395RecordCodecProvider@34647f58, com.mongodb.KotlinCodecProvider@14b96c1e]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:22:12.899 [tm] [cluster-ClusterId{value='67f49664aae0b042175a52ec', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=782458, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:22:12 CST 2025, lastUpdateTimeNanos=151660454349375}
2025-04-08 11:22:12.958 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 4 keys and 9 values
2025-04-08 11:22:13.000 [tm] [main] INFO  PDK - IPHolder [Server ip is [***********, *************, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-04-08 11:22:13.035 [tm] [main] INFO  PDK - NodeHealthManager [Node ********** added into healthy node list, nodeHealth NodeHealth id ********** health 1 online 1 time Tue Apr 08 11:22:10 CST 2025; , nodeRegistry NodeRegistry ips [***********, *************, 127.0.0.1] httpPort 3000 wsPort 8246 type proxy time Tue Apr 08 11:22:00 CST 2025; ]
2025-04-08 11:22:15.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:15.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:20.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:20.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:21.028 [tm] [http-nio-3001-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-08 11:22:21.028 [tm] [http-nio-3001-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-08 11:22:21.033 [tm] [http-nio-3001-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-04-08 11:22:25.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:25.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:30.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:30.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:35.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:35.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:40.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:40.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:45.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:45.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:50.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:50.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:22:55.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:22:55.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:00.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:00.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:05.000 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:05.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:05.005 [tm] [taskScheduler-32] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-04-08 11:23:10.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:10.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:15.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:15.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:20.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:20.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:25.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:25.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:30.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:30.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:35.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:35.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:40.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:40.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:45.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:45.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:50.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:50.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:23:55.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:23:55.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:00.020 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:00.025 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:02.036 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:24:05.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:05.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:10.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:10.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:15.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:15.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:20.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:20.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:25.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:25.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:30.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:30.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:35.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:35.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:40.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:40.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:45.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:45.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:50.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:50.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:24:55.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:24:55.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:00.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:00.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:02.020 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:25:05.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:05.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:10.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:10.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:15.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:15.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:20.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:25.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:25.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:30.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:30.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:35.000 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:35.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:40.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:40.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:45.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:45.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:50.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:50.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:25:55.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:25:55.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:00.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:00.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:02.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to start repeat inspect
2025-04-08 11:26:02.018 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to start repeat inspect
2025-04-08 11:26:05.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:05.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:10.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:10.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:15.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:15.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:20.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:20.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:25.000 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:25.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:30.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:30.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:35.000 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:35.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:40.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:45.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:45.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:50.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:50.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:26:55.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:26:55.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:00.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:00.018 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:05.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:05.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:10.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:10.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:15.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:15.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:20.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:20.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:25.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:25.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:30.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:30.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:35.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:35.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:40.000 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:40.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:45.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:45.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:50.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:50.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:27:55.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:27:55.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:00.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:00.017 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:05.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:05.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:10.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:10.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:15.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:15.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:20.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:25.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:25.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:30.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:30.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:35.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:35.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:40.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:40.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:45.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:45.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:50.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:50.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:28:55.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:28:55.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:00.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:00.064 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:05.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:05.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:10.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:10.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:15.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:15.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:20.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:20.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:25.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:25.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:30.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:30.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:35.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:35.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:40.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:45.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:45.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:50.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:50.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:29:55.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:29:55.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:00.016 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:00.022 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:00.035 [tm] [LogCollectorSchedule-removeTaskSchedule] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes'; Maybe a fragment in 'DAG -> Node' is considered a simple type; Mapper continues with dag.nodes
2025-04-08 11:30:00.037 [tm] [LogCollectorSchedule-removeTaskSchedule] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes'; Maybe a fragment in 'DAG -> Node' is considered a simple type; Mapper continues with dag.nodes
2025-04-08 11:30:05.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:05.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:10.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:10.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:15.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:15.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:20.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:20.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:25.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:25.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:30.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:30.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:35.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:35.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:40.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:45.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:45.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:50.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:50.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:30:55.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:30:55.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:00.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:00.026 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:05.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:05.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:10.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:10.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:15.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:15.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:20.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:25.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:25.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:30.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:30.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:35.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:35.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:40.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:40.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:45.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:45.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:50.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:50.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:31:55.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:31:55.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:00.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:32:00.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:05.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:32:05.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:10.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:32:10.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:15.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:32:15.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:20.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:32:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:24.671 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-08 11:32:24.674 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-08 11:32:25.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:32:25.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:32:42.949 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-08 11:32:42.985 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 54079 (/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
2025-04-08 11:32:42.986 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-04-08 11:32:43.828 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:32:43.829 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:32:43.989 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 156 ms. Found 2 MongoDB repository interfaces.
2025-04-08 11:32:43.991 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:32:43.994 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:32:43.999 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-04-08 11:32:44.006 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:32:44.010 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:32:44.012 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-04-08 11:32:44.019 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-04-08 11:32:44.266 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:32:44.266 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-04-08 11:32:44.313 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-04-08 11:32:44.314 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-04-08 11:32:44.314 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Elasticsearch repository interfaces.
2025-04-08 11:32:44.942 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3001 (http)
2025-04-08 11:32:44.951 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3001"] connector has been configured to support HTTP upgrade to [h2c]
2025-04-08 11:32:44.952 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3001"]
2025-04-08 11:32:44.952 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-08 11:32:44.952 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-08 11:32:44.996 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-08 11:32:44.996 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1981 ms
2025-04-08 11:32:45.145 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@8dbf0f2, com.mongodb.Jep395RecordCodecProvider@59d0fac9, com.mongodb.KotlinCodecProvider@1d3546f9]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:32:45.159 [tm] [cluster-ClusterId{value='67f498ddb3936f6c57878268', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=11313750, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:32:34 CST 2025, lastUpdateTimeNanos=152292718548125}
2025-04-08 11:32:47.246 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@8dbf0f2, com.mongodb.Jep395RecordCodecProvider@59d0fac9, com.mongodb.KotlinCodecProvider@1d3546f9]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:32:47.248 [tm] [cluster-ClusterId{value='67f498dfb3936f6c57878269', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2216875, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:32:47 CST 2025, lastUpdateTimeNanos=152294809407375}
2025-04-08 11:32:47.607 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-04-08 11:32:47.620 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@8dbf0f2, com.mongodb.Jep395RecordCodecProvider@59d0fac9, com.mongodb.KotlinCodecProvider@1d3546f9]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:32:47.622 [tm] [cluster-ClusterId{value='67f498dfb3936f6c5787826a', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=1474000, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:32:47 CST 2025, lastUpdateTimeNanos=152295183875500}
2025-04-08 11:32:47.625 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.cluster - Waiting for server to become available for operation with ID 13. Remaining time: 29998 ms. Selector: ReadPreferenceServerSelector{readPreference=primary}, topology description: {type=UNKNOWN, servers=[{address=localhost:27017, type=UNKNOWN, state=CONNECTING}].
2025-04-08 11:32:49.417 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-04-08 11:32:49.424 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-08 11:32:49.425 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-04-08 11:32:49.425 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-04-08 11:32:49.425 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-04-08 11:32:49.425 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-04-08 11:32:49.425 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-04-08 11:32:49.425 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@20894afb
2025-04-08 11:32:49.575 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-04-08 11:32:49.790 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-04-08 11:32:50.219 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-04-08 11:32:50.991 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-04-08 11:32:50.992 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-04-08 11:32:51.164 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3001"]
2025-04-08 11:32:51.170 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3001 (http) with context path '/'
2025-04-08 11:32:51.171 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-04-08 11:32:51.171 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-04-08 11:32:51.186 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-04-08 11:32:51.198 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 8.523 seconds (process running for 8.845)
2025-04-08 11:32:51.248 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 3.21-1
2025-04-08 11:32:51.296 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-04-08 11:32:51.297 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-04-08 11:32:51.300 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-04-08 11:32:51.441 [tm] [http-nio-3001-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-08 11:32:51.441 [tm] [http-nio-3001-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-08 11:32:51.443 [tm] [http-nio-3001-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-04-08 11:32:51.453 [tm] [main] INFO  org.reflections.Reflections - Reflections took 143 ms to scan 18 urls, producing 62 keys and 1097 values
2025-04-08 11:32:51.558 [tm] [main] INFO  org.reflections.Reflections - Reflections took 19 ms to scan 12 urls, producing 15 keys and 147 values
2025-04-08 11:32:51.560 [tm] [main] INFO  org.reflections.Reflections - Reflections took 2 ms to scan 1 urls, producing 10 keys and 24 values
2025-04-08 11:32:51.562 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@8dbf0f2, com.mongodb.Jep395RecordCodecProvider@59d0fac9, com.mongodb.KotlinCodecProvider@1d3546f9]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:32:51.563 [tm] [cluster-ClusterId{value='67f498e3b3936f6c5787826b', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=651458, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:32:51 CST 2025, lastUpdateTimeNanos=152299124477375}
2025-04-08 11:32:51.619 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values
2025-04-08 11:32:51.672 [tm] [main] INFO  PDK - IPHolder [Server ip is [***********, *************, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-04-08 11:32:51.707 [tm] [main] INFO  PDK - NodeHealthManager [Node ********** added into healthy node list, nodeHealth NodeHealth id ********** health 0 online 0 time Tue Apr 08 11:32:51 CST 2025; , nodeRegistry NodeRegistry ips [***********, *************, 127.0.0.1] httpPort 3000 wsPort 8246 type proxy time Tue Apr 08 11:32:51 CST 2025; ]
2025-04-08 11:33:00.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:33:00.028 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:33:02.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to start repeat inspect
2025-04-08 11:33:02.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to start repeat inspect
2025-04-08 11:33:05.004 [tm] [taskScheduler-40] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-04-08 11:33:20.001 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:33:20.002 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:33:40.003 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:33:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:34:00.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:34:00.021 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:34:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:34:20.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:34:40.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:34:40.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:35:00.013 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:35:00.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:35:20.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:35:20.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:35:40.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:35:40.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:36:00.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:36:00.016 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:36:02.057 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:36:20.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:36:20.012 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:36:40.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:36:40.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:37:00.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:37:00.018 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:37:02.019 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:37:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:37:20.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:37:40.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:37:40.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:38:00.018 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:38:00.022 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:38:02.016 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:38:20.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:38:20.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:38:40.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:38:40.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:39:00.016 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:39:00.021 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:39:02.019 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:39:20.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:39:20.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:39:21.922 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:39:36.959 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:39:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:39:40.008 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:39:41.983 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:39:52.018 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:00.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:40:00.019 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:40:02.052 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:12.085 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:17.102 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:20.011 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:40:20.020 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:40:27.126 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:37.158 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:40:40.007 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:40:42.176 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:40:52.211 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:41:00.009 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:41:00.016 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:41:02.018 [tm] [InspectSchedule-cleanDeadInspect] INFO  c.t.t.i.service.InspectServiceImpl - []
2025-04-08 11:41:02.247 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:41:07.272 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:41:17.309 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:41:18.282 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-08 11:41:18.283 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-04-08 11:41:32.714 [tm] [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-04-08 11:41:32.769 [tm] [main] INFO  com.tapdata.tm.TMApplication - Starting TMApplication using Java 17.0.14 with PID 67072 (/Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm/target/classes started by shihuangzhu in /Users/<USER>/IdeaProjects/tapdata_v3)
2025-04-08 11:41:32.770 [tm] [main] INFO  com.tapdata.tm.TMApplication - The following 2 profiles are active: "idaas", "default"
2025-04-08 11:41:33.715 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:41:33.715 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:41:33.874 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 155 ms. Found 2 MongoDB repository interfaces.
2025-04-08 11:41:33.875 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:41:33.876 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:41:33.878 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 MongoDB repository interfaces.
2025-04-08 11:41:33.878 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:41:33.878 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-04-08 11:41:33.880 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 MongoDB repository interfaces.
2025-04-08 11:41:33.886 [tm] [main] INFO  c.t.t.w.c.WebSocketHandlerImportBeanRegistrar - The number of webSocketHandler beans successfully registered: 17
2025-04-08 11:41:34.121 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-04-08 11:41:34.122 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-04-08 11:41:34.168 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.task.repository.TaskDagCheckLogRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-04-08 11:41:34.169 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Elasticsearch - Could not safely identify store assignment for repository candidate interface com.tapdata.tm.Settings.repository.SettingsRepository; If you want this repository to be a Elasticsearch repository, consider annotating your entities with one of these annotations: org.springframework.data.elasticsearch.annotations.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.elasticsearch.repository.ElasticsearchRepository
2025-04-08 11:41:34.169 [tm] [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Elasticsearch repository interfaces.
2025-04-08 11:41:34.782 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 3001 (http)
2025-04-08 11:41:34.791 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - The ["http-nio-3001"] connector has been configured to support HTTP upgrade to [h2c]
2025-04-08 11:41:34.791 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-3001"]
2025-04-08 11:41:34.791 [tm] [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-08 11:41:34.792 [tm] [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-04-08 11:41:34.834 [tm] [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-08 11:41:34.834 [tm] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2030 ms
2025-04-08 11:41:34.982 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-data", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5ffdd510, com.mongodb.Jep395RecordCodecProvider@8c18bde, com.mongodb.KotlinCodecProvider@6719f206]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:41:35.009 [tm] [cluster-ClusterId{value='67f49aeef95fa344179490e3', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=15311583, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:41:30 CST 2025, lastUpdateTimeNanos=152822574071291}
2025-04-08 11:41:36.606 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5ffdd510, com.mongodb.Jep395RecordCodecProvider@8c18bde, com.mongodb.KotlinCodecProvider@6719f206]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:41:36.607 [tm] [cluster-ClusterId{value='67f49af0f95fa344179490e4', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=865750, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:41:35 CST 2025, lastUpdateTimeNanos=152824173737875}
2025-04-08 11:41:36.955 [tm] [main] INFO  c.t.t.c.component.ProductComponent - Current product type settings is DAAS.
2025-04-08 11:41:36.968 [tm] [ForkJoinPool.commonPool-worker-1] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5ffdd510, com.mongodb.Jep395RecordCodecProvider@8c18bde, com.mongodb.KotlinCodecProvider@6719f206]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:41:36.969 [tm] [cluster-ClusterId{value='67f49af0f95fa344179490e5', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=794333, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:41:35 CST 2025, lastUpdateTimeNanos=152824535522291}
2025-04-08 11:41:38.670 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-04-08 11:41:38.678 [tm] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-04-08 11:41:38.678 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v2.5.0 created.
2025-04-08 11:41:38.678 [tm] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-04-08 11:41:38.678 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-04-08 11:41:38.679 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-04-08 11:41:38.679 [tm] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.5.0
2025-04-08 11:41:38.679 [tm] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@8caffbd
2025-04-08 11:41:38.847 [tm] [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name userServiceImpl
2025-04-08 11:41:39.075 [tm] [main] WARN  o.s.w.s.m.m.a.RequestMappingHandlerMapping - Multiple @RequestMapping annotations found on public com.tapdata.tm.base.dto.ResponseMessage com.tapdata.tm.message.controller.MessageController.read(java.lang.String,java.lang.String) throws java.io.IOException, but only the first will be used: [@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name="", params={}, path={}, produces={}, value={}), @org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name="", params={}, path={}, produces={}, value={})]
2025-04-08 11:41:39.511 [tm] [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoints beneath base path '/actuator'
2025-04-08 11:41:40.258 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'date'.
2025-04-08 11:41:40.259 [tm] [main] WARN  o.s.d.e.c.m.SimpleElasticsearchPersistentProperty - Unsupported type 'class java.lang.Object' for date property 'millis'.
2025-04-08 11:41:40.434 [tm] [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-3001"]
2025-04-08 11:41:40.440 [tm] [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 3001 (http) with context path '/'
2025-04-08 11:41:40.441 [tm] [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-04-08 11:41:40.441 [tm] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-04-08 11:41:40.457 [tm] [main] INFO  c.t.t.s.u.StateMachineProcessorManager - Urls path: /Users/<USER>/IdeaProjects/tapdata_v3/new_tapdata/manager/tm/target/classes/com/tapdata/tm/statemachine/processor, protocol: file
2025-04-08 11:41:40.473 [tm] [main] INFO  com.tapdata.tm.TMApplication - Started TMApplication in 8.017 seconds (process running for 8.523)
2025-04-08 11:41:40.543 [tm] [main] INFO  com.tapdata.tm.init.PatchesRunner - Not have any DAAS patches, current version is 3.21-1
2025-04-08 11:41:40.591 [tm] [main] INFO  com.tapdata.tm.init.InitLogMap - 
  _______              _       _           _                    _             _                                _      _           _ 
 |__   __|            | |     | |         | |                  | |           | |                              | |    | |         | |
    | | __ _ _ __   __| | __ _| |_ __ _   | |_ _ __ ___     ___| |_ __ _ _ __| |_     ___ ___  _ __ ___  _ __ | | ___| |_ ___  __| |
    | |/ _` | '_ \ / _` |/ _` | __/ _` |  | __| '_ ` _ \   / __| __/ _` | '__| __|   / __/ _ \| '_ ` _ \| '_ \| |/ _ \ __/ _ \/ _` |
    | | (_| | |_) | (_| | (_| | || (_| |  | |_| | | | | |  \__ \ || (_| | |  | |_   | (_| (_) | | | | | | |_) | |  __/ ||  __/ (_| |
    |_|\__,_| .__/ \__,_|\__,_|\__\__,_|   \__|_| |_| |_|  |___/\__\__,_|_|   \__|   \___\___/|_| |_| |_| .__/|_|\___|\__\___|\__,_|
            | |                                                                                         | |                         
            |_|                                                                                         |_|                         
2025-04-08 11:41:40.592 [tm] [Thread-1] INFO  com.tapdata.tm.TMApplication - No need to start TCM
2025-04-08 11:41:40.596 [tm] [main] INFO  com.tapdata.tm.TMApplication - admin access code is 3324cfdf-7d3e-4792-bd32-571638d4562f
2025-04-08 11:41:40.751 [tm] [main] INFO  org.reflections.Reflections - Reflections took 140 ms to scan 18 urls, producing 62 keys and 1097 values
2025-04-08 11:41:40.854 [tm] [main] INFO  org.reflections.Reflections - Reflections took 14 ms to scan 12 urls, producing 15 keys and 147 values
2025-04-08 11:41:40.857 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 10 keys and 24 values
2025-04-08 11:41:40.858 [tm] [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "13.6.2"}, "platform": "Java/Azul Systems, Inc./17.0.14+7-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5ffdd510, com.mongodb.Jep395RecordCodecProvider@8c18bde, com.mongodb.KotlinCodecProvider@6719f206]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-04-08 11:41:40.860 [tm] [cluster-ClusterId{value='67f49af4f95fa344179490e6', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=17, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=660917, minRoundTripTimeNanos=0, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000018, setVersion=1, topologyVersion=TopologyVersion{processId=67ecee2c09bcc0fe9725873a, counter=6}, lastWriteDate=Tue Apr 08 11:41:40 CST 2025, lastUpdateTimeNanos=152828426184666}
2025-04-08 11:41:40.919 [tm] [main] INFO  org.reflections.Reflections - Reflections took 1 ms to scan 1 urls, producing 4 keys and 9 values
2025-04-08 11:41:40.971 [tm] [main] INFO  PDK - IPHolder [Server ip is [***********, *************, 127.0.0.1] by ipPrefix null ethPrefix null]
2025-04-08 11:41:43.855 [tm] [http-nio-3001-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-04-08 11:41:43.855 [tm] [http-nio-3001-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-04-08 11:41:43.856 [tm] [http-nio-3001-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-04-08 11:41:43.857 [tm] [main] INFO  PDK - NodeHealthManager [Node ********** added into healthy node list, nodeHealth NodeHealth id ********** health 0 online 0 time Tue Apr 08 11:41:41 CST 2025; , nodeRegistry NodeRegistry ips [***********, *************, 127.0.0.1] httpPort 3000 wsPort 8246 type proxy time Tue Apr 08 11:41:41 CST 2025; ]
2025-04-08 11:42:00.014 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:42:00.036 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:42:05.015 [tm] [taskScheduler-30] INFO  o.s.s.a.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [taskScheduler, NotificationExecutor]
2025-04-08 11:42:15.600 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:42:20.004 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:42:20.006 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:42:35.679 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:42:40.005 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:42:40.010 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:42:45.716 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:42:50.736 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:43:00.017 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - begin to stop cron inspect
2025-04-08 11:43:00.037 [tm] [InspectSchedule-execute] INFO  c.t.t.i.service.InspectServiceImpl - finish to stop cron inspect
2025-04-08 11:43:00.776 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:43:10.800 [tm] [TransformerSchedule-transformer] INFO  o.s.d.m.core.convert.QueryMapper - Could not map 'TaskEntity.dag.nodes.type'; Maybe a fragment in 'DAG -> Node -> String' is considered a simple type; Mapper continues with dag.nodes.type
2025-04-08 11:43:11.643 [tm] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-04-08 11:43:11.644 [tm] [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
